{"date": "June 14", "url": "https://wikipedia.org/wiki/June_14", "data": {"Events": [{"year": "1158", "text": "The city of Munich is founded by <PERSON> the <PERSON> on the banks of the river Isar.", "html": "1158 - The city of <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lion\" title=\"<PERSON> the Lion\"><PERSON> the Lion</a> on the banks of the river <a href=\"https://wikipedia.org/wiki/Isar\" title=\"Isar\">Isar</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lion\" title=\"<PERSON> the Lion\"><PERSON> the Lion</a> on the banks of the river <a href=\"https://wikipedia.org/wiki/Isar\" title=\"Isar\">Isar</a>.", "links": [{"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "<PERSON> the Lion", "link": "https://wikipedia.org/wiki/<PERSON>_the_Lion"}, {"title": "Isar", "link": "https://wikipedia.org/wiki/Isar"}]}, {"year": "1216", "text": "First Barons' War: Prince <PERSON> of France takes the city of Winchester, abandoned by <PERSON>, King of England, and soon conquers over half of the kingdom.", "html": "1216 - <a href=\"https://wikipedia.org/wiki/First_Barons%27_War\" title=\"First Barons' War\">First Barons' War</a>: Prince <a href=\"https://wikipedia.org/wiki/Louis_VIII_of_France\" title=\"Louis VIII of France\"><PERSON> of France</a> takes the city of <a href=\"https://wikipedia.org/wiki/Winchester\" title=\"Winchester\">Winchester</a>, abandoned by <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON>, King of England</a>, and soon conquers over half of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barons%27_War\" title=\"First Barons' War\">First Barons' War</a>: Prince <a href=\"https://wikipedia.org/wiki/Louis_VIII_of_France\" title=\"<PERSON> VIII of France\"><PERSON> of France</a> takes the city of <a href=\"https://wikipedia.org/wiki/Winchester\" title=\"Winchester\">Winchester</a>, abandoned by <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON>, King of England</a>, and soon conquers over half of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">kingdom</a>.", "links": [{"title": "First Barons' War", "link": "https://wikipedia.org/wiki/First_Barons%27_War"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VIII_of_France"}, {"title": "Winchester", "link": "https://wikipedia.org/wiki/Winchester"}, {"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1276", "text": "While taking exile in Fuzhou, away from the advancing Mongol invaders, the remnants of the Song dynasty court hold the coronation ceremony for Emperor <PERSON><PERSON><PERSON>.", "html": "1276 - While taking exile in <a href=\"https://wikipedia.org/wiki/Fuzhou\" title=\"Fuzhou\">Fuzhou</a>, away from the advancing <a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Song_dynasty\" title=\"Mongol conquest of the Song dynasty\">Mongol invaders</a>, the remnants of the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> court hold the coronation ceremony for <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>zong\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a>.", "no_year_html": "While taking exile in <a href=\"https://wikipedia.org/wiki/Fuzhou\" title=\"Fuzhou\">Fuzhou</a>, away from the advancing <a href=\"https://wikipedia.org/wiki/Mongol_conquest_of_the_Song_dynasty\" title=\"Mongol conquest of the Song dynasty\">Mongol invaders</a>, the remnants of the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> court hold the coronation ceremony for <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>zong\" title=\"Emperor <PERSON><PERSON>zong\">Emperor <PERSON><PERSON><PERSON></a>.", "links": [{"title": "Fuzhou", "link": "https://wikipedia.org/wiki/Fuzhou"}, {"title": "Mongol conquest of the Song dynasty", "link": "https://wikipedia.org/wiki/Mongol_conquest_of_the_Song_dynasty"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}, {"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1285", "text": "Second Mongol invasion of Vietnam: Forces led by Prince <PERSON><PERSON><PERSON><PERSON> of the Trần dynasty destroy most of the invading Mongol naval fleet in a battle at Chuong Duong.", "html": "1285 - <a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam\" title=\"Mongol invasions of Vietnam\">Second Mongol invasion of Vietnam</a>: Forces led by Prince <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_<PERSON>uang_Kh%E1%BA%A3i\" title=\"Trần Quang Khải\">Trầ<PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a> destroy most of the invading <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Mongol</a> naval fleet in a battle at Chuong Duong.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam\" title=\"Mongol invasions of Vietnam\">Second Mongol invasion of Vietnam</a>: Forces led by Prince <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_<PERSON><PERSON>_Kh%E1%BA%A3i\" title=\"Trần Quang Khải\">Trầ<PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty\" title=\"Trần dynasty\">Trần dynasty</a> destroy most of the invading <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Mongol</a> naval fleet in a battle at Chuong Duong.", "links": [{"title": "Mongol invasions of Vietnam", "link": "https://wikipedia.org/wiki/Mongol_invasions_of_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Quang_Kh%E1%BA%A3i"}, {"title": "Trần dynasty", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_dynasty"}, {"title": "Yuan dynasty", "link": "https://wikipedia.org/wiki/Yuan_dynasty"}]}, {"year": "1287", "text": "<PERSON><PERSON><PERSON> Khan defeats the force of <PERSON><PERSON> and other traditionalist Borjigin princes in East Mongolia and Manchuria.", "html": "1287 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats the force of <PERSON><PERSON> and other traditionalist <a href=\"https://wikipedia.org/wiki/Bor<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> princes in East <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats the force of <PERSON><PERSON> and other traditionalist <a href=\"https://wikipedia.org/wiki/Bo<PERSON><PERSON><PERSON>\" title=\"Bo<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> princes in East <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}, {"title": "Manchuria", "link": "https://wikipedia.org/wiki/Manchuria"}]}, {"year": "1381", "text": "<PERSON> of England meets leaders of Peasants' Revolt at Mile End. The Tower of London is stormed by rebels who enter without resistance.", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> meets leaders of <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a> at <a href=\"https://wikipedia.org/wiki/Mile_End\" title=\"Mile End\">Mile End</a>. The <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> is stormed by rebels who enter without resistance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> meets leaders of <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a> at <a href=\"https://wikipedia.org/wiki/Mile_End\" title=\"Mile End\">Mile End</a>. The <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> is stormed by rebels who enter without resistance.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Peasants' Revolt", "link": "https://wikipedia.org/wiki/Peasants%27_<PERSON>olt"}, {"title": "Mile End", "link": "https://wikipedia.org/wiki/Mile_End"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}]}, {"year": "1404", "text": "Welsh rebel leader <PERSON><PERSON>, having declared himself Prince of Wales, allies himself with the French against King <PERSON> of England.", "html": "1404 - Welsh rebel leader <a href=\"https://wikipedia.org/wiki/O<PERSON>_Glynd%C5%B5r\" title=\"<PERSON><PERSON> Glyndŵr\"><PERSON><PERSON></a>, having declared himself <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>, allies himself with the French against King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> IV of England</a>.", "no_year_html": "Welsh rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Glynd%C5%B5r\" title=\"<PERSON><PERSON> Glyndŵr\"><PERSON><PERSON></a>, having declared himself <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a>, allies himself with the French against King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Owain_Glynd%C5%B5r"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_England"}]}, {"year": "1618", "text": "<PERSON><PERSON> prints the first Dutch newspaper Courante uyt Italien, Duytslandt, &c. in Amsterdam (approximate date).", "html": "1618 - <PERSON><PERSON> prints the first Dutch newspaper <i><a href=\"https://wikipedia.org/wiki/Courante_uyt_Italien,_Duytslandt,_%26c.\" title=\"Courante uyt Italien, Duytslandt, &amp;c.\">Courante uyt Italien, Duytslandt, &amp;c.</a></i> in <a href=\"https://wikipedia.org/wiki/Amsterdam\" title=\"Amsterdam\">Amsterdam</a> (approximate date).", "no_year_html": "<PERSON><PERSON> prints the first Dutch newspaper <i><a href=\"https://wikipedia.org/wiki/Courante_uyt_Italien,_Duytslandt,_%26c.\" title=\"Courante uyt Italien, Duytslandt, &amp;c.\">Courante uyt Italien, Duytslandt, &amp;c.</a></i> in <a href=\"https://wikipedia.org/wiki/Amsterdam\" title=\"Amsterdam\">Amsterdam</a> (approximate date).", "links": [{"title": "Courante uyt Italien, Duytslandt, &c.", "link": "https://wikipedia.org/wiki/<PERSON>ura<PERSON>_<PERSON>yt_Italien,_<PERSON><PERSON>slandt,_%26c."}, {"title": "Amsterdam", "link": "https://wikipedia.org/wiki/Amsterdam"}]}, {"year": "1645", "text": "English Civil War: Battle of Naseby: Twelve thousand Royalist forces are beaten by fifteen thousand Parliamentarian soldiers.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Naseby\" title=\"Battle of Naseby\">Battle of Naseby</a>: Twelve thousand <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Royalist</a> forces are beaten by fifteen thousand <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Parliamentarian</a> soldiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Naseby\" title=\"Battle of Naseby\">Battle of Naseby</a>: Twelve thousand <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Royalist</a> forces are beaten by fifteen thousand <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Parliamentarian</a> soldiers.", "links": [{"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Battle of Naseby", "link": "https://wikipedia.org/wiki/Battle_of_Naseby"}, {"title": "Roundhead", "link": "https://wikipedia.org/wiki/Roundhead"}, {"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}]}, {"year": "1658", "text": "Franco-Spanish War: <PERSON><PERSON><PERSON> and the French army win a decisive victory over the Spanish at the battle of the Dunes.", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Franco-Spanish_War_(1635%E2%80%931659)\" title=\"Franco-Spanish War (1635-1659)\">Franco-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Turenne\" class=\"mw-redirect\" title=\"Turenne\"><PERSON><PERSON>ne</a> and the French army win a decisive victory over the Spanish at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Dunes_(1658)\" title=\"Battle of the Dunes (1658)\">battle of the Dunes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Spanish_War_(1635%E2%80%931659)\" title=\"Franco-Spanish War (1635-1659)\">Franco-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Turenne\" class=\"mw-redirect\" title=\"Turenne\">Turenne</a> and the French army win a decisive victory over the Spanish at the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Dunes_(1658)\" title=\"Battle of the Dunes (1658)\">battle of the Dunes</a>.", "links": [{"title": "Franco-Spanish War (1635-1659)", "link": "https://wikipedia.org/wiki/Franco-Spanish_War_(1635%E2%80%931659)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renne"}, {"title": "Battle of the Dunes (1658)", "link": "https://wikipedia.org/wiki/Battle_of_the_Dunes_(1658)"}]}, {"year": "1690", "text": "King <PERSON> of England (<PERSON> of Orange) lands in Ireland to confront the former King <PERSON>.", "html": "1690 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (<PERSON> of Orange) lands in Ireland to confront the former <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\">King <PERSON> II</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> (<PERSON> of Orange) lands in Ireland to confront the former <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"<PERSON> II of England\">King <PERSON> II</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1775", "text": "American Revolutionary War: the Continental Army is established by the Continental Congress, marking the birth of the United States Armed Forces.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> is established by the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a>, marking the birth of the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> is established by the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a>, marking the birth of the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States Armed Forces</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}]}, {"year": "1777", "text": "The Second Continental Congress passes the Flag Act of 1777 adopting the Stars and Stripes as the Flag of the United States.", "html": "1777 - The <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Flag_Acts\" title=\"Flag Acts\">Flag Act of 1777</a> adopting the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Stars and Stripes</a> as the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Flag of the United States</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Flag_Acts\" title=\"Flag Acts\">Flag Act of 1777</a> adopting the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Stars and Stripes</a> as the <a href=\"https://wikipedia.org/wiki/Flag_of_the_United_States\" title=\"Flag of the United States\">Flag of the United States</a>.", "links": [{"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}, {"title": "Flag Acts", "link": "https://wikipedia.org/wiki/Flag_Acts"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}, {"title": "Flag of the United States", "link": "https://wikipedia.org/wiki/Flag_of_the_United_States"}]}, {"year": "1789", "text": "Mutiny on the Bounty: HMS Bounty mutiny survivors including Captain <PERSON> and 18 others reach Timor after a nearly 7,400 km (4,600 mi) journey in an open boat.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/Mutiny_on_the_Bounty\" title=\"Mutiny on the Bounty\">Mutiny on the Bounty</a>: <a href=\"https://wikipedia.org/wiki/HMS_Bounty\" title=\"HMS Bounty\">HMS <i>Bounty</i></a> mutiny survivors including Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 18 others reach <a href=\"https://wikipedia.org/wiki/Timor\" title=\"Timor\">Timor</a> after a nearly 7,400 km (4,600 mi) journey in an open boat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mutiny_on_the_Bounty\" title=\"Mutiny on the Bounty\">Mutiny on the Bounty</a>: <a href=\"https://wikipedia.org/wiki/HMS_Bounty\" title=\"HMS Bounty\">HMS <i>Bounty</i></a> mutiny survivors including Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 18 others reach <a href=\"https://wikipedia.org/wiki/Timor\" title=\"Timor\">Timor</a> after a nearly 7,400 km (4,600 mi) journey in an open boat.", "links": [{"title": "Mutiny on the Bounty", "link": "https://wikipedia.org/wiki/Mutiny_on_the_Bounty"}, {"title": "HMS Bounty", "link": "https://wikipedia.org/wiki/HMS_Bounty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Timor", "link": "https://wikipedia.org/wiki/Timor"}]}, {"year": "1800", "text": "The French Army of First Consul <PERSON> defeats the Austrians at the Battle of Marengo in Northern Italy and re-conquers Italy.", "html": "1800 - The French Army of <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\">First Consul <PERSON></a> defeats the Austrians at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marengo\" title=\"Battle of Marengo\">Battle of Marengo</a> in Northern Italy and re-conquers Italy.", "no_year_html": "The French Army of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">First Consul <PERSON></a> defeats the Austrians at the <a href=\"https://wikipedia.org/wiki/Battle_of_Marengo\" title=\"Battle of Marengo\">Battle of Marengo</a> in Northern Italy and re-conquers Italy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Battle of Marengo", "link": "https://wikipedia.org/wiki/Battle_of_Marengo"}]}, {"year": "1807", "text": "Emperor <PERSON>'s French Grande Armée defeats the Russian Army at the Battle of Friedland in Poland (modern Russian  Kaliningrad Oblast) ending the War of the Fourth Coalition.", "html": "1807 - Emperor <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s French <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> defeats the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian</a> Army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Friedland\" title=\"Battle of Friedland\">Battle of Friedland</a> in Poland (modern Russian <a href=\"https://wikipedia.org/wiki/Kaliningrad_Oblast\" title=\"Kaliningrad Oblast\">Kaliningrad Oblast</a>) ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a>'s French <a href=\"https://wikipedia.org/wiki/Grande_Arm%C3%A9e\" title=\"Grande Armée\">Grande Armée</a> defeats the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian</a> Army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Friedland\" title=\"Battle of Friedland\">Battle of Friedland</a> in Poland (modern Russian <a href=\"https://wikipedia.org/wiki/Kaliningrad_Oblast\" title=\"Kaliningrad Oblast\">Kaliningrad Oblast</a>) ending the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Grande Armée", "link": "https://wikipedia.org/wiki/Grande_Arm%C3%A9e"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Battle of Friedland", "link": "https://wikipedia.org/wiki/Battle_of_Friedland"}, {"title": "Kaliningrad Oblast", "link": "https://wikipedia.org/wiki/Kaliningrad_Oblast"}, {"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}]}, {"year": "1821", "text": "<PERSON><PERSON>, king of Sennar, surrenders his throne and realm to <PERSON>, general of the Ottoman Empire, bringing the 300 year old Sudanese kingdom to an end.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Bad<PERSON>_VII\" title=\"Badi VII\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Funj_Sultanate\" title=\"Funj Sultanate\"><PERSON><PERSON></a>, surrenders his throne and realm to <PERSON>, general of the Ottoman Empire, bringing the 300 year old Sudanese kingdom to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_VII\" title=\"Badi VII\"><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Funj_Sultanate\" title=\"Funj Sultanate\"><PERSON><PERSON></a>, surrenders his throne and realm to <PERSON>, general of the Ottoman Empire, bringing the 300 year old Sudanese kingdom to an end.", "links": [{"title": "Badi VII", "link": "https://wikipedia.org/wiki/Badi_VII"}, {"title": "Funj Sultanate", "link": "https://wikipedia.org/wiki/Funj_Sultanate"}]}, {"year": "1822", "text": "<PERSON> proposes a difference engine in a paper to the Royal Astronomical Society.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proposes a <a href=\"https://wikipedia.org/wiki/Difference_engine\" title=\"Difference engine\">difference engine</a> in a paper to the <a href=\"https://wikipedia.org/wiki/Royal_Astronomical_Society\" title=\"Royal Astronomical Society\">Royal Astronomical Society</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proposes a <a href=\"https://wikipedia.org/wiki/Difference_engine\" title=\"Difference engine\">difference engine</a> in a paper to the <a href=\"https://wikipedia.org/wiki/Royal_Astronomical_Society\" title=\"Royal Astronomical Society\">Royal Astronomical Society</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Difference engine", "link": "https://wikipedia.org/wiki/Difference_engine"}, {"title": "Royal Astronomical Society", "link": "https://wikipedia.org/wiki/Royal_Astronomical_Society"}]}, {"year": "1830", "text": "Beginning of the French colonization of Algeria: Thirty-four thousand French soldiers begin their invasion of Algiers, landing 27 kilometers west at Sidi Fredj.", "html": "1830 - Beginning of the <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French colonization of Algeria</a>: Thirty-four thousand French soldiers begin their <a href=\"https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830\" class=\"mw-redirect\" title=\"Invasion of Algiers in 1830\">invasion of Algiers</a>, landing 27 kilometers west at <a href=\"https://wikipedia.org/wiki/Sidi_Fredj\" title=\"Sidi Fredj\"><PERSON><PERSON></a>.", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/French_Algeria\" title=\"French Algeria\">French colonization of Algeria</a>: Thirty-four thousand French soldiers begin their <a href=\"https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830\" class=\"mw-redirect\" title=\"Invasion of Algiers in 1830\">invasion of Algiers</a>, landing 27 kilometers west at <a href=\"https://wikipedia.org/wiki/Sidi_Fredj\" title=\"Sidi Fredj\"><PERSON><PERSON></a>.", "links": [{"title": "French Algeria", "link": "https://wikipedia.org/wiki/French_Algeria"}, {"title": "Invasion of Algiers in 1830", "link": "https://wikipedia.org/wiki/Invasion_of_Algiers_in_1830"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sidi_Fredj"}]}, {"year": "1839", "text": "Henley Royal Regatta: the village of Henley-on-Thames, on the River Thames in Oxfordshire, stages its first regatta.", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Henley_Royal_Regatta\" title=\"Henley Royal Regatta\">Henley Royal Regatta</a>: the village of <a href=\"https://wikipedia.org/wiki/Henley-on-Thames\" title=\"Henley-on-Thames\">Henley-on-Thames</a>, on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> in <a href=\"https://wikipedia.org/wiki/Oxfordshire\" title=\"Oxfordshire\">Oxfordshire</a>, stages its first <a href=\"https://wikipedia.org/wiki/Regatta\" class=\"mw-redirect\" title=\"Regatta\">regatta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henley_Royal_Regatta\" title=\"Henley Royal Regatta\">Henley Royal Regatta</a>: the village of <a href=\"https://wikipedia.org/wiki/Henley-on-Thames\" title=\"Henley-on-Thames\">Henley-on-Thames</a>, on the <a href=\"https://wikipedia.org/wiki/River_Thames\" title=\"River Thames\">River Thames</a> in <a href=\"https://wikipedia.org/wiki/Oxfordshire\" title=\"Oxfordshire\">Oxfordshire</a>, stages its first <a href=\"https://wikipedia.org/wiki/Regatta\" class=\"mw-redirect\" title=\"Regatta\">regatta</a>.", "links": [{"title": "Henley Royal Regatta", "link": "https://wikipedia.org/wiki/Henley_Royal_Regatta"}, {"title": "Henley-on-Thames", "link": "https://wikipedia.org/wiki/Henley-on-Thames"}, {"title": "River Thames", "link": "https://wikipedia.org/wiki/River_Thames"}, {"title": "Oxfordshire", "link": "https://wikipedia.org/wiki/Oxfordshire"}, {"title": "Regatta", "link": "https://wikipedia.org/wiki/Regatta"}]}, {"year": "1846", "text": "Bear Flag Revolt begins: Anglo settlers in Sonoma, California, start a rebellion against Mexico and proclaim the California Republic.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Bear_Flag_Revolt\" class=\"mw-redirect\" title=\"Bear Flag Revolt\">Bear Flag Revolt</a> begins: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Anglo</a> settlers in <a href=\"https://wikipedia.org/wiki/Sonoma,_California\" title=\"Sonoma, California\">Sonoma, California</a>, start a rebellion against Mexico and proclaim the <a href=\"https://wikipedia.org/wiki/California_Republic\" title=\"California Republic\">California Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bear_Flag_Revolt\" class=\"mw-redirect\" title=\"Bear Flag Revolt\">Bear Flag Revolt</a> begins: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Anglo</a> settlers in <a href=\"https://wikipedia.org/wiki/Sonoma,_California\" title=\"Sonoma, California\">Sonoma, California</a>, start a rebellion against Mexico and proclaim the <a href=\"https://wikipedia.org/wiki/California_Republic\" title=\"California Republic\">California Republic</a>.", "links": [{"title": "Bear Flag Revolt", "link": "https://wikipedia.org/wiki/Bear_Flag_Revolt"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Sonoma, California", "link": "https://wikipedia.org/wiki/Sonoma,_California"}, {"title": "California Republic", "link": "https://wikipedia.org/wiki/California_Republic"}]}, {"year": "1863", "text": "American Civil War: Second Battle of Winchester: A Union garrison is defeated by the Army of Northern Virginia in the Shenandoah Valley town of Winchester, Virginia.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Winchester\" title=\"Second Battle of Winchester\">Second Battle of Winchester</a>: A <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> garrison is defeated by the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> in the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a> town of <a href=\"https://wikipedia.org/wiki/Winchester,_Virginia\" title=\"Winchester, Virginia\">Winchester, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Winchester\" title=\"Second Battle of Winchester\">Second Battle of Winchester</a>: A <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> garrison is defeated by the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> in the <a href=\"https://wikipedia.org/wiki/Shenandoah_Valley\" title=\"Shenandoah Valley\">Shenandoah Valley</a> town of <a href=\"https://wikipedia.org/wiki/Winchester,_Virginia\" title=\"Winchester, Virginia\">Winchester, Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Second Battle of Winchester", "link": "https://wikipedia.org/wiki/Second_Battle_of_Winchester"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Shenandoah Valley", "link": "https://wikipedia.org/wiki/Shenandoah_Valley"}, {"title": "Winchester, Virginia", "link": "https://wikipedia.org/wiki/Winchester,_Virginia"}]}, {"year": "1863", "text": "Second Assault on the Confederate works at the Siege of Port Hudson during the American Civil War.", "html": "1863 - Second Assault on the Confederate works at the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> during the American Civil War.", "no_year_html": "Second Assault on the Confederate works at the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> during the American Civil War.", "links": [{"title": "Siege of Port Hudson", "link": "https://wikipedia.org/wiki/Siege_of_Port_Hudson"}]}, {"year": "1872", "text": "Trade unions are legalized in Canada.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">Trade unions</a> are legalized in Canada.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">Trade unions</a> are legalized in Canada.", "links": [{"title": "Trade union", "link": "https://wikipedia.org/wiki/Trade_union"}]}, {"year": "1888", "text": "The White Rajahs territories become the British protectorate of Sarawak.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/White_Rajah\" class=\"mw-redirect\" title=\"White Rajah\">White Rajahs</a> territories become the <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorate</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sarawak\" class=\"mw-redirect\" title=\"Kingdom of Sarawak\">Sarawak</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/White_Rajah\" class=\"mw-redirect\" title=\"White Rajah\">White Rajahs</a> territories become the <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorate</a> of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sarawak\" class=\"mw-redirect\" title=\"Kingdom of Sarawak\">Sarawak</a>.", "links": [{"title": "White Rajah", "link": "https://wikipedia.org/wiki/White_<PERSON>h"}, {"title": "British protectorate", "link": "https://wikipedia.org/wiki/British_protectorate"}, {"title": "Kingdom of Sarawak", "link": "https://wikipedia.org/wiki/Kingdom_of_Sarawak"}]}, {"year": "1900", "text": "Hawaii becomes a United States territory.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Territory_of_Hawaii\" title=\"Territory of Hawaii\">Hawaii</a> becomes a <a href=\"https://wikipedia.org/wiki/United_States_territory\" class=\"mw-redirect\" title=\"United States territory\">United States territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Territory_of_Hawaii\" title=\"Territory of Hawaii\">Hawaii</a> becomes a <a href=\"https://wikipedia.org/wiki/United_States_territory\" class=\"mw-redirect\" title=\"United States territory\">United States territory</a>.", "links": [{"title": "Territory of Hawaii", "link": "https://wikipedia.org/wiki/Territory_of_Hawaii"}, {"title": "United States territory", "link": "https://wikipedia.org/wiki/United_States_territory"}]}, {"year": "1900", "text": "The second German Naval Law calls for the Imperial German Navy to be doubled in size, resulting in an Anglo-German naval arms race.", "html": "1900 - The second <a href=\"https://wikipedia.org/wiki/German_Naval_Laws\" title=\"German Naval Laws\">German Naval Law</a> calls for the <a href=\"https://wikipedia.org/wiki/Imperial_German_Navy\" title=\"Imperial German Navy\">Imperial German Navy</a> to be doubled in size, resulting in an <a href=\"https://wikipedia.org/wiki/Anglo-German_naval_arms_race\" title=\"Anglo-German naval arms race\">Anglo-German naval arms race</a>.", "no_year_html": "The second <a href=\"https://wikipedia.org/wiki/German_Naval_Laws\" title=\"German Naval Laws\">German Naval Law</a> calls for the <a href=\"https://wikipedia.org/wiki/Imperial_German_Navy\" title=\"Imperial German Navy\">Imperial German Navy</a> to be doubled in size, resulting in an <a href=\"https://wikipedia.org/wiki/Anglo-German_naval_arms_race\" title=\"Anglo-German naval arms race\">Anglo-German naval arms race</a>.", "links": [{"title": "German Naval Laws", "link": "https://wikipedia.org/wiki/German_Naval_Laws"}, {"title": "Imperial German Navy", "link": "https://wikipedia.org/wiki/Imperial_German_Navy"}, {"title": "Anglo-German naval arms race", "link": "https://wikipedia.org/wiki/Anglo-German_naval_arms_race"}]}, {"year": "1907", "text": "The National Association for Women's Suffrage succeeds in getting Norwegian women the right to vote in parliamentary elections.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/National_Association_for_Women%27s_Suffrage_(Norway)\" title=\"National Association for Women's Suffrage (Norway)\">National Association for Women's Suffrage</a> succeeds in getting Norwegian women the right to vote in parliamentary elections.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Association_for_Women%27s_Suffrage_(Norway)\" title=\"National Association for Women's Suffrage (Norway)\">National Association for Women's Suffrage</a> succeeds in getting Norwegian women the right to vote in parliamentary elections.", "links": [{"title": "National Association for Women's Suffrage (Norway)", "link": "https://wikipedia.org/wiki/National_Association_for_Women%27s_Suffrage_(Norway)"}]}, {"year": "1919", "text": "<PERSON> and <PERSON> depart from St. John's, Newfoundland on the first nonstop transatlantic flight.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>\" title=\"Transatlantic flight of <PERSON>cock and Brown\"><PERSON> and <PERSON> depart</a> from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St. John's, Newfoundland\">St. John's, Newfoundland</a> on the first nonstop <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>\" title=\"Transatlantic flight of <PERSON>cock and Brown\"><PERSON> and <PERSON> depart</a> from <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St. John's, Newfoundland\">St. John's, Newfoundland</a> on the first nonstop <a href=\"https://wikipedia.org/wiki/Transatlantic_flight\" title=\"Transatlantic flight\">transatlantic flight</a>.", "links": [{"title": "Transatlantic flight of <PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>"}, {"title": "St. John's, Newfoundland", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>%27s,_Newfoundland"}, {"title": "Transatlantic flight", "link": "https://wikipedia.org/wiki/Transatlantic_flight"}]}, {"year": "1926", "text": "Brazil leaves the League of Nations.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> leaves the <a href=\"https://wikipedia.org/wiki/League_of_Nations\" title=\"League of Nations\">League of Nations</a>.", "links": [{"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "League of Nations", "link": "https://wikipedia.org/wiki/League_of_Nations"}]}, {"year": "1934", "text": "The landmark Australian Eastern Mission returns from its three-month tour of East and South-East Asia.", "html": "1934 - The landmark <a href=\"https://wikipedia.org/wiki/Australian_Eastern_Mission\" title=\"Australian Eastern Mission\">Australian Eastern Mission</a> returns from its three-month tour of East and South-East Asia.", "no_year_html": "The landmark <a href=\"https://wikipedia.org/wiki/Australian_Eastern_Mission\" title=\"Australian Eastern Mission\">Australian Eastern Mission</a> returns from its three-month tour of East and South-East Asia.", "links": [{"title": "Australian Eastern Mission", "link": "https://wikipedia.org/wiki/Australian_Eastern_Mission"}]}, {"year": "1937", "text": "Pennsylvania becomes the first (and only) state of the United States to celebrate Flag Day officially as a state holiday.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> becomes the first (and only) state of the United States to celebrate <a href=\"https://wikipedia.org/wiki/Flag_Day_(United_States)\" title=\"Flag Day (United States)\">Flag Day</a> officially as a <a href=\"https://wikipedia.org/wiki/State_holiday\" class=\"mw-redirect\" title=\"State holiday\">state holiday</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> becomes the first (and only) state of the United States to celebrate <a href=\"https://wikipedia.org/wiki/Flag_Day_(United_States)\" title=\"Flag Day (United States)\">Flag Day</a> officially as a <a href=\"https://wikipedia.org/wiki/State_holiday\" class=\"mw-redirect\" title=\"State holiday\">state holiday</a>.", "links": [{"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Flag Day (United States)", "link": "https://wikipedia.org/wiki/Flag_Day_(United_States)"}, {"title": "State holiday", "link": "https://wikipedia.org/wiki/State_holiday"}]}, {"year": "1937", "text": "U.S. House of Representatives passes the Marihuana Tax Act.", "html": "1937 - U.S. House of Representatives passes the <a href=\"https://wikipedia.org/wiki/Marihuana_Tax_Act_of_1937\" title=\"Marihuana Tax Act of 1937\">Marihuana Tax Act</a>.", "no_year_html": "U.S. House of Representatives passes the <a href=\"https://wikipedia.org/wiki/Marihuana_Tax_Act_of_1937\" title=\"Marihuana Tax Act of 1937\">Marihuana Tax Act</a>.", "links": [{"title": "Marihuana Tax Act of 1937", "link": "https://wikipedia.org/wiki/Marihuana_Tax_Act_of_1937"}]}, {"year": "1940", "text": "World War II: The German occupation of Paris begins.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <a href=\"https://wikipedia.org/wiki/Paris_in_World_War_II\" title=\"Paris in World War II\">occupation of Paris</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <a href=\"https://wikipedia.org/wiki/Paris_in_World_War_II\" title=\"Paris in World War II\">occupation of Paris</a> begins.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Paris in World War II", "link": "https://wikipedia.org/wiki/Paris_in_World_War_II"}]}, {"year": "1940", "text": "The Soviet Union presents an ultimatum to Lithuania resulting in Lithuanian loss of independence.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/1940_Soviet_ultimatum_to_Lithuania\" class=\"mw-redirect\" title=\"1940 Soviet ultimatum to Lithuania\">The Soviet Union presents an ultimatum to Lithuania</a> resulting in Lithuanian loss of independence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1940_Soviet_ultimatum_to_Lithuania\" class=\"mw-redirect\" title=\"1940 Soviet ultimatum to Lithuania\">The Soviet Union presents an ultimatum to Lithuania</a> resulting in Lithuanian loss of independence.", "links": [{"title": "1940 Soviet ultimatum to Lithuania", "link": "https://wikipedia.org/wiki/1940_Soviet_ultimatum_to_Lithuania"}]}, {"year": "1940", "text": "Seven hundred and twenty-eight Polish political prisoners from Tarnów become the first inmates of the Auschwitz concentration camp.", "html": "1940 - Seven hundred and twenty-eight Polish political prisoners from <a href=\"https://wikipedia.org/wiki/Tarn%C3%B3w\" title=\"Tarnów\">Tarnów</a> become the first inmates of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "no_year_html": "Seven hundred and twenty-eight Polish political prisoners from <a href=\"https://wikipedia.org/wiki/Tarn%C3%B3w\" title=\"Tarnów\">Tarnów</a> become the first inmates of the <a href=\"https://wikipedia.org/wiki/Auschwitz_concentration_camp\" title=\"Auschwitz concentration camp\">Auschwitz concentration camp</a>.", "links": [{"title": "Tarnów", "link": "https://wikipedia.org/wiki/Tarn%C3%B3w"}, {"title": "Auschwitz concentration camp", "link": "https://wikipedia.org/wiki/Auschwitz_concentration_camp"}]}, {"year": "1941", "text": "June deportation: the first major wave of Soviet mass deportations of Estonians, Latvians and Lithuanians, begins.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/June_deportation\" title=\"June deportation\">June deportation</a>: the first major wave of Soviet mass deportations of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonians</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvians</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuanians</a>, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_deportation\" title=\"June deportation\">June deportation</a>: the first major wave of Soviet mass deportations of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonians</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvians</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuanians</a>, begins.", "links": [{"title": "June deportation", "link": "https://wikipedia.org/wiki/June_deportation"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1944", "text": "World War II: After several failed attempts, the British Army abandons Operation Perch, its plan to capture the German-occupied town of Caen.", "html": "1944 - World War II: After several failed attempts, the British Army abandons <a href=\"https://wikipedia.org/wiki/Operation_Perch\" title=\"Operation Perch\">Operation Perch</a>, its plan to capture the <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a>-occupied town of <a href=\"https://wikipedia.org/wiki/Caen\" title=\"Caen\">Caen</a>.", "no_year_html": "World War II: After several failed attempts, the British Army abandons <a href=\"https://wikipedia.org/wiki/Operation_Perch\" title=\"Operation Perch\">Operation Perch</a>, its plan to capture the <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a>-occupied town of <a href=\"https://wikipedia.org/wiki/Caen\" title=\"Caen\">Caen</a>.", "links": [{"title": "Operation Perch", "link": "https://wikipedia.org/wiki/Operation_Perch"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Caen", "link": "https://wikipedia.org/wiki/Caen"}]}, {"year": "1945", "text": "World War II: Filipino troops of the Philippine Commonwealth Army liberate the captured in Ilocos Sur and start the Battle of Bessang Pass in Northern Luzon.", "html": "1945 - World War II: Filipino troops of the <a href=\"https://wikipedia.org/wiki/Philippine_Commonwealth_Army\" class=\"mw-redirect\" title=\"Philippine Commonwealth Army\">Philippine Commonwealth Army</a> liberate the captured in <a href=\"https://wikipedia.org/wiki/Ilocos_Sur\" title=\"Ilocos Sur\">Ilocos Sur</a> and start the <a href=\"https://wikipedia.org/wiki/Battle_of_Bessang_Pass\" title=\"Battle of Bessang Pass\">Battle of Bessang Pass</a> in <a href=\"https://wikipedia.org/wiki/Northern_Luzon\" class=\"mw-redirect\" title=\"Northern Luzon\">Northern Luzon</a>.", "no_year_html": "World War II: Filipino troops of the <a href=\"https://wikipedia.org/wiki/Philippine_Commonwealth_Army\" class=\"mw-redirect\" title=\"Philippine Commonwealth Army\">Philippine Commonwealth Army</a> liberate the captured in <a href=\"https://wikipedia.org/wiki/Ilocos_Sur\" title=\"Ilocos Sur\">Ilocos Sur</a> and start the <a href=\"https://wikipedia.org/wiki/Battle_of_Bessang_Pass\" title=\"Battle of Bessang Pass\">Battle of Bessang Pass</a> in <a href=\"https://wikipedia.org/wiki/Northern_Luzon\" class=\"mw-redirect\" title=\"Northern Luzon\">Northern Luzon</a>.", "links": [{"title": "Philippine Commonwealth Army", "link": "https://wikipedia.org/wiki/Philippine_Commonwealth_Army"}, {"title": "Ilocos Sur", "link": "https://wikipedia.org/wiki/Ilocos_Sur"}, {"title": "Battle of Bessang Pass", "link": "https://wikipedia.org/wiki/Battle_of_Bessang_Pass"}, {"title": "Northern Luzon", "link": "https://wikipedia.org/wiki/Northern_Luzon"}]}, {"year": "1949", "text": "<PERSON>, a rhesus monkey, rides a V-2 rocket to an altitude of 134 km (83 mi), thereby becoming the first mammal and first monkey in space.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(monkey)\" title=\"<PERSON> II (monkey)\"><PERSON> II</a>, a <a href=\"https://wikipedia.org/wiki/Rhesus_monkey\" class=\"mw-redirect\" title=\"Rhesus monkey\">rhesus monkey</a>, rides a <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> to an altitude of 134 km (83 mi), thereby becoming the first mammal and first <a href=\"https://wikipedia.org/wiki/Monkeys_and_apes_in_space\" title=\"Monkeys and apes in space\">monkey in space</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_(monkey)\" title=\"<PERSON> II (monkey)\"><PERSON> II</a>, a <a href=\"https://wikipedia.org/wiki/Rhesus_monkey\" class=\"mw-redirect\" title=\"Rhesus monkey\">rhesus monkey</a>, rides a <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> to an altitude of 134 km (83 mi), thereby becoming the first mammal and first <a href=\"https://wikipedia.org/wiki/Monkeys_and_apes_in_space\" title=\"Monkeys and apes in space\">monkey in space</a>.", "links": [{"title": "<PERSON> (monkey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(monkey)"}, {"title": "Rhesus monkey", "link": "https://wikipedia.org/wiki/Rhesus_monkey"}, {"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}, {"title": "Monkeys and apes in space", "link": "https://wikipedia.org/wiki/Monkeys_and_apes_in_space"}]}, {"year": "1950", "text": "An Air France Douglas DC-4 crashes near Bahrain International Airport, killing 40 people. This came two days after another Air France DC-4 crashed in the same location.", "html": "1950 - An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> <a href=\"https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents\" title=\"1950 Air France multiple Douglas DC-4 accidents\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bahrain_International_Airport\" title=\"Bahrain International Airport\">Bahrain International Airport</a>, killing 40 people. This came two days after another Air France DC-4 crashed in the same location.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Air_France\" title=\"Air France\">Air France</a> <a href=\"https://wikipedia.org/wiki/Douglas_DC-4\" title=\"Douglas DC-4\">Douglas DC-4</a> <a href=\"https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents\" title=\"1950 Air France multiple Douglas DC-4 accidents\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Bahrain_International_Airport\" title=\"Bahrain International Airport\">Bahrain International Airport</a>, killing 40 people. This came two days after another Air France DC-4 crashed in the same location.", "links": [{"title": "Air France", "link": "https://wikipedia.org/wiki/Air_France"}, {"title": "Douglas DC-4", "link": "https://wikipedia.org/wiki/Douglas_DC-4"}, {"title": "1950 Air France multiple Douglas DC-4 accidents", "link": "https://wikipedia.org/wiki/1950_Air_France_multiple_Douglas_DC-4_accidents"}, {"title": "Bahrain International Airport", "link": "https://wikipedia.org/wiki/Bahrain_International_Airport"}]}, {"year": "1951", "text": "UNIVAC I is dedicated by the U.S. Census Bureau.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/UNIVAC_I\" title=\"UNIVAC I\">UNIVAC I</a> is dedicated by the <a href=\"https://wikipedia.org/wiki/United_States_Bureau_of_the_Census\" class=\"mw-redirect\" title=\"United States Bureau of the Census\">U.S. Census Bureau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/UNIVAC_I\" title=\"UNIVAC I\">UNIVAC I</a> is dedicated by the <a href=\"https://wikipedia.org/wiki/United_States_Bureau_of_the_Census\" class=\"mw-redirect\" title=\"United States Bureau of the Census\">U.S. Census Bureau</a>.", "links": [{"title": "UNIVAC I", "link": "https://wikipedia.org/wiki/UNIVAC_I"}, {"title": "United States Bureau of the Census", "link": "https://wikipedia.org/wiki/United_States_Bureau_of_the_Census"}]}, {"year": "1954", "text": "U.S. President <PERSON> signs a bill into law that places the words \"under God\" into the United States Pledge of Allegiance.", "html": "1954 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill into law that places the words \"<a href=\"https://wikipedia.org/wiki/Under_God\" class=\"mw-redirect\" title=\"Under God\">under <PERSON></a>\" into the United States <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill into law that places the words \"<a href=\"https://wikipedia.org/wiki/Under_God\" class=\"mw-redirect\" title=\"Under God\">under <PERSON></a>\" into the United States <a href=\"https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)\" class=\"mw-redirect\" title=\"Pledge of Allegiance (United States)\">Pledge of Allegiance</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Under God", "link": "https://wikipedia.org/wiki/Under_God"}, {"title": "Pledge of Allegiance (United States)", "link": "https://wikipedia.org/wiki/Pledge_of_Allegiance_(United_States)"}]}, {"year": "1955", "text": "Chile becomes a signatory to the Buenos Aires copyright treaty.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires copyright treaty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a> becomes a signatory to the <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Convention\" title=\"Buenos Aires Convention\">Buenos Aires copyright treaty</a>.", "links": [{"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Buenos Aires Convention", "link": "https://wikipedia.org/wiki/Buenos_Aires_Convention"}]}, {"year": "1959", "text": "Disneyland Monorail System, the first daily operating monorail system in the Western Hemisphere, opens to the public in Anaheim, California.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Disneyland_Monorail_System\" class=\"mw-redirect\" title=\"Disneyland Monorail System\">Disneyland Monorail System</a>, the first daily operating monorail system in the Western Hemisphere, opens to the public in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Disneyland_Monorail_System\" class=\"mw-redirect\" title=\"Disneyland Monorail System\">Disneyland Monorail System</a>, the first daily operating monorail system in the Western Hemisphere, opens to the public in <a href=\"https://wikipedia.org/wiki/Anaheim,_California\" title=\"Anaheim, California\">Anaheim, California</a>.", "links": [{"title": "Disneyland Monorail System", "link": "https://wikipedia.org/wiki/Disneyland_Monorail_System"}, {"title": "Anaheim, California", "link": "https://wikipedia.org/wiki/Anaheim,_California"}]}, {"year": "1962", "text": "The European Space Research Organisation is established in Paris - later becoming the European Space Agency.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/European_Space_Research_Organisation\" title=\"European Space Research Organisation\">European Space Research Organisation</a> is established in Paris - later becoming the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Space_Research_Organisation\" title=\"European Space Research Organisation\">European Space Research Organisation</a> is established in Paris - later becoming the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "links": [{"title": "European Space Research Organisation", "link": "https://wikipedia.org/wiki/European_Space_Research_Organisation"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}]}, {"year": "1966", "text": "The Vatican announces the abolition of the Index Librorum Prohibitorum (\"index of prohibited books\"), which was originally instituted in 1557.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Roman_Curia\" title=\"Roman Curia\">Vatican</a> announces the abolition of the <i><a href=\"https://wikipedia.org/wiki/Index_Librorum_Prohibitorum\" title=\"Index Librorum Prohibitorum\">Index Librorum Prohibitorum</a></i> (\"index of prohibited books\"), which was originally instituted in <a href=\"https://wikipedia.org/wiki/1557\" title=\"1557\">1557</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roman_Curia\" title=\"Roman Curia\">Vatican</a> announces the abolition of the <i><a href=\"https://wikipedia.org/wiki/Index_Librorum_Prohibitorum\" title=\"Index Librorum Prohibitorum\">Index Librorum Prohibitorum</a></i> (\"index of prohibited books\"), which was originally instituted in <a href=\"https://wikipedia.org/wiki/1557\" title=\"1557\">1557</a>.", "links": [{"title": "Roman Curia", "link": "https://wikipedia.org/wiki/Roman_Curia"}, {"title": "Index Librorum Prohibitorum", "link": "https://wikipedia.org/wiki/Index_Librorum_Prohibitorum"}, {"title": "1557", "link": "https://wikipedia.org/wiki/1557"}]}, {"year": "1967", "text": "Mariner program: Mariner 5 is launched towards Venus.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <i><a href=\"https://wikipedia.org/wiki/Mariner_5\" title=\"Mariner 5\">Mariner 5</a></i> is launched towards <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mariner_program\" title=\"Mariner program\">Mariner program</a>: <i><a href=\"https://wikipedia.org/wiki/Mariner_5\" title=\"Mariner 5\">Mariner 5</a></i> is launched towards <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "links": [{"title": "Mariner program", "link": "https://wikipedia.org/wiki/Mariner_program"}, {"title": "Mariner 5", "link": "https://wikipedia.org/wiki/Mariner_5"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1972", "text": "Japan Air Lines Flight 471 crashes on approach to Palam International Airport (now Indira Gandhi International Airport) in New Delhi, India, killing 82 of the 87 people on board and four more people on the ground.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_471\" title=\"Japan Air Lines Flight 471\">Japan Air Lines Flight 471</a> crashes on approach to Palam International Airport (now <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Indira Gandhi International Airport</a>) in <a href=\"https://wikipedia.org/wiki/New_Delhi\" title=\"New Delhi\">New Delhi</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 82 of the 87 people on board and four more people on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Air_Lines_Flight_471\" title=\"Japan Air Lines Flight 471\">Japan Air Lines Flight 471</a> crashes on approach to Palam International Airport (now <a href=\"https://wikipedia.org/wiki/Indira_Gandhi_International_Airport\" title=\"Indira Gandhi International Airport\">Indira Gandhi International Airport</a>) in <a href=\"https://wikipedia.org/wiki/New_Delhi\" title=\"New Delhi\">New Delhi</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, killing 82 of the 87 people on board and four more people on the ground.", "links": [{"title": "Japan Air Lines Flight 471", "link": "https://wikipedia.org/wiki/Japan_Air_Lines_Flight_471"}, {"title": "Indira Gandhi International Airport", "link": "https://wikipedia.org/wiki/Indira_Gandhi_International_Airport"}, {"title": "New Delhi", "link": "https://wikipedia.org/wiki/New_Delhi"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "1982", "text": "Falklands War: Argentine forces in the capital Stanley conditionally surrender to British forces.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> forces in the capital <a href=\"https://wikipedia.org/wiki/Stanley,_Falkland_Islands\" title=\"Stanley, Falkland Islands\">Stanley</a> <a href=\"https://wikipedia.org/wiki/Conditional_surrender\" class=\"mw-redirect\" title=\"Conditional surrender\">conditionally surrender</a> to British forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> forces in the capital <a href=\"https://wikipedia.org/wiki/Stanley,_Falkland_Islands\" title=\"Stanley, Falkland Islands\">Stanley</a> <a href=\"https://wikipedia.org/wiki/Conditional_surrender\" class=\"mw-redirect\" title=\"Conditional surrender\">conditionally surrender</a> to British forces.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Stanley, Falkland Islands", "link": "https://wikipedia.org/wiki/Stanley,_Falkland_Islands"}, {"title": "Conditional surrender", "link": "https://wikipedia.org/wiki/Conditional_surrender"}]}, {"year": "1985", "text": "Five member nations of the European Economic Community sign the Schengen Agreement establishing a free travel zone with no border controls.", "html": "1985 - Five member nations of the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> sign the <a href=\"https://wikipedia.org/wiki/Schengen_Agreement\" title=\"Schengen Agreement\">Schengen Agreement</a> establishing a <a href=\"https://wikipedia.org/wiki/Schengen_Area\" title=\"Schengen Area\">free travel zone</a> with no <a href=\"https://wikipedia.org/wiki/Border_control\" title=\"Border control\">border controls</a>.", "no_year_html": "Five member nations of the <a href=\"https://wikipedia.org/wiki/European_Economic_Community\" title=\"European Economic Community\">European Economic Community</a> sign the <a href=\"https://wikipedia.org/wiki/Schengen_Agreement\" title=\"Schengen Agreement\">Schengen Agreement</a> establishing a <a href=\"https://wikipedia.org/wiki/Schengen_Area\" title=\"Schengen Area\">free travel zone</a> with no <a href=\"https://wikipedia.org/wiki/Border_control\" title=\"Border control\">border controls</a>.", "links": [{"title": "European Economic Community", "link": "https://wikipedia.org/wiki/European_Economic_Community"}, {"title": "Schengen Agreement", "link": "https://wikipedia.org/wiki/Schengen_Agreement"}, {"title": "Schengen Area", "link": "https://wikipedia.org/wiki/Schengen_Area"}, {"title": "Border control", "link": "https://wikipedia.org/wiki/Border_control"}]}, {"year": "1986", "text": "The Mindbender derails and kills three riders at the Fantasyland (known today as Galaxyland) indoor amusement park at West Edmonton Mall in Edmonton, Alberta.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Mindbender_(Galaxyland)\" title=\"<PERSON>bender (Galaxyland)\">The Mindbender</a> derails and kills three riders at the Fantasyland (known today as <a href=\"https://wikipedia.org/wiki/Galaxyland\" title=\"Galaxyland\">Galaxyland</a>) indoor amusement park at <a href=\"https://wikipedia.org/wiki/West_Edmonton_Mall\" title=\"West Edmonton Mall\">West Edmonton Mall</a> in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a>, <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>bender_(Galaxyland)\" title=\"<PERSON>bender (Galaxyland)\">The Mindbender</a> derails and kills three riders at the Fantasyland (known today as <a href=\"https://wikipedia.org/wiki/Galaxyland\" title=\"Galaxyland\">Galaxyland</a>) indoor amusement park at <a href=\"https://wikipedia.org/wiki/West_Edmonton_Mall\" title=\"West Edmonton Mall\">West Edmonton Mall</a> in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a>, <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>.", "links": [{"title": "<PERSON>bender (Galaxyland)", "link": "https://wikipedia.org/wiki/Mindbender_(Galaxyland)"}, {"title": "Galaxyland", "link": "https://wikipedia.org/wiki/Galaxyland"}, {"title": "West Edmonton Mall", "link": "https://wikipedia.org/wiki/West_Edmonton_Mall"}, {"title": "Edmonton", "link": "https://wikipedia.org/wiki/Edmonton"}, {"title": "Alberta", "link": "https://wikipedia.org/wiki/Alberta"}]}, {"year": "1994", "text": "The 1994 Vancouver Stanley Cup riot occurs after the New York Rangers defeat the Vancouver Canucks to win the Stanley Cup, causing an estimated C$1.1 million, leading to 200 arrests and injuries.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/1994_Vancouver_Stanley_Cup_riot\" title=\"1994 Vancouver Stanley Cup riot\">1994 Vancouver Stanley Cup riot</a> occurs after the <a href=\"https://wikipedia.org/wiki/New_York_Rangers\" title=\"New York Rangers\">New York Rangers</a> defeat the <a href=\"https://wikipedia.org/wiki/Vancouver_Canucks\" title=\"Vancouver Canucks\">Vancouver Canucks</a> to <a href=\"https://wikipedia.org/wiki/1994_Stanley_Cup_Finals\" title=\"1994 Stanley Cup Finals\">win</a> the <a href=\"https://wikipedia.org/wiki/Stanley_Cup\" title=\"Stanley Cup\">Stanley Cup</a>, causing an estimated C$1.1 million, leading to 200 arrests and injuries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1994_Vancouver_Stanley_Cup_riot\" title=\"1994 Vancouver Stanley Cup riot\">1994 Vancouver Stanley Cup riot</a> occurs after the <a href=\"https://wikipedia.org/wiki/New_York_Rangers\" title=\"New York Rangers\">New York Rangers</a> defeat the <a href=\"https://wikipedia.org/wiki/Vancouver_Canucks\" title=\"Vancouver Canucks\">Vancouver Canucks</a> to <a href=\"https://wikipedia.org/wiki/1994_Stanley_Cup_Finals\" title=\"1994 Stanley Cup Finals\">win</a> the <a href=\"https://wikipedia.org/wiki/Stanley_Cup\" title=\"Stanley Cup\">Stanley Cup</a>, causing an estimated C$1.1 million, leading to 200 arrests and injuries.", "links": [{"title": "1994 Vancouver Stanley Cup riot", "link": "https://wikipedia.org/wiki/1994_Vancouver_Stanley_Cup_riot"}, {"title": "New York Rangers", "link": "https://wikipedia.org/wiki/New_York_Rangers"}, {"title": "Vancouver Canucks", "link": "https://wikipedia.org/wiki/Vancouver_Canucks"}, {"title": "1994 Stanley Cup Finals", "link": "https://wikipedia.org/wiki/1994_Stanley_Cup_Finals"}, {"title": "Stanley Cup", "link": "https://wikipedia.org/wiki/Stanley_Cup"}]}, {"year": "2002", "text": "Near-Earth asteroid 2002 <PERSON><PERSON> misses the Earth by 75,000 miles (121,000 km), about one-third of the distance between the Earth and the Moon.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">Near-Earth asteroid</a> <a href=\"https://wikipedia.org/wiki/2002_MN\" title=\"2002 MN\">2002 MN</a> misses the Earth by 75,000 miles (121,000 km), about one-third of the distance between the Earth and the Moon.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Near-Earth_asteroid\" class=\"mw-redirect\" title=\"Near-Earth asteroid\">Near-Earth asteroid</a> <a href=\"https://wikipedia.org/wiki/2002_MN\" title=\"2002 MN\">2002 MN</a> misses the Earth by 75,000 miles (121,000 km), about one-third of the distance between the Earth and the Moon.", "links": [{"title": "Near-Earth asteroid", "link": "https://wikipedia.org/wiki/Near-Earth_asteroid"}, {"title": "2002 MN", "link": "https://wikipedia.org/wiki/2002_MN"}]}, {"year": "2014", "text": "A Ukraine military Ilyushin Il-76 airlifter is shot down, killing all 49 people on board.", "html": "2014 - A Ukraine military <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> airlifter is <a href=\"https://wikipedia.org/wiki/2014_Ukrainian_Air_Force_Il-76_shootdown\" class=\"mw-redirect\" title=\"2014 Ukrainian Air Force Il-76 shootdown\">shot down</a>, killing all 49 people on board.", "no_year_html": "A Ukraine military <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> airlifter is <a href=\"https://wikipedia.org/wiki/2014_Ukrainian_Air_Force_Il-76_shootdown\" class=\"mw-redirect\" title=\"2014 Ukrainian Air Force Il-76 shootdown\">shot down</a>, killing all 49 people on board.", "links": [{"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "2014 Ukrainian Air Force Il-76 shootdown", "link": "https://wikipedia.org/wiki/2014_Ukrainian_Air_Force_Il-76_shootdown"}]}, {"year": "2017", "text": "A fire in a high-rise apartment building in North Kensington, London, UK, leaves 72 people dead and another 74 injured.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/Grenfell_Tower_fire\" title=\"Grenfell Tower fire\">fire in a high-rise apartment building</a> in <a href=\"https://wikipedia.org/wiki/North_Kensington\" title=\"North Kensington\">North Kensington</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, UK, leaves 72 people dead and another 74 injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Grenfell_Tower_fire\" title=\"Grenfell Tower fire\">fire in a high-rise apartment building</a> in <a href=\"https://wikipedia.org/wiki/North_Kensington\" title=\"North Kensington\">North Kensington</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, UK, leaves 72 people dead and another 74 injured.", "links": [{"title": "Grenfell Tower fire", "link": "https://wikipedia.org/wiki/Grenfell_Tower_fire"}, {"title": "North Kensington", "link": "https://wikipedia.org/wiki/North_Kensington"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "2017", "text": "US Republican House Majority Whip <PERSON> of Louisiana, and three others, are shot and wounded while practicing for the annual Congressional Baseball Game.", "html": "2017 - US Republican House Majority Whip <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, and three others, are <a href=\"https://wikipedia.org/wiki/Congressional_baseball_shooting\" title=\"Congressional baseball shooting\">shot</a> and wounded while practicing for the annual <a href=\"https://wikipedia.org/wiki/Congressional_Baseball_Game\" title=\"Congressional Baseball Game\">Congressional Baseball Game</a>.", "no_year_html": "US Republican House Majority Whip <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, and three others, are <a href=\"https://wikipedia.org/wiki/Congressional_baseball_shooting\" title=\"Congressional baseball shooting\">shot</a> and wounded while practicing for the annual <a href=\"https://wikipedia.org/wiki/Congressional_Baseball_Game\" title=\"Congressional Baseball Game\">Congressional Baseball Game</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "Congressional baseball shooting", "link": "https://wikipedia.org/wiki/Congressional_baseball_shooting"}, {"title": "Congressional Baseball Game", "link": "https://wikipedia.org/wiki/Congressional_Baseball_Game"}]}], "Births": [{"year": "1444", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian astronomer and mathematician (d. 1544)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian astronomer and mathematician (d. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian astronomer and mathematician (d. 1544)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>ji"}]}, {"year": "1479", "text": "<PERSON><PERSON><PERSON>, Italian poet and scholar (d. 1552)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and scholar (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and scholar (d. 1552)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1529", "text": "<PERSON>, Archduke of Austria (d. 1595)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (d. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (d. 1595)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>duke_of_Austria"}]}, {"year": "1627", "text": "<PERSON>, German astronomer (d. 1699)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1691", "text": "<PERSON>, Slovak organist and composer (d. 1758)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak organist and composer (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak organist and composer (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, Welsh ornithologist and historian (d. 1798)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh ornithologist and historian (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh ornithologist and historian (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, Italian composer and educator (d. 1786)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer and educator (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1736", "text": "<PERSON><PERSON><PERSON><PERSON>, French physicist and engineer (d. 1806)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French physicist and engineer (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French physicist and engineer (d. 1806)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German composer and educator (d. 1845)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON>, English historian and diplomat, British Consul-General in Egypt (d. 1827)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Egyptologist)\" title=\"<PERSON> (Egyptologist)\"><PERSON></a>, English historian and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_Egypt\" class=\"mw-redirect\" title=\"List of diplomats of the United Kingdom to Egypt\">British Consul-General in Egypt</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Egyptologist)\" title=\"<PERSON> (Egyptologist)\"><PERSON></a>, English historian and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_Egypt\" class=\"mw-redirect\" title=\"List of diplomats of the United Kingdom to Egypt\">British Consul-General in Egypt</a> (d. 1827)", "links": [{"title": "<PERSON> (Egyptologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Egyptologist)"}, {"title": "List of diplomats of the United Kingdom to Egypt", "link": "https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_Egypt"}]}, {"year": "1796", "text": "<PERSON>, Czech-Russian mathematician and academic (d. 1866)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Russian mathematician and academic (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Russian mathematician and academic (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech historian and politician (d. 1876)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Palack%C3%BD\" title=\"Fr<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech historian and politician (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Palack%C3%BD\" title=\"Fr<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech historian and politician (d. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Palack%C3%BD"}]}, {"year": "1801", "text": "<PERSON><PERSON> <PERSON>, American religious leader (d. 1868)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/Heber_C<PERSON>_Kimball\" title=\"Heber C. Kimball\"><PERSON><PERSON> <PERSON></a>, American religious leader (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heber_<PERSON>_Kimball\" title=\"Heber C. Kimball\"><PERSON><PERSON> <PERSON></a>, American religious leader (d. 1868)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heber_C._Kimball"}]}, {"year": "1811", "text": "<PERSON>, American author and activist (d. 1896)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1819", "text": "<PERSON>, American merchant and politician, 23rd Governor of Massachusetts (d. 1892)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 23rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1820", "text": "<PERSON>, American author and publisher (d. 1905)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, American author and publisher (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, American author and publisher (d. 1905)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_(publisher)"}]}, {"year": "1829", "text": "<PERSON>, French Roman Catholic missionary to Japan (d. 1884)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic missionary to Japan (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Roman Catholic missionary to Japan (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1838", "text": "<PERSON><PERSON><PERSON>, Japanese Field Marshal and politician, 3rd and 9th Prime Minister of Japan (d. 1922)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ya<PERSON><PERSON> Aritom<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Field Marshal and politician, 3rd and 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ya<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Field Marshal and politician, 3rd and 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yamagata_Aritomo"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1840", "text": "<PERSON>, American businessman (d. 1893)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>st\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nast\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>st"}]}, {"year": "1848", "text": "<PERSON>, English philosopher and theorist (d. 1923)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and theorist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher and theorist (d. 1923)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>(philosopher)"}]}, {"year": "1848", "text": "<PERSON>, German conductor and composer (d. 1905)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rfer\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rfer\" title=\"<PERSON>\"><PERSON></a>, German conductor and composer (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Erdmannsd%C3%B6rfer"}]}, {"year": "1855", "text": "<PERSON>, American lawyer and politician, 20th Governor of Wisconsin (d. 1925)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ette"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "1856", "text": "<PERSON><PERSON>, Russian mathematician and theorist (d. 1922)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, Swiss-American chemist and academic (d. 1915)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Swiss-American chemist and academic (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Swiss-American chemist and academic (d. 1915)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(chemist)"}]}, {"year": "1864", "text": "<PERSON><PERSON>, German psychiatrist and neuropathologist (d. 1915)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alzheimer\" title=\"Alois Alzheimer\"><PERSON><PERSON></a>, German psychiatrist and neuropathologist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alzheimer\" title=\"Alois Alzheimer\"><PERSON><PERSON></a>, German psychiatrist and neuropathologist (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Austrian biologist and physician, Nobel Prize laureate (d. 1943)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1868", "text": "<PERSON>, German peace activist (d. 1947)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German peace activist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German peace activist (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON> of Prussia (d. 1932)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Sophia_of_Prussia\" title=\"Sophia of Prussia\">Sophia of Prussia</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophia_of_Prussia\" title=\"Sophia of Prussia\">Sophia of Prussia</a> (d. 1932)", "links": [{"title": "Sophia of Prussia", "link": "https://wikipedia.org/wiki/Sophia_of_Prussia"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Dutch rower (d. 1936)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch rower (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Danish mechanic and engineer (d. 1946)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mechanic and engineer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mechanic and engineer (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Slovene priest and author (d. 1936)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene priest and author (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene priest and author (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Szlepecz"}]}, {"year": "1877", "text": "<PERSON>, French soprano (d. 1970)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, British biochemist, the first woman admitted to the London Chemical Society (d. 1944)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Ida_Maclean\" title=\"Ida Maclean\"><PERSON></a>, British biochemist, the first woman admitted to the <a href=\"https://wikipedia.org/wiki/Chemical_Society\" title=\"Chemical Society\">London Chemical Society</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ida_Maclean\" title=\"Ida Maclean\"><PERSON></a>, British biochemist, the first woman admitted to the <a href=\"https://wikipedia.org/wiki/Chemical_Society\" title=\"Chemical Society\">London Chemical Society</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_<PERSON>lean"}, {"title": "Chemical Society", "link": "https://wikipedia.org/wiki/Chemical_Society"}]}, {"year": "1878", "text": "<PERSON>, French fencer (d. 1943)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/L%C3%A9on_Thi%C3%A9baut\" title=\"<PERSON>\"><PERSON></a>, French fencer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9on_Thi%C3%A9baut\" title=\"<PERSON>\"><PERSON></a>, French fencer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Thi%C3%A9baut"}]}, {"year": "1879", "text": "<PERSON>, American sprinter and coach (d. 1955)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and coach (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Irish tenor and actor (d. 1945)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Irish tenor and actor (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Irish tenor and actor (d. 1945)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1884", "text": "<PERSON>, German swimmer (d. 1953)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American actress (d. 1989)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/May_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg (d. 1924)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9la%C3%<PERSON><PERSON><PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9la%C3%<PERSON><PERSON><PERSON>,_Grand_Duchess_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg\"><PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg</a> (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Grand Duchess of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON>-Ad%C3%A9la%C3%A<PERSON><PERSON>,_Grand_Duchess_of_Luxembourg"}]}, {"year": "1894", "text": "<PERSON> (d. 1930)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui\" title=\"<PERSON>\"><PERSON></a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui\" title=\"<PERSON>\"><PERSON></a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1tegui"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian geophysicist and poet (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian geophysicist and poet (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian geophysicist and poet (d. 1966)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Canadian-American ice hockey player, coach, and manager (d. 1968)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Irish Army Officer and painter (d. 1962)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>Gerald\" title=\"<PERSON><PERSON><PERSON> <PERSON>tzGerald\"><PERSON><PERSON><PERSON></a>, Irish Army Officer and painter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>tzGerald\" title=\"<PERSON><PERSON><PERSON> <PERSON> To<PERSON> FitzGerald\"><PERSON><PERSON><PERSON></a>, Irish Army Officer and painter (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American writer, editor, and philosopher (d. 2003)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor, and philosopher (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor, and philosopher (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American stage and film actress (d. 1966)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/June_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and film actress (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and film actress (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, American mathematician and logician (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Alonzo_Church\" title=\"Alonzo Church\"><PERSON><PERSON><PERSON></a>, American mathematician and logician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alonzo_Church\" title=\"Alonzo Church\"><PERSON><PERSON><PERSON></a>, American mathematician and logician (d. 1995)", "links": [{"title": "Alonzo Church", "link": "https://wikipedia.org/wiki/Alonzo_Church"}]}, {"year": "1903", "text": "<PERSON>, Austrian-American logician and philosopher from the Vienna Circle (d. 1980)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rose Rand\"><PERSON></a>, Austrian-American logician and philosopher from the Vienna Circle (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rose_Rand\" title=\"Rose Rand\"><PERSON></a>, Austrian-American logician and philosopher from the Vienna Circle (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_Rand"}]}, {"year": "1904", "text": "<PERSON>-<PERSON>, American photographer and journalist (d. 1971)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1971)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American businessman (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American animator and director (d. 2000)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator and director (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, American animator and director (d. 2000)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)"}]}, {"year": "1907", "text": "<PERSON>, English author and illustrator (d. 1978)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, French poet and author (d. 1988)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Cha<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Char\" title=\"<PERSON>\"><PERSON></a>, French poet and author (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Char"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, American actor and singer (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and singer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and singer (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German pianist and conductor (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and conductor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English-Canadian lieutenant and trade union leader (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, English-Canadian lieutenant and trade union leader (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)\" title=\"<PERSON> (trade unionist)\"><PERSON></a>, English-Canadian lieutenant and trade union leader (d. 1996)", "links": [{"title": "<PERSON> (trade unionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(trade_unionist)"}]}, {"year": "1916", "text": "<PERSON>, American actress (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Danish journalist, author, and screenwriter (d. 2023)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Lise_N%C3%B8rgaard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish journalist, author, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lise_N%C3%B8rgaard\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish journalist, author, and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lise_N%C3%B8rgaard"}]}, {"year": "1917", "text": "<PERSON>, French poet and director (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and director (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Norwegian-American mathematician and academic (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American mathematician and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American mathematician and academic (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lberg"}]}, {"year": "1919", "text": "<PERSON>, American actor (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actor and director (d. 1993)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martha_Greenhouse"}]}, {"year": "1923", "text": "<PERSON>, German-English author and illustrator (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English author and illustrator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English author and illustrator (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier, lawyer, and judge (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Green_Wix_Unthank\" title=\"Green Wix Unthank\">Green Wix Unthank</a>, American soldier, lawyer, and judge (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Green_Wix_Unthank\" title=\"Green Wix Unthank\">Green Wix Unthank</a>, American soldier, lawyer, and judge (d. 2013)", "links": [{"title": "Green Wix Unthank", "link": "https://wikipedia.org/wiki/Green_Wix_Unthank"}]}, {"year": "1924", "text": "<PERSON>, Scottish pharmacologist and academic, Nobel Prize laureate (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)\" title=\"<PERSON> (pharmacologist)\"><PERSON></a>, Scottish pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)\" title=\"<PERSON> (pharmacologist)\"><PERSON></a>, Scottish pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2010)", "links": [{"title": "<PERSON> (pharmacologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pharmacologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1925", "text": "<PERSON>, American journalist and politician, 11th White House Press Secretary (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Don_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Argentinian-Cuban physician, author, guerrilla leader and politician (d. 1967)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Che_Guevara\" title=\"Che Guevara\"><PERSON> \"<PERSON>\" <PERSON></a>, Argentinian-Cuban physician, author, <a href=\"https://wikipedia.org/wiki/Guerrilla\" class=\"mw-redirect\" title=\"Guerrilla\">guerrilla</a> leader and politician (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Che_Guevara\" title=\"Che Guevara\"><PERSON> \"<PERSON>\" <PERSON></a>, Argentinian-Cuban physician, author, <a href=\"https://wikipedia.org/wiki/Guerrilla\" class=\"mw-redirect\" title=\"Guerrilla\">guerrilla</a> leader and politician (d. 1967)", "links": [{"title": "<PERSON>e Guevara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}, {"title": "Guerrilla", "link": "https://wikipedia.org/wiki/Guerrilla"}]}, {"year": "1929", "text": "<PERSON>, American pianist and composer (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian cricketer (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1929)\" title=\"<PERSON> (cricketer, born 1929)\"><PERSON></a>, Australian cricketer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1929)\" title=\"<PERSON> (cricketer, born 1929)\"><PERSON></a>, Australian cricketer (d. 2021)", "links": [{"title": "<PERSON> (cricketer, born 1929)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1929)"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American ice hockey player and coach (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player and coach (d. 2011)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American actress and comedian", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian actor (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American saxophonist (d. 1995)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Polish-American novelist and screenwriter (d. 1991)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Je<PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American novelist and screenwriter (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American novelist and screenwriter (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kosi%C5%84ski"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Russian gymnast and coach (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian gymnast and coach (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American singer-songwriter (d. 2005)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Finnish author, poet, and translator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>man_Lil<PERSON>\" title=\"Irmelin Sandman Lilius\"><PERSON><PERSON><PERSON></a>, Finnish author, poet, and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>man_Lil<PERSON>\" title=\"Irmelin Sandman Lilius\"><PERSON><PERSON><PERSON></a>, Finnish author, poet, and translator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>man_Lil<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American-English singer-songwriter and guitarist (d. 2020)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>y_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English author and screenwriter (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English journalist and author", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Irish folk musician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish folk musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Irish folk musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1942", "text": "<PERSON>, English author and academic (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Spanish judge (d. 2008)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa-Cal<PERSON>_Montiel\" title=\"<PERSON>\"><PERSON></a>, Spanish judge (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa-Calvo_Montiel\" title=\"<PERSON>\"><PERSON></a>, Spanish judge (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roberto_Garc%C3%ADa-Calvo_Montiel"}]}, {"year": "1943", "text": "<PERSON>, American composer, conductor, and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American composer, conductor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American composer, conductor, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON>, American novelist and short story writer (d. 1992)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and keyboard player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American writer (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Brazilian director and producer (d. 2012)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director and producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director and producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American sprinter and educator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French-Swiss businessman (d. 2009)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss businessman (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Swiss businessman (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Estonian instrument maker and educator", "html": "1946 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nu_Sepp\" title=\"Tõnu Sepp\">T<PERSON><PERSON> Sep<PERSON></a>, Estonian instrument maker and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nu_Sepp\" title=\"Tõnu Sepp\">T<PERSON><PERSON> Sepp</a>, Estonian instrument maker and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nu_Sepp"}]}, {"year": "1946", "text": "<PERSON>, American businessman, television personality, 45th and 47th President of the United States", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, television personality, 45th and 47th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, television personality, 45th and 47th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1947", "text": "<PERSON>, Baron <PERSON>, English politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian singer, guitarist, and cyclist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer, guitarist, and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer, guitarist, and cyclist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1948", "text": "<PERSON>, American author and playwright", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yep\"><PERSON></a>, American author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Yep\" title=\"<PERSON> Yep\"><PERSON></a>, American author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, bass player, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1949", "text": "<PERSON>, English-Australian scientist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English-Australian scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English-Australian scientist and academic", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(scientist)"}]}, {"year": "1949", "text": "<PERSON>, South African-British actor, director, and screenwriter (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-British actor, director, and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-British actor, director, and screenwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American historian and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English drummer and songwriter (d. 2022)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yes_drummer)\" title=\"<PERSON> (Yes drummer)\"><PERSON></a>, English drummer and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Yes_drummer)\" title=\"<PERSON> (Yes drummer)\"><PERSON></a>, English drummer and songwriter (d. 2022)", "links": [{"title": "<PERSON> (Yes drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Yes_drummer)"}]}, {"year": "1950", "text": "<PERSON>, Welsh archbishop and theologian", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh archbishop and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh archbishop and theologian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English lawyer and politician, British High Commissioner to South Africa", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to South Africa\">British High Commissioner to South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to South Africa\">British High Commissioner to South Africa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of High Commissioners of the United Kingdom to South Africa", "link": "https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_South_Africa"}]}, {"year": "1951", "text": "<PERSON>, American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American basketball player and coach (d. 2016)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English television host, producer, and drag performer (d. 2023)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, English television host, producer, and drag performer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Grady\" title=\"<PERSON>\"><PERSON></a>, English television host, producer, and drag performer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Grady"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Indian theatre, film and television actress, TV talk show host and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian theatre, film and television actress, TV talk show host and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian theatre, film and television actress, TV talk show host and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American artist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American bass player, composer, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player, composer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American hurdler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_George\" title=\"<PERSON> George\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_George\" title=\"<PERSON> George\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Serbian singer-songwriter and bass player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Koji%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Koji%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1an_Koji%C4%87"}]}, {"year": "1961", "text": "<PERSON>, American basketball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American football player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Cuban-American actor and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Love\" title=\"Faizon Love\"><PERSON><PERSON><PERSON></a>, Cuban-American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Love\" title=\"Faizon Love\"><PERSON><PERSON><PERSON></a>, Cuban-American actor and screenwriter", "links": [{"title": "Faizon Love", "link": "https://wikipedia.org/wiki/Faizon_Love"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German tennis player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian, actress, and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American musician (d. 2024)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Swiss footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American basketball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, German computer scientist and engineer, founded KDE", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and engineer, founded <a href=\"https://wikipedia.org/wiki/KDE\" title=\"KDE\">KDE</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German computer scientist and engineer, founded <a href=\"https://wikipedia.org/wiki/KDE\" title=\"KDE\">KDE</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "KDE", "link": "https://wikipedia.org/wiki/KDE"}]}, {"year": "1972", "text": "<PERSON>, South African cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Jamaican hurdler and sprinter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican hurdler and sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Finnish-American ice hockey player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish-American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1977", "text": "<PERSON><PERSON>, South African cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dippe<PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English rugby player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gin\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gin\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Steve_B%C3%A9gin"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American director, producer, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Cuban-American gymnast and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American gymnast and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban-American gymnast and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Croatian former professional basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Vuj%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian former professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Vuj%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian former professional basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Vuj%C4%8Di%C4%87"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Brazilian footballer and manager", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Elano\" title=\"El<PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elano\" title=\"El<PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "Elano", "link": "https://wikipedia.org/wiki/Elano"}]}, {"year": "1982", "text": "<PERSON>, English racing driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian swimmer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Chinese pianist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lang\"><PERSON></a>, Chinese pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Norwegian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Trine_R%C3%B8nning\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trine_R%C3%B8nning\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trine_R%C3%B8nning"}]}, {"year": "1983", "text": "<PERSON>, Bahamian high jumper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, English singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Siobh%C3%A1n_Donaghy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siobh%C3%A1n_Donaghy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siobh%C3%A1n_Donaghy"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Russian swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>. Russian luger", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. Russian luger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>. Russian luger", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Spanish racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>, Barbadian netball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Rhe-<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>he-<PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON></a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>he-<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>he-<PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON>-<PERSON></a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>-Mapp", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>-<PERSON>p"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Senegalese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Di<PERSON>%C3%A9"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Adri%C3%A1n_Aldrete\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adri%C3%A1n_Aldrete\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adri%C3%A1n_Aldrete"}]}, {"year": "1988", "text": "<PERSON>, American actor, singer, dancer and radio personality", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, dancer and radio personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, dancer and radio personality", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1989", "text": "<PERSON>, American actress and singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian-Cook Islands rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Cook Islands rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Cook Islands rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brad_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ier"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Norwegian long-distance runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Gr%C3%B8vdal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian long-distance runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Gr%C3%B8vdal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian long-distance runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Gr%C3%B8vdal"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1991", "text": "<PERSON><PERSON>, English singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> Smith-Pelly\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> Smith-Pelly\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Devante Smith-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-il"}]}, {"year": "1997", "text": "<PERSON>, French football defender", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French football defender", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French football defender", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Taiwanese singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Tzuyu\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>yu"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Canadian basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American soccer player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "809", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 731)", "html": "809 - <a href=\"https://wikipedia.org/wiki/%C5%8Ctomo_no_Otomaro\" title=\"<PERSON>tom<PERSON> no Otomaro\"><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese general (b. 731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8Ctomo_no_Otomaro\" title=\"Ōtom<PERSON> no Otomaro\"><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese general (b. 731)", "links": [{"title": "<PERSON><PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/%C5%8Ctomo_no_<PERSON>tomaro"}]}, {"year": "847", "text": "<PERSON><PERSON>, patriarch of Constantinople", "html": "847 - <a href=\"https://wikipedia.org/wiki/Methodios_I_of_Constantinople\" title=\"<PERSON><PERSON> I of Constantinople\"><PERSON><PERSON></a>, patriarch of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Methodios_I_of_Constantinople\" title=\"<PERSON><PERSON> I of Constantinople\"><PERSON><PERSON></a>, patriarch of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "links": [{"title": "<PERSON><PERSON> I of Constantinople", "link": "https://wikipedia.org/wiki/Methodios_I_of_Constantinople"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "957", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Vic (Spain)", "html": "957 - <a href=\"https://wikipedia.org/wiki/Guadamir\" title=\"Guadamir\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Vic\" title=\"Roman Catholic Diocese of Vic\">Vic</a> (<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guadamir\" title=\"Guadami<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Vic\" title=\"Roman Catholic Diocese of Vic\">Vic</a> (<a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>)", "links": [{"title": "G<PERSON>ami<PERSON>", "link": "https://wikipedia.org/wiki/Guadamir"}, {"title": "Roman Catholic Diocese of Vic", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Vic"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "976", "text": "<PERSON><PERSON>, Bulgarian nobleman", "html": "976 - <a href=\"https://wikipedia.org/wiki/Aron_of_Bulgaria\" title=\"Aron of Bulgaria\"><PERSON><PERSON></a>, Bulgarian nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aron_of_Bulgaria\" title=\"Aron of Bulgaria\"><PERSON><PERSON></a>, Bulgarian nobleman", "links": [{"title": "Aron of Bulgaria", "link": "https://wikipedia.org/wiki/Aron_of_Bulgaria"}]}, {"year": "1161", "text": "Emperor <PERSON><PERSON> of the Song dynasty (b. 1100)", "html": "1161 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of the Song dynasty (b. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of the Song dynasty (b. 1100)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1349", "text": "<PERSON><PERSON><PERSON><PERSON> von Schwarz<PERSON>, German king (b. 1304)", "html": "1349 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_von_Schwarzburg\" title=\"<PERSON><PERSON><PERSON><PERSON> von Schwarzburg\"><PERSON><PERSON><PERSON><PERSON> <PERSON> Schwarzburg</a>, German king (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_von_Schwarzburg\" title=\"<PERSON><PERSON><PERSON><PERSON> von Schwarzburg\"><PERSON><PERSON><PERSON><PERSON> von Schwarzburg</a>, German king (b. 1304)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1381", "text": "<PERSON>, English archbishop (b. 1316)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/Simon_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simon_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (b. 1316)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Simon_<PERSON>dbury"}]}, {"year": "1497", "text": "<PERSON>, 2nd Duke of Gandía, Italian son of Pope <PERSON> (b. 1474)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Gand%C3%ADa\" title=\"<PERSON>, 2nd Duke of Gandía\"><PERSON>, 2nd Duke of Gandía</a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_<PERSON>\" title=\"Pope Alexander <PERSON>\">Pope <PERSON> VI</a> (b. 1474)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Gand%C3%ADa\" title=\"<PERSON>, 2nd Duke of Gandía\"><PERSON>, 2nd Duke of Gandía</a>, Italian son of <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_<PERSON>\" title=\"Pope Alexander VI\">Pope <PERSON></a> (b. 1474)", "links": [{"title": "<PERSON>, 2nd Duke of Gandía", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Gand%C3%ADa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1516", "text": "<PERSON> of Navarre (b. 1469)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1469)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Navarre"}]}, {"year": "1544", "text": "<PERSON>, Duke of Lorraine (b. 1489)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. 1489)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1548", "text": "<PERSON><PERSON><PERSON>, French composer (b. 1470)", "html": "1548 - <a href=\"https://wikipedia.org/wiki/Car<PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, French composer (b. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Car<PERSON><PERSON>_(composer)\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, French composer (b. 1470)", "links": [{"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(composer)"}]}, {"year": "1583", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (b. 1522)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Katsuie\" title=\"<PERSON><PERSON><PERSON> Katsuie\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sui<PERSON>\" title=\"<PERSON><PERSON><PERSON> Katsuie\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1522)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kat<PERSON>e"}]}, {"year": "1594", "text": "<PERSON>, German goldsmith, hanged in Edinburgh for stealing the jewels of <PERSON> of Denmark.", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German goldsmith, hanged in Edinburgh for stealing the jewels of <a href=\"https://wikipedia.org/wiki/Anne_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German goldsmith, hanged in Edinburgh for stealing the jewels of <a href=\"https://wikipedia.org/wiki/Anne_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denmark"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON>, Flemish composer and educator (b. 1532)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish composer and educator (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish composer and educator (b. 1532)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON> the Younger, English-American politician, Governor of the Massachusetts Bay Colony (b. 1613)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, English-American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_<PERSON>\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, English-American politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of the Massachusetts Bay Colony\">Governor of the Massachusetts Bay Colony</a> (b. 1613)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of the Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Governor_of_the_Massachusetts_Bay_Colony"}]}, {"year": "1674", "text": "<PERSON>, French author and poet (b. 1600)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Marin le <PERSON>ville\"><PERSON></a>, French author and poet (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Marin le <PERSON>\"><PERSON></a>, French author and poet (b. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1679", "text": "<PERSON>, French painter and illustrator (b. 1628)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, Scottish mathematician (b. 1698)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mathematician (b. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, 1st Marquess of Hertford, English courtier and politician, Lord Lieutenant of Ireland (b. 1718)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Hertford\" title=\"<PERSON>, 1st Marquess of Hertford\"><PERSON>, 1st Marquess of Hertford</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Hertford\" title=\"<PERSON>, 1st Marquess of Hertford\"><PERSON>, 1st Marquess of Hertford</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1718)", "links": [{"title": "<PERSON>, 1st Marquess of Hertford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Hertford"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1800", "text": "<PERSON>, French general (b. 1768)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON>, French general (b. 1753)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ber\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ber\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9ber"}]}, {"year": "1801", "text": "<PERSON>, American general during the American Revolution later turned British spy (b. 1741)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general during the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a> later turned British spy (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general during the <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a> later turned British spy (b. 1741)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}]}, {"year": "1825", "text": "<PERSON>, French-American architect and engineer, designed Washington, D.C.  (b. 1754)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Enfant\" title=\"<PERSON>\"><PERSON></a>, French-American architect and engineer, designed <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Enfant\" title=\"<PERSON>\"><PERSON></a>, French-American architect and engineer, designed <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a> (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Enfant"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1837", "text": "<PERSON>, Italian poet and philosopher (b. 1798)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and philosopher (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and philosopher (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, American general and bishop (b. 1806)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and bishop (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and bishop (b. 1806)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonidas_Polk"}]}, {"year": "1877", "text": "<PERSON>, English educational and social reformer (b. 1807)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educational and <a href=\"https://wikipedia.org/wiki/Social_reformer\" class=\"mw-redirect\" title=\"Social reformer\">social reformer</a> (b. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educational and <a href=\"https://wikipedia.org/wiki/Social_reformer\" class=\"mw-redirect\" title=\"Social reformer\">social reformer</a> (b. 1807)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Social reformer", "link": "https://wikipedia.org/wiki/Social_reformer"}]}, {"year": "1883", "text": "<PERSON>, English poet and author (b. 1809)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and author (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and author (b. 1809)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1886", "text": "<PERSON>, Russian director and playwright (b. 1823)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and playwright (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian director and playwright (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, American politician, 18th Governor of Tennessee (b. 1830)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "1907", "text": "<PERSON>, American architect and engineer, designed the Home Insurance Building  (b. 1832)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Home_Insurance_Building\" title=\"Home Insurance Building\">Home Insurance Building</a> (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Home_Insurance_Building\" title=\"Home Insurance Building\">Home Insurance Building</a> (b. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Home Insurance Building", "link": "https://wikipedia.org/wiki/Home_Insurance_Building"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Cuban soldier and politician (b. 1830)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mas%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban soldier and politician (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mas%C3%B3\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban soldier and politician (b. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_Mas%C3%B3"}]}, {"year": "1908", "text": "<PERSON>, 16th Earl of Derby, English captain and politician, 6th Governor General of Canada (b. 1841)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_<PERSON>_Derby\" title=\"<PERSON>, 16th Earl of Derby\"><PERSON>, 16th Earl of Derby</a>, English captain and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_<PERSON>_Derby\" title=\"<PERSON>, 16th Earl <PERSON> Derby\"><PERSON>, 16th Earl of Derby</a>, English captain and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a> (b. 1841)", "links": [{"title": "<PERSON>, 16th Earl of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Derby"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}]}, {"year": "1914", "text": "<PERSON><PERSON>, American lawyer and politician, 23rd Vice President of the United States (b. 1835)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> I</a>, American lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1835)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1916", "text": "<PERSON>, Brazilian author (b. 1865)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Sim%C3%B5es_Lopes_Neto\" title=\"<PERSON>\"><PERSON></a>, Brazilian author (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Sim%C3%B5es_Lopes_Neto\" title=\"<PERSON>o\"><PERSON></a>, Brazilian author (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Sim%C3%B5es_Lopes_Neto"}]}, {"year": "1920", "text": "<PERSON>, German sociologist and economist (b. 1864)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and economist (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and economist (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French philanthropist (b. 1838)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philanthropist (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philanthropist (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American-French painter (b. 1843)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Italian cyclist (b. 1894)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English author (b. 1859)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, English activist and academic (b. 1857)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English activist and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English activist and academic (b. 1857)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Canadian businesswoman, co-founded Desjardins Group (b. 1858)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Dorim%C3%A8ne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Desjardins_Group\" title=\"Desjardins Group\">Desjardins Group</a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rim%C3%A8ne_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Desjardins_Group\" title=\"Desjardins Group\">Desjardins Group</a> (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dorim%C3%A8ne_<PERSON>_<PERSON>"}, {"title": "Desjardins Group", "link": "https://wikipedia.org/wiki/Desjardins_Group"}]}, {"year": "1933", "text": "<PERSON><PERSON>, French target shooter (b. 1860)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French target shooter (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French target shooter (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, English essayist, poet, playwright, and novelist (b. 1874)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English essayist, poet, playwright, and novelist (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"G. K. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English essayist, poet, playwright, and novelist (b. 1874)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German architect, painter, and designer, designed the IG Farben Building (b. 1869)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, painter, and designer, designed the <a href=\"https://wikipedia.org/wiki/IG_Farben_Building\" title=\"IG Farben Building\">IG Farben Building</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, painter, and designer, designed the <a href=\"https://wikipedia.org/wiki/IG_Farben_Building\" title=\"IG Farben Building\">IG Farben Building</a> (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IG Farben Building", "link": "https://wikipedia.org/wiki/IG_Farben_Building"}]}, {"year": "1946", "text": "<PERSON>, Scottish-English physicist and engineer (b. 1888)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English physicist and engineer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English physicist and engineer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, 21st President of Guatemala (b. 1878)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 21st <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 21st <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "1949", "text": "<PERSON>, rhesus macaque, animal astronaut, and first mammal in space", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_(monkey)\" title=\"<PERSON> II (monkey)\"><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Rhesus_macaque\" title=\"Rhesus macaque\">rhesus macaque</a>, <a href=\"https://wikipedia.org/wiki/Animal_astronaut\" class=\"mw-redirect\" title=\"Animal astronaut\">animal astronaut</a>, and first mammal in space", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_(monkey)\" title=\"Albert II (monkey)\"><PERSON> II</a>, <a href=\"https://wikipedia.org/wiki/Rhesus_macaque\" title=\"Rhesus macaque\">rhesus macaque</a>, <a href=\"https://wikipedia.org/wiki/Animal_astronaut\" class=\"mw-redirect\" title=\"Animal astronaut\">animal astronaut</a>, and first mammal in space", "links": [{"title": "<PERSON> (monkey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(monkey)"}, {"title": "Rhesus macaque", "link": "https://wikipedia.org/wiki/Rhesus_macaque"}, {"title": "Animal astronaut", "link": "https://wikipedia.org/wiki/Animal_astronaut"}]}, {"year": "1953", "text": "<PERSON>, Welsh-American racing driver (b. 1922)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Welsh-American racing driver (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Welsh-American racing driver (b. 1922)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1968", "text": "<PERSON>, Italian novelist and poet, Nobel Prize Laureate (b. 1901)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> <PERSON><PERSON> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> Lau<PERSON> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1971", "text": "<PERSON>, 8th President of the Republic of the Philippines (b. 1896)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 8th President of the Republic of the Philippines (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 8th President of the Republic of the Philippines (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish soldier and politician (b. 1925)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/D%C3%BCndar_Ta%C5%9Fer\" title=\"<PERSON>ü<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish soldier and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%BCndar_Ta%C5%9Fer\" title=\"<PERSON>ü<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish soldier and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%BCndar_Ta%C5%9Fer"}]}, {"year": "1977", "text": "<PERSON>, American actor (b. 1911)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor, original voice of <PERSON> (b.1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, original voice of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b.1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, original voice of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b.1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Afghan singer-songwriter (b. 1946)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American saxophonist and flute player (b. 1939)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist and flute player (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist and flute player (b. 1939)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1986", "text": "<PERSON>,  Argentine short-story writer, essayist, poet and translator (b. 1899)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine short-story writer, essayist, poet and translator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine short-story writer, essayist, poet and translator (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American composer and songwriter (b. 1918)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Polish actor, director, and screenwriter (b. 1929)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Bareja\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor, director, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON>ej<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor, director, and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON>a"}]}, {"year": "1990", "text": "<PERSON><PERSON>, German soprano and actress (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English actress (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, English pianist, composer, and educator (b. 1942)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and educator (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and educator (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American composer and conductor (b. 1924)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French singer and actor (b. 1922)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Ukrainian-Estonian pianist, composer, and educator (b. 1917)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Els_<PERSON>ne\" title=\"<PERSON>s Aarne\"><PERSON><PERSON></a>, Ukrainian-Estonian pianist, composer, and educator (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Els_<PERSON>ne\" title=\"<PERSON>s Aarne\"><PERSON><PERSON></a>, Ukrainian-Estonian pianist, composer, and educator (b. 1917)", "links": [{"title": "Els Aarne", "link": "https://wikipedia.org/wiki/<PERSON>s_A<PERSON>ne"}]}, {"year": "1995", "text": "<PERSON>, Irish singer-songwriter, guitarist, and producer (b. 1948)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter, guitarist, and producer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American author and poet (b. 1937)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Argentinian sculptor and illustrator (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Noem%C3%AD_G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian sculptor and illustrator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noem%C3%AD_G<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian sculptor and illustrator (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noem%C3%<PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1926)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American-Canadian football player and sportscaster (b. 1932)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and sportscaster (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and sportscaster (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Italian poet and author (b. 1911)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and author (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author and activist (b. 1936)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/June_Jordan\" title=\"June <PERSON>\">June <PERSON></a>, American author and activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Jordan\" title=\"June Jordan\">June <PERSON></a>, American author and activist (b. 1936)", "links": [{"title": "June Jordan", "link": "https://wikipedia.org/wiki/June_Jordan"}]}, {"year": "2003", "text": "<PERSON>, American race car driver (b. 1959)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Swiss mountaineer and guide (b. 1900)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer and guide (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mountaineer and guide (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Italian conductor and director (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and director (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor and director (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian-Swiss painter (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mimi Parent\"><PERSON></a>, Canadian-Swiss painter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mimi Parent\"><PERSON></a>, Canadian-Swiss painter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rent"}]}, {"year": "2006", "text": "<PERSON>, English director, producer, and cinematographer (b. 1905)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and cinematographer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and cinematographer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Belgian author and illustrator (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Chinese-American author, poet, and painter (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American author, poet, and painter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American author, poet, and painter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American general and pilot (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Secretary-General of the United Nations, Austrian politician, 9th President of Austria (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, Austrian politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>, Austrian politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}, {"title": "President of Austria", "link": "https://wikipedia.org/wiki/President_of_Austria"}]}, {"year": "2009", "text": "<PERSON>, American musician (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian soldier, lawyer, and judge (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, Canadian soldier, lawyer, and judge (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, Canadian soldier, lawyer, and judge (b. 1918)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_(judge)"}]}, {"year": "2012", "text": "<PERSON>, Baron <PERSON> of Sandwell, English lawyer and politician, Solicitor General for England and Wales (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Sandwell\" title=\"<PERSON>, Baron <PERSON> of Sandwell\"><PERSON>, Baron <PERSON> of Sandwell</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Sandwell\" title=\"<PERSON>, Baron <PERSON> of Sandwell\"><PERSON>, Baron Archer of Sandwell</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales\" title=\"Solicitor General for England and Wales\">Solicitor General for England and Wales</a> (b. 1926)", "links": [{"title": "<PERSON>, Baron <PERSON> of Sandwell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Sandwell"}, {"title": "Solicitor General for England and Wales", "link": "https://wikipedia.org/wiki/Solicitor_General_for_England_and_Wales"}]}, {"year": "2012", "text": "<PERSON>, American football player and soldier (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and soldier (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American pianist and vibraphone player (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yam<PERSON>\" title=\"<PERSON><PERSON> Hyams\"><PERSON><PERSON></a>, American pianist and vibraphone player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hyams\"><PERSON><PERSON></a>, American pianist and vibraphone player (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, German pianist and academic (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4mmerling\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4mmerling\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist and academic (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A4mmerling"}]}, {"year": "2012", "text": "<PERSON>, Brazilian director and producer (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director and producer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Austrian-English historian, journalist, and author (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Gitta_<PERSON>\" title=\"Gitta Ser<PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-English historian, journalist, and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>itt<PERSON>_<PERSON>\" title=\"Gitta <PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian-English historian, journalist, and author (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gitta_<PERSON>eny"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American screenwriter and producer (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Costa Rican journalist and politician (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1as_Escalante\" title=\"<PERSON>\"><PERSON></a>, Costa Rican journalist and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1as_Escalante\" title=\"<PERSON>\"><PERSON></a>, Costa Rican journalist and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_Ca%C3%B1as_Escalante"}]}, {"year": "2014", "text": "<PERSON>, French actress (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German photographer and journalist (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and journalist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer and journalist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer, businessman, and academic (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(attorney)\" title=\"<PERSON> (attorney)\"><PERSON></a>, American lawyer, businessman, and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(attorney)\" title=\"<PERSON> (attorney)\"><PERSON></a>, American lawyer, businessman, and academic (b. 1938)", "links": [{"title": "<PERSON> (attorney)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(attorney)"}]}, {"year": "2015", "text": "<PERSON>, Australian geneticist and academic (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)\" title=\"<PERSON> (geneticist)\"><PERSON></a>, Australian geneticist and academic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)\" title=\"<PERSON> (geneticist)\"><PERSON></a>, Australian geneticist and academic (b. 1940)", "links": [{"title": "<PERSON> (geneticist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geneticist)"}]}, {"year": "2015", "text": "<PERSON>, American activist, co-founded the Freedom From Religion Foundation (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Freedom_From_Religion_Foundation\" title=\"Freedom From Religion Foundation\">Freedom From Religion Foundation</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Freedom_From_Religion_Foundation\" title=\"Freedom From Religion Foundation\">Freedom From Religion Foundation</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Freedom From Religion Foundation", "link": "https://wikipedia.org/wiki/Freedom_From_Religion_Foundation"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Chinese politician (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actress and singer (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian politician, Lieutenant Governor of Quebec (b. 1919)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Indian film actor (b. 1986)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actor (b. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON> <PERSON><PERSON>,  Israeli novelist, essayist, and playwright (b. 1936)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/A._B._Yehoshua\" title=\"A. B. Yehoshua\"><PERSON><PERSON> <PERSON><PERSON></a>, Israeli novelist, essayist, and playwright (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._B._Yehoshua\" title=\"A. B. Yehoshua\"><PERSON><PERSON> <PERSON><PERSON></a>, Israeli novelist, essayist, and playwright (b. 1936)", "links": [{"title": "A. B. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, South African businesswoman (b. 1963)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African businesswoman (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African businesswoman (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer, author, and politician (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}