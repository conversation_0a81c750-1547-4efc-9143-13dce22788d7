{"date": "September 10", "url": "https://wikipedia.org/wiki/September_10", "data": {"Events": [{"year": "506", "text": "The bishops of Visigothic Gaul meet in the Council of Agde.", "html": "506 - The bishops of Visigothic Gaul meet in the <a href=\"https://wikipedia.org/wiki/Council_of_Agde\" title=\"Council of Agde\">Council of Agde</a>.", "no_year_html": "The bishops of Visigothic Gaul meet in the <a href=\"https://wikipedia.org/wiki/Council_of_Agde\" title=\"Council of Agde\">Council of Agde</a>.", "links": [{"title": "Council of Agde", "link": "https://wikipedia.org/wiki/Council_of_Agde"}]}, {"year": "1089", "text": "The first synod of pope <PERSON> starts in Melfi, with seventy bishops and twelve abbots in attendance. The synod issues several decrees about church law and deals with the relation with the Greek part of the Church.", "html": "1089 - The <a href=\"https://wikipedia.org/wiki/Synod_of_Melfi_(1089)\" title=\"Synod of Melfi (1089)\">first synod</a> of <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\">pope <PERSON> II</a> starts in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, with seventy bishops and twelve abbots in attendance. The synod issues several decrees about church law and deals with the relation with the Greek part of the Church.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Synod_of_Melfi_(1089)\" title=\"Synod of Melfi (1089)\">first synod</a> of <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\">pope <PERSON> II</a> starts in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, with seventy bishops and twelve abbots in attendance. The synod issues several decrees about church law and deals with the relation with the Greek part of the Church.", "links": [{"title": "Synod of Melfi (1089)", "link": "https://wikipedia.org/wiki/Synod_of_Melfi_(1089)"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Pope_Urban_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fi"}]}, {"year": "1419", "text": "<PERSON> the Fearless, Duke of Burgundy is assassinated by adherents of the <PERSON><PERSON><PERSON>, the future <PERSON> of France.", "html": "1419 - <PERSON> the Fearless, Duke of Burgundy <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_the_Fearless\" title=\"Assassination of <PERSON> the Fearless\">is assassinated</a> by adherents of the Dauphin, the future <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VII of France\"><PERSON> of France</a>.", "no_year_html": "<PERSON> the Fearless, Duke of Burgundy <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_the_Fearless\" title=\"Assassination of <PERSON> the Fearless\">is assassinated</a> by adherents of the Dauphin, the future <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_France\" title=\"Charles VII of France\"><PERSON> of France</a>.", "links": [{"title": "Assassination of <PERSON> the Fearless", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_Fearless"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}]}, {"year": "1509", "text": "An earthquake known as \"The Lesser Judgment Day\" hits Constantinople.", "html": "1509 - An earthquake known as \"<a href=\"https://wikipedia.org/wiki/1509_Constantinople_earthquake\" title=\"1509 Constantinople earthquake\">The Lesser Judgment Day</a>\" hits Constantinople.", "no_year_html": "An earthquake known as \"<a href=\"https://wikipedia.org/wiki/1509_Constantinople_earthquake\" title=\"1509 Constantinople earthquake\">The Lesser Judgment Day</a>\" hits Constantinople.", "links": [{"title": "1509 Constantinople earthquake", "link": "https://wikipedia.org/wiki/1509_Constantinople_earthquake"}]}, {"year": "1515", "text": "<PERSON> is invested as a <PERSON>.", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is invested as a <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is invested as a <PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "The Battle of Pinkie, the last full-scale military confrontation between England and Scotland, resulting in a decisive victory for the forces of <PERSON>.", "html": "1547 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Pinkie\" title=\"Battle of Pinkie\">Battle of Pinkie</a>, the last full-scale military confrontation between England and Scotland, resulting in a decisive victory for the forces of <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Pinkie\" title=\"Battle of Pinkie\">Battle of Pinkie</a>, the last full-scale military confrontation between England and Scotland, resulting in a decisive victory for the forces of <PERSON>.", "links": [{"title": "Battle of Pinkie", "link": "https://wikipedia.org/wiki/Battle_of_Pinkie"}]}, {"year": "1561", "text": "Fourth Battle of Kawanakajima: <PERSON>da Shingen defeats <PERSON><PERSON><PERSON><PERSON> in the climax of their ongoing conflicts.", "html": "1561 - <a href=\"https://wikipedia.org/wiki/Battles_of_Kawanakajima\" title=\"Battles of Kawanakajima\">Fourth Battle of Kawanakajima</a>: Takeda Shingen defeats <PERSON><PERSON><PERSON><PERSON> in the climax of their ongoing conflicts.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battles_of_Kawanakajima\" title=\"Battles of Kawanakajima\">Fourth Battle of Kawanakajima</a>: Takeda Shingen defeats <PERSON><PERSON><PERSON><PERSON> in the climax of their ongoing conflicts.", "links": [{"title": "Battles of Kawanakajima", "link": "https://wikipedia.org/wiki/Battles_of_Kawanakajima"}]}, {"year": "1570", "text": "Spanish Jesuit missionaries land in present-day Virginia to establish the short-lived Ajacán Mission.", "html": "1570 - Spanish Jesuit missionaries land in present-day Virginia to establish the short-lived <a href=\"https://wikipedia.org/wiki/Ajac%C3%A1n_Mission\" title=\"Ajacán Mission\">Ajacán Mission</a>.", "no_year_html": "Spanish Jesuit missionaries land in present-day Virginia to establish the short-lived <a href=\"https://wikipedia.org/wiki/Ajac%C3%A1n_Mission\" title=\"Ajacán Mission\">Ajacán Mission</a>.", "links": [{"title": "Ajacán Mission", "link": "https://wikipedia.org/wiki/Ajac%C3%A1n_Mission"}]}, {"year": "1573", "text": "German pirate <PERSON> and 33 of his crew are beheaded in Hamburg.", "html": "1573 - German pirate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 33 of his crew are <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>.", "no_year_html": "German pirate <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 33 of his crew are <a href=\"https://wikipedia.org/wiki/Decapitation\" title=\"Decapitation\">beheaded</a> in <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Decapitation", "link": "https://wikipedia.org/wiki/Decapitation"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}]}, {"year": "1607", "text": "<PERSON> is ousted as first president of the governing council of the Colony of Virginia; he is replaced by <PERSON>.", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted as first president of the governing council of the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>; he is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is ousted as first president of the governing council of the <a href=\"https://wikipedia.org/wiki/Colony_of_Virginia\" title=\"Colony of Virginia\">Colony of Virginia</a>; he is replaced by <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colony of Virginia", "link": "https://wikipedia.org/wiki/Colony_of_Virginia"}, {"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}]}, {"year": "1608", "text": "<PERSON> is elected council president of Jamestown, Virginia.", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> is elected council president of Jamestown, Virginia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> is elected council president of Jamestown, Virginia.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>(explorer)"}]}, {"year": "1622", "text": "Fifty-five Christians are executed in Nagasaki during the Great Genna Martyrdom", "html": "1622 - Fifty-five Christians are executed in <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a> during the <a href=\"https://wikipedia.org/wiki/Great_Gen<PERSON>_<PERSON>rdom\" title=\"Great <PERSON><PERSON>\">Great <PERSON><PERSON></a>", "no_year_html": "Fifty-five Christians are executed in <a href=\"https://wikipedia.org/wiki/Nagasaki\" title=\"Nagasaki\">Nagasaki</a> during the <a href=\"https://wikipedia.org/wiki/Great_<PERSON><PERSON>_<PERSON>\" title=\"Great <PERSON><PERSON>\">Great <PERSON><PERSON></a>", "links": [{"title": "Nagasaki", "link": "https://wikipedia.org/wiki/Nagasaki"}, {"title": "Great <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1640", "text": "Reapers' War: Junta de Braços (Assembly of Estates) of the Principality of Catalonia summoned. It assumes the sovereignty and enacts a series of revolutionary mesures which will lead to the Catalan Republic.", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>: <a href=\"https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os\" title=\"Junta de Braços\">Junta de Braços</a> (Assembly of <a href=\"https://wikipedia.org/wiki/Estates_of_the_realm\" title=\"Estates of the realm\">Estates</a>) of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> summoned. It assumes the sovereignty and enacts a series of revolutionary mesures which will lead to the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)\" title=\"Catalan Republic (1640-1641)\">Catalan Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reapers%27_War\" title=\"Reapers' War\">Reapers' War</a>: <a href=\"https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os\" title=\"Junta de Braços\">Junta de Braços</a> (Assembly of <a href=\"https://wikipedia.org/wiki/Estates_of_the_realm\" title=\"Estates of the realm\">Estates</a>) of the <a href=\"https://wikipedia.org/wiki/Principality_of_Catalonia\" title=\"Principality of Catalonia\">Principality of Catalonia</a> summoned. It assumes the sovereignty and enacts a series of revolutionary mesures which will lead to the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)\" title=\"Catalan Republic (1640-1641)\">Catalan Republic</a>.", "links": [{"title": "Reapers' War", "link": "https://wikipedia.org/wiki/Reapers%27_War"}, {"title": "Junta de Braços", "link": "https://wikipedia.org/wiki/Junta_de_Bra%C3%A7os"}, {"title": "Estates of the realm", "link": "https://wikipedia.org/wiki/Estates_of_the_realm"}, {"title": "Principality of Catalonia", "link": "https://wikipedia.org/wiki/Principality_of_Catalonia"}, {"title": "Catalan Republic (1640-1641)", "link": "https://wikipedia.org/wiki/Catalan_Republic_(1640%E2%80%931641)"}]}, {"year": "1724", "text": "<PERSON> leads the first performance of <PERSON><PERSON>, der du meine Seele, BWV 78, a chorale cantata based on a passion hymn by <PERSON>.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_der_du_meine_Seele,_BWV_78\" title=\"<PERSON><PERSON>, der du meine Seele, BWV 78\"><i><PERSON><PERSON>, der du meine Seele</i>, BWV 78</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> based on a passion hymn by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_der_du_meine_Seele,_BWV_78\" title=\"<PERSON><PERSON>, der du meine Seele, BWV 78\"><i><PERSON><PERSON>, der du meine Seele</i>, BWV 78</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> based on a passion hymn by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>, der du meine Seele, BWV 78", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_der_<PERSON>_<PERSON><PERSON>_<PERSON>,_BWV_78"}, {"title": "Chorale cantata cycle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_cantata_cycle"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "American Revolutionary War: <PERSON> volunteers to spy for the Continental Army.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> volunteers to spy for the Continental Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> volunteers to spy for the Continental Army.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "At the Battle of St. George's <PERSON><PERSON><PERSON>, British Honduras defeats Spain.", "html": "1798 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_St._George%27s_Caye\" title=\"Battle of St. George's Caye\">Battle of St. George's Caye</a>, British Honduras defeats Spain.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_St._George%27s_Caye\" title=\"Battle of St. George's Caye\">Battle of St. George's <PERSON><PERSON><PERSON></a>, British Honduras defeats Spain.", "links": [{"title": "Battle of St. George's Caye", "link": "https://wikipedia.org/wiki/Battle_of_St._George%27s_<PERSON><PERSON>e"}]}, {"year": "1813", "text": "The United States defeats a British Fleet at the Battle of Lake Erie during the War of 1812.", "html": "1813 - The United States defeats a British Fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Erie\" title=\"Battle of Lake Erie\">Battle of Lake Erie</a> during the War of 1812.", "no_year_html": "The United States defeats a British Fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Erie\" title=\"Battle of Lake Erie\">Battle of Lake Erie</a> during the War of 1812.", "links": [{"title": "Battle of Lake Erie", "link": "https://wikipedia.org/wiki/Battle_of_Lake_Erie"}]}, {"year": "1846", "text": "<PERSON> is granted a patent for the sewing machine.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a patent for the sewing machine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is granted a patent for the sewing machine.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON> discovers the asteroid 55 Pandora.", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the asteroid <a href=\"https://wikipedia.org/wiki/55_Pandora\" title=\"55 Pandora\">55 Pandora</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the asteroid <a href=\"https://wikipedia.org/wiki/55_Pandora\" title=\"55 Pandora\">55 Pandora</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "55 Pandora", "link": "https://wikipedia.org/wiki/55_Pandora"}]}, {"year": "1897", "text": "Lattimer massacre: A sheriff's posse kills 19 unarmed striking immigrant miners in Lattimer, Pennsylvania, United States.", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Lattimer_massacre\" title=\"Lattimer massacre\">Lattimer massacre</a>: A <a href=\"https://wikipedia.org/wiki/Posse_comitatus_(common_law)\" class=\"mw-redirect\" title=\"Posse comitatus (common law)\">sheriff's posse</a> kills 19 unarmed striking immigrant miners in Lattimer, Pennsylvania, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lattimer_massacre\" title=\"Lattimer massacre\">Lattimer massacre</a>: A <a href=\"https://wikipedia.org/wiki/Posse_comitatus_(common_law)\" class=\"mw-redirect\" title=\"Posse comitatus (common law)\">sheriff's posse</a> kills 19 unarmed striking immigrant miners in Lattimer, Pennsylvania, United States.", "links": [{"title": "Lattimer massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Posse comitatus (common law)", "link": "https://wikipedia.org/wiki/Posse_comitatus_(common_law)"}]}, {"year": "1898", "text": "Empress <PERSON> of Austria is assassinated by <PERSON>.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria\" title=\"Empress <PERSON> of Austria\">Empress <PERSON> of Austria</a> is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria\" title=\"Empress <PERSON> of Austria\">Empress <PERSON> of Austria</a> is assassinated by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Empress <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "Russian Civil War: The Red Army captures Kazan.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The Red Army <a href=\"https://wikipedia.org/wiki/Kazan_Operation\" title=\"Kazan Operation\">captures Kazan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The Red Army <a href=\"https://wikipedia.org/wiki/Kazan_Operation\" title=\"Kazan Operation\">captures Kazan</a>.", "links": [{"title": "Russian Civil War", "link": "https://wikipedia.org/wiki/Russian_Civil_War"}, {"title": "Kazan Operation", "link": "https://wikipedia.org/wiki/Kazan_Operation"}]}, {"year": "1919", "text": "The Republic of German-Austria signs the Treaty of Saint-Germain-en-Laye, ceding significant territories to Italy, Yugoslavia, and Czechoslovakia.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Republic_of_German-Austria\" title=\"Republic of German-Austria\">Republic of German-Austria</a> signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1919)\" title=\"Treaty of Saint-Germain-en-Laye (1919)\">Treaty of Saint-Germain-en-Laye</a>, ceding significant territories to Italy, Yugoslavia, and Czechoslovakia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_German-Austria\" title=\"Republic of German-Austria\">Republic of German-Austria</a> signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1919)\" title=\"Treaty of Saint-Germain-en-Laye (1919)\">Treaty of Saint-Germain-en-Laye</a>, ceding significant territories to Italy, Yugoslavia, and Czechoslovakia.", "links": [{"title": "Republic of German-Austria", "link": "https://wikipedia.org/wiki/Republic_of_German-Austria"}, {"title": "Treaty of Saint-Germain-en-Laye (1919)", "link": "https://wikipedia.org/wiki/Treaty_of_Saint-Germain-en-Laye_(1919)"}]}, {"year": "1932", "text": "The New York City Subway's third competing subway system, the municipally-owned IND, is opened.", "html": "1932 - The New York City Subway's third competing subway system, the municipally-owned <i><a href=\"https://wikipedia.org/wiki/Independent_Subway_System\" title=\"Independent Subway System\">IND</a></i>, is opened.", "no_year_html": "The New York City Subway's third competing subway system, the municipally-owned <i><a href=\"https://wikipedia.org/wiki/Independent_Subway_System\" title=\"Independent Subway System\">IND</a></i>, is opened.", "links": [{"title": "Independent Subway System", "link": "https://wikipedia.org/wiki/Independent_Subway_System"}]}, {"year": "1936", "text": "First World Individual Motorcycle Speedway Championship, Held at London's (England) Wembley Stadium", "html": "1936 - First World Individual <a href=\"https://wikipedia.org/wiki/Motorcycle_Speedway\" class=\"mw-redirect\" title=\"Motorcycle Speedway\">Motorcycle Speedway</a> Championship, Held at London's (England) <a href=\"https://wikipedia.org/wiki/Wembley_Stadium_(1923)\" title=\"Wembley Stadium (1923)\">Wembley Stadium</a>", "no_year_html": "First World Individual <a href=\"https://wikipedia.org/wiki/Motorcycle_Speedway\" class=\"mw-redirect\" title=\"Motorcycle Speedway\">Motorcycle Speedway</a> Championship, Held at London's (England) <a href=\"https://wikipedia.org/wiki/Wembley_Stadium_(1923)\" title=\"Wembley Stadium (1923)\">Wembley Stadium</a>", "links": [{"title": "Motorcycle Speedway", "link": "https://wikipedia.org/wiki/Motorcycle_Speedway"}, {"title": "Wembley Stadium (1923)", "link": "https://wikipedia.org/wiki/Wembley_Stadium_(1923)"}]}, {"year": "1937", "text": "Nine nations attend the Nyon Conference to address international piracy in the Mediterranean Sea.", "html": "1937 - Nine nations attend the <a href=\"https://wikipedia.org/wiki/Nyon_Conference\" title=\"Nyon Conference\">Nyon Conference</a> to address international piracy in the Mediterranean Sea.", "no_year_html": "Nine nations attend the <a href=\"https://wikipedia.org/wiki/Nyon_Conference\" title=\"Nyon Conference\">Nyon Conference</a> to address international piracy in the Mediterranean Sea.", "links": [{"title": "Nyon Conference", "link": "https://wikipedia.org/wiki/Nyon_Conference"}]}, {"year": "1939", "text": "World War II: The submarine <PERSON> <PERSON>xley is mistakenly sunk by the submarine HMS Triton near Norway and becomes the Royal Navy's first loss of a submarine in the war.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The submarine <a href=\"https://wikipedia.org/wiki/HMS_Oxley\" title=\"HMS Oxley\">HMS <i><PERSON><PERSON></i></a> is mistakenly sunk by the submarine <a href=\"https://wikipedia.org/wiki/HMS_Triton_(N15)\" title=\"HMS Triton (N15)\">HMS <i>Triton</i></a> near Norway and becomes the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>'s first loss of a submarine in the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The submarine <a href=\"https://wikipedia.org/wiki/HMS_Oxley\" title=\"<PERSON> Oxley\">HMS <i><PERSON><PERSON></i></a> is mistakenly sunk by the submarine <a href=\"https://wikipedia.org/wiki/HMS_Triton_(N15)\" title=\"HMS Triton (N15)\">HMS <i>Triton</i></a> near Norway and becomes the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>'s first loss of a submarine in the war.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "HMS Oxley", "link": "https://wikipedia.org/wiki/HMS_Oxley"}, {"title": "HMS Triton (N15)", "link": "https://wikipedia.org/wiki/HMS_Triton_(N15)"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1939", "text": "World War II: The Canadian declaration of war on Germany receives royal assent.", "html": "1939 - World War II: The <a href=\"https://wikipedia.org/wiki/Canadian_declaration_of_war_on_Germany\" title=\"Canadian declaration of war on Germany\">Canadian declaration of war on Germany</a> receives royal assent.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Canadian_declaration_of_war_on_Germany\" title=\"Canadian declaration of war on Germany\">Canadian declaration of war on Germany</a> receives royal assent.", "links": [{"title": "Canadian declaration of war on Germany", "link": "https://wikipedia.org/wiki/Canadian_declaration_of_war_on_Germany"}]}, {"year": "1942", "text": "World War II: The British Army carries out an amphibious landing on Madagascar to re-launch Allied offensive operations in the Madagascar Campaign.", "html": "1942 - World War II: The British Army carries out an amphibious landing on Madagascar to re-launch Allied offensive operations in the <a href=\"https://wikipedia.org/wiki/Battle_of_Madagascar\" title=\"Battle of Madagascar\">Madagascar Campaign</a>.", "no_year_html": "World War II: The British Army carries out an amphibious landing on Madagascar to re-launch Allied offensive operations in the <a href=\"https://wikipedia.org/wiki/Battle_of_Madagascar\" title=\"Battle of Madagascar\">Madagascar Campaign</a>.", "links": [{"title": "Battle of Madagascar", "link": "https://wikipedia.org/wiki/Battle_of_Madagascar"}]}, {"year": "1943", "text": "World War II: In the course of Operation <PERSON>chse, German troops begin their occupation of Rome.", "html": "1943 - World War II: In the course of <a href=\"https://wikipedia.org/wiki/Operation_Achse\" title=\"Operation Achse\">Operation Achse</a>, German troops begin their occupation of Rome.", "no_year_html": "World War II: In the course of <a href=\"https://wikipedia.org/wiki/Operation_Achse\" title=\"Operation Achse\">Operation Achse</a>, German troops begin their occupation of Rome.", "links": [{"title": "Operation Achse", "link": "https://wikipedia.org/wiki/Operation_Achse"}]}, {"year": "1960", "text": "At the Summer Olympics in Rome, <PERSON><PERSON> becomes the first sub-Saharan African to win a gold medal, winning the marathon in bare feet.", "html": "1960 - At the Summer Olympics in Rome, <a href=\"https://wikipedia.org/wiki/Abe<PERSON>_Bikila\" title=\"Abebe Bikila\"><PERSON><PERSON></a> becomes the first sub-Saharan African to win a gold medal, winning the marathon in bare feet.", "no_year_html": "At the Summer Olympics in Rome, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bikila\" title=\"Abebe Bikila\"><PERSON><PERSON></a> becomes the first sub-Saharan African to win a gold medal, winning the marathon in bare feet.", "links": [{"title": "Abebe B<PERSON>", "link": "https://wikipedia.org/wiki/Abebe_Bikila"}]}, {"year": "1961", "text": "In the Italian Grand Prix, a crash causes the death of German Formula One driver <PERSON> and 15 spectators who are hit by his Ferrari, the deadliest accident in F1 history.", "html": "1961 - In the <a href=\"https://wikipedia.org/wiki/1961_Italian_Grand_Prix\" title=\"1961 Italian Grand Prix\">Italian Grand Prix</a>, a crash causes the death of German Formula One driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 15 spectators who are hit by his Ferrari, the deadliest accident in F1 history.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1961_Italian_Grand_Prix\" title=\"1961 Italian Grand Prix\">Italian Grand Prix</a>, a crash causes the death of German Formula One driver <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and 15 spectators who are hit by his Ferrari, the deadliest accident in F1 history.", "links": [{"title": "1961 Italian Grand Prix", "link": "https://wikipedia.org/wiki/1961_Italian_Grand_Prix"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "The people of Gibraltar vote to remain a British dependency rather than becoming part of Spain.", "html": "1967 - The people of Gibraltar <a href=\"https://wikipedia.org/wiki/1967_Gibraltar_sovereignty_referendum\" title=\"1967 Gibraltar sovereignty referendum\">vote</a> to remain a British dependency rather than becoming part of Spain.", "no_year_html": "The people of Gibraltar <a href=\"https://wikipedia.org/wiki/1967_Gibraltar_sovereignty_referendum\" title=\"1967 Gibraltar sovereignty referendum\">vote</a> to remain a British dependency rather than becoming part of Spain.", "links": [{"title": "1967 Gibraltar sovereignty referendum", "link": "https://wikipedia.org/wiki/1967_Gibraltar_sovereignty_referendum"}]}, {"year": "1974", "text": "Guinea-Bissau gains independence from Portugal.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> gains independence from Portugal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> gains independence from Portugal.", "links": [{"title": "Guinea-Bissau", "link": "https://wikipedia.org/wiki/Guinea-Bissau"}]}, {"year": "1976", "text": "A British Airways Hawker <PERSON><PERSON><PERSON>dent and an Inex-Adria DC-9 collide near Zagreb, Yugoslavia, killing 176.", "html": "1976 - A British Airways Hawker <PERSON> and an Inex-Adria DC-9 <a href=\"https://wikipedia.org/wiki/1976_Zagreb_mid-air_collision\" title=\"1976 Zagreb mid-air collision\">collide</a> near Zagreb, Yugoslavia, killing 176.", "no_year_html": "A British Airways Hawker <PERSON><PERSON><PERSON> and an Inex-Adria DC-9 <a href=\"https://wikipedia.org/wiki/1976_Zagreb_mid-air_collision\" title=\"1976 Zagreb mid-air collision\">collide</a> near Zagreb, Yugoslavia, killing 176.", "links": [{"title": "1976 Zagreb mid-air collision", "link": "https://wikipedia.org/wiki/1976_Zagreb_mid-air_collision"}]}, {"year": "1977", "text": "<PERSON><PERSON>, convicted of torture and murder, is the last person to be executed by guillotine in France.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>doubi\"><PERSON><PERSON></a>, convicted of torture and murder, is the last person to be executed by guillotine in France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>doubi\"><PERSON><PERSON></a>, convicted of torture and murder, is the last person to be executed by guillotine in France.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "Operation Barras successfully frees six British soldiers held captive for over two weeks and contributes to the end of the Sierra Leone Civil War.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Operation_Barras\" title=\"Operation Barras\">Operation Barras</a> successfully frees six British soldiers held captive for over two weeks and contributes to the end of the Sierra Leone Civil War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Barras\" title=\"Operation Barras\">Operation Barras</a> successfully frees six British soldiers held captive for over two weeks and contributes to the end of the Sierra Leone Civil War.", "links": [{"title": "Operation Barras", "link": "https://wikipedia.org/wiki/Operation_Barras"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, mayor of Campinas, Brazil is assassinated.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_da_Costa_Santos\" title=\"<PERSON>t<PERSON><PERSON> da <PERSON> Santos\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, mayor of Campinas, Brazil is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_da_Costa_Santos\" title=\"<PERSON>t<PERSON><PERSON> da <PERSON> Santos\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, mayor of Campinas, Brazil is assassinated.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_Costa_Santos"}]}, {"year": "2001", "text": "During his appearance on the British TV game show Who Wants to be a Millionaire?, contestant <PERSON> reaches the £1 million top prize, but it was later revealed that he had cheated to the top prize by listening to coughs from his wife and another contestant.", "html": "2001 - During his appearance on the British TV <a href=\"https://wikipedia.org/wiki/Game_show\" title=\"Game show\">game show</a> <i><a href=\"https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F\" title=\"Who Wants to Be a Millionaire?\">Who Wants to be a Millionaire?</a></i>, contestant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches the £1 million top prize, but it was later revealed that he had cheated to the top prize by listening to coughs from his wife and another contestant.", "no_year_html": "During his appearance on the British TV <a href=\"https://wikipedia.org/wiki/Game_show\" title=\"Game show\">game show</a> <i><a href=\"https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F\" title=\"Who Wants to Be a Millionaire?\">Who Wants to be a Millionaire?</a></i>, contestant <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches the £1 million top prize, but it was later revealed that he had cheated to the top prize by listening to coughs from his wife and another contestant.", "links": [{"title": "Game show", "link": "https://wikipedia.org/wiki/Game_show"}, {"title": "Who Wants to Be a Millionaire?", "link": "https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "Switzerland, traditionally a neutral country, becomes a full member of the United Nations.", "html": "2002 - Switzerland, traditionally a neutral country, <a href=\"https://wikipedia.org/wiki/Foreign_relations_of_Switzerland\" title=\"Foreign relations of Switzerland\">becomes a full member of the United Nations</a>.", "no_year_html": "Switzerland, traditionally a neutral country, <a href=\"https://wikipedia.org/wiki/Foreign_relations_of_Switzerland\" title=\"Foreign relations of Switzerland\">becomes a full member of the United Nations</a>.", "links": [{"title": "Foreign relations of Switzerland", "link": "https://wikipedia.org/wiki/Foreign_relations_of_Switzerland"}]}, {"year": "2007", "text": "Former Prime Minister of Pakistan <PERSON><PERSON><PERSON> returns to Pakistan after seven years in exile, following a military coup in October 1999.", "html": "2007 - Former Prime Minister of Pakistan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns to Pakistan after seven years in exile, following a military coup in October 1999.", "no_year_html": "Former Prime Minister of Pakistan <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> returns to Pakistan after seven years in exile, following a military coup in October 1999.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "The Large Hadron Collider at CERN, described as the biggest scientific experiment in history, is powered up in Geneva, Switzerland.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Large_Hadron_Collider\" title=\"Large Hadron Collider\">Large Hadron Collider</a> at <a href=\"https://wikipedia.org/wiki/European_Organization_for_Nuclear_Research\" class=\"mw-redirect\" title=\"European Organization for Nuclear Research\">CERN</a>, described as the biggest scientific experiment in history, is powered up in Geneva, Switzerland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Large_Hadron_Collider\" title=\"Large Hadron Collider\">Large Hadron Collider</a> at <a href=\"https://wikipedia.org/wiki/European_Organization_for_Nuclear_Research\" class=\"mw-redirect\" title=\"European Organization for Nuclear Research\">CERN</a>, described as the biggest scientific experiment in history, is powered up in Geneva, Switzerland.", "links": [{"title": "Large Hadron Collider", "link": "https://wikipedia.org/wiki/Large_Hadron_Collider"}, {"title": "European Organization for Nuclear Research", "link": "https://wikipedia.org/wiki/European_Organization_for_Nuclear_Research"}]}, {"year": "2017", "text": "Hurricane <PERSON>rma makes landfall on Cudjoe Key, Florida as a Category 4, after causing catastrophic damage throughout the Caribbean. Irma resulted in 134 deaths and $77.2 billion (2017 USD) in damage.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Hurricane_Irma\" title=\"Hurricane Irma\">Hurricane Irma</a> makes landfall on <a href=\"https://wikipedia.org/wiki/Cudjoe_Key,_Florida\" title=\"Cudjoe Key, Florida\">Cudjoe Key, Florida</a> as a Category 4, after causing catastrophic damage throughout the Caribbean. Irma resulted in 134 deaths and $77.2 billion (2017 USD) in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Irma\" title=\"Hurricane Irma\">Hurricane Irma</a> makes landfall on <a href=\"https://wikipedia.org/wiki/Cudjoe_Key,_Florida\" title=\"Cudjoe Key, Florida\">Cudjoe Key, Florida</a> as a Category 4, after causing catastrophic damage throughout the Caribbean. Irma resulted in 134 deaths and $77.2 billion (2017 USD) in damage.", "links": [{"title": "Hurricane Irma", "link": "https://wikipedia.org/wiki/Hurricane_Irma"}, {"title": "Cudjoe Key, Florida", "link": "https://wikipedia.org/wiki/Cud<PERSON>e_Key,_Florida"}]}, {"year": "2022", "text": "Death of Queen <PERSON>: King <PERSON> is formally proclaimed as monarch at a meeting of the Accession Council in St James's Palace.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Death_of_Queen_Elizabeth_II\" class=\"mw-redirect\" title=\"Death of Queen <PERSON> II\">Death of Queen <PERSON> II</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is formally proclaimed as monarch at a meeting of the <a href=\"https://wikipedia.org/wiki/Accession_Council\" title=\"Accession Council\">Accession Council</a> in <a href=\"https://wikipedia.org/wiki/St_James%27s_Palace\" title=\"St James's Palace\">St James's Palace</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Death_of_Queen_Elizabeth_II\" class=\"mw-redirect\" title=\"Death of Queen Elizabeth II\">Death of Queen <PERSON> II</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a> is formally proclaimed as monarch at a meeting of the <a href=\"https://wikipedia.org/wiki/Accession_Council\" title=\"Accession Council\">Accession Council</a> in <a href=\"https://wikipedia.org/wiki/St_James%27s_Palace\" title=\"St James's Palace\">St James's Palace</a>.", "links": [{"title": "Death of Queen <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_Queen_<PERSON>_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Accession Council", "link": "https://wikipedia.org/wiki/Accession_Council"}, {"title": "St James's Palace", "link": "https://wikipedia.org/wiki/St_James%27s_Palace"}]}], "Births": [{"year": "877", "text": "<PERSON><PERSON><PERSON><PERSON>, patriarch of Alexandria (d. 940)", "html": "877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Greek_Orthodox_Patriarchate_of_Alexandria_and_all_Africa\" class=\"mw-redirect\" title=\"Greek Orthodox Patriarchate of Alexandria and all Africa\">patriarch of Alexandria</a> (d. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Greek_Orthodox_Patriarchate_of_Alexandria_and_all_Africa\" class=\"mw-redirect\" title=\"Greek Orthodox Patriarchate of Alexandria and all Africa\">patriarch of Alexandria</a> (d. 940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/Eutychius_of_Alexandria"}, {"title": "Greek Orthodox Patriarchate of Alexandria and all Africa", "link": "https://wikipedia.org/wiki/Greek_Orthodox_Patriarchate_of_Alexandria_and_all_Africa"}]}, {"year": "904", "text": "<PERSON>, posthumously known as Emperor <PERSON><PERSON> of Later Zhou", "html": "904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, posthumously known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Emperor <PERSON><PERSON> of Later Zhou</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, posthumously known as <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Emperor <PERSON><PERSON> of Later Zhou</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1423", "text": "<PERSON>, Princess of Asturias (d. 1425)", "html": "1423 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias\" title=\"<PERSON>, Princess of Asturias\"><PERSON>, Princess of Asturias</a> (d. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias\" title=\"<PERSON>, Princess of Asturias\"><PERSON>, Princess of Asturias</a> (d. 1425)", "links": [{"title": "<PERSON>, Princess of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_of_Asturias"}]}, {"year": "1487", "text": "<PERSON> (d. 1555)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"<PERSON> Julius <PERSON>\">Pope <PERSON></a> (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Julius <PERSON>\">Pope <PERSON></a> (d. 1555)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1497", "text": "<PERSON>, German theologian (d. 1563)", "html": "1497 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1547", "text": "<PERSON>, Landgrave of Hesse-Darmstadt (d. 1596)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Darmstadt\" title=\"<PERSON>, Landgrave of Hesse-Darmstadt\"><PERSON>, Landgrave of Hesse-Darmstadt</a> (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Darmstadt\" title=\"<PERSON>, Landgrave of Hesse-Darmstadt\"><PERSON>, Landgrave of Hesse-Darmstadt</a> (d. 1596)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Darmstadt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Darmstadt"}]}, {"year": "1550", "text": "<PERSON>, 7th Duke of Medina Sidonia, Spanish general (d. 1615)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>uzm%C3%A1n,_7th_Duke_of_Medina_Sidonia\" class=\"mw-redirect\" title=\"<PERSON>, 7th Duke of Medina Sidonia\"><PERSON>, 7th Duke of Medina Sidonia</a>, Spanish general (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>_Guzm%C3%A1n,_7th_Duke_of_Medina_Sidonia\" class=\"mw-redirect\" title=\"<PERSON>, 7th Duke of Medina Sidonia\"><PERSON>, 7th Duke of Medina Sidonia</a>, Spanish general (d. 1615)", "links": [{"title": "<PERSON>, 7th Duke of Medina Sidonia", "link": "https://wikipedia.org/wiki/<PERSON>_P%C3%A9<PERSON>_de_<PERSON>uzm%C3%A1n,_7th_Duke_of_Medina_Sidonia"}]}, {"year": "1561", "text": "<PERSON><PERSON><PERSON>, Paraguayan-Argentinian soldier and politician (d. 1634)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>dra\"><PERSON><PERSON><PERSON></a>, Paraguayan-Argentinian soldier and politician (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan-Argentinian soldier and politician (d. 1634)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1588", "text": "<PERSON>, English singer-songwriter and lute player (d. 1666)", "html": "1588 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>te\" title=\"<PERSON><PERSON>\">lute</a> player (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and <a href=\"https://wikipedia.org/wiki/<PERSON>te\" title=\"<PERSON><PERSON>\">lute</a> player (d. 1666)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lute", "link": "https://wikipedia.org/wiki/Lute"}]}, {"year": "1624", "text": "<PERSON>, English physician and author (d. 1689)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and author (d. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>ydenham"}]}, {"year": "1638", "text": "<PERSON> of Spain (d. 1683)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1683)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Spain"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON> the Younger, Danish anatomist (d. 1738)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON> the Younger</a>, Danish anatomist (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Younger\" title=\"<PERSON><PERSON><PERSON> the Younger\"><PERSON><PERSON><PERSON> the Younger</a>, Danish anatomist (d. 1738)", "links": [{"title": "<PERSON><PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_the_<PERSON>"}]}, {"year": "1659", "text": "<PERSON>, English organist and composer (d. 1695)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (d. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (d. 1774)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (d. 1774)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1753", "text": "<PERSON>, English architect and academic, designed the Royal Academy and Freemasons' Hall (d. 1837)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Arts\" title=\"Royal Academy of Arts\">Royal Academy</a> and <a href=\"https://wikipedia.org/wiki/Freemasons%27_Hall,_London\" title=\"Freemasons' Hall, London\">Freemasons' Hall</a> (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Arts\" title=\"Royal Academy of Arts\">Royal Academy</a> and <a href=\"https://wikipedia.org/wiki/Freemasons%27_Hall,_London\" title=\"Freemasons' Hall, London\">Freemasons' Hall</a> (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Royal Academy of Arts", "link": "https://wikipedia.org/wiki/Royal_Academy_of_Arts"}, {"title": "Freemasons' Hall, London", "link": "https://wikipedia.org/wiki/Freemasons%27_Hall,_London"}]}, {"year": "1758", "text": "<PERSON>, American author (d. 1840)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON><PERSON><PERSON>, Mexican soldier and politician, 11th President of Mexico (d. 1854)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo\" title=\"Nicolás Bravo\"><PERSON><PERSON><PERSON></a>, Mexican soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo\" title=\"Nicolás Bravo\"><PERSON><PERSON><PERSON></a>, Mexican soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Bravo"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1786", "text": "<PERSON>, American surgeon and politician (d. 1860)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American surgeon and politician (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)\" title=\"<PERSON> (New York politician)\"><PERSON></a>, American surgeon and politician (d. 1860)", "links": [{"title": "<PERSON> (New York politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_York_politician)"}]}, {"year": "1787", "text": "<PERSON>, American statesman and politician (d. 1863)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statesman and politician (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statesman and politician (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON><PERSON><PERSON>, French archaeologist and author (d. 1868)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>\" title=\"<PERSON> Crèvecœur de Perthes\"><PERSON>ur de <PERSON></a>, French archaeologist and author (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>\" title=\"<PERSON> Crèvecœur de Perthes\"><PERSON>ur de <PERSON></a>, French archaeologist and author (d. 1868)", "links": [{"title": "<PERSON>rève<PERSON>œur de Perth<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_Cr%C3%A8vec%C5%93ur_de_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, English diarist (d. 1834)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, American voodoo practitioner (d. 1881)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Louisiana_Voodoo\" title=\"Louisiana Voodoo\">voodoo</a> practitioner (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Louisiana_Voodoo\" title=\"Louisiana Voodoo\">voodoo</a> practitioner (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Louisiana Voodoo", "link": "https://wikipedia.org/wiki/Louisiana_Voodoo"}]}, {"year": "1821", "text": "<PERSON>, English captain, engineer, and politician, 10th Governor of South Australia (d. 1897)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, engineer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, engineer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1836", "text": "<PERSON>, American general and politician (d. 1906)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, American minister and publisher, co-founded Funk & Wagnalls (d. 1912)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Funk_%26_Wagnalls\" title=\"Funk &amp; Wagnalls\">Funk &amp; Wagnalls</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Funk_%26_Wagnalls\" title=\"Funk &amp; Wagnalls\">Funk &amp; Wagnalls</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Funk & Wagnalls", "link": "https://wikipedia.org/wiki/Funk_%26_Wagnalls"}]}, {"year": "1839", "text": "<PERSON>, American mathematician, statistician, and philosopher (d. 1914)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and philosopher (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and philosopher (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English-Australian candy maker, created the Violet Crumble (d. 1918)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian candy maker, created the <a href=\"https://wikipedia.org/wiki/Violet_Crumble\" title=\"Violet Crumble\"><PERSON> Crumble</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian candy maker, created the <a href=\"https://wikipedia.org/wiki/Violet_Crumble\" title=\"Violet Crumble\"><PERSON> Crumble</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>rumble", "link": "https://wikipedia.org/wiki/Violet_Crumble"}]}, {"year": "1852", "text": "<PERSON>, Danish businessman, founded the East Asiatic Company (d. 1937)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish businessman, founded the <a href=\"https://wikipedia.org/wiki/East_Asiatic_Company\" class=\"mw-redirect\" title=\"East Asiatic Company\">East Asiatic Company</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish businessman, founded the <a href=\"https://wikipedia.org/wiki/East_Asiatic_Company\" class=\"mw-redirect\" title=\"East Asiatic Company\">East Asiatic Company</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "East Asiatic Company", "link": "https://wikipedia.org/wiki/East_Asiatic_Company"}]}, {"year": "1852", "text": "<PERSON>, American tribal chief (d. 1935)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tribal chief (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tribal chief (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Russian-Swiss painter (d. 1938)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss painter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss painter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German botanist and geneticist (d. 1933)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and geneticist (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist and geneticist (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Danish author and poet (d. 1930)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>akj%C3%A6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish author and poet (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Je<PERSON>_<PERSON>akj%C3%A6r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish author and poet (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Je<PERSON>_Aakj%C3%A6r"}]}, {"year": "1871", "text": "<PERSON>, English engineer (d. 1952)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian cricketer (d. 1933)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Ranjits<PERSON><PERSON><PERSON>\" title=\"Ranjits<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranjits<PERSON><PERSON><PERSON>\" title=\"Ranjitsin<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>ji"}]}, {"year": "1874", "text": "<PERSON><PERSON>, African American educator, clubwoman and suffragist (d. 1954)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African American educator, clubwoman and suffragist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African American educator, clubwoman and suffragist (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American forester and philanthropist (d. 1957)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American forester and philanthropist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American forester and philanthropist (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Australian businessman (d. 1942)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American poet and playwright (d. 1966)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georgia_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Native American activist (d. 1947)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American activist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American activist (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Estonian architect (d. 1964)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Dutch cardinal (d. 1955)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cardinal (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cardinal (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American critic and biographer (d. 1950)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American critic and biographer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American critic and biographer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, American poet, novelist, and memoirist (d. 1961)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/H.D.\" title=\"H.D.\"><PERSON><PERSON><PERSON>.</a>, American poet, novelist, and memoirist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H.D.\" title=\"H.D.\"><PERSON>.<PERSON>.</a>, American poet, novelist, and memoirist (d. 1961)", "links": [{"title": "H.D.", "link": "https://wikipedia.org/wiki/H.D."}]}, {"year": "1887", "text": "<PERSON>, Italian soldier and politician, 3rd President of the Italian Republic (d. 1978)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>"}, {"title": "President of the Italian Republic", "link": "https://wikipedia.org/wiki/President_of_the_Italian_Republic"}]}, {"year": "1887", "text": "<PERSON>, English soldier and geographer (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English soldier and geographer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English soldier and geographer (d. 1976)", "links": [{"title": "<PERSON> (geographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geographer)"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, 1st Chief Minister of Uttar Pradesh (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Uttar_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Uttar Pradesh\">Chief Minister of Uttar Pradesh</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Uttar_Pradesh\" class=\"mw-redirect\" title=\"Chief Minister of Uttar Pradesh\">Chief Minister of Uttar Pradesh</a> (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Uttar Pradesh", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Uttar_Pradesh"}]}, {"year": "1888", "text": "<PERSON>, Russian-American painter (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Israel_<PERSON>\" title=\"Israel Abramofsky\"><PERSON></a>, Russian-American painter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_<PERSON>\" title=\"Israel Abramofsky\">Israel <PERSON></a>, Russian-American painter (d. 1976)", "links": [{"title": "Israel A<PERSON>ky", "link": "https://wikipedia.org/wiki/Israel_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Finnish wrestler (d. 1929)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B%C3%B6hling\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_B%C3%B6hling\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivar_B%C3%B6hling"}]}, {"year": "1890", "text": "<PERSON>, New Zealand-Australian miner and politician, 30th Premier of New South Wales (d. 1978)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian miner and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian miner and politician, 30th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1890", "text": "<PERSON>, Italian-French fashion designer (d. 1973)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French fashion designer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French fashion designer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Austrian-Bohemian author, poet, and playwright (d. 1945)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Bohemian author, poet, and playwright (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Bohemian author, poet, and playwright (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, British archaeologist and officer (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British archaeologist and officer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British archaeologist and officer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1894", "text": "<PERSON>, Soviet screenwriter/producer/director of Ukrainian origin (d. 1956)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet screenwriter/producer/director of Ukrainian origin (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet screenwriter/producer/director of Ukrainian origin (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and author (d. 1976)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/V<PERSON>wana<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>wana<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>wana<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American actress and dancer (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Canadian lawyer, judge, and politician, 11th Chief Justice of Canada (d. 1970)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1896", "text": "<PERSON>, Chinese general (d. 1946)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ting\"><PERSON></a>, Chinese general (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tin<PERSON>\"><PERSON></a>, Chinese general (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ting"}]}, {"year": "1897", "text": "<PERSON>, French philosopher, novelist, and poet (d. 1962)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, novelist, and poet (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, novelist, and poet (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, German actress and singer (d. 1976)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress and singer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hilde<PERSON>\"><PERSON><PERSON></a>, German actress and singer (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American actress (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, American actress (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, American chemist and engineer (d. 1999)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aldo Se<PERSON>\"><PERSON><PERSON></a>, American chemist and engineer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Se<PERSON>\"><PERSON><PERSON></a>, American chemist and engineer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON>mon"}]}, {"year": "1903", "text": "<PERSON>, English author and critic (d. 1974)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American horse rider and manager (d. 2003)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craven\"><PERSON></a>, American horse rider and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Craven\"><PERSON></a>, American horse rider and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American theorist and politician (d. 1972)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and politician (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German geographer, academic, and mountaineer (d. 1937)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Karl_Wien\" title=\"Karl Wien\"><PERSON></a>, German geographer, academic, and mountaineer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Karl_Wien\" title=\"Karl Wien\"><PERSON></a>, German geographer, academic, and mountaineer (d. 1937)", "links": [{"title": "Karl Wien", "link": "https://wikipedia.org/wiki/Karl_Wien"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American general (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>va_<PERSON>_<PERSON>tch\" title=\"<PERSON>va R<PERSON>\"><PERSON><PERSON></a>, American general (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>va_<PERSON>_<PERSON>tch\" title=\"Alva R. <PERSON>tch\"><PERSON><PERSON></a>, American general (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>tch"}]}, {"year": "1907", "text": "<PERSON>, Australian geologist and palaeontologist (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian geologist and palaeontologist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dorothy <PERSON>\"><PERSON></a>, Australian geologist and palaeontologist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian soldier and politician, 33rd Premier of Tasmania (d. 2004)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian soldier and politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 2004)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1908", "text": "<PERSON>, American pianist, composer, and bandleader (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American archaeologist and author (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archaeologist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archaeologist and author (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, 5th Vice President of India (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Basappa_Danappa_Jatti\" class=\"mw-redirect\" title=\"Basappa Danappa Jatti\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India\" class=\"mw-redirect\" title=\"List of Vice Presidents of India\">Vice President of India</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Basappa_Danappa_Jatti\" class=\"mw-redirect\" title=\"Basappa Danappa Jatti\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India\" class=\"mw-redirect\" title=\"List of Vice Presidents of India\">Vice President of India</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Basa<PERSON>_Danappa_Jatti"}, {"title": "List of Vice Presidents of India", "link": "https://wikipedia.org/wiki/List_of_Vice_Presidents_of_India"}]}, {"year": "1913", "text": "<PERSON>, American academic and diplomat, United States Ambassador to Brazil (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Brazil\" class=\"mw-redirect\" title=\"United States Ambassador to Brazil\">United States Ambassador to Brazil</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Brazil\" class=\"mw-redirect\" title=\"United States Ambassador to Brazil\">United States Ambassador to Brazil</a> (d. 2009)", "links": [{"title": "<PERSON> Gordon", "link": "https://wikipedia.org/wiki/<PERSON>_Gordon"}, {"title": "United States Ambassador to Brazil", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Brazil"}]}, {"year": "1914", "text": "<PERSON>, Baron <PERSON> of the Maine, Anglo-Irish captain and politician, 4th Prime Minister of Northern Ireland (d. 1990)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_Baron_<PERSON>%27N<PERSON><PERSON>_of_the_Maine\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of the Maine\"><PERSON>, Baron <PERSON> of the Maine</a>, Anglo-Irish captain and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland\" title=\"Prime Minister of Northern Ireland\">Prime Minister of Northern Ireland</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>,_Baron_<PERSON>%27N<PERSON><PERSON>_of_the_Maine\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of the Maine\"><PERSON>, Baron <PERSON> of the Maine</a>, Anglo-Irish captain and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland\" title=\"Prime Minister of Northern Ireland\">Prime Minister of Northern Ireland</a> (d. 1990)", "links": [{"title": "<PERSON>, Baron <PERSON> of the Maine", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27N<PERSON><PERSON>,_<PERSON>_<PERSON>%27Neil<PERSON>_of_the_Maine"}, {"title": "Prime Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Northern_Ireland"}]}, {"year": "1914", "text": "<PERSON>, American director and producer (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wise\"><PERSON></a>, American director and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edmond_O%27Brien"}]}, {"year": "1917", "text": "<PERSON>, Chilean poet and diplomat (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean poet and diplomat (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean poet and diplomat (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Dutch composer (d. 1988)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch composer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Italian engineer (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Estonian basketball player and coach (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Joann_L%C3%B5ssov\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Joann_L%C3%B5ssov\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player and coach (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joann_L%C3%B5ssov"}]}, {"year": "1921", "text": "<PERSON>, American general (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American businessman, founded Scientific Atlanta (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Scientific_Atlanta\" title=\"Scientific Atlanta\">Scientific Atlanta</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Scientific_Atlanta\" title=\"Scientific Atlanta\">Scientific Atlanta</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Scientific Atlanta", "link": "https://wikipedia.org/wiki/Scientific_Atlanta"}]}, {"year": "1924", "text": "<PERSON>, American baseball player and coach (d. 1988)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American educator and religious leader, 26th President of the Quorum of the Twelve Apostles (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and religious leader, 26th <a href=\"https://wikipedia.org/wiki/President_of_the_Quorum_of_the_Twelve_Apostles_(LDS_Church)\" title=\"President of the Quorum of the Twelve Apostles (LDS Church)\">President of the Quorum of the Twelve Apostles</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and religious leader, 26th <a href=\"https://wikipedia.org/wiki/President_of_the_Quorum_of_the_Twelve_Apostles_(LDS_Church)\" title=\"President of the Quorum of the Twelve Apostles (LDS Church)\">President of the Quorum of the Twelve Apostles</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>er"}, {"title": "President of the Quorum of the Twelve Apostles (LDS Church)", "link": "https://wikipedia.org/wiki/President_of_the_Quorum_of_the_Twelve_Apostles_(LDS_Church)"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter (d. 1981)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American singer-songwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)\" title=\"<PERSON> (blues musician)\"><PERSON></a>, American singer-songwriter (d. 1981)", "links": [{"title": "<PERSON> (blues musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(blues_musician)"}]}, {"year": "1925", "text": "<PERSON>, English minister and cleric", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister and cleric", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)\" title=\"<PERSON> (minister)\"><PERSON></a>, English minister and cleric", "links": [{"title": "<PERSON> (minister)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(minister)"}]}, {"year": "1925", "text": "<PERSON>, Russian pianist and composer (d. 1996)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, English painter and illustrator (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English painter and illustrator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English painter and illustrator (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Scottish trombonist, composer, and producer (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish trombonist, composer, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish trombonist, composer, and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Canadian civil servant and politician", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bold<PERSON>\"><PERSON><PERSON></a>, Canadian civil servant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bold<PERSON>\"><PERSON><PERSON></a>, Canadian civil servant and politician", "links": [{"title": "Roch <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American minister and author, founded the Christian Research Institute (d. 1989)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author, founded the <a href=\"https://wikipedia.org/wiki/Christian_Research_Institute\" title=\"Christian Research Institute\">Christian Research Institute</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author, founded the <a href=\"https://wikipedia.org/wiki/Christian_Research_Institute\" title=\"Christian Research Institute\">Christian Research Institute</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Christian Research Institute", "link": "https://wikipedia.org/wiki/Christian_Research_Institute"}]}, {"year": "1928", "text": "<PERSON>, Canadian philosopher and humanitarian, founded L'Arche (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and humanitarian, founded <a href=\"https://wikipedia.org/wiki/L%27Arche\" title=\"L'Arche\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian philosopher and humanitarian, founded <a href=\"https://wikipedia.org/wiki/L%27Arche\" title=\"L'Arche\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "L'Arche", "link": "https://wikipedia.org/wiki/L%27Arche"}]}, {"year": "1929", "text": "<PERSON>, Canadian businessman and banker (d. 1997)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and banker (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and banker (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michel_B%C3%A9langer"}]}, {"year": "1929", "text": "<PERSON>, English historian, scholar, and curator (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist_and_writer)\" class=\"mw-redirect\" title=\"<PERSON> (artist and writer)\"><PERSON></a>, English historian, scholar, and curator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist_and_writer)\" class=\"mw-redirect\" title=\"<PERSON> (artist and writer)\"><PERSON></a>, English historian, scholar, and curator (d. 2012)", "links": [{"title": "<PERSON> (artist and writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist_and_writer)"}]}, {"year": "1929", "text": "<PERSON>, American golfer and businessman (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and businessman (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Palmer\"><PERSON></a>, American golfer and businessman (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Estonian chess player and engineer (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player and engineer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player and engineer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ain<PERSON>_<PERSON>k"}]}, {"year": "1931", "text": "<PERSON>, English author and agent (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and agent (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and agent (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Philip <PERSON> Hall\"><PERSON></a>, American actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Philip <PERSON> Hall\"><PERSON></a>, American actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American playwright, screenwriter, and producer (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, screenwriter, and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Russian colonel and cosmonaut (d. 2000)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel and cosmonaut (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel and cosmonaut (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, German-French fashion designer and photographer (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French fashion designer and photographer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French fashion designer and photographer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American journalist (d. 1997)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American baseball player and coach (d. 1985)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American educator and politician (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian pianist, composer, and educator", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "Mr. Wrestling II, American wrestler (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Mr._Wrestling_II\" title=\"Mr. Wrestling II\">Mr. Wrestling II</a>, American wrestler (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr._Wrestling_II\" title=\"Mr. Wrestling II\">Mr. Wrestling II</a>, American wrestler (d. 2020)", "links": [{"title": "Mr. Wrestling II", "link": "https://wikipedia.org/wiki/Mr._Wrestling_II"}]}, {"year": "1935", "text": "<PERSON>, American poet (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American biologist, geographer, and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Diamond\"><PERSON></a>, American biologist, geographer, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jared Diamond\"><PERSON></a>, American biologist, geographer, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>reet"}]}, {"year": "1938", "text": "<PERSON>, English radio and television host", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio and television host", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter, keyboard player, vibraphonist, and producer (d. 2025)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, vibraphonist, and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, vibraphonist, and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American football player (d. 1992)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chance\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chance\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American paleontologist, biologist, and author (d. 2002)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist, biologist, and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist, biologist, and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English harpsichord player and conductor, founded the Academy of Ancient Music (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor, founded the <a href=\"https://wikipedia.org/wiki/Academy_of_Ancient_Music\" title=\"Academy of Ancient Music\">Academy of Ancient Music</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor, founded the <a href=\"https://wikipedia.org/wiki/Academy_of_Ancient_Music\" title=\"Academy of Ancient Music\">Academy of Ancient Music</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}, {"title": "Academy of Ancient Music", "link": "https://wikipedia.org/wiki/Academy_of_Ancient_Music"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Japanese video game designer, invented Game Boy (d. 1997)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Gunpei_Yokoi\" title=\"Gunpei Yokoi\"><PERSON><PERSON><PERSON>ko<PERSON></a>, Japanese video game designer, invented <a href=\"https://wikipedia.org/wiki/Game_Boy\" title=\"Game Boy\">Game Boy</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gunpei_Yokoi\" title=\"Gunpei Yokoi\"><PERSON><PERSON><PERSON>ko<PERSON></a>, Japanese video game designer, invented <a href=\"https://wikipedia.org/wiki/Game_Boy\" title=\"Game Boy\">Game Boy</a> (d. 1997)", "links": [{"title": "Gun<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gunpei_Yokoi"}, {"title": "Game Boy", "link": "https://wikipedia.org/wiki/Game_Boy"}]}, {"year": "1942", "text": "<PERSON>, Irish-American singer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actor, singer, and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baritone)\" title=\"<PERSON> (baritone)\"><PERSON></a>, English actor, singer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(baritone)\" title=\"<PERSON> (baritone)\"><PERSON></a>, English actor, singer, and academic", "links": [{"title": "<PERSON> (baritone)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baritone)"}]}, {"year": "1945", "text": "<PERSON>, Puerto Rican singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian journalist and author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American colonel and astronaut", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, French lawyer and politician, French Minister of Foreign and European Affairs", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign and European Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign and European Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_All<PERSON>-<PERSON>"}, {"title": "Ministry of Foreign and European Affairs (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)"}]}, {"year": "1946", "text": "<PERSON>, American sprinter and football player (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English rock drummer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian singer-songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1947", "text": "<PERSON>, American golfer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English director and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Chinese historian and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player and coach  (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian actress and talk show host, 12th Spouse of the Prime Minister of Canada", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and talk show host, 12th <a href=\"https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Canada\" class=\"mw-redirect\" title=\"Spouse of the Prime Minister of Canada\">Spouse of the Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and talk show host, 12th <a href=\"https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Canada\" class=\"mw-redirect\" title=\"Spouse of the Prime Minister of Canada\">Spouse of the Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spouse of the Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Spouse_of_the_Prime_Minister_of_Canada"}]}, {"year": "1948", "text": "<PERSON>, American football player, coach, and radio host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, English rock drummer and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Barlow\" title=\"Barrie<PERSON> Barlow\"><PERSON><PERSON><PERSON></a>, English rock drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Barlow\" title=\"Barrie<PERSON> Barlow\"><PERSON><PERSON><PERSON></a>, English rock drummer and songwriter", "links": [{"title": "Barriemore Barlow", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Barlow"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English author and illustrator (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cole\"><PERSON><PERSON></a>, English author and illustrator (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cole\"><PERSON><PERSON></a>, English author and illustrator (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Cole"}]}, {"year": "1949", "text": "<PERSON>, American wrestler", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Reil<PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(political_commentator)\" title=\"<PERSON> (political commentator)\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON> (political commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(political_commentator)"}]}, {"year": "1950", "text": "<PERSON>, American singer and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Norwegian football player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1951", "text": "<PERSON>, English philosopher, theologian, and academic", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, theologian, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, theologian, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American wrestler", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American activist, founder of Code Pink", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Me<PERSON> Benjamin\"><PERSON><PERSON></a>, American activist, founder of <a href=\"https://wikipedia.org/wiki/Code_Pink\" title=\"Code Pink\">Code Pink</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Me<PERSON> Benjamin\"><PERSON><PERSON></a>, American activist, founder of <a href=\"https://wikipedia.org/wiki/Code_Pink\" title=\"Code Pink\">Code Pink</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Me<PERSON>_<PERSON>"}, {"title": "Code Pink", "link": "https://wikipedia.org/wiki/Code_Pink"}]}, {"year": "1952", "text": "<PERSON>, Paraguayan-Canadian lawyer and politician, 48th Canadian Minister of Justice", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan-Canadian lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan-Canadian lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1953", "text": "<PERSON>, American science fiction author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish businessman and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English journalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, English politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American-Canadian actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American kickboxer and actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kickboxer)\" title=\"<PERSON> (kickboxer)\"><PERSON></a>, American kickboxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kickboxer)\" title=\"<PERSON> (kickboxer)\"><PERSON></a>, American kickboxer and actor", "links": [{"title": "<PERSON> (kickboxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(kickboxer)"}]}, {"year": "1955", "text": "<PERSON>, American rock drummer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Irish keyboard player and songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Fin<PERSON>\"><PERSON><PERSON></a>, Irish keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gers\" title=\"<PERSON><PERSON> Fin<PERSON>\"><PERSON><PERSON></a>, Irish keyboard player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nie_Fingers"}]}, {"year": "1957", "text": "<PERSON>, British-American actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British-American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actress)"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Irish singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ey"}]}, {"year": "1959", "text": "<PERSON>, American actor, singer, and puppeteer (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American actor, singer, and puppeteer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)\" title=\"<PERSON> (puppeteer)\"><PERSON></a>, American actor, singer, and puppeteer (d. 2015)", "links": [{"title": "<PERSON> (puppeteer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(puppeteer)"}]}, {"year": "1960", "text": "<PERSON>, American author and illustrator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Scottish politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English actor and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1963", "text": "<PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1964", "text": "<PERSON>, Chinese businessman, co-founder of Alibaba Group", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Alibaba_Group\" title=\"Alibaba Group\">Alibaba Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Alibaba_Group\" title=\"Alibaba Group\">Alibaba Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alibaba Group", "link": "https://wikipedia.org/wiki/Alibaba_Group"}]}, {"year": "1964", "text": "<PERSON>, American engineer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(actress)\" title=\"<PERSON><PERSON> (actress)\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(actress)"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Austrian footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON> <PERSON>, American rapper, producer, and actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Big_Daddy_Kane\" title=\"Big Daddy Kane\"><PERSON> Daddy <PERSON></a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Daddy_Kane\" title=\"Big Daddy Kane\"><PERSON> Daddy <PERSON></a>, American rapper, producer, and actor", "links": [{"title": "Big Daddy Kane", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, New Zealand rugby player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American actor, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Cameroonian-French rapper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/M%C3%A9n%C3%A9lik\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian-French rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9n%C3%A9lik\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cameroonian-French rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9n%C3%A9lik"}]}, {"year": "1970", "text": "<PERSON>, Surinamese footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Dean_Gorr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Surinamese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rr%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Surinamese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rr%C3%A9"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American lawyer and policy analyst", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Neera_Tanden\" title=\"Neera Tanden\"><PERSON><PERSON></a>, American lawyer and policy analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neera_Tanden\" title=\"Neera Tanden\"><PERSON><PERSON></a>, American lawyer and policy analyst", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Neera_Tanden"}]}, {"year": "1971", "text": "<PERSON>, American jockey", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, American jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, American jockey", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)"}]}, {"year": "1972", "text": "<PERSON>, American actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Senegalese footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Dutch martial artist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Pakistani cricketer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1974)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1974)\"><PERSON></a>, Pakistani cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1974)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1974)\"><PERSON></a>, Pakistani cricketer and coach", "links": [{"title": "<PERSON> (cricketer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1974)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Croatian mixed martial artist, boxer, and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mixed martial artist, boxer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian mixed martial artist, boxer, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mir<PERSON>_Filipovi%C4%87"}]}, {"year": "1974", "text": "<PERSON>, American actor and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1975", "text": "<PERSON>, Canadian sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Toole\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Toole\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_O%27Toole"}]}, {"year": "1975", "text": "<PERSON>, American photographer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Brazilian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Dutch volleyball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> N<PERSON>dor\"><PERSON><PERSON></a>, Dutch volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dor"}]}, {"year": "1977", "text": "<PERSON>, American wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1977)\" class=\"mw-redirect\" title=\"<PERSON> (born 1977)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1977)\" class=\"mw-redirect\" title=\"<PERSON> (born 1977)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1977)"}]}, {"year": "1977", "text": "<PERSON>, New Zealand rugby player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, British comedian ", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comedian ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comedian ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Ram%C5%ABnas_%C5%A0i%C5%A1kauskas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C5%ABnas_%C5%A0i%C5%A1kauskas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ram%C5%ABnas_%C5%A0i%C5%A1kauskas"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1980", "text": "<PERSON>, American wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American bass player and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Japanese assassin of <PERSON><PERSON>", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese assassin of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Swiss tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Germ%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germ%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germ%C3%A1n_Denis"}]}, {"year": "1981", "text": "<PERSON>, American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American ballerina and author", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Copeland\"><PERSON></a>, American ballerina and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Copeland\"><PERSON></a>, American ballerina and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Argentinian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Guyanese-American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_Toulalan"}]}, {"year": "1983", "text": "<PERSON>, Canadian baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sander_Post\" title=\"Sander Post\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sander_Post\" title=\"Sander Post\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>er_Post"}]}, {"year": "1984", "text": "<PERSON>, English actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Treadaway\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Treadaway\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Treadaway\" title=\"<PERSON> Treadaway\"><PERSON> Tread<PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Treadaway\" title=\"<PERSON> Treadaway\"><PERSON> Treadaway</a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Treadaway"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Younger\" class=\"mw-redirect\" title=\"<PERSON> Younger\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Younger\" class=\"mw-redirect\" title=\"Drake Younger\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Latvian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Aleksandr<PERSON>_%C4%8Cekulajevs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%8Cekulajevs\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandrs_%C4%8Cekulajevs"}]}, {"year": "1985", "text": "<PERSON>, English rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" class=\"mw-redirect\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> M<PERSON>Coughtry\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ought<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Irish- English cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish- English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish- English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Japanese singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1988", "text": "<PERSON>, Canadian wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jordan_Staal\" title=\"Jordan Staal\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Staal\" title=\"Jordan Staal\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Jordan Staal", "link": "https://wikipedia.org/wiki/Jordan_Staal"}]}, {"year": "1988", "text": "<PERSON>, American mass murderer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Manish Pandey\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Manish Pandey\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Pandey"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Corban_Knight\" title=\"Corban Knight\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Corban_Knight\" title=\"Corban Knight\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "Corban Knight", "link": "https://wikipedia.org/wiki/Corban_Knight"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Egyptian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>y"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Kenyan footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1993", "text": "<PERSON>, Australian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, French rapper", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON></a>, French rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON></a>, French rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ish"}]}, {"year": "1995", "text": "<PERSON>, American comedian and actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ife"}]}, {"year": "1997", "text": "<PERSON>, Canadian golfer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Russian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American rapper", "html": "1998 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>\" title=\"Sheck <PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>\" title=\"Sheck <PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian swimmer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Albanian-born English footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian-born English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian-born English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2004", "text": "<PERSON>, American actor", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}], "Deaths": [{"year": "210 BC", "text": "<PERSON>, first emperor of China (b. 260 BC)", "html": "210 BC - 210 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first emperor of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> (b. 260 BC)", "no_year_html": "210 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first emperor of <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> (b. 260 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "602", "text": "<PERSON><PERSON>, empress of the Chinese Sui dynasty (b. 544)", "html": "602 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Qieluo\" title=\"<PERSON><PERSON> Qieluo\"><PERSON><PERSON></a>, empress of the Chinese <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> (b. 544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Qieluo\"><PERSON><PERSON></a>, empress of the Chinese <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> (b. 544)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}]}, {"year": "689", "text": "<PERSON>, official of the Chinese Tang dynasty", "html": "689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "History of China", "link": "https://wikipedia.org/wiki/History_of_China"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "710", "text": "<PERSON>, imperial prince of the Chinese Tang dynasty (b. c.  680)", "html": "710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fu\" title=\"<PERSON> Chongfu\"><PERSON></a>, imperial prince of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (b. c. 680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fu\" title=\"<PERSON> Chongfu\"><PERSON></a>, imperial prince of the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">Chinese</a> <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (b. c. 680)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>fu"}, {"title": "History of China", "link": "https://wikipedia.org/wiki/History_of_China"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "918", "text": "<PERSON>, Frankish margrave (b. c. 865)", "html": "918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Flanders\" title=\"<PERSON> II, Margrave of Flanders\"><PERSON> II</a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Margrave\" title=\"<PERSON>grave\">margrave</a> (b. c. 865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Flanders\" title=\"<PERSON> II, Mar<PERSON> of Flanders\"><PERSON> II</a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>grave\" title=\"<PERSON>grave\">margrave</a> (b. c. 865)", "links": [{"title": "<PERSON>, Margrave of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Flanders"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>grave"}]}, {"year": "952", "text": "<PERSON>, Chinese general (b. 885)", "html": "952 - <a href=\"https://wikipedia.org/wiki/Gao_Xingzhou\" title=\"Gao Xingzhou\"><PERSON></a>, Chinese general (b. 885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gao_Xingzhou\" title=\"Gao Xingzhou\"><PERSON></a>, Chinese general (b. 885)", "links": [{"title": "Gao Xingzhou", "link": "https://wikipedia.org/wiki/Gao_Xingzhou"}]}, {"year": "954", "text": "<PERSON>, king of West Francia (b. 920)", "html": "954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> IV</a>, king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (b. 920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> IV of France\"><PERSON> IV</a>, king of <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Francia</a> (b. 920)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IV_of_France"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}]}, {"year": "1167", "text": "<PERSON> of England, Holy Roman Empress (b. 1102)", "html": "1167 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Matilda of England</a>, <a href=\"https://wikipedia.org/wiki/List_of_Holy_Roman_Empresses\" class=\"mw-redirect\" title=\"List of Holy Roman Empresses\">Holy Roman Empress</a> (b. 1102)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Matilda of England</a>, <a href=\"https://wikipedia.org/wiki/List_of_Holy_Roman_Empresses\" class=\"mw-redirect\" title=\"List of Holy Roman Empresses\">Holy Roman Empress</a> (b. 1102)", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_Matilda"}, {"title": "List of Holy Roman Empresses", "link": "https://wikipedia.org/wiki/List_of_Holy_Roman_Empresses"}]}, {"year": "1197", "text": "<PERSON>, Count of Champagne (b. 1166)", "html": "1197 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON>, Count of Champagne</a> (b. 1166)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne\" title=\"<PERSON>, Count of Champagne\"><PERSON>, Count of Champagne</a> (b. 1166)", "links": [{"title": "<PERSON>, Count of Champagne", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Champagne"}]}, {"year": "1217", "text": "<PERSON>, 5th Earl of Devon, English politician", "html": "1217 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_5th_Earl_of_Devon\" title=\"<PERSON>, 5th Earl of Devon\"><PERSON>, 5th Earl of Devon</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_5th_Earl_of_Devon\" title=\"<PERSON>, 5th Earl of Devon\"><PERSON>, 5th Earl of Devon</a>, English politician", "links": [{"title": "<PERSON>, 5th Earl of Devon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_5th_Earl_of_Devon"}]}, {"year": "1281", "text": "<PERSON> <PERSON>, Margrave of Brandenburg-Stendal (b. 1237)", "html": "1281 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Mar<PERSON>_of_Brandenburg-Stendal\" title=\"<PERSON>, Margrave of Brandenburg-Stendal\"><PERSON>, Margrave of Brandenburg-Stendal</a> (b. 1237)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Stendal\" title=\"<PERSON>, Margrave of Brandenburg-Stendal\"><PERSON>, Margrave of Brandenburg-Stendal</a> (b. 1237)", "links": [{"title": "<PERSON> <PERSON>, Margrave of Brandenburg-Stendal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Brandenburg-Stendal"}]}, {"year": "1306", "text": "<PERSON> of Tolentino, Italian mystic and saint (b. 1245)", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Tolentino\" title=\"<PERSON> of Tolentino\"><PERSON> of Tolentino</a>, Italian mystic and saint (b. 1245)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tolentino\" title=\"<PERSON> of Tolentino\"><PERSON> of Tolentino</a>, Italian mystic and saint (b. 1245)", "links": [{"title": "<PERSON> of Tolentino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tolentino"}]}, {"year": "1308", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1285)", "html": "1308 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Nij%C5%8D\" title=\"Emperor Go-Nijō\">Emperor <PERSON><PERSON>Nijō</a> of Japan (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Nij%C5%8D\" title=\"Emperor Go-Nijō\">Emperor <PERSON><PERSON>Nijō</a> of Japan (b. 1285)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Nij%C5%8D"}]}, {"year": "1364", "text": "<PERSON> of Taranto, King of Albania", "html": "1364 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON> of Taranto\"><PERSON> of Taranto</a>, King of Albania", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Taranto\" class=\"mw-redirect\" title=\"<PERSON> of Taranto\"><PERSON> of Taranto</a>, King of Albania", "links": [{"title": "<PERSON> of Taranto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tara<PERSON>"}]}, {"year": "1382", "text": "<PERSON> of Hungary (b. 1326)", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1326)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1326)", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1384", "text": "<PERSON> of Dreux, Countess of Penthievre and Duchess of Brittany (b. 1319)", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON> Penthièvre\"><PERSON> of Dreux</a>, Countess of Penthievre and Duchess of Brittany (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8vre\" title=\"<PERSON> Penthièvre\"><PERSON> Dreux</a>, Countess of Penthievre and Duchess of Brittany (b. 1319)", "links": [{"title": "<PERSON> Pen<PERSON>èvre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>thi%C3%A8vre"}]}, {"year": "1419", "text": "<PERSON> the <PERSON>less, Duke of Burgundy (b. 1371)", "html": "1419 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fearless\" title=\"<PERSON> the Fearless\"><PERSON> the Fearless</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (b. 1371)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fearless\" title=\"<PERSON> the Fearless\"><PERSON> the Fearless</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Burgundy\" title=\"Duke of Burgundy\">Duke of Burgundy</a> (b. 1371)", "links": [{"title": "<PERSON> the <PERSON>less", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fearless"}, {"title": "Duke of Burgundy", "link": "https://wikipedia.org/wiki/Duke_of_Burgundy"}]}, {"year": "1479", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian cardinal and humanist (b. 1422)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal and humanist (b. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal and humanist (b. 1422)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1482", "text": "<PERSON>, Italian warlord (b. 1422)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian warlord (b. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian warlord (b. 1422)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1504", "text": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy (b. 1480)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy</a> (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Savoy\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy</a> (b. 1480)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Savoy"}]}, {"year": "1519", "text": "<PERSON>, English theologian and scholar (b. 1467)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (b. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian and scholar (b. 1467)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1549", "text": "<PERSON>, English politician (b. 1501)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1501)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1501)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1591", "text": "<PERSON>, English admiral and politician (b. 1542)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1542)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON>, Welsh bishop and translator (b. 1545)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)\" title=\"<PERSON> (Bible translator)\"><PERSON></a>, Welsh bishop and translator (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)\" title=\"<PERSON> (Bible translator)\"><PERSON></a>, Welsh bishop and translator (b. 1545)", "links": [{"title": "<PERSON> (Bible translator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_translator)"}]}, {"year": "1607", "text": "<PERSON><PERSON><PERSON>, Italian organist and composer (b. 1545)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sch<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian organist and composer (b. 1545)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1669", "text": "<PERSON> of France, Queen of England, Scotland and Ireland (b. 1609)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, Queen of England, Scotland and Ireland (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a>, Queen of England, Scotland and Ireland (b. 1609)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON><PERSON>, English activist (b. 1609)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English activist (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English activist (b. 1609)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1748", "text": "<PERSON><PERSON><PERSON> Espíritu <PERSON>, Filipino nun, founded the Religious of the Virgin Mary (b. 1663)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/Ignacia_del_Esp%C3%ADritu_Santo\" title=\"Ignacia del Espíritu Santo\"><PERSON><PERSON><PERSON> del Espíritu Santo</a>, Filipino nun, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a> (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Igna<PERSON>_del_Esp%C3%ADritu_Santo\" title=\"Ignacia del Espíritu Santo\"><PERSON><PERSON><PERSON> del Espíritu Santo</a>, Filipino nun, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary\" title=\"Religious of the Virgin Mary\">Religious of the Virgin Mary</a> (b. 1663)", "links": [{"title": "Ignacia del Espíritu Santo", "link": "https://wikipedia.org/wiki/Ignacia_del_Esp%C3%ADritu_Santo"}, {"title": "Religious of the Virgin Mary", "link": "https://wikipedia.org/wiki/Religious_of_the_Virgin_Mary"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON>, French mathematician and physicist (b. 1706)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet\" title=\"<PERSON><PERSON><PERSON> du Châtelet\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet\" title=\"<PERSON><PERSON><PERSON> du Châtelet\"><PERSON><PERSON><PERSON></a>, French mathematician and physicist (b. 1706)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89milie_du_Ch%C3%A2telet"}]}, {"year": "1759", "text": "<PERSON>, Croatian missionary and explorer (b. 1703)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C5%A1%C4%8Dak\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian missionary and explorer (b. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1%C4%8Dak\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian missionary and explorer (b. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Kon%C5%A1%C4%8Dak"}]}, {"year": "1797", "text": "<PERSON>, English philosopher, historian, and novelist (b. 1759)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, historian, and novelist (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, historian, and novelist (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>craft"}]}, {"year": "1842", "text": "<PERSON>, Irish-New Zealand soldier and politician, 1st Governor of New Zealand (b. 1792)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-New Zealand soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_New_Zealand\" class=\"mw-redirect\" title=\"Governor of New Zealand\">Governor of New Zealand</a> (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of New Zealand", "link": "https://wikipedia.org/wiki/Governor_of_New_Zealand"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, American wife of <PERSON>, 11th First Lady of the United States (b. 1790)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Letitia_<PERSON>_<PERSON>\" title=\"<PERSON>iti<PERSON>\">Letitia <PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>\" title=\"Letiti<PERSON>\">Letitia <PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (b. 1790)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1851", "text": "<PERSON>, American minister and educator (b. 1787)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and educator (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>allaudet\"><PERSON></a>, American minister and educator (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Austrian organist, composer, and conductor (b. 1788)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist, composer, and conductor (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist, composer, and conductor (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Prince of Monaco (b. 1818)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1818)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1891", "text": "<PERSON>, American physician and naturalist (b. 1804)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and naturalist (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and naturalist (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON> <PERSON> of Austria (b. 1837)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria\" title=\"Empress Elisabeth of Austria\">Empress <PERSON> of Austria</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria\" title=\"Empress <PERSON> of Austria\">Empress <PERSON> of Austria</a> (b. 1837)", "links": [{"title": "Empress <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Empress_Elisabeth_of_Austria"}]}, {"year": "1905", "text": "<PERSON>, American baseball player (b. 1861)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian physician and politician, 3rd Premier of Quebec (b. 1822)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Bouche<PERSON>\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Canadian physician and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Bo<PERSON>\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Canadian physician and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Indian philosopher and author (b. 1879 )", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>gh<PERSON> Jati<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and author (b. 1879 )", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>gh<PERSON> Jati<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian philosopher and author (b. 1879 )", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1919", "text": "<PERSON><PERSON> <PERSON><PERSON>, Australian journalist and publisher, founded the Archibald Prize (b. 1856)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian journalist and publisher, founded the <a href=\"https://wikipedia.org/wiki/Archibald_Prize\" title=\"Archibald Prize\">Archibald Prize</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian journalist and publisher, founded the <a href=\"https://wikipedia.org/wiki/Archibald_Prize\" title=\"Archibald Prize\">Archibald Prize</a> (b. 1856)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Archibald Prize", "link": "https://wikipedia.org/wiki/Archibald_Prize"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, English poet and activist (b. 1840)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English poet and activist (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf<PERSON>_<PERSON>_<PERSON>\" title=\"Wil<PERSON><PERSON>\">Wil<PERSON><PERSON></a>, English poet and activist (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Indian poet and playwright (b. 1887)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and playwright (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and playwright (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and academic (b. 1869)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Italian-American gangster (b. 1886)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American gangster (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American gangster (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian race car driver (b. 1892)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Italian race car driver (b. 1898)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian race car driver (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Polish race car driver (b. 1899)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish race car driver (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish race car driver (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German-English pianist, composer, and conductor (b. 1850)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English pianist, composer, and conductor (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English pianist, composer, and conductor (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American lawyer and politician, 40th Governor of Louisiana (b. 1893)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}]}, {"year": "1937", "text": "<PERSON>, Russian author and playwright (b. 1892)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Russian author and playwright (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Russian author and playwright (b. 1892)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1938", "text": "<PERSON>, English businessman, founded <PERSON><PERSON><PERSON> (b. 1852)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(showman)\" title=\"<PERSON> (showman)\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Crufts\" title=\"Crufts\"><PERSON>ru<PERSON></a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(showman)\" title=\"<PERSON> (showman)\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Crufts\" title=\"Crufts\"><PERSON><PERSON><PERSON></a> (b. 1852)", "links": [{"title": "<PERSON> (showman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(showman)"}, {"title": "Crufts", "link": "https://wikipedia.org/wiki/Crufts"}]}, {"year": "1939", "text": "<PERSON>, German general (b. 1888)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON> of Bulgaria (b. 1861)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a> (b. 1861)", "links": [{"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Lebanese engineer and architect, designed the Beirut City Hall (b. 1866)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aftimus\" title=\"<PERSON><PERSON><PERSON> Aftimus\"><PERSON><PERSON><PERSON></a>, Lebanese engineer and architect, designed the <a href=\"https://wikipedia.org/wiki/Beirut_City_Hall\" title=\"Beirut City Hall\">Beirut City Hall</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Aftimus\" title=\"<PERSON><PERSON><PERSON> Aftimus\"><PERSON><PERSON><PERSON></a>, Lebanese engineer and architect, designed the <a href=\"https://wikipedia.org/wiki/Beirut_City_Hall\" title=\"Beirut City Hall\">Beirut City Hall</a> (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Youssef_Aftimus"}, {"title": "Beirut City Hall", "link": "https://wikipedia.org/wiki/Beirut_City_Hall"}]}, {"year": "1954", "text": "<PERSON>, German tenor and actor (b. 1908)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, German tenor and actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, German tenor and actor (b. 1908)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1961", "text": "<PERSON>, American actor and singer (b. 1880)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_<PERSON>illo"}]}, {"year": "1961", "text": "<PERSON>, German race car driver (b. 1928)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American spiritual leader (b. 1880)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Father_Divine\" title=\"Father Divine\">Father Divine</a>, American spiritual leader (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Father_Divine\" title=\"Father Divine\">Father Divine</a>, American spiritual leader (b. 1880)", "links": [{"title": "Father Divine", "link": "https://wikipedia.org/wiki/Father_Divine"}]}, {"year": "1966", "text": "<PERSON>, German mathematician and statistician (b. 1891)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and statistician (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and statistician (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, German zoologist (b. 1894)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German zoologist (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian-American actress and singer (b. 1932)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actress and singer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American actress and singer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Angeli"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American author and playwright (b. 1884)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Cornelia_Meigs\" title=\"Cornelia Meigs\"><PERSON><PERSON><PERSON></a>, American author and playwright (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Meigs\" title=\"Cornelia Meigs\"><PERSON><PERSON><PERSON></a>, American author and playwright (b. 1884)", "links": [{"title": "Cornelia <PERSON>", "link": "https://wikipedia.org/wiki/Cornelia_Meigs"}]}, {"year": "1975", "text": "<PERSON>, Hungarian-Austrian conductor and educator (b. 1899)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian conductor and educator (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian conductor and educator (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (b. 1892)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1976", "text": "<PERSON>, American screenwriter and novelist (b. 1905)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Trumbo\" title=\"Dalton Trumbo\"><PERSON></a>, American screenwriter and novelist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Trumbo\" title=\"Dalton Trumbo\"><PERSON></a>, American screenwriter and novelist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dalton_Trumbo"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Angolan politician, 1st President of Angola (b. 1922)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Agostinho_Neto\" title=\"Agostin<PERSON> Neto\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agos<PERSON><PERSON>_<PERSON>o\" title=\"Agostin<PERSON> Neto\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a> (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agostinho_Neto"}, {"title": "President of Angola", "link": "https://wikipedia.org/wiki/President_of_Angola"}]}, {"year": "1983", "text": "<PERSON>, Swiss-American physicist and academic, Nobel Prize laureate (b. 1905)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English author (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lofts\"><PERSON><PERSON></a>, English author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lofts\"><PERSON><PERSON></a>, English author (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American heaviest man (b. 1941)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/List_of_the_heaviest_people\" class=\"mw-redirect\" title=\"List of the heaviest people\">heaviest</a> man (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/List_of_the_heaviest_people\" class=\"mw-redirect\" title=\"List of the heaviest people\">heaviest</a> man (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of the heaviest people", "link": "https://wikipedia.org/wiki/List_of_the_heaviest_people"}]}, {"year": "1983", "text": "<PERSON><PERSON> <PERSON><PERSON>, South African lawyer and politician, 4th State President of South Africa (b. 1915)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, South African lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/State_President_of_South_Africa\" title=\"State President of South Africa\">State President of South Africa</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "State President of South Africa", "link": "https://wikipedia.org/wiki/State_President_of_South_Africa"}]}, {"year": "1985", "text": "<PERSON>, Estonian astronomer and astrophysicist (b. 1893)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96pik\" title=\"<PERSON>\"><PERSON></a>, Estonian astronomer and astrophysicist (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96pik\" title=\"<PERSON>\"><PERSON></a>, Estonian astronomer and astrophysicist (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_%C3%96pik"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Scottish footballer and manager (b. 1922)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and manager (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Estonian chess player (b. 1937)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5tov\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5tov\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_R%C3%B5tov"}]}, {"year": "1988", "text": "<PERSON>, American psychotherapist and author (b. 1916)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Virginia_Satir\" title=\"Virginia Satir\"><PERSON></a>, American psychotherapist and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Satir\" title=\"Virginia Satir\"><PERSON></a>, American psychotherapist and author (b. 1916)", "links": [{"title": "Virginia Satir", "link": "https://wikipedia.org/wiki/Virginia_Satir"}]}, {"year": "1991", "text": "<PERSON>, Australian tennis player (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Australian tennis player (b. 1908)", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1996", "text": "<PERSON>, American actress (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}]}, {"year": "1996", "text": "<PERSON>, Austrian scientist and inventor (b. 1896)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_List\" title=\"Hans List\"><PERSON></a>, Austrian scientist and inventor (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans_List\" title=\"Hans List\"><PERSON></a>, Austrian scientist and inventor (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_List"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani journalist and author (b. 1921)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Indian-Pakistani journalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Indian-Pakistani journalist and author (b. 1921)", "links": [{"title": "<PERSON>ai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American soldier, lawyer, and politician, 5th United States Secretary of Transportation (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Transportation\" title=\"United States Secretary of Transportation\">United States Secretary of Transportation</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Transportation", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Transportation"}]}, {"year": "2005", "text": "<PERSON>, Austrian mathematician and cosmologist (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and cosmologist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician and cosmologist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American singer and guitarist (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Gatemouth%22_<PERSON>\" title='<PERSON> \"<PERSON>\" Brown'><PERSON> \"<PERSON>\" <PERSON></a>, American singer and guitarist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Gatemouth%22_<PERSON>\" title='<PERSON> \"Gatemouth\" Brown'><PERSON> \"Gate<PERSON>\" <PERSON></a>, American singer and guitarist (b. 1924)", "links": [{"title": "<PERSON> \"Gate<PERSON>\" Brown", "link": "https://wikipedia.org/wiki/<PERSON>_%22Gatemouth%22_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American golfer (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tongan king (b. 1918)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/T%C4%81ufa%CA%BB%C4%81hau_Tupou_IV\" title=\"Tāufaʻāhau Tupou IV\">T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tupou IV</a>, Tongan king (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C4%81ufa%CA%BB%C4%81hau_Tupou_IV\" title=\"Tāufaʻāhau Tupou IV\">T<PERSON>uf<PERSON><PERSON><PERSON><PERSON><PERSON>pou IV</a>, Tongan king (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C4%81ufa%CA%BB%C4%81hau_Tupou_IV"}]}, {"year": "2007", "text": "<PERSON>, English businesswoman, founded The Body Shop (b. 1942)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, founded <a href=\"https://wikipedia.org/wiki/The_Body_Shop\" title=\"The Body Shop\">The Body Shop</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman, founded <a href=\"https://wikipedia.org/wiki/The_Body_Shop\" title=\"The Body Shop\">The Body Shop</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Body Shop", "link": "https://wikipedia.org/wiki/The_Body_Shop"}]}, {"year": "2007", "text": "<PERSON>, Irish politician (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American actress (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yman"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Canadian lawyer and politician (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9ral<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English conductor (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actor (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Chilean journalist (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean journalist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean journalist (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American captain, lawyer, and politician (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor and stuntman (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and stuntman (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English director, producer, cinematographer, and screenwriter (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, cinematographer, and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, cinematographer, and screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English actor and playwright (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright (b. 1922)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2013", "text": "<PERSON>, American journalist and actor (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Syrian politician, Syrian Minister of Foreign Affairs (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_Expatriates_(Syria)\" title=\"Ministry of Foreign Affairs and Expatriates (Syria)\">Syrian Minister of Foreign Affairs</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_Expatriates_(Syria)\" title=\"Ministry of Foreign Affairs and Expatriates (Syria)\">Syrian Minister of Foreign Affairs</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs and Expatriates (Syria)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_Expatriates_(Syria)"}]}, {"year": "2013", "text": "<PERSON>, Czech boxer (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Bmec\" title=\"<PERSON>\"><PERSON></a>, Czech boxer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Bmec\" title=\"<PERSON>\"><PERSON></a>, Czech boxer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_N%C4%9Bmec"}]}, {"year": "2013", "text": "<PERSON>, American accountant and politician (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American accountant and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American accountant and politician (b. 1939)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "2013", "text": "<PERSON>, Canadian general (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Canadian general (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" title=\"<PERSON> (general)\"><PERSON></a>, Canadian general (b. 1933)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "2014", "text": "<PERSON>, Spanish banker and businessman (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish banker and businessman (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish banker and businessman (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilio_Bo<PERSON>%C3%ADn"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American mathematician and academic (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1926)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2014", "text": "<PERSON>, American religious leader and academic (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and academic (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and academic (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American psychologist and academic (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Swiss typeface designer (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss typeface designer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss typeface designer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Lebanese general (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese general (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese general (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, British actress (b. 1938)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ig<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ig<PERSON>\" title=\"<PERSON>ig<PERSON>\"><PERSON></a>, British actress (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>igg"}]}, {"year": "2023", "text": "<PERSON>, British embryologist (b. 1944)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British embryologist (b. <a href=\"https://wikipedia.org/wiki/1944\" title=\"1944\">1944</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British embryologist (b. <a href=\"https://wikipedia.org/wiki/1944\" title=\"1944\">1944</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "1944", "link": "https://wikipedia.org/wiki/1944"}]}, {"year": "2024", "text": "<PERSON>, American soul/funk singer-songwriter, musician, and producer (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Beverly\" title=\"Frankie Beverly\"><PERSON></a>, American soul/funk singer-songwriter, musician, and producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frankie_Beverly\" title=\"Frankie Beverly\"><PERSON></a>, American soul/funk singer-songwriter, musician, and producer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer and politician, 6th United States Ambassador to China (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_China\" class=\"mw-redirect\" title=\"United States Ambassador to China\">United States Ambassador to China</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to China", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_China"}]}]}}