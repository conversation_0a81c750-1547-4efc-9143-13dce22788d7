{"date": "April 6", "url": "https://wikipedia.org/wiki/April_6", "data": {"Events": [{"year": "46 BC", "text": "<PERSON> defeats <PERSON><PERSON><PERSON><PERSON> and <PERSON> (<PERSON><PERSON> the Younger) at the Battle of Thapsus.", "html": "46 BC - 46 BC - <a href=\"https://wikipedia.org/wiki/Julius_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> defeats <a href=\"https://wikipedia.org/wiki/Q<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Pius_<PERSON>ipio_Nasica\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>aecilius <PERSON>ellus Pius Scipio Nasica\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON> (<PERSON><PERSON> the Younger)</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Thapsus\" title=\"Battle of Thapsus\">Battle of Thapsus</a>.", "no_year_html": "46 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_Caesar\" title=\"Julius Caesar\"><PERSON> Caesar</a> defeats <a href=\"https://wikipedia.org/wiki/Quin<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Pius_Scipio_Nasica\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>aecilius <PERSON>ellus Pius Scipio Nasica\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON> (<PERSON><PERSON> the Younger)</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Thapsus\" title=\"Battle of Thapsus\">Battle of Thapsus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_Nasica"}, {"title": "<PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Younger"}, {"title": "Battle of Thapsus", "link": "https://wikipedia.org/wiki/Battle_of_Thapsus"}]}, {"year": "402", "text": "<PERSON><PERSON><PERSON> defeats the Visigoths under <PERSON><PERSON><PERSON> in the Battle of Pollentia.", "html": "402 - <a href=\"https://wikipedia.org/wiki/St<PERSON>cho\" title=\"St<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> under <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pollentia\" title=\"Battle of Pollentia\">Battle of Pollentia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St<PERSON>cho\" title=\"St<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> under <a href=\"https://wikipedia.org/wiki/Alaric_I\" title=\"Alaric I\"><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pollentia\" title=\"Battle of Pollentia\">Battle of Pollentia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "Alaric I", "link": "https://wikipedia.org/wiki/Alaric_I"}, {"title": "Battle of Pollentia", "link": "https://wikipedia.org/wiki/Battle_of_Pollentia"}]}, {"year": "1320", "text": "The Scots reaffirm their independence by signing the Declaration of Arbroath.", "html": "1320 - The Scots reaffirm their independence by signing the <a href=\"https://wikipedia.org/wiki/Declaration_of_Arbroath\" title=\"Declaration of Arbroath\">Declaration of Arbroath</a>.", "no_year_html": "The Scots reaffirm their independence by signing the <a href=\"https://wikipedia.org/wiki/Declaration_of_Arbroath\" title=\"Declaration of Arbroath\">Declaration of Arbroath</a>.", "links": [{"title": "Declaration of Arbroath", "link": "https://wikipedia.org/wiki/Declaration_of_Arbroath"}]}, {"year": "1453", "text": "<PERSON><PERSON><PERSON> <PERSON> begins his siege of Constantinople. The city falls on May 29 and is renamed Istanbul.", "html": "1453 - <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\"><PERSON><PERSON><PERSON> II</a> begins his <a href=\"https://wikipedia.org/wiki/Fall_of_Constantinople\" title=\"Fall of Constantinople\">siege of Constantinople</a>. The city falls on <a href=\"https://wikipedia.org/wiki/May_29\" title=\"May 29\">May 29</a> and is renamed <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mehm<PERSON>_II\" title=\"Mehmed II\"><PERSON><PERSON><PERSON> II</a> begins his <a href=\"https://wikipedia.org/wiki/Fall_of_Constantinople\" title=\"Fall of Constantinople\">siege of Constantinople</a>. The city falls on <a href=\"https://wikipedia.org/wiki/May_29\" title=\"May 29\">May 29</a> and is renamed <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}, {"title": "Fall of Constantinople", "link": "https://wikipedia.org/wiki/Fall_of_Constantinople"}, {"title": "May 29", "link": "https://wikipedia.org/wiki/May_29"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}]}, {"year": "1580", "text": "One of the largest earthquakes recorded in the history of England, Flanders, or Northern France, takes place.", "html": "1580 - One of the <a href=\"https://wikipedia.org/wiki/1580_Dover_Straits_earthquake\" title=\"1580 Dover Straits earthquake\">largest earthquakes</a> recorded in the history of England, Flanders, or Northern France, takes place.", "no_year_html": "One of the <a href=\"https://wikipedia.org/wiki/1580_Dover_Straits_earthquake\" title=\"1580 Dover Straits earthquake\">largest earthquakes</a> recorded in the history of England, Flanders, or Northern France, takes place.", "links": [{"title": "1580 Dover Straits earthquake", "link": "https://wikipedia.org/wiki/1580_Dover_Straits_earthquake"}]}, {"year": "1652", "text": "At the Cape of Good Hope, Dutch sailor <PERSON> establishes a resupply camp that eventually becomes Cape Town.", "html": "1652 - At the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>, Dutch sailor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes a resupply camp that eventually becomes <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a>, Dutch sailor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establishes a resupply camp that eventually becomes <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "links": [{"title": "Cape of Good Hope", "link": "https://wikipedia.org/wiki/Cape_of_Good_Hope"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1712", "text": "The New York Slave Revolt of 1712 begins near Broadway.", "html": "1712 - The <a href=\"https://wikipedia.org/wiki/New_York_Slave_Revolt_of_1712\" title=\"New York Slave Revolt of 1712\">New York Slave Revolt of 1712</a> begins near <a href=\"https://wikipedia.org/wiki/Broadway_(Manhattan)\" title=\"Broadway (Manhattan)\">Broadway</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Slave_Revolt_of_1712\" title=\"New York Slave Revolt of 1712\">New York Slave Revolt of 1712</a> begins near <a href=\"https://wikipedia.org/wiki/Broadway_(Manhattan)\" title=\"Broadway (Manhattan)\">Broadway</a>.", "links": [{"title": "New York Slave Revolt of 1712", "link": "https://wikipedia.org/wiki/New_York_Slave_Revolt_of_1712"}, {"title": "Broadway (Manhattan)", "link": "https://wikipedia.org/wiki/Broadway_(Manhattan)"}]}, {"year": "1776", "text": "American Revolutionary War: Ships of the Continental Navy fail in their attempt to capture a Royal Navy dispatch boat.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Ships of the <a href=\"https://wikipedia.org/wiki/Continental_Navy\" title=\"Continental Navy\">Continental Navy</a> fail in their <a href=\"https://wikipedia.org/wiki/Battle_of_Block_Island\" title=\"Battle of Block Island\">attempt to capture</a> a <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Dispatch_boat\" title=\"Dispatch boat\">dispatch boat</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Ships of the <a href=\"https://wikipedia.org/wiki/Continental_Navy\" title=\"Continental Navy\">Continental Navy</a> fail in their <a href=\"https://wikipedia.org/wiki/Battle_of_Block_Island\" title=\"Battle of Block Island\">attempt to capture</a> a <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Dispatch_boat\" title=\"Dispatch boat\">dispatch boat</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Continental Navy", "link": "https://wikipedia.org/wiki/Continental_Navy"}, {"title": "Battle of Block Island", "link": "https://wikipedia.org/wiki/Battle_of_Block_Island"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Dispatch boat", "link": "https://wikipedia.org/wiki/Dispatch_boat"}]}, {"year": "1782", "text": "King <PERSON><PERSON> (Rama <PERSON>) of Siam (modern day Thailand) establishes the Chakri dynasty.", "html": "1782 - King <a href=\"https://wikipedia.org/wiki/Buddha_Yo<PERSON><PERSON>_<PERSON>laloke\" class=\"mw-redirect\" title=\"Buddha Yod<PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON></a> (<PERSON>) of Siam (modern day <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) establishes the Chakri dynasty.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Buddha_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Buddha Yo<PERSON><PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON></a> (<PERSON>) of Siam (modern day <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) establishes the Chakri dynasty.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Chulaloke"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}]}, {"year": "1793", "text": "During the French Revolution, the Committee of Public Safety becomes the executive organ of the republic.", "html": "1793 - During the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, the <a href=\"https://wikipedia.org/wiki/Committee_of_Public_Safety\" title=\"Committee of Public Safety\">Committee of Public Safety</a> becomes the executive organ of the republic.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>, the <a href=\"https://wikipedia.org/wiki/Committee_of_Public_Safety\" title=\"Committee of Public Safety\">Committee of Public Safety</a> becomes the executive organ of the republic.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "Committee of Public Safety", "link": "https://wikipedia.org/wiki/Committee_of_Public_Safety"}]}, {"year": "1800", "text": "The Treaty of Constantinople establishes the Septinsular Republic, the first autonomous Greek state since the Fall of the Byzantine Empire. (Under the Old Style calendar then still in use in the Ottoman Empire, the treaty was signed on 21 March.)", "html": "1800 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Constantinople_(1800)\" title=\"Treaty of Constantinople (1800)\">Treaty of Constantinople</a> establishes the <a href=\"https://wikipedia.org/wiki/Septinsular_Republic\" title=\"Septinsular Republic\">Septinsular Republic</a>, the first autonomous Greek state since the <a href=\"https://wikipedia.org/wiki/Fall_of_the_Byzantine_Empire\" class=\"mw-redirect\" title=\"Fall of the Byzantine Empire\">Fall of the Byzantine Empire</a>. (Under the <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">Old Style</a> calendar then still in use in the Ottoman Empire, the treaty was signed on 21 March.)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Constantinople_(1800)\" title=\"Treaty of Constantinople (1800)\">Treaty of Constantinople</a> establishes the <a href=\"https://wikipedia.org/wiki/Septinsular_Republic\" title=\"Septinsular Republic\">Septinsular Republic</a>, the first autonomous Greek state since the <a href=\"https://wikipedia.org/wiki/Fall_of_the_Byzantine_Empire\" class=\"mw-redirect\" title=\"Fall of the Byzantine Empire\">Fall of the Byzantine Empire</a>. (Under the <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">Old Style</a> calendar then still in use in the Ottoman Empire, the treaty was signed on 21 March.)", "links": [{"title": "Treaty of Constantinople (1800)", "link": "https://wikipedia.org/wiki/Treaty_of_Constantinople_(1800)"}, {"title": "Septinsular Republic", "link": "https://wikipedia.org/wiki/Septinsular_Republic"}, {"title": "Fall of the Byzantine Empire", "link": "https://wikipedia.org/wiki/Fall_of_the_Byzantine_Empire"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1808", "text": "<PERSON> incorporates the American Fur Company, that would eventually make him America's first millionaire.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporates the <a href=\"https://wikipedia.org/wiki/American_Fur_Company\" title=\"American Fur Company\">American Fur Company</a>, that would eventually make him America's first millionaire.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporates the <a href=\"https://wikipedia.org/wiki/American_Fur_Company\" title=\"American Fur Company\">American Fur Company</a>, that would eventually make him America's first millionaire.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Fur Company", "link": "https://wikipedia.org/wiki/American_Fur_Company"}]}, {"year": "1812", "text": "British forces under the command of the <PERSON> of Wellington assault the fortress of Badajoz. This would be the turning point in the Peninsular War against Napoleon-led France.", "html": "1812 - British forces under the command of the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\">Duke <PERSON> Wellington</a> assault the fortress of <a href=\"https://wikipedia.org/wiki/Siege_of_Badajoz_(1812)\" title=\"Siege of Badajoz (1812)\">Badajoz</a>. This would be the turning point in the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a> against <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>-led France.", "no_year_html": "British forces under the command of the <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\">Duke of Wellington</a> assault the fortress of <a href=\"https://wikipedia.org/wiki/Siege_of_Badajoz_(1812)\" title=\"Siege of Badajoz (1812)\">Badajoz</a>. This would be the turning point in the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a> against <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>-led France.", "links": [{"title": "<PERSON>, 1st Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington"}, {"title": "Siege of Badajoz (1812)", "link": "https://wikipedia.org/wiki/Siege_of_Badajoz_(1812)"}, {"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1814", "text": "Nominal beginning of the Bourbon Restoration; anniversary date that <PERSON> abdicates and is exiled to Elba.", "html": "1814 - Nominal beginning of the <a href=\"https://wikipedia.org/wiki/Bourbon_Restoration_in_France\" title=\"Bourbon Restoration in France\">Bourbon Restoration</a>; anniversary date that <PERSON> abdicates and is exiled to <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>.", "no_year_html": "Nominal beginning of the <a href=\"https://wikipedia.org/wiki/Bourbon_Restoration_in_France\" title=\"Bourbon Restoration in France\">Bourbon Restoration</a>; anniversary date that <PERSON> abdicates and is exiled to <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a>.", "links": [{"title": "Bourbon Restoration in France", "link": "https://wikipedia.org/wiki/Bourbon_Restoration_in_France"}, {"title": "Elba", "link": "https://wikipedia.org/wiki/Elba"}]}, {"year": "1830", "text": "Church of Christ, the original church of the Latter Day Saint movement, is organized by <PERSON> and others at either Fayette or Manchester, New York.", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Church_of_Christ_(Latter_Day_Saints)\" title=\"Church of Christ (Latter Day Saints)\">Church of Christ</a>, the original church of the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a>, is <a href=\"https://wikipedia.org/wiki/April_6_(LDS_Church)\" title=\"April 6 (LDS Church)\">organized</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and others at either <a href=\"https://wikipedia.org/wiki/<PERSON>,_New_York\" title=\"Fayette, New York\"><PERSON><PERSON></a> or <a href=\"https://wikipedia.org/wiki/Manchester,_New_York\" title=\"Manchester, New York\">Manchester, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Church_of_Christ_(Latter_Day_Saints)\" title=\"Church of Christ (Latter Day Saints)\">Church of Christ</a>, the original church of the <a href=\"https://wikipedia.org/wiki/Latter_Day_Saint_movement\" title=\"Latter Day Saint movement\">Latter Day Saint movement</a>, is <a href=\"https://wikipedia.org/wiki/April_6_(LDS_Church)\" title=\"April 6 (LDS Church)\">organized</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and others at either <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_New_York\" title=\"Fayette, New York\"><PERSON><PERSON></a> or <a href=\"https://wikipedia.org/wiki/Manchester,_New_York\" title=\"Manchester, New York\">Manchester, New York</a>.", "links": [{"title": "Church of Christ (Latter Day Saints)", "link": "https://wikipedia.org/wiki/Church_of_Christ_(Latter_Day_Saints)"}, {"title": "Latter Day Saint movement", "link": "https://wikipedia.org/wiki/Latter_Day_Saint_movement"}, {"title": "April 6 (LDS Church)", "link": "https://wikipedia.org/wiki/April_6_(LDS_Church)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Fayette, New York", "link": "https://wikipedia.org/wiki/Fayette,_New_York"}, {"title": "Manchester, New York", "link": "https://wikipedia.org/wiki/Manchester,_New_York"}]}, {"year": "1841", "text": "U.S. President <PERSON> is sworn in, two days after having become president upon <PERSON>'s death.", "html": "1841 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in, two days after having become president upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in, two days after having become president upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "The Reorganized Church of Jesus Christ of Latter Day Saints, later renamed Community of Christ, is organized by <PERSON> and others at Amboy, Illinois.", "html": "1860 - The <a href=\"https://wikipedia.org/wiki/Reorganized_Church_of_Jesus_Christ_of_Latter_Day_Saints\" class=\"mw-redirect\" title=\"Reorganized Church of Jesus Christ of Latter Day Saints\">Reorganized Church of Jesus Christ of Latter Day Saints</a>, later renamed <a href=\"https://wikipedia.org/wiki/Community_of_Christ\" title=\"Community of Christ\">Community of Christ</a>, is organized by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a> and others at <a href=\"https://wikipedia.org/wiki/Amboy,_Illinois\" title=\"Amboy, Illinois\">Amboy, Illinois</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Reorganized_Church_of_Jesus_Christ_of_Latter_Day_Saints\" class=\"mw-redirect\" title=\"Reorganized Church of Jesus Christ of Latter Day Saints\">Reorganized Church of Jesus Christ of Latter Day Saints</a>, later renamed <a href=\"https://wikipedia.org/wiki/Community_of_Christ\" title=\"Community of Christ\">Community of Christ</a>, is organized by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a> and others at <a href=\"https://wikipedia.org/wiki/Amboy,_Illinois\" title=\"Amboy, Illinois\">Amboy, Illinois</a>.", "links": [{"title": "Reorganized Church of Jesus Christ of Latter Day Saints", "link": "https://wikipedia.org/wiki/Reorganized_Church_of_Jesus_Christ_of_Latter_Day_Saints"}, {"title": "Community of Christ", "link": "https://wikipedia.org/wiki/Community_of_Christ"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Amboy, Illinois", "link": "https://wikipedia.org/wiki/Amboy,_Illinois"}]}, {"year": "1862", "text": "American Civil War: The Battle of Shiloh begins: In Tennessee, forces under Union General <PERSON> meet Confederate troops led by General <PERSON>.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Shiloh\" title=\"Battle of Shiloh\">Battle of Shiloh</a> begins: In <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ulysses <PERSON> Grant\">Ulysses <PERSON></a> meet <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Shiloh\" title=\"Battle of Shiloh\">Battle of Shiloh</a> begins: In <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a>, forces under <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Grant\" title=\"Ulysses <PERSON> Grant\"><PERSON></a> meet <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Shiloh", "link": "https://wikipedia.org/wiki/Battle_of_Shiloh"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "American Civil War: The Battle of Sailor's Creek: Confederate General <PERSON>'s Army of Northern Virginia fights and loses its last major battle while in retreat from Richmond, Virginia, during the Appomattox Campaign.", "html": "1865 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sailor%27s_Creek\" title=\"Battle of Sailor's Creek\">Battle of Sailor's Creek</a>: Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> fights and loses its last major battle while in retreat from <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, during the <a href=\"https://wikipedia.org/wiki/Appomattox_Campaign\" class=\"mw-redirect\" title=\"Appomattox Campaign\">Appomattox Campaign</a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Sailor%27s_Creek\" title=\"Battle of Sailor's Creek\">Battle of Sailor's Creek</a>: Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> fights and loses its last major battle while in retreat from <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>, during the <a href=\"https://wikipedia.org/wiki/Appomattox_Campaign\" class=\"mw-redirect\" title=\"Appomattox Campaign\">Appomattox Campaign</a>.", "links": [{"title": "Battle of Sailor's Creek", "link": "https://wikipedia.org/wiki/Battle_of_Sailor%27s_Creek"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}, {"title": "Appomattox Campaign", "link": "https://wikipedia.org/wiki/Appomattox_Campaign"}]}, {"year": "1866", "text": "The Grand Army of the Republic, an American patriotic organization composed of Union veterans of the American Civil War, is founded. It lasts until 1956.", "html": "1866 - The <a href=\"https://wikipedia.org/wiki/Grand_Army_of_the_Republic\" title=\"Grand Army of the Republic\">Grand Army of the Republic</a>, an American patriotic organization composed of Union veterans of the American Civil War, is founded. It lasts until 1956.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Army_of_the_Republic\" title=\"Grand Army of the Republic\">Grand Army of the Republic</a>, an American patriotic organization composed of Union veterans of the American Civil War, is founded. It lasts until 1956.", "links": [{"title": "Grand Army of the Republic", "link": "https://wikipedia.org/wiki/Grand_Army_of_the_Republic"}]}, {"year": "1896", "text": "In Athens, the opening of the first modern Olympic Games is celebrated, 1,500 years after the original games are banned by Roman emperor <PERSON><PERSON><PERSON>.", "html": "1896 - In <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, the opening of the <a href=\"https://wikipedia.org/wiki/1896_Summer_Olympics\" title=\"1896 Summer Olympics\">first modern Olympic Games</a> is celebrated, 1,500 years after the original games are banned by <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Theodosius_I\" title=\"Theodosius I\"><PERSON><PERSON><PERSON> I</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, the opening of the <a href=\"https://wikipedia.org/wiki/1896_Summer_Olympics\" title=\"1896 Summer Olympics\">first modern Olympic Games</a> is celebrated, 1,500 years after the original games are banned by <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Theodosius_I\" title=\"Theodosius I\">Theodosius I</a>.", "links": [{"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}, {"title": "1896 Summer Olympics", "link": "https://wikipedia.org/wiki/1896_Summer_Olympics"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "1909", "text": "<PERSON> and <PERSON> become the first people to reach the North Pole; <PERSON><PERSON><PERSON>'s claim has been disputed because of failings in his navigational ability.", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first people to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>; <PERSON><PERSON><PERSON>'s claim has been disputed because of failings in his navigational ability.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> become the first people to reach the <a href=\"https://wikipedia.org/wiki/North_Pole\" title=\"North Pole\">North Pole</a>; <PERSON><PERSON><PERSON>'s claim has been disputed because of failings in his navigational ability.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "North Pole", "link": "https://wikipedia.org/wiki/North_Pole"}]}, {"year": "1911", "text": "During the Battle of Deçiq, <PERSON><PERSON>ë G<PERSON>, leader of the Malësori Albanians, raises the Albanian flag in the town of Tuzi, Montenegro, for the first time after <PERSON> (Skanderbeg).", "html": "1911 - During the <a href=\"https://wikipedia.org/wiki/Battle_of_De%C3%A7iq\" title=\"Battle of Deçiq\">Battle of Deçiq</a>, <a href=\"https://wikipedia.org/wiki/Ded_<PERSON><PERSON>_<PERSON>\" title=\"Ded <PERSON>\">De<PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Mal%C3%ABsori\" class=\"mw-redirect\" title=\"Malësori\">Malësori</a> <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albanians</a>, raises the Albanian flag in the town of <a href=\"https://wikipedia.org/wiki/Tuzi\" title=\"Tu<PERSON>\">Tuzi</a>, Montenegro, for the first time after <PERSON> (<a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a>).", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Battle_of_De%C3%A7iq\" title=\"Battle of Deçiq\">Battle of Deçiq</a>, <a href=\"https://wikipedia.org/wiki/Ded_<PERSON><PERSON>_<PERSON>\" title=\"Ded <PERSON>\">Ded<PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Mal%C3%ABsori\" class=\"mw-redirect\" title=\"Malësori\">Malësori</a> <a href=\"https://wikipedia.org/wiki/Albania\" title=\"Albania\">Albanians</a>, raises the Albanian flag in the town of <a href=\"https://wikipedia.org/wiki/Tuzi\" title=\"<PERSON><PERSON>\">Tuzi</a>, Montenegro, for the first time after <PERSON> (<a href=\"https://wikipedia.org/wiki/Skanderbeg\" title=\"Skanderbeg\">Skanderbeg</a>).", "links": [{"title": "Battle of Deçiq", "link": "https://wikipedia.org/wiki/Battle_of_De%C3%A7iq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ded_<PERSON><PERSON>_<PERSON>"}, {"title": "Malësori", "link": "https://wikipedia.org/wiki/Mal%C3%ABsori"}, {"title": "Albania", "link": "https://wikipedia.org/wiki/Albania"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Skanderbeg", "link": "https://wikipedia.org/wiki/Skanderbeg"}]}, {"year": "1917", "text": "World War I: The United States declares war on Germany.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)\" title=\"United States declaration of war on Germany (1917)\">United States declares war on Germany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)\" title=\"United States declaration of war on Germany (1917)\">United States declares war on Germany</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "United States declaration of war on Germany (1917)", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)"}]}, {"year": "1918", "text": "Finnish Civil War: The battle of Tampere ends.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tampere\" title=\"Battle of Tampere\">battle of Tampere</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tampere\" title=\"Battle of Tampere\">battle of Tampere</a> ends.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Battle of Tampere", "link": "https://wikipedia.org/wiki/Battle_of_Tampere"}]}, {"year": "1926", "text": "Varney Airlines makes its first commercial flight (Varney is the root company of United Airlines).", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Varney_Airlines\" class=\"mw-redirect\" title=\"Varney Airlines\">Varney Airlines</a> makes its first commercial flight (Varney is the root company of <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varney_Airlines\" class=\"mw-redirect\" title=\"Varney Airlines\">Varney Airlines</a> makes its first commercial flight (Varney is the root company of <a href=\"https://wikipedia.org/wiki/United_Airlines\" title=\"United Airlines\">United Airlines</a>).", "links": [{"title": "Varney Airlines", "link": "https://wikipedia.org/wiki/Varney_Airlines"}, {"title": "United Airlines", "link": "https://wikipedia.org/wiki/United_Airlines"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Governor of Louisiana, is impeached by the Louisiana House of Representatives.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a>, is <a href=\"https://wikipedia.org/wiki/Impeachment_in_the_United_States\" title=\"Impeachment in the United States\">impeached</a> by the <a href=\"https://wikipedia.org/wiki/Louisiana_House_of_Representatives\" title=\"Louisiana House of Representatives\">Louisiana House of Representatives</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Governor_of_Louisiana\" title=\"Governor of Louisiana\">Governor of Louisiana</a>, is <a href=\"https://wikipedia.org/wiki/Impeachment_in_the_United_States\" title=\"Impeachment in the United States\">impeached</a> by the <a href=\"https://wikipedia.org/wiki/Louisiana_House_of_Representatives\" title=\"Louisiana House of Representatives\">Louisiana House of Representatives</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Louisiana", "link": "https://wikipedia.org/wiki/Governor_of_Louisiana"}, {"title": "Impeachment in the United States", "link": "https://wikipedia.org/wiki/Impeachment_in_the_United_States"}, {"title": "Louisiana House of Representatives", "link": "https://wikipedia.org/wiki/Louisiana_House_of_Representatives"}]}, {"year": "1930", "text": "At the end of the Salt March, <PERSON> raises a lump of mud and salt and declares, \"With this, I am shaking the foundations of the British Empire.\"", "html": "1930 - At the end of the <a href=\"https://wikipedia.org/wiki/Salt_March\" title=\"Salt March\">Salt March</a>, <PERSON> raises a lump of mud and salt and declares, \"With this, I am shaking the foundations of the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.\"", "no_year_html": "At the end of the <a href=\"https://wikipedia.org/wiki/Salt_March\" title=\"Salt March\">Salt March</a>, <PERSON> raises a lump of mud and salt and declares, \"With this, I am shaking the foundations of the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.\"", "links": [{"title": "Salt March", "link": "https://wikipedia.org/wiki/Salt_March"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1936", "text": "Tupelo-Gainesville tornado outbreak: Another tornado from the same storm system as the Tupelo tornado hits Gainesville, Georgia, killing 203.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/1936_Tupelo%E2%80%93Gainesville_tornado_outbreak\" title=\"1936 Tupelo-Gainesville tornado outbreak\">Tupelo-Gainesville tornado outbreak</a>: Another tornado from the same storm system as the Tupelo tornado hits <a href=\"https://wikipedia.org/wiki/Gainesville,_Georgia\" title=\"Gainesville, Georgia\">Gainesville, Georgia</a>, killing 203.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1936_Tupelo%E2%80%93Gainesville_tornado_outbreak\" title=\"1936 Tupelo-Gainesville tornado outbreak\">Tupelo-Gainesville tornado outbreak</a>: Another tornado from the same storm system as the Tupelo tornado hits <a href=\"https://wikipedia.org/wiki/Gainesville,_Georgia\" title=\"Gainesville, Georgia\">Gainesville, Georgia</a>, killing 203.", "links": [{"title": "1936 Tupelo-Gainesville tornado outbreak", "link": "https://wikipedia.org/wiki/1936_Tupelo%E2%80%93Gainesville_tornado_outbreak"}, {"title": "Gainesville, Georgia", "link": "https://wikipedia.org/wiki/Gainesville,_Georgia"}]}, {"year": "1941", "text": "World War II: Nazi Germany launches Operation 25 (the invasion of Kingdom of Yugoslavia) and Operation Marita (the invasion of Greece).", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> launches <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">Operation 25</a> (the invasion of Kingdom of Yugoslavia) and <a href=\"https://wikipedia.org/wiki/Operation_Marita\" class=\"mw-redirect\" title=\"Operation Marita\">Operation Marita</a> (the invasion of Greece).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> launches <a href=\"https://wikipedia.org/wiki/Invasion_of_Yugoslavia\" title=\"Invasion of Yugoslavia\">Operation 25</a> (the invasion of Kingdom of Yugoslavia) and <a href=\"https://wikipedia.org/wiki/Operation_Marita\" class=\"mw-redirect\" title=\"Operation Marita\">Operation Marita</a> (the invasion of Greece).", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Invasion of Yugoslavia", "link": "https://wikipedia.org/wiki/Invasion_of_Yugoslavia"}, {"title": "Operation Marita", "link": "https://wikipedia.org/wiki/Operation_Marita"}]}, {"year": "1945", "text": "World War II: Sarajevo is liberated from German and Croatian forces by the Yugoslav Partisans.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a> is liberated from <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> and <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Croatian</a> forces by the <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a> is liberated from <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> and <a href=\"https://wikipedia.org/wiki/Independent_State_of_Croatia\" title=\"Independent State of Croatia\">Croatian</a> forces by the <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a>.", "links": [{"title": "Sarajevo", "link": "https://wikipedia.org/wiki/Sarajevo"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Independent State of Croatia", "link": "https://wikipedia.org/wiki/Independent_State_of_Croatia"}, {"title": "Yugoslav Partisans", "link": "https://wikipedia.org/wiki/Yugoslav_Partisans"}]}, {"year": "1945", "text": "World War II: The Battle of Slater's Knoll on Bougainville comes to an end.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Slater%27s_Knoll\" title=\"Battle of Slater's Knoll\">Battle of Slater's Knoll</a> on <a href=\"https://wikipedia.org/wiki/Bougainville_Island\" title=\"Bougainville Island\">Bougainville</a> comes to an end.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Slater%27s_Knoll\" title=\"Battle of Slater's Knoll\">Battle of Slater's Knoll</a> on <a href=\"https://wikipedia.org/wiki/Bougainville_Island\" title=\"Bougainville Island\">Bougainville</a> comes to an end.", "links": [{"title": "Battle of Slater's Knoll", "link": "https://wikipedia.org/wiki/Battle_of_Slater%27s_Knoll"}, {"title": "Bougainville Island", "link": "https://wikipedia.org/wiki/Bougainville_Island"}]}, {"year": "1947", "text": "The first Tony Awards are presented for theatrical achievement.", "html": "1947 - The first <a href=\"https://wikipedia.org/wiki/Tony_Award\" class=\"mw-redirect\" title=\"Tony Award\">Tony Awards</a> are presented for theatrical achievement.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Tony_Award\" class=\"mw-redirect\" title=\"Tony Award\">Tony Awards</a> are presented for theatrical achievement.", "links": [{"title": "Tony Award", "link": "https://wikipedia.org/wiki/Tony_Award"}]}, {"year": "1957", "text": "The flag carrier airline of Greece for decades, Olympic Airways, is founded by <PERSON> following the acquisition of \"TAE - Greek National Airlines\".", "html": "1957 - The flag carrier airline of <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> for decades, <a href=\"https://wikipedia.org/wiki/Olympic_Airlines\" title=\"Olympic Airlines\">Olympic Airways</a>, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> following the acquisition of \"TAE - Greek National Airlines\".", "no_year_html": "The flag carrier airline of <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a> for decades, <a href=\"https://wikipedia.org/wiki/Olympic_Airlines\" title=\"Olympic Airlines\">Olympic Airways</a>, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> following the acquisition of \"TAE - Greek National Airlines\".", "links": [{"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}, {"title": "Olympic Airlines", "link": "https://wikipedia.org/wiki/Olympic_Airlines"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "Capital Airlines Flight 67 crashes in Tittabawassee Township, Michigan, near Freeland Tri-City Airport, killing 47.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_67\" title=\"Capital Airlines Flight 67\">Capital Airlines Flight 67</a> crashes in <a href=\"https://wikipedia.org/wiki/Tittabawassee_Township,_Michigan\" title=\"Tittabawassee Township, Michigan\">Tittabawassee Township, Michigan</a>, near <a href=\"https://wikipedia.org/wiki/MBS_International_Airport\" title=\"MBS International Airport\">Freeland Tri-City Airport</a>, killing 47.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capital_Airlines_Flight_67\" title=\"Capital Airlines Flight 67\">Capital Airlines Flight 67</a> crashes in <a href=\"https://wikipedia.org/wiki/Tittabawassee_Township,_Michigan\" title=\"Tittabawassee Township, Michigan\">Tittabawassee Township, Michigan</a>, near <a href=\"https://wikipedia.org/wiki/MBS_International_Airport\" title=\"MBS International Airport\">Freeland Tri-City Airport</a>, killing 47.", "links": [{"title": "Capital Airlines Flight 67", "link": "https://wikipedia.org/wiki/Capital_Airlines_Flight_67"}, {"title": "Tittabawassee Township, Michigan", "link": "https://wikipedia.org/wiki/Tittabawassee_Township,_Michigan"}, {"title": "MBS International Airport", "link": "https://wikipedia.org/wiki/MBS_International_Airport"}]}, {"year": "1965", "text": "Launch of Early Bird, the first commercial communications satellite to be placed in geosynchronous orbit.", "html": "1965 - Launch of <a href=\"https://wikipedia.org/wiki/Intelsat_I\" title=\"Intelsat I\">Early Bird</a>, the first commercial communications <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> to be placed in <a href=\"https://wikipedia.org/wiki/Geosynchronous_orbit\" title=\"Geosynchronous orbit\">geosynchronous orbit</a>.", "no_year_html": "Launch of <a href=\"https://wikipedia.org/wiki/Intelsat_I\" title=\"Intelsat I\">Early Bird</a>, the first commercial communications <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> to be placed in <a href=\"https://wikipedia.org/wiki/Geosynchronous_orbit\" title=\"Geosynchronous orbit\">geosynchronous orbit</a>.", "links": [{"title": "Intelsat I", "link": "https://wikipedia.org/wiki/Intelsat_I"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}, {"title": "Geosynchronous orbit", "link": "https://wikipedia.org/wiki/Geosynchronous_orbit"}]}, {"year": "1968", "text": "In the downtown district of Richmond, Indiana, a double explosion kills 41 and injures 150.", "html": "1968 - In the downtown district of <a href=\"https://wikipedia.org/wiki/Richmond,_Indiana\" title=\"Richmond, Indiana\">Richmond, Indiana</a>, a <a href=\"https://wikipedia.org/wiki/Richmond,_Indiana_explosion\" title=\"Richmond, Indiana explosion\">double explosion</a> kills 41 and injures 150.", "no_year_html": "In the downtown district of <a href=\"https://wikipedia.org/wiki/Richmond,_Indiana\" title=\"Richmond, Indiana\">Richmond, Indiana</a>, a <a href=\"https://wikipedia.org/wiki/Richmond,_Indiana_explosion\" title=\"Richmond, Indiana explosion\">double explosion</a> kills 41 and injures 150.", "links": [{"title": "Richmond, Indiana", "link": "https://wikipedia.org/wiki/Richmond,_Indiana"}, {"title": "Richmond, Indiana explosion", "link": "https://wikipedia.org/wiki/Richmond,_Indiana_explosion"}]}, {"year": "1968", "text": "<PERSON> wins the Liberal Party leadership election, and becomes Prime Minister of Canada soon afterward.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada_leadership_election,_1968\" class=\"mw-redirect\" title=\"Liberal Party of Canada leadership election, 1968\">Liberal Party leadership election</a>, and becomes Prime Minister of Canada soon afterward.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada_leadership_election,_1968\" class=\"mw-redirect\" title=\"Liberal Party of Canada leadership election, 1968\">Liberal Party leadership election</a>, and becomes Prime Minister of Canada soon afterward.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Liberal Party of Canada leadership election, 1968", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Canada_leadership_election,_1968"}]}, {"year": "1970", "text": "Newhall massacre: Four California Highway Patrol officers are killed in a shootout.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Newhall_massacre\" class=\"mw-redirect\" title=\"Newhall massacre\">Newhall massacre</a>: Four <a href=\"https://wikipedia.org/wiki/California_Highway_Patrol\" title=\"California Highway Patrol\">California Highway Patrol</a> officers are killed in a shootout.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Newhall_massacre\" class=\"mw-redirect\" title=\"Newhall massacre\">Newhall massacre</a>: Four <a href=\"https://wikipedia.org/wiki/California_Highway_Patrol\" title=\"California Highway Patrol\">California Highway Patrol</a> officers are killed in a shootout.", "links": [{"title": "Newhall massacre", "link": "https://wikipedia.org/wiki/Newhall_massacre"}, {"title": "California Highway Patrol", "link": "https://wikipedia.org/wiki/California_Highway_Patrol"}]}, {"year": "1972", "text": "Vietnam War: Easter Offensive: American forces begin sustained air strikes and naval bombardments.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Easter_Offensive\" title=\"Easter Offensive\">Easter Offensive</a>: American forces begin sustained air strikes and naval bombardments.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Easter_Offensive\" title=\"Easter Offensive\">Easter Offensive</a>: American forces begin sustained air strikes and naval bombardments.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Easter Offensive", "link": "https://wikipedia.org/wiki/Easter_Offensive"}]}, {"year": "1973", "text": "Launch of Pioneer 11 spacecraft.", "html": "1973 - Launch of <i><a href=\"https://wikipedia.org/wiki/Pioneer_11\" title=\"Pioneer 11\">Pioneer 11</a></i> <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a>.", "no_year_html": "Launch of <i><a href=\"https://wikipedia.org/wiki/Pioneer_11\" title=\"Pioneer 11\">Pioneer 11</a></i> <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a>.", "links": [{"title": "Pioneer 11", "link": "https://wikipedia.org/wiki/Pioneer_11"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}]}, {"year": "1973", "text": "The American League of Major League Baseball begins using the designated hitter.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> of <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> begins using the <a href=\"https://wikipedia.org/wiki/Designated_hitter\" title=\"Designated hitter\">designated hitter</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_League\" title=\"American League\">American League</a> of <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> begins using the <a href=\"https://wikipedia.org/wiki/Designated_hitter\" title=\"Designated hitter\">designated hitter</a>.", "links": [{"title": "American League", "link": "https://wikipedia.org/wiki/American_League"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}, {"title": "Designated hitter", "link": "https://wikipedia.org/wiki/Designated_hitter"}]}, {"year": "1974", "text": "In Brighton, United Kingdom, ABBA wins the 1974 edition of the Eurovision Song Contest with \"Waterloo\", the first of a joint-record seven Swedish wins.", "html": "1974 - In <a href=\"https://wikipedia.org/wiki/Brighton\" title=\"Brighton\">Brighton</a>, United Kingdom, <a href=\"https://wikipedia.org/wiki/ABBA\" title=\"ABBA\">ABBA</a> wins the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest_1974\" title=\"Eurovision Song Contest 1974\">1974 edition</a> of the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> with \"<a href=\"https://wikipedia.org/wiki/Waterloo_(song)\" title=\"Waterloo (song)\">Waterloo</a>\", the first of a joint-record seven <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Swedish</a> wins.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Brighton\" title=\"Brighton\">Brighton</a>, United Kingdom, <a href=\"https://wikipedia.org/wiki/ABBA\" title=\"ABBA\">ABBA</a> wins the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest_1974\" title=\"Eurovision Song Contest 1974\">1974 edition</a> of the <a href=\"https://wikipedia.org/wiki/Eurovision_Song_Contest\" title=\"Eurovision Song Contest\">Eurovision Song Contest</a> with \"<a href=\"https://wikipedia.org/wiki/Waterloo_(song)\" title=\"Waterloo (song)\">Waterloo</a>\", the first of a joint-record seven <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Swedish</a> wins.", "links": [{"title": "Brighton", "link": "https://wikipedia.org/wiki/Brighton"}, {"title": "ABBA", "link": "https://wikipedia.org/wiki/ABBA"}, {"title": "Eurovision Song Contest 1974", "link": "https://wikipedia.org/wiki/Eurovision_Song_Contest_1974"}, {"title": "Eurovision Song Contest", "link": "https://wikipedia.org/wiki/Eurovision_Song_Contest"}, {"title": "Waterloo (song)", "link": "https://wikipedia.org/wiki/Waterloo_(song)"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}]}, {"year": "1984", "text": "Members of Cameroon's Republican Guard unsuccessfully attempt to overthrow the government headed by <PERSON>.", "html": "1984 - Members of <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a>'s Republican Guard <a href=\"https://wikipedia.org/wiki/1984_Cameroonian_coup_attempt\" title=\"1984 Cameroonian coup attempt\">unsuccessfully attempt</a> to overthrow the government headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Members of <a href=\"https://wikipedia.org/wiki/Cameroon\" title=\"Cameroon\">Cameroon</a>'s Republican Guard <a href=\"https://wikipedia.org/wiki/1984_Cameroonian_coup_attempt\" title=\"1984 Cameroonian coup attempt\">unsuccessfully attempt</a> to overthrow the government headed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cameroon", "link": "https://wikipedia.org/wiki/Cameroon"}, {"title": "1984 Cameroonian coup attempt", "link": "https://wikipedia.org/wiki/1984_Cameroonian_coup_attempt"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "Sudanese President <PERSON><PERSON><PERSON> is ousted from power in a coup d'état led by Field Marshal <PERSON><PERSON>.", "html": "1985 - Sudanese President <a href=\"https://wikipedia.org/wiki/Gaafar_<PERSON>\" title=\"Gaafar <PERSON>\"><PERSON><PERSON><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/1985_Sudanese_coup_d%27%C3%A9tat\" title=\"1985 Sudanese coup d'état\">coup d'état</a> led by Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Sudanese President <a href=\"https://wikipedia.org/wiki/Gaafar_<PERSON>\" title=\"Gaa<PERSON>\"><PERSON><PERSON><PERSON></a> is ousted from power in a <a href=\"https://wikipedia.org/wiki/1985_Sudanese_coup_d%27%C3%A9tat\" title=\"1985 Sudanese coup d'état\">coup d'état</a> led by Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaafar_<PERSON>ry"}, {"title": "1985 Sudanese coup d'état", "link": "https://wikipedia.org/wiki/1985_Sudanese_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "The Bosnian War begins.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bosnian_War\" title=\"Bosnian War\">Bosnian War</a> begins.", "links": [{"title": "Bosnian War", "link": "https://wikipedia.org/wiki/Bosnian_War"}]}, {"year": "1994", "text": "The Rwandan genocide begins when the aircraft carrying Rwandan president <PERSON><PERSON><PERSON><PERSON> and Burundian president <PERSON><PERSON><PERSON> is shot down.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a> begins when the aircraft carrying <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">president</a> <a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"Juvénal Habyarimana\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundian</a> president <a href=\"https://wikipedia.org/wiki/Cy<PERSON><PERSON>_<PERSON>taryamira\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Juv%C3%A9nal_Habyarimana_and_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>\">shot down</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a> begins when the aircraft carrying <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">president</a> <a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"Juvénal Habyarimana\"><PERSON>v<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Burundi\" title=\"Burundi\">Burundian</a> president <a href=\"https://wikipedia.org/wiki/Cy<PERSON><PERSON>_Ntaryamira\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Juv%C3%A9nal_Habyarimana_and_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Assassination of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>\">shot down</a>.", "links": [{"title": "Rwandan genocide", "link": "https://wikipedia.org/wiki/Rwandan_genocide"}, {"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}, {"title": "President (government title)", "link": "https://wikipedia.org/wiki/President_(government_title)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana"}, {"title": "Burundi", "link": "https://wikipedia.org/wiki/Burundi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_Juv%C3%A9nal_Habyarimana_and_<PERSON><PERSON><PERSON>_<PERSON>ami<PERSON>"}]}, {"year": "1997", "text": "In Greene County, Tennessee, the <PERSON><PERSON> murders occur.", "html": "1997 - In Greene County, Tennessee, the <a href=\"https://wikipedia.org/wiki/Lillelid_murders\" title=\"Lillelid murders\">Lillelid murders</a> occur.", "no_year_html": "In Greene County, Tennessee, the <a href=\"https://wikipedia.org/wiki/Lillelid_murders\" title=\"Lillelid murders\">Lillelid murders</a> occur.", "links": [{"title": "<PERSON><PERSON> murders", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_murders"}]}, {"year": "1998", "text": "Nuclear weapons testing: Pakistan tests medium-range missiles capable of reaching India.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> tests medium-range missiles capable of reaching India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> tests medium-range missiles capable of reaching India.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2004", "text": "<PERSON><PERSON> becomes the first president of Lithuania to be peacefully removed from office by impeachment.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first president of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> to be peacefully removed from office by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> becomes the first president of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> to be peacefully removed from office by <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rolandas_Pak<PERSON>s"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}]}, {"year": "2005", "text": "Kurdish leader <PERSON><PERSON><PERSON> becomes Iraqi president; Shiite Arab <PERSON> is named premier the next day.", "html": "2005 - Kurdish leader <a href=\"https://wikipedia.org/wiki/Jalal_Talabani\" title=\"Jalal Talabani\"><PERSON><PERSON><PERSON>la<PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> president; Shiite <PERSON> is named <a href=\"https://wikipedia.org/wiki/Premier\" title=\"Premier\">premier</a> the next day.", "no_year_html": "Kurdish leader <a href=\"https://wikipedia.org/wiki/Jalal_Talabani\" title=\"Jalal Talabani\"><PERSON><PERSON><PERSON>laban<PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraqi</a> president; Shiite <PERSON> is named <a href=\"https://wikipedia.org/wiki/Premier\" title=\"Premier\">premier</a> the next day.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>l_Talabani"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Premier", "link": "https://wikipedia.org/wiki/Premier"}]}, {"year": "2008", "text": "The 2008 Egyptian general strike starts led by Egyptian workers later to be adopted by April 6 Youth Movement and Egyptian activists.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/2008_Egyptian_general_strike\" title=\"2008 Egyptian general strike\">2008 Egyptian general strike</a> starts led by Egyptian workers later to be adopted by <a href=\"https://wikipedia.org/wiki/April_6_Youth_Movement\" title=\"April 6 Youth Movement\">April 6 Youth Movement</a> and Egyptian activists.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2008_Egyptian_general_strike\" title=\"2008 Egyptian general strike\">2008 Egyptian general strike</a> starts led by Egyptian workers later to be adopted by <a href=\"https://wikipedia.org/wiki/April_6_Youth_Movement\" title=\"April 6 Youth Movement\">April 6 Youth Movement</a> and Egyptian activists.", "links": [{"title": "2008 Egyptian general strike", "link": "https://wikipedia.org/wiki/2008_Egyptian_general_strike"}, {"title": "April 6 Youth Movement", "link": "https://wikipedia.org/wiki/April_6_Youth_Movement"}]}, {"year": "2009", "text": "A 6.3 magnitude earthquake strikes near L'Aquila, Italy, killing 307.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/2009_L%27Aquila_earthquake\" title=\"2009 L'Aquila earthquake\">6.3 magnitude earthquake</a> strikes near <a href=\"https://wikipedia.org/wiki/L%27Aquila\" title=\"L'Aquila\">L'Aquila</a>, Italy, killing 307.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2009_L%27Aquila_earthquake\" title=\"2009 L'Aquila earthquake\">6.3 magnitude earthquake</a> strikes near <a href=\"https://wikipedia.org/wiki/L%27Aquila\" title=\"L'Aquila\">L'Aquila</a>, Italy, killing 307.", "links": [{"title": "2009 L'Aquila earthquake", "link": "https://wikipedia.org/wiki/2009_L%27Aquila_earthquake"}, {"title": "L'Aquila", "link": "https://wikipedia.org/wiki/L%27Aquila"}]}, {"year": "2010", "text": "Maoist rebels kill 76 CRPF officers in Dantewada district, India.", "html": "2010 - Maoist rebels <a href=\"https://wikipedia.org/wiki/April_2010_Maoist_attack_in_Dantewada\" title=\"April 2010 Maoist attack in Dantewada\">kill</a> 76 <a href=\"https://wikipedia.org/wiki/Central_Reserve_Police_Force\" title=\"Central Reserve Police Force\">CRPF</a> officers in <a href=\"https://wikipedia.org/wiki/Dantewada_district\" title=\"Dantewada district\">Dantewada district</a>, India.", "no_year_html": "Maoist rebels <a href=\"https://wikipedia.org/wiki/April_2010_Maoist_attack_in_Dantewada\" title=\"April 2010 Maoist attack in Dantewada\">kill</a> 76 <a href=\"https://wikipedia.org/wiki/Central_Reserve_Police_Force\" title=\"Central Reserve Police Force\">CRPF</a> officers in <a href=\"https://wikipedia.org/wiki/Dantewada_district\" title=\"Dantewada district\">Dantewada district</a>, India.", "links": [{"title": "April 2010 Maoist attack in Dantewada", "link": "https://wikipedia.org/wiki/April_2010_Maoist_attack_in_Dantewada"}, {"title": "Central Reserve Police Force", "link": "https://wikipedia.org/wiki/Central_Reserve_Police_Force"}, {"title": "Dantewada district", "link": "https://wikipedia.org/wiki/Dantewada_district"}]}, {"year": "2011", "text": "In San Fernando, Tamaulipas, Mexico, over 193 victims of Los Zetas were exhumed from several mass graves.", "html": "2011 - In <a href=\"https://wikipedia.org/wiki/San_Fernando,_Tamaulipas\" title=\"San Fernando, Tamaulipas\">San Fernando, Tamaulipas</a>, Mexico, over 193 victims of <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> were exhumed from several <a href=\"https://wikipedia.org/wiki/2011_San_Fernando_massacre\" title=\"2011 San Fernando massacre\">mass graves</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/San_Fernando,_Tamaulipas\" title=\"San Fernando, Tamaulipas\">San Fernando, Tamaulipas</a>, Mexico, over 193 victims of <a href=\"https://wikipedia.org/wiki/Los_Zetas\" title=\"Los Zetas\">Los Zetas</a> were exhumed from several <a href=\"https://wikipedia.org/wiki/2011_San_Fernando_massacre\" title=\"2011 San Fernando massacre\">mass graves</a>.", "links": [{"title": "San Fernando, Tamaulipas", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>as"}, {"title": "Los Zetas", "link": "https://wikipedia.org/wiki/Los_Zetas"}, {"title": "2011 San Fernando massacre", "link": "https://wikipedia.org/wiki/2011_San_Fernando_massacre"}]}, {"year": "2012", "text": "Azawad declares itself independent from the Republic of Mali.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Azawad\" title=\"Azawad\">Azawad</a> <a href=\"https://wikipedia.org/wiki/Azawadi_declaration_of_independence\" title=\"Azawadi declaration of independence\">declares itself independent</a> from the <a href=\"https://wikipedia.org/wiki/Republic_of_Mali\" class=\"mw-redirect\" title=\"Republic of Mali\">Republic of Mali</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azawad\" title=\"Azawad\">Azawad</a> <a href=\"https://wikipedia.org/wiki/Azawadi_declaration_of_independence\" title=\"Azawadi declaration of independence\">declares itself independent</a> from the <a href=\"https://wikipedia.org/wiki/Republic_of_Mali\" class=\"mw-redirect\" title=\"Republic of Mali\">Republic of Mali</a>.", "links": [{"title": "Azawad", "link": "https://wikipedia.org/wiki/Azawad"}, {"title": "Azawadi declaration of independence", "link": "https://wikipedia.org/wiki/Azawadi_declaration_of_independence"}, {"title": "Republic of Mali", "link": "https://wikipedia.org/wiki/Republic_of_Mali"}]}, {"year": "2017", "text": "U.S. military launches 59 Tomahawk cruise missiles at an air base in Syria. Russia describes the strikes as an \"aggression\", adding they significantly damage US-Russia ties.", "html": "2017 - U.S. military launches <a href=\"https://wikipedia.org/wiki/2017_Shayrat_missile_strike\" title=\"2017 Shayrat missile strike\">59 Tomahawk cruise missiles</a> at an air base in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>. Russia describes the strikes as an \"aggression\", adding they significantly damage US-Russia ties.", "no_year_html": "U.S. military launches <a href=\"https://wikipedia.org/wiki/2017_Shayrat_missile_strike\" title=\"2017 Shayrat missile strike\">59 Tomahawk cruise missiles</a> at an air base in <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>. Russia describes the strikes as an \"aggression\", adding they significantly damage US-Russia ties.", "links": [{"title": "2017 Shayrat missile strike", "link": "https://wikipedia.org/wiki/2017_Shayrat_missile_strike"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}]}, {"year": "2018", "text": "A bus carrying the Humboldt Broncos junior ice hockey team collides with a semi-truck in Saskatchewan, Canada, killing 16 people and injuring 13 others.", "html": "2018 - A bus carrying the <a href=\"https://wikipedia.org/wiki/Humboldt_Broncos\" title=\"Humboldt Broncos\">Humboldt Broncos</a> junior ice hockey team <a href=\"https://wikipedia.org/wiki/Humboldt_Broncos_bus_crash\" title=\"Humboldt Broncos bus crash\">collides with a semi-truck</a> in <a href=\"https://wikipedia.org/wiki/Saskatchewan\" title=\"Saskatchewan\">Saskatchewan</a>, Canada, killing 16 people and injuring 13 others.", "no_year_html": "A bus carrying the <a href=\"https://wikipedia.org/wiki/Humboldt_Broncos\" title=\"Humboldt Broncos\">Humboldt Broncos</a> junior ice hockey team <a href=\"https://wikipedia.org/wiki/Humboldt_Broncos_bus_crash\" title=\"Humboldt Broncos bus crash\">collides with a semi-truck</a> in <a href=\"https://wikipedia.org/wiki/Saskatchewan\" title=\"Saskatchewan\">Saskatchewan</a>, Canada, killing 16 people and injuring 13 others.", "links": [{"title": "Humboldt Broncos", "link": "https://wikipedia.org/wiki/Humboldt_Broncos"}, {"title": "Humboldt Broncos bus crash", "link": "https://wikipedia.org/wiki/Humboldt_Broncos_bus_crash"}, {"title": "Saskatchewan", "link": "https://wikipedia.org/wiki/Saskatchewan"}]}], "Births": [{"year": "1135", "text": "<PERSON><PERSON><PERSON>, Jewish philosopher, Torah scholar, physician and astronomer (March 30 also proposed, d. 1204)", "html": "1135 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jewish philosopher, <a href=\"https://wikipedia.org/wiki/Torah\" title=\"Torah\">Torah</a> scholar, physician and astronomer (<a href=\"https://wikipedia.org/wiki/March_30\" title=\"March 30\">March 30</a> also proposed, d. 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jewish philosopher, <a href=\"https://wikipedia.org/wiki/Torah\" title=\"Torah\">Torah</a> scholar, physician and astronomer (<a href=\"https://wikipedia.org/wiki/March_30\" title=\"March 30\">March 30</a> also proposed, d. 1204)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Torah", "link": "https://wikipedia.org/wiki/Torah"}, {"title": "March 30", "link": "https://wikipedia.org/wiki/March_30"}]}, {"year": "1342", "text": "<PERSON><PERSON><PERSON>, Marchioness of Tortosa", "html": "1342 - <a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Marchioness_of_Tortosa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Marchioness of Tortosa\"><PERSON><PERSON><PERSON> <PERSON>, Marchioness of Tortosa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>,_Marchioness_of_Tortosa\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Marchioness of Tortosa\"><PERSON><PERSON><PERSON> <PERSON>, Marchioness of Tortosa</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Marchioness of Tortosa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Marchioness_of_Tortosa"}]}, {"year": "1573", "text": "<PERSON> of Brunswick-Lüneburg, German noble (d. 1643)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON> of Brunswick-Lüneburg\"><PERSON> of Brunswick-Lüneburg</a>, German noble (d. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brunswick-L%C3%BCneburg\" title=\"<PERSON> of Brunswick-Lüneburg\"><PERSON> of Brunswick-Lüneburg</a>, German noble (d. 1643)", "links": [{"title": "Margaret of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/Margaret_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1632", "text": "<PERSON> of Austria (d. 1649)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1649)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Maria_Leopoldine_of_Austria"}]}, {"year": "1651", "text": "<PERSON>, French scholar and academic (d. 1722)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Da<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (d. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>cier"}]}, {"year": "1660", "text": "<PERSON>, German organist and composer (d. 1722)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1664", "text": "<PERSON><PERSON><PERSON>, Swedish general and politician, Governor of Västerbotten County (d. 1742)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish general and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_V%C3%A4sterbotten_County\" title=\"List of governors of Västerbotten County\">Governor of Västerbotten County</a> (d. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish general and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_V%C3%A4sterbotten_County\" title=\"List of governors of Västerbotten County\">Governor of Västerbotten County</a> (d. 1742)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ar<PERSON>_<PERSON>"}, {"title": "List of governors of Västerbotten County", "link": "https://wikipedia.org/wiki/List_of_governors_of_V%C3%A4sterbotten_County"}]}, {"year": "1671", "text": "<PERSON><PERSON><PERSON>, French poet and playwright (d. 1741)", "html": "1671 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and playwright (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and playwright (d. 1741)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1672", "text": "<PERSON>, French composer (d. 1749)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>_<PERSON>uches"}]}, {"year": "1706", "text": "<PERSON>, French playwright and composer (d. 1759)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON>, Austrian organist and composer (d. 1772)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON><PERSON>, French soldier and politician (d. 1807)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soldier and politician (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soldier and politician (d. 1807)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, Italian saint (d. 1755)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian saint (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian saint (d. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1741", "text": "<PERSON>, French author and playwright (d. 1794)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, German painter and educator (d. 1853)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and educator (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and educator (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Scottish historian, economist, and philosopher (d. 1836)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/James_Mill\" title=\"James Mill\"><PERSON></a>, Scottish historian, economist, and philosopher (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_Mill\" title=\"James Mill\"><PERSON></a>, Scottish historian, economist, and philosopher (d. 1836)", "links": [{"title": "James Mill", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON><PERSON><PERSON>, Puerto Rican educator (d. 1862)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cordero\" title=\"<PERSON><PERSON><PERSON> Cordero\"><PERSON><PERSON><PERSON></a>, Puerto Rican educator (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cordero\" title=\"<PERSON><PERSON><PERSON> Cordero\"><PERSON><PERSON><PERSON></a>, Puerto Rican educator (d. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Celestina_Cordero"}]}, {"year": "1810", "text": "<PERSON>, English biologist and academic (d. 1888)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Russian philosopher and author (d. 1870)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and author (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian philosopher and author (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, German organist, composer, and conductor (d. 1883)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and conductor (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON><PERSON><PERSON>, Norwegian journalist and poet (d. 1870)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and poet (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian journalist and poet (d. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON>, French photographer, journalist, and author (d. 1910)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Nadar\" title=\"Na<PERSON>\"><PERSON><PERSON></a>, French photographer, journalist, and author (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nadar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French photographer, journalist, and author (d. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nadar"}]}, {"year": "1823", "text": "<PERSON>, Canadian-American publisher and politician, 26th Mayor of Chicago (d. 1899)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American publisher and politician, 26th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American publisher and politician, 26th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1824", "text": "<PERSON>, English-New Zealand politician, 7th Prime Minister of New Zealand (d. 1906)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-New Zealand politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English-New Zealand politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1906)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1826", "text": "<PERSON><PERSON>, French painter and academic (d. 1898)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and academic (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and academic (d. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Australian politician, 13th Premier of New South Wales (d. 1913)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 13th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1851", "text": "<PERSON>, French astronomer and academic (d. 1932)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, English trade unionist and politician (d. 1921)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade unionist and politician (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade unionist and politician (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>s"}]}, {"year": "1855", "text": "<PERSON>, Canadian painter and illustrator (d. 1930)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and illustrator (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and illustrator (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, American painter and photographer (d. 1922)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and photographer (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, French sculptor and jewellery designer (d. 1945)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and jewellery designer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and jewellery designer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>ique"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, French poet and author (d. 1897)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Guaita\" title=\"<PERSON><PERSON><PERSON> Guaita\"><PERSON><PERSON><PERSON> Guaita</a>, French poet and author (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Guaita\" title=\"<PERSON><PERSON><PERSON> Guaita\"><PERSON><PERSON><PERSON>ua<PERSON></a>, French poet and author (d. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English biologist and academic (d. 1934)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (d. 1931)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 1931)", "links": [{"title": "Felix<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON>, Armenian author, poet, and playwright (d. 1951)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Levon_Shant\" title=\"Levon Shant\"><PERSON><PERSON></a>, Armenian author, poet, and playwright (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Levon_Shant\" title=\"Lev<PERSON> Shan<PERSON>\"><PERSON><PERSON></a>, Armenian author, poet, and playwright (d. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Shant"}]}, {"year": "1878", "text": "<PERSON>, German author, poet, and playwright (d. 1934)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BChsam\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BChsam\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and playwright (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erich_M%C3%BChsam"}]}, {"year": "1881", "text": "<PERSON>, Swedish pole vaulter and hammer thrower (d. 1953)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish pole vaulter and hammer thrower (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Swedish pole vaulter and hammer thrower (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON> <PERSON><PERSON>, Welsh race car driver and engineer (d. 1927)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/J<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh race car driver and engineer (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh race car driver and engineer (d. 1927)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON> of Constantinople (d. 1972)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> I of Constantinople</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Constantinople\" title=\"<PERSON><PERSON><PERSON> I of Constantinople\"><PERSON><PERSON><PERSON> I of Constantinople</a> (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Athenagoras_I_of_Constantinople"}]}, {"year": "1886", "text": "<PERSON>, American physician and neurosurgeon (d. 1946)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and neurosurgeon (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and neurosurgeon (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, <PERSON><PERSON>, Indian ruler (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON>, <PERSON><PERSON></a>, Indian ruler (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, <PERSON><PERSON>\"><PERSON><PERSON>, <PERSON><PERSON></a>, Indian ruler (d. 1967)", "links": [{"title": "<PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Swiss painter, illustrator, and director (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Swiss painter, illustrator, and director (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Swiss painter, illustrator, and director (d. 1976)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1888", "text": "<PERSON>, German historian and academic (d. 1967)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and academic (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Dutch engineer and businessman, founded Fokker Aircraft Manufacturer (d. 1939)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Fokker\" title=\"Fokker\">Fokker Aircraft Manufacturer</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Fokker\" title=\"Fokker\">Fokker Aircraft Manufacturer</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fokker", "link": "https://wikipedia.org/wiki/F<PERSON>ker"}]}, {"year": "1892", "text": "<PERSON>, Sr., American businessman, founded the Douglas Aircraft Company (d. 1981)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Douglas_Aircraft_Company\" title=\"Douglas Aircraft Company\">Douglas Aircraft Company</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Douglas_Aircraft_Company\" title=\"Douglas Aircraft Company\">Douglas Aircraft Company</a> (d. 1981)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}, {"title": "Douglas Aircraft Company", "link": "https://wikipedia.org/wiki/Douglas_Aircraft_Company"}]}, {"year": "1892", "text": "<PERSON>, American journalist and author (d. 1981)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Thomas\"><PERSON></a>, American journalist and author (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "1895", "text": "<PERSON>, American director, producer, and screenwriter (d. 1960)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, French painter and author (d. 1920)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9buterne\" title=\"<PERSON>\"><PERSON></a>, French painter and author (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and author (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_<PERSON>%C3%A9buterne"}]}, {"year": "1900", "text": "<PERSON>, American composer and songwriter (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Robin\"><PERSON></a>, American composer and songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Robin\" title=\"Leo Robin\"><PERSON></a>, American composer and songwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Italian activist (d. 1925)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pier <PERSON>\"><PERSON></a>, Italian activist (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, French author, poet, and playwright (d. 1933)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player and manager (d. 1962)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American engineer and academic (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, German lawyer, politician and Chancellor of Germany (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, politician and <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, politician and <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1904", "text": "<PERSON>, Austrian car designer and engineer (d. 1966)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian car designer and engineer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian car designer and engineer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erwin_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American who was a spy in France for the UK and US during WWII (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Virginia_Hall\" title=\"Virginia Hall\">Virginia Hall</a>, American who was a spy in France for the UK and US during WWII (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Hall\" title=\"Virginia Hall\">Virginia Hall</a>, American who was a spy in France for the UK and US during WWII (d. 1982)", "links": [{"title": "Virginia Hall", "link": "https://wikipedia.org/wiki/Virginia_Hall"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Canadian preacher, missionary, and author (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian preacher, missionary, and author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian preacher, missionary, and author (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American baseball player (d. 1977)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American minister and theologian (d. 1965)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, German race car driver (d. 1987)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, Belarusian-American rocket scientist (d. 2018)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>ys_Kit\" title=\"Barys Kit\">Barys Kit</a>, <a href=\"https://wikipedia.org/wiki/Belarusian_Americans\" title=\"Belarusian Americans\">Belarusian-American</a> rocket scientist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kit\" title=\"Barys Kit\">Barys Kit</a>, <a href=\"https://wikipedia.org/wiki/Belarusian_Americans\" title=\"Belarusian Americans\">Belarusian-American</a> rocket scientist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barys_Kit"}, {"title": "Belarusian Americans", "link": "https://wikipedia.org/wiki/Belarusian_Americans"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, German biochemist and academic, Nobel Prize laureate (d. 1979)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1913", "text": "<PERSON>-<PERSON>, American geographer and academic (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON> McCune\"><PERSON>-<PERSON></a>, American geographer and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_<PERSON>c<PERSON>\" title=\"<PERSON>-<PERSON> McCune\"><PERSON>-<PERSON></a>, American geographer and academic (d. 1993)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Polish director, painter, and set designer (d. 1990)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ntor\" title=\"<PERSON><PERSON><PERSON> Kantor\"><PERSON><PERSON><PERSON></a>, Polish director, painter, and set designer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nto<PERSON>\" title=\"<PERSON><PERSON><PERSON>nto<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish director, painter, and set designer (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ntor"}]}, {"year": "1916", "text": "<PERSON>, American actor (d. 1998)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Leeds"}]}, {"year": "1916", "text": "<PERSON>, American geologist and author (d. 1987)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and author (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, English-Mexican painter and author (d. 2011)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Mexican painter and author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Mexican painter and author (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ington"}]}, {"year": "1918", "text": "<PERSON>, Bolivian general and politician, 56th President of Bolivia (d. 1982)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>d%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>d%C3%ADa\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bolivian general and politician, 56th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cand%C3%ADa"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Greek politician, 11th Greek Minister of Culture (d. 1998)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 11th <a href=\"https://wikipedia.org/wiki/Ministry_of_Culture_and_Sport_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Culture and Sport (Greece)\">Greek Minister of Culture</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek politician, 11th <a href=\"https://wikipedia.org/wiki/Ministry_of_Culture_and_Sport_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Culture and Sport (Greece)\">Greek Minister of Culture</a> (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georg<PERSON>_Mylonas"}, {"title": "Ministry of Culture and Sport (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Culture_and_Sport_(Greece)"}]}, {"year": "1920", "text": "<PERSON>, American pilot and physicist, invented the Taser gun (d. 2009)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Jack_Cover\" title=\"Jack Cover\"><PERSON></a>, American pilot and physicist, invented the <a href=\"https://wikipedia.org/wiki/Taser\" title=\"Taser\">Taser gun</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_Cover\" title=\"Jack Cover\"><PERSON></a>, American pilot and physicist, invented the <a href=\"https://wikipedia.org/wiki/Taser\" title=\"Taser\">Taser gun</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Cover"}, {"title": "Taser", "link": "https://wikipedia.org/wiki/Taser"}]}, {"year": "1920", "text": "<PERSON>, Swiss-American biochemist and academic, Nobel Prize laureate (d. 2021)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American shot putter (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American shot putter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American shot putter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English-Australian comedian and actor (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian comedian and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian comedian and actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American race car driver (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Italian-American singer and actor (d. 1990)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American singer and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American singer and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Latvian-American author and illustrator (d. 2000)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American author and illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-American author and illustrator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Northern Irish evangelical minister and politician, 2nd First Minister of Northern Ireland (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish evangelical minister and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish evangelical minister and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland\" class=\"mw-redirect\" title=\"First Minister of Northern Ireland\">First Minister of Northern Ireland</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Northern Ireland", "link": "https://wikipedia.org/wiki/First_Minister_of_Northern_Ireland"}]}, {"year": "1926", "text": "<PERSON>, American jazz pianist and composer (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz pianist and composer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American saxophonist, clarinet player, and composer (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and composer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American biologist, geneticist, and zoologist, Nobel Prize laureate", "html": "1928 - <a href=\"https://wikipedia.org/wiki/1928\" title=\"1928\">1928</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and zoologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1928\" title=\"1928\">1928</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, geneticist, and zoologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "1928", "link": "https://wikipedia.org/wiki/1928"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON>, English playwright and author (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Hall\"><PERSON></a>, English playwright and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Willis_Hall\" title=\"Willis Hall\"><PERSON> Hall</a>, English playwright and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willis_Hall"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American model, actress and nightclub singer (d. 1972)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress and nightclub singer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model, actress and nightclub singer (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American pianist, composer, and conductor (d. 2019)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Previn\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Previn\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and conductor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Previn"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Greek jurist, supreme justice and President of Greece (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek jurist, supreme justice and <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek jurist, supreme justice and <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Chinese coastal and offshore engineer, member of the Chinese Academy of Sciences (d. 2025)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese coastal and offshore engineer, member of the <a href=\"https://wikipedia.org/wiki/Chinese_Academy_of_Sciences\" title=\"Chinese Academy of Sciences\">Chinese Academy of Sciences</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese coastal and offshore engineer, member of the <a href=\"https://wikipedia.org/wiki/Chinese_Academy_of_Sciences\" title=\"Chinese Academy of Sciences\">Chinese Academy of Sciences</a> (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Chinese Academy of Sciences", "link": "https://wikipedia.org/wiki/Chinese_Academy_of_Sciences"}]}, {"year": "1931", "text": "<PERSON>, American author and educator (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Das<PERSON>\"><PERSON></a>, American author and educator (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Das<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor, director, and producer (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German actor and director (d. 2004)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English lawyer and academic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American journalist and diplomat, United States Ambassador to Belgium", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Belgium\" class=\"mw-redirect\" title=\"United States Ambassador to Belgium\">United States Ambassador to Belgium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Belgium\" class=\"mw-redirect\" title=\"United States Ambassador to Belgium\">United States Ambassador to Belgium</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "United States Ambassador to Belgium", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Belgium"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and politician, Mayor of Kauai (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Kauai\" title=\"Mayor of Kauai\">Mayor of Kauai</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Kauai\" title=\"Mayor of Kauai\">Mayor of Kauai</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Kauai", "link": "https://wikipedia.org/wiki/Mayor_of_Kauai"}]}, {"year": "1934", "text": "<PERSON>, Mexican actor (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON>ez_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON>ez_F%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Enrique_%C3%81l<PERSON><PERSON>_F%C3%A9lix"}]}, {"year": "1934", "text": "<PERSON>, Dutch martial artist and wrestler (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist and wrestler (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist and wrestler (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Belgian painter, illustrator, and photographer (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter, illustrator, and photographer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter, illustrator, and photographer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian author and critic (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Douglas_<PERSON>\" title=\"Douglas Hill\"><PERSON></a>, Canadian author and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Douglas_Hill\" title=\"Douglas Hill\"><PERSON></a>, Canadian author and critic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Douglas_Hill"}]}, {"year": "1936", "text": "<PERSON>, Dutch-Israeli painter and illustrator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Israeli painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Israeli painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, French neuroscientist, biologist, and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French neuroscientist, biologist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French neuroscientist, biologist, and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard\" title=\"Me<PERSON> Haggard\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard\" title=\"Me<PERSON> Haggard\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard"}]}, {"year": "1937", "text": "<PERSON>, Australian cricketer and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eivers"}]}, {"year": "1937", "text": "<PERSON>, American actor, singer, and writer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English magician and television host (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician and television host (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English magician and television host (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American television and film actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television and film actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television and film actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian lawyer and politician, 1st Canadian Minister of Foreign Affairs", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_O<PERSON>let"}, {"title": "Minister of Foreign Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)"}]}, {"year": "1939", "text": "<PERSON>, American businessman, co-founded Zeta Interactive", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Zeta_Interactive\" class=\"mw-redirect\" title=\"Zeta Interactive\">Zeta Interactive</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Zeta_Interactive\" class=\"mw-redirect\" title=\"Zeta Interactive\">Zeta Interactive</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zeta Interactive", "link": "https://wikipedia.org/wiki/Zeta_Interactive"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Mexican journalist, author, and poet", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist, author, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist, author, and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>jis"}]}, {"year": "1940", "text": "<PERSON>, Jr., Mexican-American actor and producer (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Mexican-American actor and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, Mexican-American actor and producer (d. 2011)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/Pedro_Armend%C3%<PERSON><PERSON>,_<PERSON>."}]}, {"year": "1941", "text": "<PERSON>, English economist and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American comedian, actor, and screenwriter (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Phil_<PERSON>\" title=\"Phil Austin\"><PERSON></a>, American comedian, actor, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil_Austin\" title=\"Phil Austin\"><PERSON></a>, American comedian, actor, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Austin"}]}, {"year": "1941", "text": "<PERSON>, German director and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>%C3%9Fend%C3%B6rfer\" title=\"<PERSON>\"><PERSON></a>, German director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>%C3%9Fend%C3%B6rfer\" title=\"<PERSON>\"><PERSON></a>, German director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Gei%C3%9Fend%C3%B6rfer"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Greek-American Byzantinist and politician (d. 2008)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American Byzantinist and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American Byzantinist and politician (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American race car driver and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian flute player and composer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian flute player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian flute player and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Italian-English model, actress, and fashion designer (d. 2017)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English model, actress, and fashion designer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-English model, actress, and fashion designer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English journalist and publicist (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publicist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publicist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, New Zealand-English journalist and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, New Zealand-English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, New Zealand-English journalist and academic", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1943", "text": "<PERSON>, New Zealand rugby player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lawyer and politician (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, English operatic soprano", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English operatic soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English operatic soprano", "links": [{"title": "Felicity <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English trade union leader (d. 2017)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English journalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1946", "text": "<PERSON>, New Zealand-English dentist and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English dentist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English dentist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French-American director, producer, and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English mathematician and computer scientist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and computer scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and computer scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, English academic and diplomat (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English academic and diplomat (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English academic and diplomat (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Singaporean athlete, entrepreneur and diplomat", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ng Ser Miang\"><PERSON></a>, Singaporean athlete, entrepreneur and diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ng Ser Miang\"><PERSON></a>, Singaporean athlete, entrepreneur and diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German physicist and academic, Nobel Prize laureate", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B6rmer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%B6rmer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ho<PERSON>_Ludwig_St%C3%B6rmer"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1950", "text": "<PERSON>, Canadian cycling activist (d. 2007)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cycling activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cycling activist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American anthropologist and author (d. 2001)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American anthropologist and author (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Dutch-American baseball player and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, French skier, mountaineer, and pilot (d. 1990)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French skier, mountaineer, and pilot (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French skier, mountaineer, and pilot (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, French pianist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON> Rogé\"><PERSON></a>, French pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON> Rogé\"><PERSON></a>, French pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pascal_Rog%C3%A9"}]}, {"year": "1952", "text": "<PERSON><PERSON>, German singer-songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Dirk<PERSON>ider"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Greek-Polish American actress and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Polish American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Polish American actress and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player and manager (d. 1992)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish actor and composer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German-American drummer and songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American director and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor, director, and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian actress, comedian, and writer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, comedian, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, comedian, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Canadian composer (d. 2013)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian composer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Mudassar_<PERSON>zar\" title=\"Mudassar Nazar\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mudassar_<PERSON>zar\" title=\"Mudassar Nazar\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mudassar_<PERSON>zar"}]}, {"year": "1956", "text": "<PERSON>, English politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1956", "text": "<PERSON>, Argentinian-American painter and journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American painter and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American painter and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>reng"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian cricketer and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Italian race walker and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race walker and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race walker and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Italian race walker and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race walker and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race walker and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech soprano and educator", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech soprano and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ov%C3%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech soprano and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>slava_Maxov%C3%A1"}]}, {"year": "1957", "text": "<PERSON>, Italian soldier, engineer, and astronaut", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, engineer, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian soldier, engineer, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>poli"}]}, {"year": "1958", "text": "<PERSON>, Australian author and illustrator", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, New Zealand rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish impressionist and comedian", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish impressionist and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish impressionist and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1961)\" title=\"<PERSON> (footballer, born 1961)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1961)\" title=\"<PERSON> (footballer, born 1961)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1961)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1961)"}]}, {"year": "1962", "text": "<PERSON>, German sculptor and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Iris_H%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iris_H%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iris_H%C3%A4<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Swiss footballer, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A<PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Ecuadorian economist and politician, 54th President of Ecuador", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian economist and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian economist and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "1964", "text": "<PERSON>, American conductor and writer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American politician, Governor of Minnesota & vice presidential candidate", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, Governor of Minnesota &amp; vice presidential candidate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, Governor of Minnesota &amp; vice presidential candidate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>z"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Francis\" title=\"Black Francis\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Francis\" title=\"Black Francis\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Francis"}]}, {"year": "1965", "text": "<PERSON>, American football player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American author (d. 2013)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, South Korean-American director and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Man_<PERSON>\" title=\"Young Man <PERSON>\"><PERSON></a>, South Korean-American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Man_<PERSON>\" title=\"Young Man <PERSON>\"><PERSON></a>, South Korean-American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English composer and educator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Canadian voice actress and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English psychologist and academic", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American political scientist, author, and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fung\" title=\"Arch<PERSON> Fung\"><PERSON><PERSON></a>, American political scientist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Fung\" title=\"Archon Fung\"><PERSON><PERSON></a>, American political scientist, author, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fung"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Affonso_G<PERSON>ffone\" title=\"Affons<PERSON> G<PERSON>\">A<PERSON><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Affonso_G<PERSON>ffone\" title=\"Affonso G<PERSON>ffone\">A<PERSON><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "Affonso <PERSON>", "link": "https://wikipedia.org/wiki/Affonso_Giaffone"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American baseball player and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American basketball player (d. 2002)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Bison_Dele\" title=\"Bison Dele\"><PERSON><PERSON></a>, American basketball player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bison_Dele\" title=\"Bison Dele\"><PERSON><PERSON></a>, American basketball player (d. 2002)", "links": [{"title": "Bison Dele", "link": "https://wikipedia.org/wiki/Bison_Dele"}]}, {"year": "1969", "text": "<PERSON>, Austrian race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American geneticist and anthropologist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and anthropologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and anthropologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, South African-German ice hockey player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lzig\" title=\"<PERSON>\"><PERSON></a>, South African-German ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6lzig\" title=\"<PERSON>\"><PERSON></a>, South African-German ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%B6lzig"}]}, {"year": "1970", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Chinese swimmer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Danish director and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Simp<PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Si<PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Chinese footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1975", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress and talk show panelist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show panelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and talk show panelist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Welsh singer-songwriter, guitarist, and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Welsh singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Icelandic bass player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Georg_H%C3%B3lm\" title=\"<PERSON>\"><PERSON></a>, Icelandic bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georg_H%C3%B3lm\" title=\"<PERSON>\"><PERSON></a>, Icelandic bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_H%C3%B3lm"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Japanese author and educator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>rot<PERSON>_<PERSON>totake\" title=\"<PERSON><PERSON><PERSON> Ototake\"><PERSON><PERSON><PERSON></a>, Japanese author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rot<PERSON>_<PERSON>totake\" title=\"<PERSON><PERSON><PERSON>totake\"><PERSON><PERSON><PERSON></a>, Japanese author and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>take"}]}, {"year": "1977", "text": "<PERSON>, Finnish ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer-songwriter and violinist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Coppola\"><PERSON><PERSON></a>, American singer-songwriter and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Austrian/Filipino-English singer, pianist, and model", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian/Filipino-English singer, pianist, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian/Filipino-English singer, pianist, and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Uruguayan bass player and songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_M%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_M%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Uruguayan bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_M%C3%A9ndez"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Russian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "Lord <PERSON>, English journalist and financier", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English journalist and financier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English journalist and financier", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American sports journalist, blogger, and broadcaster", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports journalist, blogger, and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports journalist, blogger, and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Finnish long jumper", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Tommi_Evil%C3%A4\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tommi_Evil%C3%A4\" title=\"<PERSON><PERSON> Evilä\"><PERSON><PERSON></a>, Finnish long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tommi_Evil%C3%A4"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Finnish skier", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Welsh footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aine"}]}, {"year": "1981", "text": "<PERSON>, Argentine footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American bass player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Spanish actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Moroccan footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gata"}]}, {"year": "1983", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English darts player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English darts player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Micha%C3%ABl_Ciani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C3%ABl_Ciani\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C3%ABl_<PERSON><PERSON>i"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, South African footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Siboniso_Gaxa\" title=\"Siboniso Gaxa\"><PERSON><PERSON><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siboniso_Gaxa\" title=\"Siboniso Gaxa\"><PERSON><PERSON><PERSON><PERSON></a>, South African footballer", "links": [{"title": "Si<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Siboniso_Gaxa"}]}, {"year": "1984", "text": "<PERSON>, Canadian soccer player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Cameroonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American basketball player and actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Sinqua_Walls\" title=\"Sinqua Walls\"><PERSON><PERSON></a>, American basketball player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sinqua_Walls\" title=\"Sinqua Walls\"><PERSON><PERSON></a>, American basketball player and actor", "links": [{"title": "Sinqua Walls", "link": "https://wikipedia.org/wiki/Sinqua_Walls"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Cypriot footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Goei<PERSON>_Gotaro\" class=\"mw-redirect\" title=\"Goei<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goei<PERSON>_Gotaro\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ei<PERSON>_<PERSON>aro"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Heidi Mount\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Heidi Mount\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Mexican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Porter\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American model", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ju<PERSON><PERSON>i\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ju<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ju<PERSON>lei"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Italian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Congolese-English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese-English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese-English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Puerto Rican model and television host, Miss World Puerto Rico 2008", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican model and television host, <a href=\"https://wikipedia.org/wiki/Miss_World_Puerto_Rico_2008\" title=\"Miss World Puerto Rico 2008\">Miss World Puerto Rico 2008</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican model and television host, <a href=\"https://wikipedia.org/wiki/Miss_World_Puerto_Rico_2008\" title=\"Miss World Puerto Rico 2008\">Miss World Puerto Rico 2008</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Miss World Puerto Rico 2008", "link": "https://wikipedia.org/wiki/Miss_World_Puerto_Rico_2008"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Estonian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, South Korean singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(VIXX_singer)\" class=\"mw-redirect\" title=\"<PERSON> (VIXX singer)\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(VIXX_singer)\" class=\"mw-redirect\" title=\"<PERSON> (VIXX singer)\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON> (VIXX singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(VIXX_singer)"}]}, {"year": "1992", "text": "<PERSON>, American soccer player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Mexican actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Adri%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adri%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adri%C3%A1<PERSON>_Alonso"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Belarusian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress and model", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1998)\" title=\"<PERSON> (actress, born 1998)\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1998)\" title=\"<PERSON> (actress, born 1998)\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON> (actress, born 1998)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1998)"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_List\" title=\"Spencer List\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spencer_List\" title=\"Spencer List\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_List"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>rid<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>i"}]}, {"year": "2001", "text": "<PERSON>, Australian racing driver", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, German ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian-American chess player, commentator, Twitch streamer and YouTuber", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chess player, commentator, Twitch streamer and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chess player, commentator, Twitch streamer and YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Spanish tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, deaf American actress and YouTuber", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, deaf American actress and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, deaf American actress and YouTuber", "links": [{"title": "Shaylee Mansfield", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, French child singer", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Valentina_Tronel\" class=\"mw-redirect\" title=\"Valentina Tronel\"><PERSON><PERSON></a>, French child singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentina_Tronel\" class=\"mw-redirect\" title=\"Valentina Tronel\"><PERSON><PERSON>rone<PERSON></a>, French child singer", "links": [{"title": "Valentina Tronel", "link": "https://wikipedia.org/wiki/Valentina_Tronel"}]}], "Deaths": [{"year": "861", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Troyes", "html": "861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Troyes\" title=\"<PERSON><PERSON><PERSON><PERSON> of Troyes\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Troyes\" title=\"Roman Catholic Diocese of Troyes\">Troyes</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Troyes\" title=\"<PERSON><PERSON><PERSON><PERSON> of Troyes\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Troyes\" title=\"Roman Catholic Diocese of Troyes\">Troyes</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Troyes", "link": "https://wikipedia.org/wiki/Prudent<PERSON>_of_Troyes"}, {"title": "Roman Catholic Diocese of Troyes", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Troyes"}]}, {"year": "885", "text": "<PERSON> <PERSON><PERSON>, Byzantine missionary and saint (b. 815)", "html": "885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"Saints <PERSON> and <PERSON>ius\"><PERSON> <PERSON></a>, Byzantine missionary and saint (b. 815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"Saints <PERSON> and <PERSON>\"><PERSON> <PERSON></a>, Byzantine missionary and saint (b. 815)", "links": [{"title": "<PERSON> Cyril and Methodius", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "887", "text": "<PERSON><PERSON>, chancellor of the Tang Dynasty", "html": "887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>e\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang Dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON>e\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty\" title=\"Chancellor of the Tang dynasty\">chancellor of the Tang Dynasty</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pei_Che"}, {"title": "Chancellor of the Tang dynasty", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Tang_dynasty"}]}, {"year": "943", "text": "<PERSON>, Chinese general and chief of staff (b. 881)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chu<PERSON>\"><PERSON></a>, Chinese general and chief of staff (b. 881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Churang\"><PERSON></a>, Chinese general and chief of staff (b. 881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "943", "text": "<PERSON><PERSON><PERSON> <PERSON>, ruler (amir) of the Samanid Empire (b. 906)", "html": "943 - <a href=\"https://wikipedia.org/wiki/Nasr_II\" title=\"Nasr II\">Nasr II</a>, ruler (<i><a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">amir</a></i>) of the <a href=\"https://wikipedia.org/wiki/Samanid_Empire\" title=\"Samanid Empire\">Samanid Empire</a> (b. 906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nasr_II\" title=\"Nasr II\">Nasr II</a>, ruler (<i><a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">amir</a></i>) of the <a href=\"https://wikipedia.org/wiki/Samanid_Empire\" title=\"Samanid Empire\">Samanid Empire</a> (b. 906)", "links": [{"title": "Nasr II", "link": "https://wikipedia.org/wiki/Nasr_II"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "Samanid Empire", "link": "https://wikipedia.org/wiki/Samanid_Empire"}]}, {"year": "1147", "text": "<PERSON>, duke of Swabia (b. 1090)", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Swabia\" title=\"Duchy of Swabia\">Swabia</a> (b. 1090)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON> II</a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Swabia\" title=\"Duchy of Swabia\">Swabia</a> (b. 1090)", "links": [{"title": "<PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia"}, {"title": "Duchy of Swabia", "link": "https://wikipedia.org/wiki/Duchy_of_Swabia"}]}, {"year": "1174", "text": "<PERSON><PERSON>, Yemeni poet and historian (b. 1121)", "html": "1174 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_al-<PERSON>\" title=\"<PERSON><PERSON> al-Yamani\"><PERSON><PERSON></a>, Yemeni poet and historian (b. <a href=\"https://wikipedia.org/wiki/1121\" title=\"1121\">1121</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON> al-Yamani\"><PERSON><PERSON></a>, Yemeni poet and historian (b. <a href=\"https://wikipedia.org/wiki/1121\" title=\"1121\">1121</a>)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "1121", "link": "https://wikipedia.org/wiki/1121"}]}, {"year": "1199", "text": "<PERSON>, king of England (b. 1157)", "html": "1199 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, king of England (b. 1157)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, king of England (b. 1157)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1231", "text": "<PERSON>, 2nd Earl of Pembroke", "html": "1231 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>", "links": [{"title": "<PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke"}]}, {"year": "1250", "text": "<PERSON>, Grand Master of the Knights Templar", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Templar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1252", "text": "<PERSON> of Verona, Italian priest and saint (b. 1206)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Verona\" title=\"<PERSON> of Verona\"><PERSON> of Verona</a>, Italian priest and saint (b. 1206)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Verona\" title=\"<PERSON> of Verona\"><PERSON> of Verona</a>, Italian priest and saint (b. 1206)", "links": [{"title": "<PERSON> of Verona", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Verona"}]}, {"year": "1340", "text": "<PERSON>, emperor of Trebizond (Turkey)", "html": "1340 - <a href=\"https://wikipedia.org/wiki/Basil_of_Trebizond\" title=\"Basil of Trebizond\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Trebizond</a> (<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Basil_of_Trebizond\" title=\"Basil of Trebizond\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Trebizond</a> (<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>)", "links": [{"title": "Basil of Trebizond", "link": "https://wikipedia.org/wiki/Basil_of_Trebizond"}, {"title": "Empire of Trebizond", "link": "https://wikipedia.org/wiki/Empire_of_Trebizond"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}]}, {"year": "1362", "text": "<PERSON>, count of La Marche (b. 1319)", "html": "1362 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_La_Marche\" title=\"<PERSON>, Count of La Marche\"><PERSON></a>, count of <a href=\"https://wikipedia.org/wiki/County_of_La_Marche\" title=\"County of La Marche\">La Marche</a> (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_La_Marche\" title=\"<PERSON>, Count of La Marche\"><PERSON> I</a>, count of <a href=\"https://wikipedia.org/wiki/County_of_La_Marche\" title=\"County of La Marche\">La Marche</a> (b. 1319)", "links": [{"title": "<PERSON>, Count of La Marche", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_<PERSON>_<PERSON>"}, {"title": "County of La Marche", "link": "https://wikipedia.org/wiki/County_of_La_Marche"}]}, {"year": "1376", "text": "<PERSON><PERSON><PERSON> of Pogarell, Cardinal and Bishop of Wrocław (b. 1310)", "html": "1376 - <a href=\"https://wikipedia.org/wiki/Precz<PERSON>_of_Pogarell\" class=\"mw-redirect\" title=\"Precz<PERSON> of Pogarell\"><PERSON><PERSON><PERSON> of Pogarell</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">Cardinal</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw\" title=\"Bishop of Wrocław\">Bishop of Wrocław</a> (b. 1310)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Precz<PERSON>_of_Pogarell\" class=\"mw-redirect\" title=\"Precz<PERSON> of Pogarell\"><PERSON><PERSON><PERSON> of Pogarell</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholicism)\" class=\"mw-redirect\" title=\"<PERSON> (Catholicism)\">Cardinal</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw\" title=\"Bishop of Wrocław\">Bishop of Wrocław</a> (b. 1310)", "links": [{"title": "Preczlaw of Pogarell", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Pogarell"}, {"title": "<PERSON> (Catholicism)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholicism)"}, {"title": "Bishop of Wrocław", "link": "https://wikipedia.org/wiki/Bishop_of_Wroc%C5%82aw"}]}, {"year": "1490", "text": "<PERSON>, King of Hungary and Croatia from 1458 to 1490 (b. 1443)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> and <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Croatia\" class=\"mw-redirect\" title=\"List of rulers of Croatia\">Croatia</a> from 1458 to 1490 (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> and <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Croatia\" class=\"mw-redirect\" title=\"List of rulers of Croatia\">Croatia</a> from 1458 to 1490 (b. 1443)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "King of Hungary", "link": "https://wikipedia.org/wiki/King_of_Hungary"}, {"title": "List of rulers of Croatia", "link": "https://wikipedia.org/wiki/List_of_rulers_of_Croatia"}]}, {"year": "1520", "text": "<PERSON>, Italian painter and architect (b. 1483)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (b. 1483)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1523", "text": "<PERSON>, 1st Earl of Wiltshire, English nobleman (b. 1479)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Wiltshire\" title=\"<PERSON>, 1st Earl of Wiltshire\"><PERSON>, 1st Earl of Wiltshire</a>, English nobleman (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Wiltshire\" title=\"<PERSON>, 1st Earl of Wiltshire\"><PERSON>, 1st Earl of Wiltshire</a>, English nobleman (b. 1479)", "links": [{"title": "<PERSON>, 1st Earl of Wiltshire", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Wiltshire"}]}, {"year": "1528", "text": "<PERSON><PERSON>, German painter, engraver, and mathematician (b. 1471)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%BCrer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter, engraver, and mathematician (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C3%BCrer\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter, engraver, and mathematician (b. 1471)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Albrecht_D%C3%BCrer"}]}, {"year": "1551", "text": "<PERSON>, Swiss scholar and politician (b. 1484)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss scholar and politician (b. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss scholar and politician (b. 1484)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON>, Scottish archbishop and academic (b. 1512)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_St_Andrews)\" title=\"<PERSON> (archbishop of St Andrews)\"><PERSON></a>, Scottish archbishop and academic (b. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_St_Andrews)\" title=\"<PERSON> (archbishop of St Andrews)\"><PERSON></a>, Scottish archbishop and academic (b. 1512)", "links": [{"title": "<PERSON> (archbishop of St Andrews)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_St_Andrews)"}]}, {"year": "1590", "text": "<PERSON>, English politician and diplomat, Chancellor of the Duchy of Lancaster (b. 1532)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1532)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1532)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1593", "text": "<PERSON>, English Puritan and separatist (b. 1550)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Puritan and separatist (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Puritan and separatist (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, English historian and author (b. 1525)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1525)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, 1st Earl of Hertford (b. 1539)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford\" title=\"<PERSON>, 1st Earl of Hertford\"><PERSON>, 1st Earl of Hertford</a> (b. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford\" title=\"<PERSON>, 1st Earl of Hertford\"><PERSON>, 1st Earl of Hertford</a> (b. 1539)", "links": [{"title": "<PERSON>, 1st Earl of Hertford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford"}]}, {"year": "1641", "text": "<PERSON> (Domenichino), Italian painter (b. 1581)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Domenichino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON> (Domenichino)</a>, Italian painter (b. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Domenic<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON> (Domenichino)</a>, Italian painter (b. 1581)", "links": [{"title": "Domenichino", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1655", "text": "<PERSON>, French minister, historian, and scholar (b. 1591)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French minister, historian, and scholar (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French minister, historian, and scholar (b. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON><PERSON>, Italian composer (b. 1611)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer (b. 1611)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1676", "text": "<PERSON> the Younger, English politician, 1st Governor of Connecticut (b. 1606)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut\" title=\"List of colonial governors of Connecticut\">Governor of Connecticut</a> (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, English politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut\" title=\"List of colonial governors of Connecticut\">Governor of Connecticut</a> (b. 1606)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Younger"}, {"title": "List of colonial governors of Connecticut", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Connecticut"}]}, {"year": "1686", "text": "<PERSON>, 1st Earl of Anglesey, Irish-English politician (b. 1614)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, Irish-English politician (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, Irish-English politician (b. 1614)", "links": [{"title": "<PERSON>, 1st Earl of Anglesey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Anglesey"}]}, {"year": "1707", "text": "<PERSON> the Younger, Dutch-English painter (b. 1633)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, Dutch-English painter (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the Younger</a>, Dutch-English painter (b. 1633)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_the_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, English minister and historian (b. 1690)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and historian (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and historian (b. 1690)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, Landgrave of Hesse-Darmstadt (b. 1719)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Darmstadt\" title=\"<PERSON>, Landgrave of Hesse-Darmstadt\"><PERSON>, Landgrave of Hesse-Darmstadt</a> (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Darmstadt\" title=\"<PERSON>, Landgrave of Hesse-Darmstadt\"><PERSON>, Landgrave of Hesse-Darmstadt</a> (b. 1719)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Darmstadt", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Darmstadt"}]}, {"year": "1825", "text": "<PERSON>, Ukrainian-Russian painter and educator (b. 1757)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and educator (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian painter and educator (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Greek naval commander during the Greek War of Independence (b. 1770)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Nikolis_Apostolis\" title=\"Nikolis Apostolis\">Nik<PERSON> Apostolis</a>, Greek naval commander during the Greek War of Independence (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikolis_Apostolis\" title=\"Nikolis Apostolis\">Nik<PERSON> Apostolis</a>, Greek naval commander during the Greek War of Independence (b. 1770)", "links": [{"title": "Nikolis Apostolis", "link": "https://wikipedia.org/wiki/Nikolis_Apostolis"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Norwegian mathematician and theorist (b. 1802)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Norwegian mathematician and theorist (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian mathematician and theorist (b. 1802)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON><PERSON><PERSON>, Greek philosopher and scholar (b. 1748)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/Adamantios_Korais\" title=\"Adamantios Korais\"><PERSON><PERSON><PERSON></a>, Greek philosopher and scholar (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adamantios_Korais\" title=\"Adamantios Korais\"><PERSON><PERSON><PERSON></a>, Greek philosopher and scholar (b. 1748)", "links": [{"title": "Adamanti<PERSON>", "link": "https://wikipedia.org/wiki/Adamantios_Korais"}]}, {"year": "1838", "text": "<PERSON>, Brazilian poet, academic, and politician (b. 1763)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%<PERSON><PERSON>_de_Andrada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, academic, and politician (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%<PERSON><PERSON>_de_Andrada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, academic, and politician (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%A1<PERSON>_de_Andrada"}]}, {"year": "1860", "text": "<PERSON>, American author and politician, 11th United States Secretary of the Navy (b. 1778)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1862", "text": "<PERSON>, American general (b. 1803)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American merchant and politician, 3rd Mayor of Chicago (b. 1801)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1886", "text": "<PERSON>, English businessman, philanthropist, and politician, Chief Secretary for Ireland (b. 1818)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, philanthropist, and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, philanthropist, and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Secretary for Ireland", "link": "https://wikipedia.org/wiki/Chief_Secretary_for_Ireland"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American physician and botanist (b. 1809)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and botanist (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and botanist (b. 1809)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Norwegian author, playwright, and politician, 6th County Governor of Møre og Romsdal (b. 1849)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, playwright, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_County_Governors_of_M%C3%B8re_og_Romsdal\" class=\"mw-redirect\" title=\"List of County Governors of Møre og Romsdal\">County Governor of Møre og Romsdal</a> (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author, playwright, and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_County_Governors_of_M%C3%B8<PERSON>_og_Romsdal\" class=\"mw-redirect\" title=\"List of County Governors of Møre og Romsdal\">County Governor of Møre og Romsdal</a> (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of County Governors of Møre og Romsdal", "link": "https://wikipedia.org/wiki/List_of_County_Governors_of_M%C3%B8re_og_Romsdal"}]}, {"year": "1913", "text": "<PERSON>-<PERSON><PERSON>, 4th Earl <PERSON> (b. 1835)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Somerset_Lowry-<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Earl <PERSON></a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Somerset_Lowry-<PERSON>,_4th_Earl_<PERSON>\" title=\"<PERSON>, 4th <PERSON>\"><PERSON>, 4th Earl <PERSON></a> (b. 1835)", "links": [{"title": "Somerset Lowry-<PERSON><PERSON>, 4th Earl <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>,_4th_Earl_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American poet (b. 1850)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Coates\"><PERSON></a>, American poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Coates\"><PERSON></a>, American poet (b. 1850)", "links": [{"title": "<PERSON> Earle Coates", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American poet and playwright (b. 1869)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and playwright (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American cartoonist, illustrator, artist, and writer (b. 1874)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neill\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a>, illustrator, artist, and writer (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Neill\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Cartoonist\" title=\"Cartoonist\">cartoonist</a>, illustrator, artist, and writer (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_O%27Neill"}, {"title": "Cartoonist", "link": "https://wikipedia.org/wiki/Cartoonist"}]}, {"year": "1947", "text": "<PERSON>, German agronomist and politician (b. 1896)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German agronomist and politician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German agronomist and politician (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American pole vaulter (b. 1882)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pole vaulter (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Welsh poet and author (b. 1905)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh poet and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh poet and author (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idr<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Polish-Israeli scholar and academic (b. 1895)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli scholar and academic (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli scholar and academic (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Belgian microbiologist and immunologist, Nobel Prize laureate (b. 1870)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian microbiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian microbiologist and immunologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1963", "text": "<PERSON>, Ukrainian-American astronomer and academic (b. 1897)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American astronomer and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American astronomer and academic (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otto_Struve"}]}, {"year": "1970", "text": "<PERSON>, American basketball player (b. 1933)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Russian-American pianist, composer, and conductor (b. 1882)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist, composer, and conductor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist, composer, and conductor (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dutch architect (b. 1884)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch architect (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian pilot and businessman, co-founded Qantas Airways Limited (b. 1895)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Hudson_F<PERSON>\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, Australian pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas Airways Limited</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hudson_F<PERSON>\" title=\"<PERSON> F<PERSON>\"><PERSON></a>, Australian pilot and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qantas\" title=\"Qantas\">Qantas Airways Limited</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hudson_F<PERSON>h"}, {"title": "Qantas", "link": "https://wikipedia.org/wiki/Qantas"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 13th Lord Keeper of the Privy Seal of Japan (b. 1889)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 13th <a href=\"https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan\" title=\"Lord Keeper of the Privy Seal of Japan\">Lord Keeper of the Privy Seal of Japan</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 13th <a href=\"https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan\" title=\"Lord Keeper of the Privy Seal of Japan\">Lord Keeper of the Privy Seal of Japan</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dichi_Kido"}, {"title": "Lord Keeper of the Privy Seal of Japan", "link": "https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan"}]}, {"year": "1979", "text": "<PERSON>, Bulgarian architect, designed the SS. Cyril and Methodius National Library (b. 1893)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/SS<PERSON>_<PERSON>_and_Methodius_National_Library\" title=\"SS. Cyril and Methodius National Library\">SS. Cyril and Methodius National Library</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian architect, designed the <a href=\"https://wikipedia.org/wiki/SS._<PERSON>_and_Methodius_National_Library\" title=\"SS. Cyril and Methodius National Library\">SS. Cyril and Methodius National Library</a> (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS. Cyril and Methodius National Library", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_and_<PERSON><PERSON>_National_Library"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Indian General who served as the Chief of Army Staff of the Indian Army from 1962 to 1966 and the Military Governor of Hyderabad State from 1948 to 1949. (b. 1908)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cha<PERSON>\"><PERSON><PERSON></a>, Indian General who served as the Chief of Army Staff of the Indian Army from 1962 to 1966 and the Military Governor of Hyderabad State from 1948 to 1949. (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Chaudhur<PERSON>\"><PERSON><PERSON></a>, Indian General who served as the Chief of Army Staff of the Indian Army from 1962 to 1966 and the Military Governor of Hyderabad State from 1948 to 1949. (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>huri"}]}, {"year": "1992", "text": "<PERSON>, American science fiction writer (b. 1920)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American science fiction writer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Rwandan banker and politician, 3rd President of Rwanda (b. 1937)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Rwandan banker and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Rwandan banker and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Rwanda\" class=\"mw-redirect\" title=\"President of Rwanda\">President of Rwanda</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juv%C3%A9nal_Habyarimana"}, {"title": "President of Rwanda", "link": "https://wikipedia.org/wiki/President_of_Rwanda"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Burundian politician, 5th President of Burundi (b. 1955)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Burundi\" title=\"President of Burundi\">President of Burundi</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Burundi\" title=\"President of Burundi\">President of Burundi</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Burundi", "link": "https://wikipedia.org/wiki/President_of_Burundi"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Greek banker and politician, President of Greece (b. 1912)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1996", "text": "<PERSON><PERSON>, English-American actress (b. 1904)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>on\"><PERSON><PERSON></a>, English-American actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actress (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, German footballer (b. 1958)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter (b. 1942)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American vibraphone player and composer (b. 1908)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Red_Norvo\" title=\"Red Norvo\"><PERSON></a>, American vibraphone player and composer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Norvo\" title=\"Red Norvo\"><PERSON></a>, American vibraphone player and composer (b. 1908)", "links": [{"title": "Red Norvo", "link": "https://wikipedia.org/wiki/Red_Norvo"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Tunisian politician, 1st President of Tunisia (b. 1903)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Habib_Bourguiba\" title=\"Habib Bourguiba\"><PERSON><PERSON><PERSON></a>, Tunisian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Habib_Bourguiba\" title=\"<PERSON><PERSON><PERSON> Bourguiba\"><PERSON><PERSON><PERSON></a>, Tunisian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Tunisia\" title=\"President of Tunisia\">President of Tunisia</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ha<PERSON>b_Bourguiba"}, {"title": "President of Tunisia", "link": "https://wikipedia.org/wiki/President_of_Tunisia"}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter (b. 1963)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American journalist (b. 1963)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American computer scientist and educator; founded Anita Borg Institute for Women and Technology (b. 1949)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator; founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Institute_for_Women_and_Technology\" class=\"mw-redirect\" title=\"Anita Borg Institute for Women and Technology\">Anita Borg Institute for Women and Technology</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and educator; founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Institute_for_Women_and_Technology\" class=\"mw-redirect\" title=\"Anita Borg Institute for Women and Technology\">Anita Borg Institute for Women and Technology</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Institute for Women and Technology", "link": "https://wikipedia.org/wiki/Anita_Borg_Institute_for_Women_and_Technology"}]}, {"year": "2003", "text": "<PERSON>, Canadian cardinal (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Nigerian drummer, educator, and activist (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lat<PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian drummer, educator, and activist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian drummer, educator, and activist (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji"}]}, {"year": "2003", "text": "<PERSON>, Greek stage director of the Metropolitan Opera (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek stage director of the Metropolitan Opera (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek stage director of the Metropolitan Opera (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Russian linguist and activist (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian linguist and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian linguist and activist (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Prince of Monaco (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON>, Prince of Monaco</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON>, Prince of Monaco</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "2005", "text": "<PERSON>, American orthopedic surgeon and professor (b. 1904)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American orthopedic surgeon and professor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American orthopedic surgeon and professor (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American basketball player and coach (b. 1977)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American soldier and diplomat (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and diplomat (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Greek actor and director (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor and director (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Greek actor and director (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rat<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Italian director and producer (b. 1916)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and producer (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and producer (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian historian and academic (b. 1919)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._<PERSON><PERSON>_Careless\" title=\"J. M. <PERSON><PERSON> Careless\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Care<PERSON></a>, Canadian historian and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_Careless\" title=\"J. M<PERSON> <PERSON><PERSON> Careless\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Care<PERSON></a>, Canadian historian and academic (b. 1919)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON><PERSON>_Careless"}]}, {"year": "2009", "text": "<PERSON>, Australian rugby player and coach (b. 1982)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (b. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, American tribal leader (b. 1945)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_Mankiller\" title=\"<PERSON><PERSON><PERSON> Mankill<PERSON>\"><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>kill<PERSON>\"><PERSON><PERSON><PERSON></a>, American tribal leader (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>er"}]}, {"year": "2010", "text": "<PERSON><PERSON>, English actor (b. 1939)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>grave"}]}, {"year": "2011", "text": "<PERSON>, American director and cinematographer (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American admiral (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American painter and illustrator (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Chinese astrophysicist and academic (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese astrophysicist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese astrophysicist and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian fashion designer and journalist (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fashion designer and journalist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fashion designer and journalist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American poet and critic (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W<PERSON>\" title=\"<PERSON> Whittemore\"><PERSON></a>, American poet and critic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_W<PERSON>\" title=\"<PERSON> Whittemore\"><PERSON></a>, American poet and critic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Reed_Whittemore"}]}, {"year": "2013", "text": "<PERSON>, Grenadian physician and politician, 2nd Governor of Grenada (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Grenada\" class=\"mw-redirect\" title=\"Governor of Grenada\">Governor of Grenada</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grenadian physician and politician, 2nd <a href=\"https://wikipedia.org/wiki/Governor_of_Grenada\" class=\"mw-redirect\" title=\"Governor of Grenada\">Governor of Grenada</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Grenada", "link": "https://wikipedia.org/wiki/Governor_of_Grenada"}]}, {"year": "2013", "text": "<PERSON>, English footballer and manager (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Spanish director and screenwriter (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Bigas_Luna\" title=\"Bigas Luna\"><PERSON><PERSON></a>, Spanish director and screenwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bigas_Luna\" title=\"Bigas Luna\"><PERSON><PERSON></a>, Spanish director and screenwriter (b. 1946)", "links": [{"title": "Bigas Luna", "link": "https://wikipedia.org/wiki/Bigas_Luna"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, German lawyer and politician (b. 1946)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)\" title=\"<PERSON> (actress, born 1918)\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)\" title=\"<PERSON> (actress, born 1918)\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON> (actress, born 1918)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_1918)"}]}, {"year": "2014", "text": "<PERSON>, French pianist and composer (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9r%C3%A8de\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_Cast%C3%A9r%C3%A8de"}]}, {"year": "2014", "text": "<PERSON>, Norwegian actress (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Liv_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liv_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liv_Dom<PERSON>nes"}]}, {"year": "2014", "text": "<PERSON>, American soldier, actor, and dancer (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, actor, and dancer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, actor, and dancer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier, journalist, and academic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian motorcycle designer, co-founded <PERSON><PERSON><PERSON> (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle designer, co-founded <a href=\"https://wikipedia.org/wiki/B<PERSON>ta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle designer, co-founded <a href=\"https://wikipedia.org/wiki/B<PERSON>ta\" title=\"B<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Massimo_Tamburini"}, {"title": "B<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON>ta"}]}, {"year": "2015", "text": "<PERSON>, Italian lawyer and politician (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>guer"}]}, {"year": "2015", "text": "<PERSON>, American actor, director, and screenwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"James Best\"><PERSON></a>, American actor, director, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"James Best\"><PERSON></a>, American actor, director, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter and conductor (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)\" title=\"<PERSON> (musician, born 1918)\"><PERSON></a>, American singer-songwriter and conductor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)\" title=\"<PERSON> (musician, born 1918)\"><PERSON></a>, American singer-songwriter and conductor (b. 1918)", "links": [{"title": "<PERSON> (musician, born 1918)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON>, Canadian ice hockey player (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Dollard_St._Laurent\" title=\"Dollard St. Laurent\"><PERSON><PERSON>. Laurent</a>, Canadian ice hockey player (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dollard_St._Laurent\" title=\"Dollard St. Laurent\"><PERSON><PERSON>. Laurent</a>, Canadian ice hockey player (b. 1929)", "links": [{"title": "Dollard St. Laurent", "link": "https://wikipedia.org/wiki/Dollard_St._Laurent"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard\" title=\"Me<PERSON> Haggard\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard\" title=\"Me<PERSON> Haggard\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ggard"}]}, {"year": "2017", "text": "<PERSON>, American actor and comedian (b. 1926)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, British physician, journalist, author and broadcaster (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, British physician, journalist, author and broadcaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, British physician, journalist, author and broadcaster (b. 1928)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(physician)"}]}, {"year": "2020", "text": "<PERSON>, American baseball player, broadcaster and executive (b. 1934)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ne\" title=\"<PERSON>\"><PERSON></a>, American baseball player, broadcaster and executive (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Kaline\" title=\"<PERSON>\"><PERSON></a>, American baseball player, broadcaster and executive (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Al_Kaline"}]}, {"year": "2021", "text": "<PERSON>, Swiss Catholic priest, theologian, and author (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Hans_K%C3%BCng\" title=\"<PERSON>\"><PERSON></a>, Swiss Catholic priest, theologian, and author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans_<PERSON>%C3%BCng\" title=\"<PERSON>\"><PERSON></a>, Swiss Catholic priest, theologian, and author (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_K%C3%BCng"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, American politician (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Al<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician (b. 1936)", "links": [{"title": "Alce<PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>e_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Russian and Soviet politician (b. 1946)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian and Soviet politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian and Soviet politician (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, British politician (b. 1923)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American politician, 70th Governor of Maine (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 70th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}]}}