{"date": "July 28", "url": "https://wikipedia.org/wiki/July_28", "data": {"Events": [{"year": "1364", "text": "Troops of the Republic of Pisa and the Republic of Florence clash in the Battle of Cascina.", "html": "1364 - Troops of the <a href=\"https://wikipedia.org/wiki/Republic_of_Pisa\" title=\"Republic of Pisa\">Republic of Pisa</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">Republic of Florence</a> clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cascina\" title=\"Battle of Cascina\">Battle of Cascina</a>.", "no_year_html": "Troops of the <a href=\"https://wikipedia.org/wiki/Republic_of_Pisa\" title=\"Republic of Pisa\">Republic of Pisa</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">Republic of Florence</a> clash in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cascina\" title=\"Battle of Cascina\">Battle of Cascina</a>.", "links": [{"title": "Republic of Pisa", "link": "https://wikipedia.org/wiki/Republic_of_Pisa"}, {"title": "Republic of Florence", "link": "https://wikipedia.org/wiki/Republic_of_Florence"}, {"title": "Battle of Cascina", "link": "https://wikipedia.org/wiki/Battle_of_Cascina"}]}, {"year": "1540", "text": "<PERSON> of England marries his fifth wife, <PERSON>.", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a> marries his fifth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> marries his fifth wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "La Laguna encomienda, known today as the Laguna province in the Philippines, is founded by the Spaniards as one of the oldest encomiendas (provinces) in the country.", "html": "1571 - La Laguna <a href=\"https://wikipedia.org/wiki/Encomienda\" title=\"Encomienda\">encomienda</a>, known today as the <a href=\"https://wikipedia.org/wiki/Laguna_(province)\" title=\"Laguna (province)\">Laguna</a> province in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, is founded by the Spaniards as one of the oldest encomiendas (provinces) in the country.", "no_year_html": "La Laguna <a href=\"https://wikipedia.org/wiki/Encomienda\" title=\"Encomienda\">encomienda</a>, known today as the <a href=\"https://wikipedia.org/wiki/Laguna_(province)\" title=\"Laguna (province)\">Laguna</a> province in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, is founded by the Spaniards as one of the oldest encomiendas (provinces) in the country.", "links": [{"title": "Encomienda", "link": "https://wikipedia.org/wiki/Encomienda"}, {"title": "Laguna (province)", "link": "https://wikipedia.org/wiki/Laguna_(province)"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1635", "text": "In the Eighty Years' War, the Spanish capture the strategic Dutch fortress of Schenkenschans.", "html": "1635 - In the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>, the Spanish capture the strategic Dutch fortress of <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">Schenkenschans</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>, the Spanish capture the strategic Dutch fortress of <a href=\"https://wikipedia.org/wiki/Siege_of_Schenkenschans\" title=\"Siege of Schenkenschans\">Schenkenschans</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Siege of Schenkenschans", "link": "https://wikipedia.org/wiki/Siege_of_Schenkenschans"}]}, {"year": "1656", "text": "Second Northern War: Battle of Warsaw begins.", "html": "1656 - <a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)\" title=\"Battle of Warsaw (1656)\">Battle of Warsaw</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Northern_War\" class=\"mw-redirect\" title=\"Second Northern War\">Second Northern War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)\" title=\"Battle of Warsaw (1656)\">Battle of Warsaw</a> begins.", "links": [{"title": "Second Northern War", "link": "https://wikipedia.org/wiki/Second_Northern_War"}, {"title": "Battle of Warsaw (1656)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1656)"}]}, {"year": "1778", "text": "Constitution of the province of Cantabria ratified at the Assembly Hall in Bárcena la Puente, Reocín, Spain.", "html": "1778 - Constitution of the <a href=\"https://wikipedia.org/wiki/Province_of_Cantabria\" class=\"mw-redirect\" title=\"Province of Cantabria\">province of Cantabria</a> ratified at the Assembly Hall in <a href=\"https://wikipedia.orghttps://es.wikipedia.org/wiki/Puente_San_Miguel_(Reoc%C3%ADn)\" class=\"extiw\" title=\"es:Puente San Miguel (Reocín)\">Bárcena la Puente, Reocín, Spain</a>.", "no_year_html": "Constitution of the <a href=\"https://wikipedia.org/wiki/Province_of_Cantabria\" class=\"mw-redirect\" title=\"Province of Cantabria\">province of Cantabria</a> ratified at the Assembly Hall in <a href=\"https://wikipedia.orghttps://es.wikipedia.org/wiki/Puente_San_Miguel_(Reoc%C3%ADn)\" class=\"extiw\" title=\"es:Puente San Miguel (Reocín)\">Bárcena la Puente, Reocín, Spain</a>.", "links": [{"title": "Province of Cantabria", "link": "https://wikipedia.org/wiki/Province_of_Cantabria"}, {"title": "es:Puente San Miguel (Reocín)", "link": "https://wikipedia.orghttps://es.wikipedia.org/wiki/Puente_San_Miguel_(Reoc%C3%ADn)"}]}, {"year": "1794", "text": "French Revolution: <PERSON><PERSON><PERSON> and <PERSON> are executed by guillotine in Paris, France.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Louis<PERSON>\" title=\"Louis <PERSON>\"><PERSON></a> are executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/Maximilien_Robespierre\" title=\"Maximilien Robespierre\">Maxim<PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"Louis Antoine de <PERSON>-Just\"><PERSON></a> are executed by <a href=\"https://wikipedia.org/wiki/Guillotine\" title=\"Guillotine\">guillotine</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris, France</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Guillotine", "link": "https://wikipedia.org/wiki/Guillotine"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON> <PERSON> became Sultan of the Ottoman Empire and <PERSON><PERSON><PERSON> of Islam.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Mahmud II\"><PERSON><PERSON><PERSON> II</a> became <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\">Caliph of Islam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a> became <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> and <a href=\"https://wikipedia.org/wiki/Caliph_of_Islam\" class=\"mw-redirect\" title=\"Caliph of Islam\">Cal<PERSON><PERSON> of Islam</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}, {"title": "Sultan of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire"}, {"title": "Cal<PERSON>h of Islam", "link": "https://wikipedia.org/wiki/Caliph_of_Islam"}]}, {"year": "1809", "text": "Peninsular War: Battle of Talavera: Sir <PERSON>'s British, Portuguese and Spanish army defeats a French force led by <PERSON>.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Talavera\" title=\"Battle of Talavera\">Battle of Talavera</a>: Sir <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON></a>'s British, Portuguese and Spanish army defeats a French force led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Talavera\" title=\"Battle of Talavera\">Battle of Talavera</a>: Sir <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON></a>'s British, Portuguese and Spanish army defeats a French force led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Battle of Talavera", "link": "https://wikipedia.org/wiki/Battle_of_Talavera"}, {"title": "<PERSON>, 1st Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1821", "text": "<PERSON> declares the independence of Peru from Spain.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"<PERSON>\"><PERSON></a> declares the independence of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn\" title=\"José de <PERSON> Martín\"><PERSON></a> declares the independence of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> from Spain.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_San_Mart%C3%ADn"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1854", "text": "USS Constellation (1854), the last all-sail warship built by the United States Navy and now a museum ship in Baltimore Harbor, is commissioned.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/USS_Constellation_(1854)\" title=\"USS Constellation (1854)\">USS <i>Constellation</i> (1854)</a>, the last all-sail warship built by the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> and now a museum ship in <a href=\"https://wikipedia.org/wiki/Baltimore_Harbor\" class=\"mw-redirect\" title=\"Baltimore Harbor\">Baltimore Harbor</a>, is commissioned.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Constellation_(1854)\" title=\"USS Constellation (1854)\">USS <i>Constellation</i> (1854)</a>, the last all-sail warship built by the <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">United States Navy</a> and now a museum ship in <a href=\"https://wikipedia.org/wiki/Baltimore_Harbor\" class=\"mw-redirect\" title=\"Baltimore Harbor\">Baltimore Harbor</a>, is commissioned.", "links": [{"title": "USS Constellation (1854)", "link": "https://wikipedia.org/wiki/USS_Constellation_(1854)"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "Baltimore Harbor", "link": "https://wikipedia.org/wiki/Baltimore_Harbor"}]}, {"year": "1864", "text": "American Civil War: Battle of Ezra Church: Confederate troops make a third unsuccessful attempt to drive Union forces from Atlanta, Georgia.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ezra_Church\" title=\"Battle of Ezra Church\">Battle of Ezra Church</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops make a third unsuccessful attempt to drive <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces from <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ezra_Church\" title=\"Battle of Ezra Church\">Battle of Ezra Church</a>: <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> troops make a third unsuccessful attempt to drive <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces from <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Ezra Church", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}]}, {"year": "1866", "text": "At the age of 18, <PERSON> becomes the first and youngest female artist to receive a commission from the United States government for a statue (of <PERSON>).", "html": "1866 - At the age of 18, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first and youngest female artist to receive a commission from the United States government for a statue (of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "no_year_html": "At the age of 18, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first and youngest female artist to receive a commission from the United States government for a statue (of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "The 14th Amendment to the United States Constitution is certified, establishing African American citizenship and guaranteeing due process of law.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> is certified, establishing <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> citizenship and guaranteeing <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process</a> of law.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment</a> to the <a href=\"https://wikipedia.org/wiki/United_States_Constitution\" class=\"mw-redirect\" title=\"United States Constitution\">United States Constitution</a> is certified, establishing <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> citizenship and guaranteeing <a href=\"https://wikipedia.org/wiki/Due_process\" title=\"Due process\">due process</a> of law.", "links": [{"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}, {"title": "United States Constitution", "link": "https://wikipedia.org/wiki/United_States_Constitution"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "Due process", "link": "https://wikipedia.org/wiki/Due_process"}]}, {"year": "1883", "text": "A moderate earthquake measuring magnitude 4.3-5.2 strikes the Italian island of Ischia, killing over 2,300 people.", "html": "1883 - A <a href=\"https://wikipedia.org/wiki/1883_Casamicciola_earthquake\" title=\"1883 Casamicciola earthquake\">moderate earthquake</a> measuring magnitude 4.3-5.2 strikes the Italian island of <a href=\"https://wikipedia.org/wiki/Ischia\" title=\"Ischia\">Ischia</a>, killing over 2,300 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1883_Casamicciola_earthquake\" title=\"1883 Casamicciola earthquake\">moderate earthquake</a> measuring magnitude 4.3-5.2 strikes the Italian island of <a href=\"https://wikipedia.org/wiki/Ischia\" title=\"Ischia\">Ischia</a>, killing over 2,300 people.", "links": [{"title": "1883 Casamicciola earthquake", "link": "https://wikipedia.org/wiki/1883_Casamicciola_earthquake"}, {"title": "Ischia", "link": "https://wikipedia.org/wiki/Ischia"}]}, {"year": "1896", "text": "The city of Miami is incorporated.", "html": "1896 - The city of <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a> is incorporated.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a> is incorporated.", "links": [{"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}]}, {"year": "1911", "text": "The Australasian Antarctic Expedition began as the SY Aurora departed London.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/Australasian_Antarctic_Expedition\" title=\"Australasian Antarctic Expedition\">Australasian Antarctic Expedition</a> began as the <a href=\"https://wikipedia.org/wiki/SY_Aurora\" title=\"SY Aurora\">SY <i>Aurora</i></a> departed <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australasian_Antarctic_Expedition\" title=\"Australasian Antarctic Expedition\">Australasian Antarctic Expedition</a> began as the <a href=\"https://wikipedia.org/wiki/SY_Aurora\" title=\"SY Aurora\">SY <i>Aurora</i></a> departed <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Australasian Antarctic Expedition", "link": "https://wikipedia.org/wiki/Australasian_Antarctic_Expedition"}, {"title": "SY Aurora", "link": "https://wikipedia.org/wiki/SY_Aurora"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1914", "text": "In the culmination of the July Crisis, Austria-Hungary declares war on Serbia, igniting World War I.", "html": "1914 - In the culmination of the <a href=\"https://wikipedia.org/wiki/July_Crisis\" title=\"July Crisis\">July Crisis</a>, <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> declares war on <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, igniting <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "no_year_html": "In the culmination of the <a href=\"https://wikipedia.org/wiki/July_Crisis\" title=\"July Crisis\">July Crisis</a>, <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a> declares war on <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, igniting <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>.", "links": [{"title": "July Crisis", "link": "https://wikipedia.org/wiki/July_Crisis"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1915", "text": "The United States begins a 19-year occupation of Haiti.", "html": "1915 - The United States begins a 19-year <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_Haiti\" title=\"United States occupation of Haiti\">occupation of Haiti</a>.", "no_year_html": "The United States begins a 19-year <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_Haiti\" title=\"United States occupation of Haiti\">occupation of Haiti</a>.", "links": [{"title": "United States occupation of Haiti", "link": "https://wikipedia.org/wiki/United_States_occupation_of_Haiti"}]}, {"year": "1917", "text": "The Silent Parade takes place in New York City, in protest against murders, lynchings, and other violence directed towards African Americans.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Silent_Parade\" title=\"Silent Parade\">Silent Parade</a> takes place in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, in protest against murders, lynchings, and other violence directed towards <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African Americans</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Silent_Parade\" title=\"Silent Parade\">Silent Parade</a> takes place in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>, in protest against murders, lynchings, and other violence directed towards <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">African Americans</a>.", "links": [{"title": "Silent Parade", "link": "https://wikipedia.org/wiki/Silent_Parade"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}]}, {"year": "1932", "text": "U.S. President <PERSON> orders the United States Army to forcibly evict the \"Bonus Army\" of World War I veterans gathered in Washington, D.C.", "html": "1932 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the United States Army to forcibly evict the \"<a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>\" of World War I veterans gathered in Washington, D.C.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the United States Army to forcibly evict the \"<a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>\" of World War I veterans gathered in Washington, D.C.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bonus Army", "link": "https://wikipedia.org/wiki/Bonus_Army"}]}, {"year": "1935", "text": "First flight of the Boeing B-17 Flying Fortress.", "html": "1935 - First flight of the <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress\" title=\"Boeing B-17 Flying Fortress\">Boeing B-17 Flying Fortress</a>.", "links": [{"title": "Boeing B-17 Flying Fortress", "link": "https://wikipedia.org/wiki/Boeing_B-17_Flying_Fortress"}]}, {"year": "1938", "text": "Hawaii Clipper disappears between Guam and Manila as the first loss of an airliner in trans-Pacific China Clipper service.", "html": "1938 - <i><a href=\"https://wikipedia.org/wiki/Hawaii_Clipper\" title=\"Hawaii Clipper\">Hawaii Clipper</a></i> disappears between <a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a> and <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> as the first loss of an airliner in trans-Pacific <i><a href=\"https://wikipedia.org/wiki/China_Clipper\" title=\"China Clipper\">China Clipper</a></i> service.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Hawaii_Clipper\" title=\"Hawaii Clipper\">Hawaii Clipper</a></i> disappears between <a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a> and <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a> as the first loss of an airliner in trans-Pacific <i><a href=\"https://wikipedia.org/wiki/China_Clipper\" title=\"China Clipper\">China Clipper</a></i> service.", "links": [{"title": "Hawaii Clipper", "link": "https://wikipedia.org/wiki/Hawaii_Clipper"}, {"title": "Guam", "link": "https://wikipedia.org/wiki/Guam"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}, {"title": "China Clipper", "link": "https://wikipedia.org/wiki/China_Clipper"}]}, {"year": "1939", "text": "The Sutton Hoo helmet is discovered.", "html": "1939 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_Ho<PERSON>_helmet\" title=\"Sutton Hoo helmet\">Sutton Hoo helmet</a> is discovered.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_Ho<PERSON>_helmet\" title=\"Sutton Hoo helmet\">Sutton Hoo helmet</a> is discovered.", "links": [{"title": "<PERSON> helmet", "link": "https://wikipedia.org/wiki/<PERSON>_Hoo_helmet"}]}, {"year": "1942", "text": "World War II: Soviet leader <PERSON> issues Order No. 227. In response to alarming German advances, all those who retreat or otherwise leave their positions without orders to do so are to be tried in a military court, with punishment ranging from duty in a shtrafbat battalion, imprisonment in a Gulag, or execution.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues <a href=\"https://wikipedia.org/wiki/Order_No._227\" title=\"Order No. 227\">Order No. 227</a>. In response to alarming <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> advances, all those who retreat or otherwise leave their positions without orders to do so are to be tried in a military court, with punishment ranging from duty in a <a href=\"https://wikipedia.org/wiki/Shtrafbat\" title=\"Shtrafbat\">shtrafbat</a> battalion, imprisonment in a <a href=\"https://wikipedia.org/wiki/Gulag\" title=\"Gulag\">Gulag</a>, or execution.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues <a href=\"https://wikipedia.org/wiki/Order_No._227\" title=\"Order No. 227\">Order No. 227</a>. In response to alarming <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> advances, all those who retreat or otherwise leave their positions without orders to do so are to be tried in a military court, with punishment ranging from duty in a <a href=\"https://wikipedia.org/wiki/Shtrafbat\" title=\"Shtrafbat\">shtrafbat</a> battalion, imprisonment in a <a href=\"https://wikipedia.org/wiki/Gulag\" title=\"Gulag\">Gulag</a>, or execution.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Order No. 227", "link": "https://wikipedia.org/wiki/Order_No._227"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shtrafbat"}, {"title": "Gulag", "link": "https://wikipedia.org/wiki/Gulag"}]}, {"year": "1943", "text": "World War II: Operation Gomorrah: The Royal Air Force bombs Hamburg, Germany causing a firestorm that kills 42,000 German civilians.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">Operation Gomorrah</a>: The Royal Air Force bombs <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg, Germany</a> causing a <a href=\"https://wikipedia.org/wiki/Firestorm\" title=\"Firestorm\">firestorm</a> that kills 42,000 German civilians.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II\" title=\"Bombing of Hamburg in World War II\">Operation Gomorrah</a>: The Royal Air Force bombs <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg, Germany</a> causing a <a href=\"https://wikipedia.org/wiki/Firestorm\" title=\"Firestorm\">firestorm</a> that kills 42,000 German civilians.", "links": [{"title": "Bombing of Hamburg in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Hamburg_in_World_War_II"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}, {"title": "Firestorm", "link": "https://wikipedia.org/wiki/Firestorm"}]}, {"year": "1945", "text": "A U.S. Army B-25 bomber crashes into the 79th floor of the Empire State Building killing 14 and injuring 26.", "html": "1945 - A U.S. Army <a href=\"https://wikipedia.org/wiki/North_American_B-25_Mitchell\" title=\"North American B-25 Mitchell\">B-25</a> bomber <a href=\"https://wikipedia.org/wiki/B-25_Empire_State_Building_crash\" class=\"mw-redirect\" title=\"B-25 Empire State Building crash\">crashes</a> into the 79th floor of the <a href=\"https://wikipedia.org/wiki/Empire_State_Building\" title=\"Empire State Building\">Empire State Building</a> killing 14 and injuring 26.", "no_year_html": "A U.S. Army <a href=\"https://wikipedia.org/wiki/North_American_B-25_Mitchell\" title=\"North American B-25 Mitchell\">B-25</a> bomber <a href=\"https://wikipedia.org/wiki/B-25_Empire_State_Building_crash\" class=\"mw-redirect\" title=\"B-25 Empire State Building crash\">crashes</a> into the 79th floor of the <a href=\"https://wikipedia.org/wiki/Empire_State_Building\" title=\"Empire State Building\">Empire State Building</a> killing 14 and injuring 26.", "links": [{"title": "North American B-25 Mitchell", "link": "https://wikipedia.org/wiki/North_American_B-25_<PERSON>"}, {"title": "B-25 Empire State Building crash", "link": "https://wikipedia.org/wiki/B-25_Empire_State_Building_crash"}, {"title": "Empire State Building", "link": "https://wikipedia.org/wiki/Empire_State_Building"}]}, {"year": "1957", "text": "Heavy rain and a mudslide in Isahaya, western Kyushu, Japan, kills 992.", "html": "1957 - Heavy rain and a mudslide in <a href=\"https://wikipedia.org/wiki/Isahaya,_Nagasaki\" title=\"Isahaya, Nagasaki\"><PERSON><PERSON><PERSON></a>, western <a href=\"https://wikipedia.org/wiki/Kyushu\" title=\"Kyushu\">Kyushu, Japan</a>, kills 992.", "no_year_html": "Heavy rain and a mudslide in <a href=\"https://wikipedia.org/wiki/Isahaya,_Nagasaki\" title=\"Isahaya, Nagasaki\"><PERSON><PERSON><PERSON></a>, western <a href=\"https://wikipedia.org/wiki/Kyushu\" title=\"Kyushu\">Kyushu, Japan</a>, kills 992.", "links": [{"title": "Isahaya, Nagasaki", "link": "https://wikipedia.org/wiki/Isa<PERSON>a,_Nagasaki"}, {"title": "Kyushu", "link": "https://wikipedia.org/wiki/Kyushu"}]}, {"year": "1960", "text": "The German Volkswagen Act comes into force.", "html": "1960 - The German <a href=\"https://wikipedia.org/wiki/Volkswagen_Act\" title=\"Volkswagen Act\">Volkswagen Act</a> comes into force.", "no_year_html": "The German <a href=\"https://wikipedia.org/wiki/Volkswagen_Act\" title=\"Volkswagen Act\">Volkswagen Act</a> comes into force.", "links": [{"title": "Volkswagen Act", "link": "https://wikipedia.org/wiki/Volkswagen_Act"}]}, {"year": "1962", "text": "Beginning of the 8th World Festival of Youth and Students", "html": "1962 - Beginning of the <a href=\"https://wikipedia.org/wiki/8th_World_Festival_of_Youth_and_Students\" title=\"8th World Festival of Youth and Students\">8th World Festival of Youth and Students</a>", "no_year_html": "Beginning of the <a href=\"https://wikipedia.org/wiki/8th_World_Festival_of_Youth_and_Students\" title=\"8th World Festival of Youth and Students\">8th World Festival of Youth and Students</a>", "links": [{"title": "8th World Festival of Youth and Students", "link": "https://wikipedia.org/wiki/8th_World_Festival_of_Youth_and_Students"}]}, {"year": "1965", "text": "Vietnam War: U.S. President <PERSON> announces his order to increase the number of United States troops in South Vietnam from 75,000 to 125,000.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his order to increase the number of United States troops in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> from 75,000 to 125,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his order to increase the number of United States troops in <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> from 75,000 to 125,000.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1973", "text": "Summer Jam at Watkins Glen: Nearly 600,000 people attend a rock festival at the Watkins Glen International Raceway.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Summer_Jam_at_Watkins_Glen\" title=\"Summer Jam at Watkins Glen\">Summer Jam at Watkins Glen</a>: Nearly 600,000 people attend a <a href=\"https://wikipedia.org/wiki/Rock_festival\" title=\"Rock festival\">rock festival</a> at the <a href=\"https://wikipedia.org/wiki/Watkins_Glen_International\" title=\"Watkins Glen International\">Watkins Glen International</a> Raceway.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Summer_Jam_at_Watkins_Glen\" title=\"Summer Jam at Watkins Glen\">Summer Jam at Watkins Glen</a>: Nearly 600,000 people attend a <a href=\"https://wikipedia.org/wiki/Rock_festival\" title=\"Rock festival\">rock festival</a> at the <a href=\"https://wikipedia.org/wiki/Watkins_Glen_International\" title=\"Watkins Glen International\">Watkins Glen International</a> Raceway.", "links": [{"title": "Summer Jam at Watkins Glen", "link": "https://wikipedia.org/wiki/Summer_Jam_at_Watkins_Glen"}, {"title": "Rock festival", "link": "https://wikipedia.org/wiki/Rock_festival"}, {"title": "Watkins Glen International", "link": "https://wikipedia.org/wiki/Watkins_Glen_International"}]}, {"year": "1974", "text": "Spetsgruppa A, Russia's elite special force, was formed.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Spetsgruppa_A\" class=\"mw-redirect\" title=\"Spetsgruppa A\">Spetsgruppa A</a>, Russia's elite special force, was formed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spetsgruppa_A\" class=\"mw-redirect\" title=\"Spetsgruppa A\">Spetsgruppa A</a>, Russia's elite special force, was formed.", "links": [{"title": "Spetsgruppa A", "link": "https://wikipedia.org/wiki/Spetsgruppa_A"}]}, {"year": "1976", "text": "The Tangshan earthquake measuring between 7.8 and 8.2 moment magnitude flattens Tangshan in the People's Republic of China, killing 242,769 and injuring 164,851.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/1976_Tangshan_earthquake\" title=\"1976 Tangshan earthquake\">Tangshan earthquake</a> measuring between 7.8 and 8.2 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">moment magnitude</a> flattens <a href=\"https://wikipedia.org/wiki/Tangshan\" title=\"Tangshan\">Tangshan</a> in the People's Republic of China, killing 242,769 and injuring 164,851.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1976_Tangshan_earthquake\" title=\"1976 Tangshan earthquake\">Tangshan earthquake</a> measuring between 7.8 and 8.2 <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">moment magnitude</a> flattens <a href=\"https://wikipedia.org/wiki/Tangshan\" title=\"Tangshan\">Tangshan</a> in the People's Republic of China, killing 242,769 and injuring 164,851.", "links": [{"title": "1976 Tangshan earthquake", "link": "https://wikipedia.org/wiki/1976_Tangshan_earthquake"}, {"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "Tangshan", "link": "https://wikipedia.org/wiki/Tang<PERSON>"}]}, {"year": "1984", "text": "Olympic Games: Games of the XXIII Olympiad: The summer Olympics were opened in Los Angeles.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a>: <a href=\"https://wikipedia.org/wiki/1984_Summer_Olympics\" title=\"1984 Summer Olympics\">Games of the XXIII Olympiad</a>: The summer Olympics were opened in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a>: <a href=\"https://wikipedia.org/wiki/1984_Summer_Olympics\" title=\"1984 Summer Olympics\">Games of the XXIII Olympiad</a>: The summer Olympics were opened in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "links": [{"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}, {"title": "1984 Summer Olympics", "link": "https://wikipedia.org/wiki/1984_Summer_Olympics"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1996", "text": "The remains of a prehistoric man are discovered near Kennewick, Washington. Such remains will be known as the Kennewick Man.", "html": "1996 - The remains of a <a href=\"https://wikipedia.org/wiki/Prehistory\" title=\"Prehistory\">prehistoric</a> man are discovered near <a href=\"https://wikipedia.org/wiki/Kennewick,_Washington\" title=\"Kennewick, Washington\">Kennewick, Washington</a>. Such remains will be known as the <a href=\"https://wikipedia.org/wiki/Kennewick_Man\" title=\"Kennewick Man\">Kennewick Man</a>.", "no_year_html": "The remains of a <a href=\"https://wikipedia.org/wiki/Prehistory\" title=\"Prehistory\">prehistoric</a> man are discovered near <a href=\"https://wikipedia.org/wiki/Kennewick,_Washington\" title=\"Kennewick, Washington\">Kennewick, Washington</a>. Such remains will be known as the <a href=\"https://wikipedia.org/wiki/Kennewick_Man\" title=\"Kennewick Man\">Kennewick Man</a>.", "links": [{"title": "Prehistory", "link": "https://wikipedia.org/wiki/Prehistory"}, {"title": "Kennewick, Washington", "link": "https://wikipedia.org/wiki/Kennewick,_Washington"}, {"title": "Kennewick Man", "link": "https://wikipedia.org/wiki/Kennewick_Man"}]}, {"year": "2001", "text": "Australian <PERSON> becomes the first swimmer to win six gold medals at a single World Championship meeting.", "html": "2001 - Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first swimmer to win six gold medals at a single World Championship meeting.", "no_year_html": "Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first swimmer to win six gold medals at a single World Championship meeting.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "Nine coal miners trapped in the flooded Quecreek Mine in Somerset County, Pennsylvania, are rescued after 77 hours underground.", "html": "2002 - Nine <a href=\"https://wikipedia.org/wiki/Coal_mining\" title=\"Coal mining\">coal miners</a> trapped in the flooded <a href=\"https://wikipedia.org/wiki/Quecreek_Mine_Rescue\" class=\"mw-redirect\" title=\"Quecreek Mine Rescue\">Quecreek Mine</a> in <a href=\"https://wikipedia.org/wiki/Somerset_County,_Pennsylvania\" title=\"Somerset County, Pennsylvania\">Somerset County, Pennsylvania</a>, are rescued after 77 hours underground.", "no_year_html": "Nine <a href=\"https://wikipedia.org/wiki/Coal_mining\" title=\"Coal mining\">coal miners</a> trapped in the flooded <a href=\"https://wikipedia.org/wiki/Quecreek_Mine_Rescue\" class=\"mw-redirect\" title=\"Quecreek Mine Rescue\">Quecreek Mine</a> in <a href=\"https://wikipedia.org/wiki/Somerset_County,_Pennsylvania\" title=\"Somerset County, Pennsylvania\">Somerset County, Pennsylvania</a>, are rescued after 77 hours underground.", "links": [{"title": "Coal mining", "link": "https://wikipedia.org/wiki/Coal_mining"}, {"title": "Quecreek Mine Rescue", "link": "https://wikipedia.org/wiki/Quecreek_Mine_Rescue"}, {"title": "Somerset County, Pennsylvania", "link": "https://wikipedia.org/wiki/Somerset_County,_Pennsylvania"}]}, {"year": "2002", "text": "Pulkovo Aviation Enterprise Flight 9560 crashes after takeoff from Sheremetyevo International Airport in Moscow, Russia, killing 14 of the 16 people on board.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_9560\" title=\"Pulkovo Aviation Enterprise Flight 9560\">Pulkovo Aviation Enterprise Flight 9560</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>, killing 14 of the 16 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_9560\" title=\"Pulkovo Aviation Enterprise Flight 9560\">Pulkovo Aviation Enterprise Flight 9560</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Sheremetyevo_International_Airport\" title=\"Sheremetyevo International Airport\">Sheremetyevo International Airport</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>, <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>, killing 14 of the 16 people on board.", "links": [{"title": "Pulkovo Aviation Enterprise Flight 9560", "link": "https://wikipedia.org/wiki/Pulkovo_Aviation_Enterprise_Flight_9560"}, {"title": "Sheremetyevo International Airport", "link": "https://wikipedia.org/wiki/Sheremetyevo_International_Airport"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "2005", "text": "The Provisional Irish Republican Army calls an end to its thirty-year-long armed campaign against British rule in Northern Ireland.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> calls an end to its thirty-year-long armed campaign against British rule in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> calls an end to its thirty-year-long armed campaign against British rule in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "2010", "text": "Airblue Flight 202 crashes into the Margalla Hills north of Islamabad, Pakistan, killing all 152 people aboard. It is the deadliest aviation accident in Pakistan history and the first involving an Airbus A321.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Airblue_Flight_202\" title=\"Airblue Flight 202\">Airblue Flight 202</a> crashes into the <a href=\"https://wikipedia.org/wiki/Margalla_Hills\" title=\"Margalla Hills\">Margalla Hills</a> north of <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad, Pakistan</a>, killing all 152 people aboard. It is the deadliest aviation accident in Pakistan history and the first involving an <a href=\"https://wikipedia.org/wiki/Airbus_A321\" title=\"Airbus A321\">Airbus A321</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Airblue_Flight_202\" title=\"Airblue Flight 202\">Airblue Flight 202</a> crashes into the <a href=\"https://wikipedia.org/wiki/Margalla_Hills\" title=\"Margalla Hills\">Margalla Hills</a> north of <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad, Pakistan</a>, killing all 152 people aboard. It is the deadliest aviation accident in Pakistan history and the first involving an <a href=\"https://wikipedia.org/wiki/Airbus_A321\" title=\"Airbus A321\">Airbus A321</a>.", "links": [{"title": "Airblue Flight 202", "link": "https://wikipedia.org/wiki/Airblue_Flight_202"}, {"title": "Margalla Hills", "link": "https://wikipedia.org/wiki/Margalla_Hills"}, {"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}, {"title": "Airbus A321", "link": "https://wikipedia.org/wiki/Airbus_A321"}]}, {"year": "2011", "text": "While flying from Seoul, South Korea to Shanghai, China, Asiana Airlines Flight 991 develops an in-flight fire in the cargo hold. The Boeing 747-400F freighter attempts to divert to Jeju International Airport, but crashes into the sea South-West of Jeju island, killing both crew members on board.", "html": "2011 - While flying from <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> to <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, <a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_991\" title=\"Asiana Airlines Flight 991\">Asiana Airlines Flight 991</a> develops an in-flight fire in the cargo hold. The <a href=\"https://wikipedia.org/wiki/Boeing_747-400\" title=\"Boeing 747-400\">Boeing 747-400F</a> freighter attempts to divert to <a href=\"https://wikipedia.org/wiki/Jeju_International_Airport\" title=\"Jeju International Airport\">Jeju International Airport</a>, but crashes into the sea South-West of <a href=\"https://wikipedia.org/wiki/Jeju_Island\" title=\"Jeju Island\">Jeju island</a>, killing both crew members on board.", "no_year_html": "While flying from <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> to <a href=\"https://wikipedia.org/wiki/Shanghai\" title=\"Shanghai\">Shanghai</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>, <a href=\"https://wikipedia.org/wiki/Asiana_Airlines_Flight_991\" title=\"Asiana Airlines Flight 991\">Asiana Airlines Flight 991</a> develops an in-flight fire in the cargo hold. The <a href=\"https://wikipedia.org/wiki/Boeing_747-400\" title=\"Boeing 747-400\">Boeing 747-400F</a> freighter attempts to divert to <a href=\"https://wikipedia.org/wiki/Jeju_International_Airport\" title=\"Jeju International Airport\">Jeju International Airport</a>, but crashes into the sea South-West of <a href=\"https://wikipedia.org/wiki/Jeju_Island\" title=\"Jeju Island\">Jeju island</a>, killing both crew members on board.", "links": [{"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "Shanghai", "link": "https://wikipedia.org/wiki/Shanghai"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Asiana Airlines Flight 991", "link": "https://wikipedia.org/wiki/Asiana_Airlines_Flight_991"}, {"title": "Boeing 747-400", "link": "https://wikipedia.org/wiki/Boeing_747-400"}, {"title": "Jeju International Airport", "link": "https://wikipedia.org/wiki/Jeju_International_Airport"}, {"title": "Jeju Island", "link": "https://wikipedia.org/wiki/Jeju_Island"}]}, {"year": "2017", "text": "Prime Minister of Pakistan, <PERSON><PERSON><PERSON> was disqualified from office for life by Supreme Court of Pakistan after finding him guilty of corruption charges.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> was disqualified from office for life by <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Pakistan\" title=\"Supreme Court of Pakistan\">Supreme Court of Pakistan</a> after finding him guilty of <a href=\"https://wikipedia.org/wiki/Panama_Papers_case_(Pakistan)\" class=\"mw-redirect\" title=\"Panama Papers case (Pakistan)\">corruption charges</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> was disqualified from office for life by <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Pakistan\" title=\"Supreme Court of Pakistan\">Supreme Court of Pakistan</a> after finding him guilty of <a href=\"https://wikipedia.org/wiki/Panama_Papers_case_(Pakistan)\" class=\"mw-redirect\" title=\"Panama Papers case (Pakistan)\">corruption charges</a>.", "links": [{"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Supreme Court of Pakistan", "link": "https://wikipedia.org/wiki/Supreme_Court_of_Pakistan"}, {"title": "Panama Papers case (Pakistan)", "link": "https://wikipedia.org/wiki/Panama_Papers_case_(Pakistan)"}]}, {"year": "2018", "text": "Australian <PERSON> becomes the first female skipper to win the Clipper Round the World Yacht Race.", "html": "2018 - Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Skipper_(boating)\" class=\"mw-redirect\" title=\"Skipper (boating)\">skipper</a> to win the <a href=\"https://wikipedia.org/wiki/Clipper_Round_the_World_Yacht_Race\" title=\"Clipper Round the World Yacht Race\">Clipper Round the World Yacht Race</a>.", "no_year_html": "Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Skipper_(boating)\" class=\"mw-redirect\" title=\"Skipper (boating)\">skipper</a> to win the <a href=\"https://wikipedia.org/wiki/Clipper_Round_the_World_Yacht_Race\" title=\"Clipper Round the World Yacht Race\">Clipper Round the World Yacht Race</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Skipper (boating)", "link": "https://wikipedia.org/wiki/Skipper_(boating)"}, {"title": "Clipper Round the World Yacht Race", "link": "https://wikipedia.org/wiki/Clipper_Round_the_World_Yacht_Race"}]}, {"year": "2022", "text": "Catastrophic floods devastate Eastern Kentucky, resulting in 45 fatalities and causing damage to thousands of homes and businesses. ", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022_Eastern_Kentucky_floods\" class=\"mw-redirect\" title=\"2022 Eastern Kentucky floods\">Catastrophic floods</a> devastate <a href=\"https://wikipedia.org/wiki/Eastern_Kentucky\" class=\"mw-redirect\" title=\"Eastern Kentucky\">Eastern Kentucky</a>, resulting in 45 fatalities and causing damage to thousands of homes and businesses. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022_Eastern_Kentucky_floods\" class=\"mw-redirect\" title=\"2022 Eastern Kentucky floods\">Catastrophic floods</a> devastate <a href=\"https://wikipedia.org/wiki/Eastern_Kentucky\" class=\"mw-redirect\" title=\"Eastern Kentucky\">Eastern Kentucky</a>, resulting in 45 fatalities and causing damage to thousands of homes and businesses. ", "links": [{"title": "2022 Eastern Kentucky floods", "link": "https://wikipedia.org/wiki/2022_Eastern_Kentucky_floods"}, {"title": "Eastern Kentucky", "link": "https://wikipedia.org/wiki/Eastern_Kentucky"}]}], "Births": [{"year": "1347", "text": "<PERSON> of Durazzo, Queen of Naples and Hungary (d. 1412)", "html": "1347 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Durazzo\" title=\"<PERSON> of Durazzo\"><PERSON> of Durazzo</a>, Queen of Naples and Hungary (d. 1412)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Durazzo\"><PERSON> of Durazzo</a>, Queen of Naples and Hungary (d. 1412)", "links": [{"title": "<PERSON> of Durazzo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1458", "text": "<PERSON><PERSON><PERSON>, Italian poet, humanist and epigrammist (d. 1530)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet, humanist and epigrammist (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet, humanist and epigrammist (d. 1530)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1516", "text": "<PERSON>, Duke of Jülich-Cleves-Berg, German nobleman (d. 1592)", "html": "1516 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_J%C3%BClich-Cleves-Berg\" title=\"<PERSON>, Duke of Jülich-Cleves-Berg\"><PERSON>, Duke of Jülich-Cleves-Berg</a>, German nobleman (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_J%C3%BClich-Cleves-Berg\" title=\"<PERSON>, Duke of Jülich-Cleves-Berg\"><PERSON>, Duke of Jülich-Cleves-Berg</a>, German nobleman (d. 1592)", "links": [{"title": "<PERSON>, Duke of Jülich-Cleves-Berg", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_J%C3%<PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, Dutch painter (d. 1660)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, French princess (d. 1721)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, French princess (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, French princess (d. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans"}]}, {"year": "1659", "text": "<PERSON>, French jurist and diplomat (d. 1715)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jurist and diplomat (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French jurist and diplomat (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1746", "text": "<PERSON>, Jr., American judge and politician (d. 1809)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American judge and politician (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American judge and politician (d. 1809)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1750", "text": "<PERSON><PERSON><PERSON>, French actor, playwright, and politician (d. 1794)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/Fabre_d%27%C3%89glantine\" title=\"Fabre d'Églantine\"><PERSON><PERSON><PERSON> <PERSON>'Églant<PERSON></a>, French actor, playwright, and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabre_d%27%C3%89glantine\" title=\"Fabre d'Églantine\"><PERSON><PERSON><PERSON> <PERSON>'Églant<PERSON></a>, French actor, playwright, and politician (d. 1794)", "links": [{"title": "Fabre d'Églantine", "link": "https://wikipedia.org/wiki/Fabre_d%27%C3%89glantine"}]}, {"year": "1783", "text": "<PERSON>, German army officer and writer (d. 1860)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German army officer and writer (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German army officer and writer (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, Austrian businessman, founded the Bösendorfer Company (d. 1859)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Ignaz_B%C3%B6sendorfer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian businessman, founded the <a href=\"https://wikipedia.org/wiki/B%C3%B6sendorfer\" title=\"Bösendorfer\">Bösendorfer Company</a> (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignaz_B%C3%B6sendorfer\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian businessman, founded the <a href=\"https://wikipedia.org/wiki/B%C3%B6sendorfer\" title=\"Bösendorfer\">Bösendorfer Company</a> (d. 1859)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignaz_B%C3%B6sendorfer"}, {"title": "Bösendorfer", "link": "https://wikipedia.org/wiki/B%C3%B6sendorfer"}]}, {"year": "1804", "text": "<PERSON>, German anthropologist and philosopher (d. 1872)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and philosopher (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and philosopher (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Bulgarian colonel (d. 1889)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian colonel (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian colonel (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, English poet (d. 1889)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, American author and publisher, second son of <PERSON> and <PERSON>", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and publisher, second son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and publisher, second son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON>, English-American activist, co-founded Volunteers of America (d. 1940)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Ballington_Booth\" title=\"Ballington Booth\">Ballington Booth</a>, English-American activist, co-founded <a href=\"https://wikipedia.org/wiki/Volunteers_of_America\" title=\"Volunteers of America\">Volunteers of America</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ballington_Booth\" title=\"Ballington Booth\">Ballington Booth</a>, English-American activist, co-founded <a href=\"https://wikipedia.org/wiki/Volunteers_of_America\" title=\"Volunteers of America\">Volunteers of America</a> (d. 1940)", "links": [{"title": "Ballington Booth", "link": "https://wikipedia.org/wiki/Ballington_Booth"}, {"title": "Volunteers of America", "link": "https://wikipedia.org/wiki/Volunteers_of_America"}]}, {"year": "1860", "text": "<PERSON>, American businessman and politician, 19th Governor of Colorado (d. 1925)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 19th <a href=\"https://wikipedia.org/wiki/Governor_of_Colorado\" title=\"Governor of Colorado\">Governor of Colorado</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Colorado", "link": "https://wikipedia.org/wiki/Governor_of_Colorado"}]}, {"year": "1860", "text": "Grand Duchess <PERSON> of Russia (d. 1922)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\">Grand Duchess <PERSON> of Russia</a> (d. 1922)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, Russian general (d. 1919)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>vanski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, English children's book writer and illustrator (d. 1943)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English children's book writer and illustrator (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English children's book writer and illustrator (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON>, American fencer (d. 1938)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>son Van <PERSON>\"><PERSON><PERSON></a>, American fencer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Albertson Van Zo Post\"><PERSON><PERSON></a>, American fencer (d. 1938)", "links": [{"title": "<PERSON><PERSON> Van <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Van_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American-Argentinian astronomer (d. 1951)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian astronomer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian astronomer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, French journalist and politician, 106th Prime Minister of France (d. 1962)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1874", "text": "<PERSON>, Polish-American philosopher and academic (d. 1945)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American philosopher and academic (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American philosopher and academic (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_Cassirer"}]}, {"year": "1879", "text": "<PERSON>, American activist, co-founded the National Woman's Party (d. 1966)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/National_Woman%27s_Party\" title=\"National Woman's Party\">National Woman's Party</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/National_Woman%27s_Party\" title=\"National Woman's Party\">National Woman's Party</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Woman's Party", "link": "https://wikipedia.org/wiki/National_Woman%27s_Party"}]}, {"year": "1879", "text": "<PERSON>, Polish painter (d. 1944)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, French-American painter and sculptor (d. 1968)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter and sculptor (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter and sculptor (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Canadian-American journalist and author (d. 1983)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American journalist and author (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Danish organist and composer (d. 1952)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish organist and composer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish organist and composer (d. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American actress and screenwriter (d. 1926)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American actor (d. 1970)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American baseball player, coach, and manager (d. 1979)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American actor, singer, and saxophonist (d. 1986)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and saxophonist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and saxophonist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1902", "text": "<PERSON>, Australian painter (d. 1959)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "Sir <PERSON>, Austrian-English philosopher and academic (d. 1994)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, Austrian-English philosopher and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\">Sir <PERSON></a>, Austrian-English philosopher and academic (d. 1994)", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American inventor and businessman, founded Tupperware Brands (d. 1983)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Tupperware_Brands\" title=\"Tupperware Brands\">Tupperware Brands</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/Tupperware_Brands\" title=\"Tupperware Brands\">Tupperware Brands</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tupperware Brands", "link": "https://wikipedia.org/wiki/Tupperware_Brands"}]}, {"year": "1909", "text": "<PERSON><PERSON>, German publisher (d. 2005)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>enne B<PERSON>da\"><PERSON><PERSON></a>, German publisher (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Aenne Burda\"><PERSON><PERSON></a>, German publisher (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English novelist and poet (d. 1957)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American conductor and composer (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carmen Dragon\"><PERSON></a>, American conductor and composer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dragon\" title=\"Carmen Dragon\"><PERSON></a>, American conductor and composer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Dragon"}]}, {"year": "1915", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2015)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Charles Hard Townes\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Charles Hard Townes\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Hard_Townes"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1915", "text": "<PERSON>, American illustrator (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American polka musician (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American polka musician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American polka musician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American journalist and producer (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American journalist and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American journalist and producer (d. 2010)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(producer)"}]}, {"year": "1920", "text": "<PERSON>, English-American director and producer (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Belgian-Swiss oceanographer and engineer (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Swiss oceanographer and engineer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-Swiss oceanographer and engineer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American conductor and producer (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and producer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Italian racing driver (d. 1958)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American physician and academic, Nobel Prize laureate (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Baruch <PERSON>\">Baruch <PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Baruch <PERSON>\">Baruch <PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "Baruch <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1926", "text": "<PERSON>, American-Canadian bassist (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian bassist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian bassist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American poet (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American journalist and socialite, 37th First Lady of the United States (d. 1994)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and socialite, 37th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and socialite, 37th <a href=\"https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States\" class=\"mw-redirect\" title=\"List of First Ladies of the United States\">First Lady of the United States</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of First Ladies of the United States", "link": "https://wikipedia.org/wiki/List_of_First_Ladies_of_the_United_States"}]}, {"year": "1929", "text": "<PERSON>, American novelist and short story writer (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Bangladeshi singer (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Bangladeshi singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Bangladeshi singer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(singer)"}]}, {"year": "1930", "text": "<PERSON>, American singer and guitarist (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Belgian author and illustrator (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian general (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English poet and author (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian cricketer (d. 1992)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 1992)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1932", "text": "<PERSON>, American author and illustrator (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Brazilian colonel (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian colonel (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian colonel (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian ice hockey player and scout (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and scout (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and scout (d. 2016)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1934", "text": "<PERSON>, American dancer and choreographer (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American dancer and choreographer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, American dancer and choreographer (d. 2021)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(dancer)"}]}, {"year": "1935", "text": "<PERSON>, English historian and academic", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Canadian football player and coach", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Barbadian cricketer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, French director and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Spanish footballer, coach, and manager (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Luis_Aragon%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer, coach, and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_Aragon%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer, coach, and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Aragon%C3%A9s"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Croatian singer-songwriter and poet (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian singer-songwriter and poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian singer-songwriter and poet (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arsen_Dedi%C4%87"}]}, {"year": "1938", "text": "<PERSON>, Peruvian engineer, academic, and politician, 90th President of Peru (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian engineer, academic, and politician, 90th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian engineer, academic, and politician, 90th <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Thai lawyer and politician, 20th Prime Minister of Thailand", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1941", "text": "<PERSON>, American author (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian conductor and educator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian conductor and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian conductor and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Filipino actress and producer (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Greek director and screenwriter (d. 1994)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek director and screenwriter (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian rugby league player (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American guitarist and songwriter (d. 1981)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American basketball player and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter and keyboard player (d. 2008)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and keyboard player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and keyboard player (d. 2008)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON>, American cartoonist, created <PERSON>", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i>", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Garfield"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani poet and activist (d. 2018)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Fahmida_Riaz\" title=\"Fahmida Riaz\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani poet and activist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ahmida_Riaz\" title=\"Fahmida Riaz\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani poet and activist (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fahmida_Riaz"}]}, {"year": "1947", "text": "<PERSON>, Australian general and politician, 26th Governor General of Australia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and politician, 26th <a href=\"https://wikipedia.org/wiki/Governor_General_of_Australia\" class=\"mw-redirect\" title=\"Governor General of Australia\">Governor General of Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor General of Australia", "link": "https://wikipedia.org/wiki/Governor_General_of_Australia"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter, guitarist, and director", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and producer (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and producer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ki"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American baseball player and sportscaster (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Vida_Blue\" title=\"Vida Blue\"><PERSON><PERSON> Blue</a>, American baseball player and sportscaster (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vida_Blue\" title=\"Vida Blue\"><PERSON><PERSON> Blue</a>, American baseball player and sportscaster (d. 2023)", "links": [{"title": "Vida Blue", "link": "https://wikipedia.org/wiki/Vida_Blue"}]}, {"year": "1949", "text": "<PERSON>, American screenwriter and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Iranian singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Shahyar_Ghanbari\" title=\"Shahyar Ghanbari\"><PERSON><PERSON></a>, Iranian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shahyar_Ghanbari\" title=\"Shahyar Ghanbari\"><PERSON><PERSON></a>, Iranian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shahyar_Ghanbari"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Kittitian politician, 4th Governor-General of Saint Kitts and <PERSON><PERSON><PERSON> (d. 2023)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Tapley_Seaton\" title=\"Tapley Seaton\"><PERSON><PERSON><PERSON></a>, Kittitian politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Saint_Kitts_and_Nevis\" title=\"Governor-General of Saint Kitts and Nevis\">Governor-General of Saint Kitts and Nevis</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tapley_Seaton\" title=\"Tapley Seaton\"><PERSON><PERSON><PERSON></a>, Kittitian politician, 4th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Saint_Kitts_and_Nevis\" title=\"Governor-General of Saint Kitts and Nevis\">Governor-General of Saint Kitts and Nevis</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tapley_Seaton"}, {"title": "Governor-General of Saint Kitts and Nevis", "link": "https://wikipedia.org/wiki/Governor-General_of_Saint_Kitts_and_<PERSON><PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Spanish architect and engineer, designed the Athens Olympic Sports Complex", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Santiago_Calatrava\" title=\"Santiago Calatrava\">Santiago Calatrava</a>, Spanish architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Athens_Olympic_Sports_Complex\" title=\"Athens Olympic Sports Complex\">Athens Olympic Sports Complex</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Santiago_Calatrava\" title=\"Santiago Calatrava\">Santiago Calatrava</a>, Spanish architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Athens_Olympic_Sports_Complex\" title=\"Athens Olympic Sports Complex\">Athens Olympic Sports Complex</a>", "links": [{"title": "Santiago Calatrava", "link": "https://wikipedia.org/wiki/Santiago_Calatrava"}, {"title": "Athens Olympic Sports Complex", "link": "https://wikipedia.org/wiki/Athens_Olympic_Sports_Complex"}]}, {"year": "1951", "text": "<PERSON>, American basketball player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1951", "text": "<PERSON>, American rock musician and businessman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock musician and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock musician and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English footballer (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian journalist and author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, King of Thailand", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Vajiralongkorn\" title=\"Vajiralongkorn\"><PERSON><PERSON>iralongkor<PERSON></a>, King of Thailand", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vajiralongkorn\" title=\"Vajiralongkorn\"><PERSON><PERSON><PERSON>longkor<PERSON></a>, King of Thailand", "links": [{"title": "Vajiralongkorn", "link": "https://wikipedia.org/wiki/Vajiralongkorn"}]}, {"year": "1954", "text": "<PERSON>, Venezuelan colonel and politician, President of Venezuela (d. 2013)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German mathematician and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Faltings\" title=\"Gerd Faltings\"><PERSON><PERSON></a>, German mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Faltings\" title=\"Gerd Faltings\"><PERSON><PERSON></a>, German mathematician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>s"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Irish footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American journalist and author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English explorer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian runner and activist (d. 1981)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and activist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and activist (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American novelist, short story writer and journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Brazilian director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and illustrator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Y%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%8D<PERSON>_<PERSON><PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, French racing driver", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer, television writer, and actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, television writer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, television writer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Hong Kong singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Hong Kong singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, Hong Kong singer", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Ethiopian American chemist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ethiopian American chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Ethiopian American chemist", "links": [{"title": "Sossin<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Spanish footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Nadal\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Nadal\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Nadal"}]}, {"year": "1966", "text": "<PERSON>, American stand-up comedian, actor, and host", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stand-up comedian, actor, and host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stand-up comedian, actor, and host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Japanese bass player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress (d. 2016)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American ice hockey player and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American businessman, president of the Ultimate Fighting Championship", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_White\" title=\"<PERSON>\"><PERSON></a>, American businessman, president of the <a href=\"https://wikipedia.org/wiki/Ultimate_Fighting_Championship\" title=\"Ultimate Fighting Championship\">Ultimate Fighting Championship</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_White\" title=\"<PERSON> White\"><PERSON></a>, American businessman, president of the <a href=\"https://wikipedia.org/wiki/Ultimate_Fighting_Championship\" title=\"Ultimate Fighting Championship\">Ultimate Fighting Championship</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ultimate Fighting Championship", "link": "https://wikipedia.org/wiki/Ultimate_Fighting_Championship"}]}, {"year": "1970", "text": "<PERSON>, Swedish guitarist and songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Canadian figure skater", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Zimbabwean cricketer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Iraqi leader of the Islamic State of Iraq and the Levant (d. 2019)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Abu_Bakr_al-Baghdadi\" title=\"Abu Bakr al-Baghdadi\"><PERSON> Bakr al-Baghdadi</a>, Iraqi leader of the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abu_Bakr_al-Baghdadi\" title=\"Abu Bakr al-Baghdadi\"><PERSON> Bakr al-Baghdadi</a>, Iraqi leader of the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abu_Ba<PERSON>_al-Baghdad<PERSON>"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Andorran writer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Lud<PERSON><PERSON>_Lacueva_Canut\" title=\"Lud<PERSON><PERSON> Lacueva Canut\"><PERSON><PERSON><PERSON><PERSON>ueva Canut</a>, Andorran writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>_Canut\" title=\"<PERSON>d<PERSON><PERSON> Lacueva Canut\"><PERSON><PERSON><PERSON><PERSON> Canut</a>, Andorran writer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>_Canut"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1971", "text": "<PERSON>, Canadian speed skater", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1973", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Afroman, American rapper and comedian", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Afroman\" title=\"Afroman\">Afroman</a>, American rapper and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afroman\" title=\"Afroman\">Afroman</a>, American rapper and comedian", "links": [{"title": "Afroman", "link": "https://wikipedia.org/wiki/Afroman"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Greek engineer and politician, 186th Prime Minister of Greece", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek engineer and politician, 186th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek engineer and politician, 186th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Spanish actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ling"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>di<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>di<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>di<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Finnish-Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Argentinian basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Manu_Gin%C3%B3bili\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manu_Gin%C3%B3bili\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manu_Gin%C3%B3bili"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>tsushi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>tsushi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>shi"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian poet", "html": "1978 - <a href=\"https://wikipedia.org/wiki/K%C4%81rlis_V%C4%93rdi%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C4%81rlis_V%C4%93rdi%C5%86%C5%A1\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian poet", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C4%81rlis_V%C4%93rdi%C5%86%C5%A1"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Japanese singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ida"}]}, {"year": "1979", "text": "<PERSON>, Danish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic singer-songwriter and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ir<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgitta_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>woo, South Korean singer-songwriter and dancer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>-woo\"><PERSON>-woo</a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo\" title=\"<PERSON>-woo\"><PERSON>-woo</a>, South Korean singer-songwriter and dancer", "links": [{"title": "<PERSON>-woo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-woo"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Belarusian-French swimmer and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-French swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-French swimmer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Mexican-American mixed martial artist and wrestler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American mixed martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Iranian-Australian politician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian figure skater", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hay\"><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hay\"><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Swedish-Kosovar mixed martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Kosovar mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-Kosovar mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player and coach", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"DeMeco Ryans\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"DeMeco Ryans\"><PERSON><PERSON><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "DeMeco Ryans", "link": "https://wikipedia.org/wiki/DeMeco_Ryans"}]}, {"year": "1984", "text": "<PERSON>, American actor and football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1987", "text": "<PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1987)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Icelandic professional fighter and mixed martial artist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fighter)\" title=\"<PERSON><PERSON> (fighter)\"><PERSON><PERSON></a>, Icelandic professional fighter and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fighter)\" title=\"<PERSON><PERSON> (fighter)\"><PERSON><PERSON></a>, Icelandic professional fighter and mixed martial artist", "links": [{"title": "<PERSON><PERSON> (fighter)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(fighter)"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American rapper, producer, and actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Soulja_Boy\" title=\"Soulja Boy\"><PERSON><PERSON> Boy</a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soulja_Boy\" title=\"Soulja Boy\"><PERSON><PERSON> Boy</a>, American rapper, producer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Boy"}]}, {"year": "1990", "text": "<PERSON>, Italian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>er <PERSON>\"><PERSON><PERSON> <PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Cher <PERSON>\"><PERSON><PERSON> <PERSON></a>, English singer", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Cher_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American baseball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Hyojung\" title=\"Hyojung\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyojung\" title=\"Hyojung\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hyojung"}]}, {"year": "1996", "text": "<PERSON>, British tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/GloRilla\" title=\"GloRilla\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/GloR<PERSON>\" title=\"GloRilla\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "GloRilla", "link": "https://wikipedia.org/wiki/GloRilla"}]}, {"year": "2000", "text": "<PERSON><PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American football player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "450", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 401)", "html": "450 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (b. 401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"<PERSON><PERSON><PERSON> II\"><PERSON><PERSON><PERSON> II</a>, Roman emperor (b. 401)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theodosius_II"}]}, {"year": "631", "text": "<PERSON><PERSON><PERSON>, Syriac Orthodox Patriarch of Antioch.", "html": "631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>am<PERSON>lo\" title=\"<PERSON><PERSON><PERSON>am<PERSON>lo\"><PERSON><PERSON><PERSON></a>, Syriac Orthodox Patriarch of Antioch.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>lo\" title=\"<PERSON><PERSON><PERSON>am<PERSON>lo\"><PERSON><PERSON><PERSON></a>, Syriac Orthodox Patriarch of Antioch.", "links": [{"title": "Athanasius I Gammolo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_<PERSON>am<PERSON>lo"}]}, {"year": "938", "text": "<PERSON><PERSON>, half-brother of <PERSON> (during Siege of Eresburg) (b. c. 908)", "html": "938 - <a href=\"https://wikipedia.org/wiki/Thankmar\" title=\"Thankmar\">Thank<PERSON></a>, half-brother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON> I</a> (during Siege of <a href=\"https://wikipedia.org/wiki/Eresburg\" title=\"Eresburg\">Eresburg</a>) (b. c. 908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thankmar\" title=\"Thankmar\">Thank<PERSON></a>, half-brother of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON> I</a> (during Siege of <a href=\"https://wikipedia.org/wiki/Eresburg\" title=\"Eresburg\">Eresburg</a>) (b. c. 908)", "links": [{"title": "Thankmar", "link": "https://wikipedia.org/wiki/Thankmar"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Eresburg", "link": "https://wikipedia.org/wiki/Eresburg"}]}, {"year": "942", "text": "<PERSON>, emperor of Later Jin (b. 892)", "html": "942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tan<PERSON>\" title=\"Shi Jingtang\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Later_Jin_(Five_Dynasties)\" title=\"Later Jin (Five Dynasties)\">Later Jin</a> (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tang\" title=\"Shi Jingtang\"><PERSON></a>, emperor of <a href=\"https://wikipedia.org/wiki/Later_Jin_(Five_Dynasties)\" title=\"Later Jin (Five Dynasties)\">Later Jin</a> (b. 892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tang"}, {"title": "Later Jin (Five Dynasties)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Five_Dynasties)"}]}, {"year": "1057", "text": "<PERSON>, pope of the Catholic Church (b. 1018)", "html": "1057 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Victor <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Victor <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1128", "text": "<PERSON>, English son of <PERSON><PERSON><PERSON><PERSON> of Conversano (b. 1102)", "html": "1128 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON><PERSON>_of_Conversano\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Conversano\"><PERSON><PERSON><PERSON><PERSON> of Conversano</a> (b. 1102)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/S<PERSON><PERSON><PERSON>_of_Conversano\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> of Conversano\"><PERSON><PERSON><PERSON><PERSON> of Conversano</a> (b. 1102)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sybilla of Conversano", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Conversano"}]}, {"year": "1230", "text": "<PERSON>, Duke of Austria (b. 1176)", "html": "1230 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1176)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1176)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1271", "text": "<PERSON>, 1st Earl of Ulster (b. 1220)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Ulster\" title=\"<PERSON>, 1st Earl of Ulster\"><PERSON>, 1st Earl of Ulster</a> (b. 1220)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Ulster\" title=\"<PERSON>, 1st Earl of Ulster\"><PERSON>, 1st Earl of Ulster</a> (b. 1220)", "links": [{"title": "<PERSON>, 1st Earl of Ulster", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Ulster"}]}, {"year": "1285", "text": "<PERSON><PERSON>, Queen of Armenia ( b. before 1262)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Queen_of_Armenia\" title=\"<PERSON><PERSON>, Queen of Armenia\"><PERSON><PERSON>, Queen of Armenia</a> ( b. before 1262)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Queen_of_Armenia\" title=\"<PERSON><PERSON>, Queen of Armenia\"><PERSON><PERSON>, Queen of Armenia</a> ( b. before 1262)", "links": [{"title": "<PERSON><PERSON>, Queen of Armenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Queen_of_Armenia"}]}, {"year": "1333", "text": "<PERSON> of Viennois, <PERSON><PERSON><PERSON> of Vienne (b. 1309)", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Viennois\" class=\"mw-redirect\" title=\"<PERSON> of Viennois\"><PERSON> of Viennois</a>, <PERSON><PERSON><PERSON> of Vienne (b. 1309)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Viennois\" class=\"mw-redirect\" title=\"<PERSON> VIII of Viennois\"><PERSON> of Viennois</a>, <PERSON><PERSON><PERSON> of Vienne (b. 1309)", "links": [{"title": "<PERSON> of Viennois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Viennois"}]}, {"year": "1345", "text": "<PERSON><PERSON> of Majorca, queen regent of Naples (b. c. 1285)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/Sancia_of_Majorca\" title=\"San<PERSON> of Majorca\"><PERSON><PERSON> of Majorca</a>, queen regent of Naples (b. c. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sancia_of_Majorca\" title=\"San<PERSON> of Majorca\"><PERSON><PERSON> of Majorca</a>, queen regent of Naples (b. c. 1285)", "links": [{"title": "Sancia of Majorca", "link": "https://wikipedia.org/wiki/Sancia_of_Majorca"}]}, {"year": "1458", "text": "<PERSON> <PERSON>, king of Cyprus and Armenia (b. 1418)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Cyprus\" title=\"<PERSON> II of Cyprus\"><PERSON> II</a>, king of Cyprus and Armenia (b. 1418)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> II of Cyprus\"><PERSON> II</a>, king of Cyprus and Armenia (b. 1418)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1488", "text": "<PERSON>, Lord Scales (at the Battle of St. Aubin-du-Cormier)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>s\" title=\"<PERSON>, Lord <PERSON>s\"><PERSON>, Lord <PERSON>s</a> (at the Battle of St. Aubin-du-Cormier)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>s\" title=\"<PERSON>, Lord Scales\"><PERSON>, Lord <PERSON>s</a> (at the Battle of St. Aubin-du-Cormier)", "links": [{"title": "<PERSON>, Lord Scales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_Scales"}]}, {"year": "1508", "text": "<PERSON>, bishop of Glasgow", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Glasgow", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Glasgow", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1527", "text": "<PERSON>, Spanish explorer, founded the city of Santa Marta (b. 1460)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer, founded the city of <a href=\"https://wikipedia.org/wiki/Santa_Marta\" title=\"Santa Marta\">Santa Marta</a> (b. 1460)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer, founded the city of <a href=\"https://wikipedia.org/wiki/Santa_Marta\" title=\"Santa Marta\">Santa Marta</a> (b. 1460)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Santa Marta", "link": "https://wikipedia.org/wiki/Santa_Marta"}]}, {"year": "1540", "text": "<PERSON>, English lawyer and politician, Chancellor of the Exchequer (b. 1495)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1585", "text": "<PERSON>, 2nd Earl of Bedford (b. 1527)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Bedford\" title=\"<PERSON>, 2nd Earl of Bedford\"><PERSON>, 2nd Earl of Bedford</a> (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Bedford\" title=\"<PERSON>, 2nd Earl of Bedford\"><PERSON>, 2nd Earl of Bedford</a> (b. 1527)", "links": [{"title": "<PERSON>, 2nd Earl of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Bedford"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish playwright (b. 1569)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/Guill%C3%A9n_de_Castro_y_Bellvis\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> y <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON> y <PERSON></a>, Spanish playwright (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guill%C3%A9n_de_Castro_y_Bellvis\" title=\"Guil<PERSON><PERSON> de <PERSON> y <PERSON>vis\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Spanish playwright (b. 1569)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Guill%C3%A9n_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1655", "text": "<PERSON><PERSON>, French poet and playwright (b. 1619)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and playwright (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and playwright (b. 1619)", "links": [{"title": "<PERSON><PERSON> de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, English poet and author (b. 1618)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1675", "text": "<PERSON><PERSON><PERSON><PERSON>, English lawyer and politician (b. 1605)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/Bulstrode_Whitelocke\" title=\"Bulstrode Whitelocke\"><PERSON><PERSON><PERSON><PERSON></a>, English lawyer and politician (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulstrode_Whitelocke\" title=\"Bulstrode Whitelocke\"><PERSON><PERSON><PERSON><PERSON></a>, English lawyer and politician (b. 1605)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bulstrode_Whitelocke"}]}, {"year": "1685", "text": "<PERSON>, 1st Earl of Arlington, English politician and diplomat, Secretary of State for the Southern Department (b. 1618)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Arlington\" title=\"<PERSON>, 1st Earl of Arlington\"><PERSON>, 1st Earl of Arlington</a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Arlington\" title=\"<PERSON>, 1st Earl <PERSON> Arlington\"><PERSON>, 1st Earl of Arlington</a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (b. 1618)", "links": [{"title": "<PERSON>, 1st Earl of Arlington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Arlington"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1718", "text": "<PERSON>, French scholar and academic (b. 1630)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scholar and academic (b. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>luze"}]}, {"year": "1741", "text": "<PERSON>, Italian violinist and composer (b. 1678)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1678)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, German organist and composer (b. 1685)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Lord Lieutenant of Somerset (b. 1691)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset\" title=\"Lord Lieutenant of Somerset\">Lord Lieutenant of Somerset</a> (b. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset\" title=\"Lord Lieutenant of Somerset\">Lord Lieutenant of Somerset</a> (b. 1691)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Lieutenant of Somerset", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Somerset"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON>, French politician, (b. 1758)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Maxim<PERSON>en_Robespierre\" title=\"Maxim<PERSON>en Robespierre\"><PERSON><PERSON><PERSON></a>, French politician, (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxim<PERSON>en_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a>, French politician, (b. 1758)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}]}, {"year": "1794", "text": "<PERSON>, French soldier and politician (b. 1767)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>Just\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>Just\" title=\"<PERSON>Just\"><PERSON></a>, French soldier and politician (b. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1761)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Selim_III\" title=\"Selim III\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Selim_III\" title=\"Selim III\"><PERSON><PERSON></a>, Ottoman sultan (b. 1761)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se<PERSON>_III"}]}, {"year": "1809", "text": "<PERSON>, English cricketer and captain (b. 1772)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and captain (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and captain (b. 1772)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1818", "text": "<PERSON><PERSON>, French mathematician and engineer (b. 1746)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Monge\" title=\"<PERSON>pard Monge\"><PERSON><PERSON></a>, French mathematician and engineer (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Monge\" title=\"<PERSON>pard Monge\"><PERSON><PERSON></a>, French mathematician and engineer (b. 1746)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>pard_Monge"}]}, {"year": "1835", "text": "<PERSON><PERSON><PERSON>, du<PERSON>, French general and politician, 15th Prime Minister of France (b. 1768)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>,_duc_de_Tr%C3%A9vise\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, duc de <PERSON>\"><PERSON><PERSON><PERSON>, duc de <PERSON></a>, French general and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>,_duc_de_Tr%C3%A9vise\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, duc de <PERSON>\"><PERSON><PERSON><PERSON>, duc de <PERSON></a>, French general and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1768)", "links": [{"title": "<PERSON><PERSON><PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>,_duc_de_Tr%C3%A9vise"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1836", "text": "<PERSON>, German-English banker and financier (b. 1777)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English banker and financier (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English banker and financier (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, Finnish composer (b. 1775)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish composer (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish composer (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1842", "text": "<PERSON><PERSON><PERSON>, German author and poet (b. 1778)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (b. 1778)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, French diplomat and brother of <PERSON> (b. 1768)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and brother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and brother of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1849", "text": "<PERSON> of Sardinia (b. 1798)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sardinia\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sardinia\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (b. 1798)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sardinia"}]}, {"year": "1869", "text": "<PERSON>, Czech anatomist and physiologist (b. 1787)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C4%9B\" title=\"<PERSON>\"><PERSON></a>, Czech anatomist and physiologist (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C4%9B\" title=\"<PERSON>\"><PERSON></a>, Czech anatomist and physiologist (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>%C4%9B"}]}, {"year": "1878", "text": "<PERSON>, American publisher and politician (b. 1820)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, British philanthropist, sheriff and banker (b. 1784)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British philanthropist, sheriff and banker (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British philanthropist, sheriff and banker (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American minister and theologian (b. 1803)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (b. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter (b. 1849)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1849)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American hammer thrower (b. 1881)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hammer thrower (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, American hammer thrower (b. 1881)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Swedish ophthalmologist and optician, Nobel Prize laureate (b. 1862)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>strand\" title=\"<PERSON><PERSON> Gullstrand\"><PERSON><PERSON></a>, Swedish ophthalmologist and optician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nd\" title=\"<PERSON><PERSON> Gullstrand\"><PERSON><PERSON></a>, Swedish ophthalmologist and optician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>var_<PERSON>strand"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, 30th yokozuna (b. 1890)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ni<PERSON>ou<PERSON>_Kajir%C5%8D_III\" title=\"Nishinoumi Kajirō III\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, 30th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\">yoko<PERSON>na</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Kajir%C5%8D_III\" title=\"Nishinoumi Kajirō III\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, 30th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\">yoko<PERSON>na</a> (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Kajir%C5%8D_III"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1934", "text": "<PERSON>, Canadian-American actress and singer (b. 1868)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, South African cricketer and pilot (b. 1876)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and pilot (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and pilot (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON> of Constantinople (b. 1871)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Meletius_IV_of_Constantinople\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> IV of Constantinople\"><PERSON><PERSON><PERSON> IV of Constantinople</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel<PERSON><PERSON>_IV_of_Constantinople\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> IV of Constantinople\"><PERSON><PERSON><PERSON> IV of Constantinople</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Meletius_IV_of_Constantinople"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, English archaeologist and academic (b. 1853)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petrie\" title=\"Flind<PERSON> Petrie\"><PERSON><PERSON><PERSON></a>, English archaeologist and academic (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petrie\" title=\"Flinders Petrie\"><PERSON><PERSON><PERSON></a>, English archaeologist and academic (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petrie"}]}, {"year": "1946", "text": "<PERSON> <PERSON><PERSON><PERSON>,  first woman of Indian origin to be canonized as a saint by the Catholic Church (b. 1910)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON><PERSON><PERSON>\">Saint <PERSON><PERSON><PERSON></a>, first woman of Indian origin to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON><PERSON><PERSON>\">Saint <PERSON><PERSON></a>, first woman of Indian origin to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saint_Alphonsa"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1957", "text": "<PERSON>, American economist, social worker, and educator (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, social worker, and educator (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist, social worker, and educator (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German-Israeli scholar and academic (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli scholar and academic (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Israeli scholar and academic (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German engineer, founder of Borgward Group", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer, founder of Borgward Group", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer, founder of Borgward Group", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese author and critic (b. 1894)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Edogawa_Ranpo\" title=\"Edogawa Ranpo\"><PERSON><PERSON></a>, Japanese author and critic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edogawa_Ranpo\" title=\"Edogawa Ranpo\"><PERSON><PERSON></a>, Japanese author and critic (b. 1894)", "links": [{"title": "<PERSON><PERSON> Ranpo", "link": "https://wikipedia.org/wiki/Edogawa_Ranpo"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Jordanian politician (b. 1875)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Attallah_Suheimat\" title=\"Attallah Suheimat\">Attallah Suheimat</a>, Jordanian politician (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attallah_Suheimat\" title=\"Attallah Suheimat\">Attallah Suheimat</a>, Jordanian politician (b. 1875)", "links": [{"title": "Attallah Suheimat", "link": "https://wikipedia.org/wiki/Attallah_Suheimat"}]}, {"year": "1967", "text": "<PERSON>, American lieutenant and pilot (b. 1942)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1879)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1969", "text": "<PERSON>, Cuban physician and politician, 6th President of Cuba (b. 1882)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Grau\" title=\"<PERSON>\"><PERSON></a>, Cuban physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Grau\" title=\"<PERSON>\"><PERSON></a>, Cuban physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Grau"}, {"title": "President of Cuba", "link": "https://wikipedia.org/wiki/President_of_Cuba"}]}, {"year": "1969", "text": "<PERSON>, American composer (b. 1910)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian colonel and diplomat (b. 1890)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and diplomat (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel and diplomat (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>grave"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American baseball player (b. 1908)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ag"}]}, {"year": "1971", "text": "<PERSON>, French-American minister and painter (b. 1898)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American minister and painter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American minister and painter (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American soprano and actress (b. 1903)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Finnish dancer and choreographer (b. 1881)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish dancer and choreographer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish dancer and choreographer (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player and coach (b. 1902)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1902)\" title=\"<PERSON> (American football, born 1902)\"><PERSON></a>, American football player and coach (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1902)\" title=\"<PERSON> (American football, born 1902)\"><PERSON></a>, American football player and coach (b. 1902)", "links": [{"title": "<PERSON> (American football, born 1902)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1902)"}]}, {"year": "1979", "text": "<PERSON>, English conductor and bandleader (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English conductor and bandleader (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English conductor and bandleader (b. 1898)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1980", "text": "<PERSON>, Austrian-born American logician and philosopher (b. 1903)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Rose Rand\"><PERSON></a>, Austrian-born American logician and philosopher (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rose_Rand\" title=\"Rose Rand\"><PERSON></a>, Austrian-born American logician and philosopher (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_Rand"}]}, {"year": "1981", "text": "<PERSON>, American priest and missionary (b. 1935)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and missionary (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and missionary (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter and pianist (b. 1953)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian politician, 31st Premier of New South Wales (b. 1909)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1990", "text": "<PERSON>, English actress (b. 1908)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Estonian actor and director (b. 1931)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Sulev_N%C3%B5mmik\" title=\"<PERSON><PERSON> Nõmmik\"><PERSON><PERSON></a>, Estonian actor and director (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sulev_N%C3%B5mmik\" title=\"<PERSON><PERSON> Nõmmik\"><PERSON><PERSON></a>, Estonian actor and director (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sulev_N%C3%B5mmik"}]}, {"year": "1993", "text": "<PERSON>, Irish motorcycle racer (b. 1903)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish motorcycle racer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stanley Woods\"><PERSON></a>, Irish motorcycle racer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ornithologist and academic (b. 1908)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, English actress (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Thai lawyer and politician, 6th Prime Minister of Thailand (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Seni_Pramoj"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish poet and author (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English boxer, actor, and author (b. 1949)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, actor, and author (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, actor, and author (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Consalvo_Sanesi\" title=\"Consalvo Sanesi\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Consalvo_Sanesi\" title=\"Consalvo Sanesi\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1911)", "links": [{"title": "Consalvo Sanesi", "link": "https://wikipedia.org/wiki/Consalvo_Sanesi"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Norwegian economist and mathematician, Nobel Prize laureate (b. 1911)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian economist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tryg<PERSON>_<PERSON>o"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2000", "text": "<PERSON>, Dutch-American physicist and historian (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist and historian (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American physicist and historian (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Bangladeshi poet, author, and critic (b. 1943)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi poet, author, and critic (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi poet, author, and critic (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Archer <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Archer <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2003", "text": "<PERSON>, Irish activist and politician (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish activist and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish activist and politician (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English biologist and biophysicist, Nobel Prize laureate (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Italian journalist and author (b. 1938)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English author (b. 1948)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Belgian-American wrestler and trainer (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American wrestler and trainer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American wrestler and trainer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American soldier and pilot (b. 1961)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American football player and coach (b. 1941)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (b. 1941)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2011", "text": "<PERSON>, Libyan general (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Libyan general (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Libyan general (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, New Zealand-English pianist and educator (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English pianist and educator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English pianist and educator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Austrian mountaineer (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian mountaineer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian mountaineer (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "2012", "text": "<PERSON>, American race car driver and engineer (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American race car driver and engineer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American race car driver and engineer (b. 1911)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "2013", "text": "<PERSON>,  Ugandan general and politician, 3rd Vice President of Uganda (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Uganda\" title=\"Vice President of Uganda\">Vice President of Uganda</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Vice_President_of_Uganda\" title=\"Vice President of Uganda\">Vice President of Uganda</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of Uganda", "link": "https://wikipedia.org/wiki/Vice_President_of_Uganda"}]}, {"year": "2013", "text": "<PERSON>, American actress and singer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Dutch jazz singer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch jazz singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch jazz singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American captain and politician, 13th United States Ambassador to the United Nations (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Scottish footballer and manager (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Azerbaijani footballer and manager (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Alak<PERSON>_Ma<PERSON>do<PERSON>\" title=\"Alak<PERSON> Mammadov\"><PERSON><PERSON><PERSON></a>, Azerbaijani footballer and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alak<PERSON>_<PERSON>do<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mammadov\"><PERSON><PERSON><PERSON></a>, Azerbaijani footballer and manager (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Polish businessman (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businessman (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish businessman (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Vanuatuan politician, 6th Prime Minister of Vanuatu (b. 1954)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vanuatuan politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu\" title=\"Prime Minister of Vanuatu\">Prime Minister of Vanuatu</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Vanuatu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Vanuatu"}]}, {"year": "2015", "text": "<PERSON>, South African cricketer and coach (b. 1949)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Beninese politician (b. 1918)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese politician (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian Bengali fiction writer and socio-political activist (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Bengali fiction writer and socio-political activist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian Bengali fiction writer and socio-political activist (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Devi"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Dutch footballer (b. 1959)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_van_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Filipino record holder (b. 1993)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lawing\" title=\"Junrey Balawing\"><PERSON><PERSON></a>, Filipino record holder (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>law<PERSON>\" title=\"Junrey Balawing\"><PERSON><PERSON></a>, Filipino record holder (b. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ing"}]}, {"year": "2021", "text": "<PERSON>, American musician (b. 1949)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Hill\"><PERSON></a>, American musician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dusty Hill\"><PERSON></a>, American musician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, British actor (b. 1928)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Scottish television personality, teacher and coach (b. 1931)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_personality)\" class=\"mw-redirect\" title=\"<PERSON> (TV personality)\"><PERSON></a>, Scottish television personality, teacher and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(TV_personality)\" class=\"mw-redirect\" title=\"<PERSON> (TV personality)\"><PERSON></a>, Scottish television personality, teacher and coach (b. 1931)", "links": [{"title": "<PERSON> (TV personality)", "link": "https://wikipedia.org/wiki/<PERSON>_(TV_personality)"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1969)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doug <PERSON>\"><PERSON></a>, American baseball player (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Doug Creek\"><PERSON></a>, American baseball player (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Dominican baseball player (b. 1993)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Reyes_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American author (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>an<PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>an<PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}