{"date": "December 6", "url": "https://wikipedia.org/wiki/December_6", "data": {"Events": [{"year": "1060", "text": "<PERSON><PERSON><PERSON> <PERSON> is crowned king of Hungary.", "html": "1060 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary\" title=\"Béla I of Hungary\"><PERSON><PERSON><PERSON> <PERSON></a> is crowned king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_in_the_Middle_Ages\" class=\"mw-redirect\" title=\"Kingdom of Hungary in the Middle Ages\">Hungary</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary\" title=\"Béla I of Hungary\"><PERSON><PERSON><PERSON> <PERSON></a> is crowned king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary_in_the_Middle_Ages\" class=\"mw-redirect\" title=\"Kingdom of Hungary in the Middle Ages\">Hungary</a>.", "links": [{"title": "Béla I of Hungary", "link": "https://wikipedia.org/wiki/B%C3%A9la_I_of_Hungary"}, {"title": "Kingdom of Hungary in the Middle Ages", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary_in_the_Middle_Ages"}]}, {"year": "1240", "text": "Mongol invasion of Rus': Kyiv, defended by Voivode <PERSON><PERSON><PERSON>, falls to the Mongols under <PERSON><PERSON>.", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Mongol_invasion_of_Rus%27\" class=\"mw-redirect\" title=\"Mongol invasion of Rus'\">Mongol invasion of Rus'</a>: <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, defended by <a href=\"https://wikipedia.org/wiki/Voivode_Dmytro\" class=\"mw-redirect\" title=\"Voivode Dmytro\">Voivode Dmytro</a>, <a href=\"https://wikipedia.org/wiki/Siege_of_Kiev_(1240)\" title=\"Siege of Kiev (1240)\">falls</a> to the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khan\" title=\"<PERSON>u Khan\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_invasion_of_Rus%27\" class=\"mw-redirect\" title=\"Mongol invasion of Rus'\">Mongol invasion of Rus'</a>: <a href=\"https://wikipedia.org/wiki/Kyiv\" title=\"Kyiv\">Kyiv</a>, defended by <a href=\"https://wikipedia.org/wiki/Voivode_Dmytro\" class=\"mw-redirect\" title=\"Voivode Dmytro\">Voivode Dmytro</a>, <a href=\"https://wikipedia.org/wiki/Siege_of_Kiev_(1240)\" title=\"Siege of Kiev (1240)\">falls</a> to the <a href=\"https://wikipedia.org/wiki/Mongols\" title=\"Mongols\">Mongols</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>u_Khan\" title=\"Batu Khan\"><PERSON><PERSON></a>.", "links": [{"title": "Mongol invasion of Rus'", "link": "https://wikipedia.org/wiki/Mongol_invasion_of_Rus%27"}, {"title": "Kyiv", "link": "https://wikipedia.org/wiki/Kyiv"}, {"title": "Voivode Dmytro", "link": "https://wikipedia.org/wiki/Voivode_Dmytro"}, {"title": "Siege of Kiev (1240)", "link": "https://wikipedia.org/wiki/Siege_of_Kiev_(1240)"}, {"title": "Mongols", "link": "https://wikipedia.org/wiki/Mongols"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1492", "text": "After exploring the island of Cuba (which he had mistaken for Japan) for gold, <PERSON> lands on an island he names Hispaniola.", "html": "1492 - After exploring the island of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> (which he had mistaken for <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>) for gold, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> lands on an island he names <a href=\"https://wikipedia.org/wiki/Hispaniola\" title=\"Hispaniola\">Hispaniola</a>.", "no_year_html": "After exploring the island of <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> (which he had mistaken for <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>) for gold, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christopher Columbus\"><PERSON></a> lands on an island he names <a href=\"https://wikipedia.org/wiki/Hispaniola\" title=\"Hispaniola\">Hispaniola</a>.", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hispaniola", "link": "https://wikipedia.org/wiki/Hispaniola"}]}, {"year": "1534", "text": "The city of Quito in Ecuador is founded by Spanish settlers led by <PERSON><PERSON><PERSON><PERSON>.", "html": "1534 - The city of <a href=\"https://wikipedia.org/wiki/Quito\" title=\"Quito\">Quito</a> in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> is founded by Spanish settlers led by <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"Sebastián de Belalcázar\"><PERSON><PERSON><PERSON><PERSON>al<PERSON>ázar</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Quito\" title=\"Quito\">Quito</a> in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> is founded by Spanish settlers led by <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar\" title=\"Sebas<PERSON>án de Belalcázar\"><PERSON><PERSON><PERSON><PERSON> Belalcázar</a>.", "links": [{"title": "Quito", "link": "https://wikipedia.org/wiki/Quito"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "<PERSON><PERSON><PERSON><PERSON> de Belalcázar", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_de_Belalc%C3%A1zar"}]}, {"year": "1648", "text": "Pride's Purge removes royalist sympathizers from Parliament so that the High Court of Justice could put the King on trial.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/Pride%27s_Purge\" title=\"Pride's Purge\">Pride's Purge</a> removes royalist sympathizers from Parliament so that the <a href=\"https://wikipedia.org/wiki/High_Court_of_Justice_(1649)\" class=\"mw-redirect\" title=\"High Court of Justice (1649)\">High Court of Justice</a> could put the King on trial.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pride%27s_Purge\" title=\"Pride's Purge\">Pride's Purge</a> removes royalist sympathizers from Parliament so that the <a href=\"https://wikipedia.org/wiki/High_Court_of_Justice_(1649)\" class=\"mw-redirect\" title=\"High Court of Justice (1649)\">High Court of Justice</a> could put the King on trial.", "links": [{"title": "Pride's Purge", "link": "https://wikipedia.org/wiki/Pride%27s_Purge"}, {"title": "High Court of Justice (1649)", "link": "https://wikipedia.org/wiki/High_Court_of_Justice_(1649)"}]}, {"year": "1704", "text": "Battle of Chamkaur: During the Mughal-Sikh Wars, an outnumbered Sikh Khalsa defeats a Mughal army.", "html": "1704 - <a href=\"https://wikipedia.org/wiki/Battle_of_Chamkaur_(1704)\" class=\"mw-redirect\" title=\"Battle of Chamkaur (1704)\">Battle of Chamkaur</a>: During the <a href=\"https://wikipedia.org/wiki/List_of_battles_between_Mughals_and_Sikhs\" title=\"List of battles between Mughals and Sikhs\">Mughal-Sikh Wars</a>, an outnumbered <a href=\"https://wikipedia.org/wiki/Khalsa\" title=\"Khalsa\">Sikh Khalsa</a> defeats a <a href=\"https://wikipedia.org/wiki/Mughal_Empire\" title=\"Mughal Empire\">Mughal</a> army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Chamkaur_(1704)\" class=\"mw-redirect\" title=\"Battle of Chamkaur (1704)\">Battle of Chamkaur</a>: During the <a href=\"https://wikipedia.org/wiki/List_of_battles_between_Mughals_and_Sikhs\" title=\"List of battles between Mughals and Sikhs\">Mughal-Sikh Wars</a>, an outnumbered <a href=\"https://wikipedia.org/wiki/Khalsa\" title=\"Khalsa\">Sikh Khalsa</a> defeats a <a href=\"https://wikipedia.org/wiki/Mughal_Empire\" title=\"Mughal Empire\">Mughal</a> army.", "links": [{"title": "Battle of Chamkaur (1704)", "link": "https://wikipedia.org/wiki/Battle_of_Chamkaur_(1704)"}, {"title": "List of battles between Mughals and Sikhs", "link": "https://wikipedia.org/wiki/List_of_battles_between_Mughals_and_Sikhs"}, {"title": "Khalsa", "link": "https://wikipedia.org/wiki/Khalsa"}, {"title": "Mughal Empire", "link": "https://wikipedia.org/wiki/Mughal_Empire"}]}, {"year": "1745", "text": "<PERSON>'s army begins retreat during the second Jacobite Rising.", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army begins retreat during the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">second Jacobite Rising</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s army begins retreat during the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">second Jacobite Rising</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Jacobite rising of 1745", "link": "https://wikipedia.org/wiki/Jacobite_rising_of_1745"}]}, {"year": "1790", "text": "The U.S. Congress moves from New York City to Philadelphia.", "html": "1790 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> moves from <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> to <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> moves from <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> to <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}]}, {"year": "1803", "text": "Five French warships attempting to escape the Royal Naval blockade of Saint-Domingue are all seized by British warships, signifying the end of the Haitian Revolution.", "html": "1803 - Five <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French warships</a> attempting to escape the <a href=\"https://wikipedia.org/wiki/Blockade_of_Saint-Domingue\" title=\"Blockade of Saint-Domingue\">Royal Naval blockade of Saint-Domingue</a> are all seized by British warships, signifying the end of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "no_year_html": "Five <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French warships</a> attempting to escape the <a href=\"https://wikipedia.org/wiki/Blockade_of_Saint-Domingue\" title=\"Blockade of Saint-Domingue\">Royal Naval blockade of Saint-Domingue</a> are all seized by British warships, signifying the end of the <a href=\"https://wikipedia.org/wiki/Haitian_Revolution\" title=\"Haitian Revolution\">Haitian Revolution</a>.", "links": [{"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Blockade of Saint-Domingue", "link": "https://wikipedia.org/wiki/Blockade_of_Saint-Domingue"}, {"title": "Haitian Revolution", "link": "https://wikipedia.org/wiki/Haitian_Revolution"}]}, {"year": "1865", "text": "Georgia ratifies the 13th Amendment to the U.S. Constitution.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> ratifies the <a href=\"https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"13th Amendment to the U.S. Constitution\">13th Amendment to the U.S. Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> ratifies the <a href=\"https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution\" class=\"mw-redirect\" title=\"13th Amendment to the U.S. Constitution\">13th Amendment to the U.S. Constitution</a>.", "links": [{"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "13th Amendment to the U.S. Constitution", "link": "https://wikipedia.org/wiki/13th_Amendment_to_the_U.S._Constitution"}]}, {"year": "1882", "text": "Transit of Venus, second and last of the 19th century.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/1882_transit_of_Venus\" title=\"1882 transit of Venus\">Transit of Venus</a>, second and last of the <a href=\"https://wikipedia.org/wiki/19th_century\" title=\"19th century\">19th century</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1882_transit_of_Venus\" title=\"1882 transit of Venus\">Transit of Venus</a>, second and last of the <a href=\"https://wikipedia.org/wiki/19th_century\" title=\"19th century\">19th century</a>.", "links": [{"title": "1882 transit of Venus", "link": "https://wikipedia.org/wiki/1882_transit_of_Venus"}, {"title": "19th century", "link": "https://wikipedia.org/wiki/19th_century"}]}, {"year": "1884", "text": "The Washington Monument in Washington, D.C., is completed.", "html": "1884 - The <a href=\"https://wikipedia.org/wiki/Washington_Monument\" title=\"Washington Monument\">Washington Monument</a> in Washington, D.C., is completed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Washington_Monument\" title=\"Washington Monument\">Washington Monument</a> in Washington, D.C., is completed.", "links": [{"title": "Washington Monument", "link": "https://wikipedia.org/wiki/Washington_Monument"}]}, {"year": "1897", "text": "London becomes the world's first city to host licensed taxicabs.", "html": "1897 - London becomes the world's first city to host licensed <a href=\"https://wikipedia.org/wiki/Taxicab\" class=\"mw-redirect\" title=\"Taxicab\">taxicabs</a>.", "no_year_html": "London becomes the world's first city to host licensed <a href=\"https://wikipedia.org/wiki/Taxicab\" class=\"mw-redirect\" title=\"Taxicab\">taxicabs</a>.", "links": [{"title": "Taxicab", "link": "https://wikipedia.org/wiki/Taxicab"}]}, {"year": "1904", "text": "<PERSON> articulated his \"Corollary\" to the Monroe Doctrine, stating that the U.S. would intervene in the Western Hemisphere should Latin American governments prove incapable or unstable.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> articulated his <a href=\"https://wikipedia.org/wiki/<PERSON>_Corollary\" title=\"<PERSON> Corollary\">\"Corollary\" to the Monroe Doctrine</a>, stating that the U.S. would intervene in the Western Hemisphere should Latin American governments prove incapable or unstable.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> articulated his <a href=\"https://wikipedia.org/wiki/<PERSON>_Corollary\" title=\"<PERSON> Corollary\">\"Corollary\" to the Monroe Doctrine</a>, stating that the U.S. would intervene in the Western Hemisphere should Latin American governments prove incapable or unstable.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Roosevelt Corollary", "link": "https://wikipedia.org/wiki/Roosevelt_Corollary"}]}, {"year": "1907", "text": "A coal mine explosion at Monongah, West Virginia, kills 362 workers.", "html": "1907 - A <a href=\"https://wikipedia.org/wiki/Monongah_mining_disaster\" title=\"Monongah mining disaster\">coal mine explosion</a> at <a href=\"https://wikipedia.org/wiki/Monongah,_West_Virginia\" title=\"Monongah, West Virginia\">Monongah, West Virginia</a>, kills 362 workers.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Monongah_mining_disaster\" title=\"Monongah mining disaster\">coal mine explosion</a> at <a href=\"https://wikipedia.org/wiki/Monongah,_West_Virginia\" title=\"Monongah, West Virginia\">Monongah, West Virginia</a>, kills 362 workers.", "links": [{"title": "Monongah mining disaster", "link": "https://wikipedia.org/wiki/Monongah_mining_disaster"}, {"title": "Monongah, West Virginia", "link": "https://wikipedia.org/wiki/Monongah,_West_Virginia"}]}, {"year": "1912", "text": "The Nefertiti Bust is discovered.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Nefertiti_Bust\" title=\"Nefertiti Bust\">Nefertiti Bust</a> is discovered.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nefertiti_Bust\" title=\"Nefertiti Bust\">Nefertiti Bust</a> is discovered.", "links": [{"title": "Nefertiti Bust", "link": "https://wikipedia.org/wiki/Nefertiti_Bust"}]}, {"year": "1916", "text": "World War I: The Central Powers capture Bucharest.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Central Powers <a href=\"https://wikipedia.org/wiki/Battle_of_Bucharest\" title=\"Battle of Bucharest\">capture Bucharest</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The Central Powers <a href=\"https://wikipedia.org/wiki/Battle_of_Bucharest\" title=\"Battle of Bucharest\">capture Bucharest</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Bucharest", "link": "https://wikipedia.org/wiki/Battle_of_Bucharest"}]}, {"year": "1917", "text": "Finland declares independence from the Russian Empire.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Finnish_Declaration_of_Independence\" title=\"Finnish Declaration of Independence\">Finland declares independence</a> from <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">the Russian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Declaration_of_Independence\" title=\"Finnish Declaration of Independence\">Finland declares independence</a> from <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">the Russian Empire</a>.", "links": [{"title": "Finnish Declaration of Independence", "link": "https://wikipedia.org/wiki/Finnish_Declaration_of_Independence"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1917", "text": "Halifax Explosion: A munitions explosion near Halifax, Nova Scotia kills more than 1,900 people in the largest artificial explosion up to that time.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Halifax_Explosion\" title=\"Halifax Explosion\">Halifax Explosion</a>: A munitions explosion near <a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a> kills more than 1,900 people in the <a href=\"https://wikipedia.org/wiki/Largest_artificial_non-nuclear_explosions#Largest_accidental_artificial_non-nuclear_explosions_by_magnitude\" title=\"Largest artificial non-nuclear explosions\">largest artificial explosion up to that time</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halifax_Explosion\" title=\"Halifax Explosion\">Halifax Explosion</a>: A munitions explosion near <a href=\"https://wikipedia.org/wiki/Halifax,_Nova_Scotia\" title=\"Halifax, Nova Scotia\">Halifax, Nova Scotia</a> kills more than 1,900 people in the <a href=\"https://wikipedia.org/wiki/Largest_artificial_non-nuclear_explosions#Largest_accidental_artificial_non-nuclear_explosions_by_magnitude\" title=\"Largest artificial non-nuclear explosions\">largest artificial explosion up to that time</a>.", "links": [{"title": "Halifax Explosion", "link": "https://wikipedia.org/wiki/Halifax_Explosion"}, {"title": "Halifax, Nova Scotia", "link": "https://wikipedia.org/wiki/Halifax,_Nova_Scotia"}, {"title": "Largest artificial non-nuclear explosions", "link": "https://wikipedia.org/wiki/Largest_artificial_non-nuclear_explosions#Largest_accidental_artificial_non-nuclear_explosions_by_magnitude"}]}, {"year": "1917", "text": "World War I: <PERSON> is the first American destroyer to be sunk by enemy action when it is torpedoed by German submarine SM U-53.", "html": "1917 - World War I: <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON>_(DD-61)\" title=\"USS <PERSON> (DD-61)\">USS <i><PERSON></i></a> is the first American <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a> to be sunk by enemy action when it is <a href=\"https://wikipedia.org/wiki/Torpedo\" title=\"Torpedo\">torpedoed</a> by German submarine <a href=\"https://wikipedia.org/wiki/SM_U-53\" title=\"SM U-53\">SM <i>U-53</i></a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(DD-61)\" title=\"<PERSON> (DD-61)\">USS <i><PERSON></i></a> is the first American <a href=\"https://wikipedia.org/wiki/Destroyer\" title=\"Destroyer\">destroyer</a> to be sunk by enemy action when it is <a href=\"https://wikipedia.org/wiki/Torpedo\" title=\"Torpedo\">torpedoed</a> by German submarine <a href=\"https://wikipedia.org/wiki/SM_U-53\" title=\"SM U-53\">SM <i>U-53</i></a>.", "links": [{"title": "<PERSON> (DD-61)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(DD-61)"}, {"title": "Destroyer", "link": "https://wikipedia.org/wiki/Destroyer"}, {"title": "Torped<PERSON>", "link": "https://wikipedia.org/wiki/Torpedo"}, {"title": "SM U-53", "link": "https://wikipedia.org/wiki/SM_U-53"}]}, {"year": "1921", "text": "The Anglo-Irish Treaty is signed in London by British and Irish representatives.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> is signed in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> by British and Irish representatives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> is signed in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> by British and Irish representatives.", "links": [{"title": "Anglo-Irish Treaty", "link": "https://wikipedia.org/wiki/Anglo-Irish_Treaty"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1922", "text": "One year to the day after the signing of the Anglo-Irish Treaty Ireland is partitioned. Northern Ireland and the Irish Free State come into existence.", "html": "1922 - One year to the day after the signing of the <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> Ireland is <a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">partitioned</a>. <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> come into existence.", "no_year_html": "One year to the day after the signing of the <a href=\"https://wikipedia.org/wiki/Anglo-Irish_Treaty\" title=\"Anglo-Irish Treaty\">Anglo-Irish Treaty</a> Ireland is <a href=\"https://wikipedia.org/wiki/Partition_of_Ireland\" title=\"Partition of Ireland\">partitioned</a>. <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> and the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> come into existence.", "links": [{"title": "Anglo-Irish Treaty", "link": "https://wikipedia.org/wiki/Anglo-Irish_Treaty"}, {"title": "Partition of Ireland", "link": "https://wikipedia.org/wiki/Partition_of_Ireland"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}]}, {"year": "1928", "text": "The government of Colombia sends military forces to suppress a month-long strike by United Fruit Company workers, resulting in an unknown number of deaths.", "html": "1928 - The government of Colombia <a href=\"https://wikipedia.org/wiki/Banana_massacre\" class=\"mw-redirect\" title=\"Banana massacre\">sends military forces</a> to suppress a month-long <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> by <a href=\"https://wikipedia.org/wiki/United_Fruit_Company\" title=\"United Fruit Company\">United Fruit Company</a> workers, resulting in an unknown number of deaths.", "no_year_html": "The government of Colombia <a href=\"https://wikipedia.org/wiki/Banana_massacre\" class=\"mw-redirect\" title=\"Banana massacre\">sends military forces</a> to suppress a month-long <a href=\"https://wikipedia.org/wiki/Strike_action\" title=\"Strike action\">strike</a> by <a href=\"https://wikipedia.org/wiki/United_Fruit_Company\" title=\"United Fruit Company\">United Fruit Company</a> workers, resulting in an unknown number of deaths.", "links": [{"title": "Banana massacre", "link": "https://wikipedia.org/wiki/Banana_massacre"}, {"title": "Strike action", "link": "https://wikipedia.org/wiki/Strike_action"}, {"title": "United Fruit Company", "link": "https://wikipedia.org/wiki/United_Fruit_Company"}]}, {"year": "1933", "text": "In United States v. One Book Called Ulysses Judge <PERSON> rules that <PERSON>'s novel <PERSON> is not obscene despite coarse language and sexual content, a leading decision affirming free expression.", "html": "1933 - In <i><a href=\"https://wikipedia.org/wiki/United_States_v._One_Book_Called_Ulysses\" title=\"United States v. One Book Called Ulysses\">United States v. One Book Called Ulysses</a></i> Judge <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ool<PERSON>\" title=\"<PERSON>\"><PERSON></a> rules that <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s novel <i><a href=\"https://wikipedia.org/wiki/Ulysses_(novel)\" title=\"<PERSON> (novel)\">Ulysses</a></i> is not obscene despite coarse language and sexual content, a leading decision affirming free expression.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/United_States_v._One_Book_Called_Ulysses\" title=\"United States v. One Book Called Ulysses\">United States v. One Book Called Ulysses</a></i> Judge <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ool<PERSON>\" title=\"<PERSON>\"><PERSON></a> rules that <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s novel <i><a href=\"https://wikipedia.org/wiki/Ulysses_(novel)\" title=\"<PERSON> (novel)\"><PERSON></a></i> is not obscene despite coarse language and sexual content, a leading decision affirming free expression.", "links": [{"title": "United States v. One Book Called Ulysses", "link": "https://wikipedia.org/wiki/United_States_v._One_Book_Called_Ulysses"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (novel)", "link": "https://wikipedia.org/wiki/<PERSON>_(novel)"}]}, {"year": "1939", "text": "Winter War: The Red Army's advance on the Karelian Isthmus is stopped by Finns at the Mannerheim Line during the Battle of Taipale.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>'s advance on the <a href=\"https://wikipedia.org/wiki/Karelian_Isthmus\" title=\"Karelian Isthmus\">Karelian Isthmus</a> is stopped by Finns at the <a href=\"https://wikipedia.org/wiki/Mannerheim_Line\" title=\"Mannerheim Line\">Mannerheim Line</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_Taipale\" title=\"Battle of Taipale\">Battle of Taipale</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>'s advance on the <a href=\"https://wikipedia.org/wiki/Karelian_Isthmus\" title=\"Karelian Isthmus\">Karelian Isthmus</a> is stopped by Finns at the <a href=\"https://wikipedia.org/wiki/Mannerheim_Line\" title=\"Mannerheim Line\">Mannerheim Line</a> during the <a href=\"https://wikipedia.org/wiki/Battle_of_Taipale\" title=\"Battle of Taipale\">Battle of Taipale</a>.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Karelian Isthmus", "link": "https://wikipedia.org/wiki/Karelian_Isthmus"}, {"title": "Mannerheim Line", "link": "https://wikipedia.org/wiki/Mannerheim_Line"}, {"title": "Battle of Taipale", "link": "https://wikipedia.org/wiki/Battle_of_Taipale"}]}, {"year": "1941", "text": "World War II:  Camp X opens in Canada to begin training Allied secret agents for the war.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Camp_X\" title=\"Camp X\">Camp X</a> opens in Canada to begin training Allied secret agents for the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Camp_X\" title=\"Camp X\">Camp X</a> opens in Canada to begin training Allied secret agents for the war.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Camp X", "link": "https://wikipedia.org/wiki/Camp_X"}]}, {"year": "1956", "text": "A violent water polo match between Hungary and the USSR takes place during the 1956 Summer Olympics in Melbourne, against the backdrop of the Hungarian Revolution of 1956.", "html": "1956 - A violent <a href=\"https://wikipedia.org/wiki/Blood_in_the_Water_match\" title=\"Blood in the Water match\">water polo match</a> between Hungary and the USSR takes place during the <a href=\"https://wikipedia.org/wiki/1956_Summer_Olympics\" title=\"1956 Summer Olympics\">1956 Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, against the backdrop of the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution of 1956</a>.", "no_year_html": "A violent <a href=\"https://wikipedia.org/wiki/Blood_in_the_Water_match\" title=\"Blood in the Water match\">water polo match</a> between Hungary and the USSR takes place during the <a href=\"https://wikipedia.org/wiki/1956_Summer_Olympics\" title=\"1956 Summer Olympics\">1956 Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Melbourne\" title=\"Melbourne\">Melbourne</a>, against the backdrop of the <a href=\"https://wikipedia.org/wiki/Hungarian_Revolution_of_1956\" title=\"Hungarian Revolution of 1956\">Hungarian Revolution of 1956</a>.", "links": [{"title": "Blood in the Water match", "link": "https://wikipedia.org/wiki/Blood_in_the_Water_match"}, {"title": "1956 Summer Olympics", "link": "https://wikipedia.org/wiki/1956_Summer_Olympics"}, {"title": "Melbourne", "link": "https://wikipedia.org/wiki/Melbourne"}, {"title": "Hungarian Revolution of 1956", "link": "https://wikipedia.org/wiki/Hungarian_Revolution_of_1956"}]}, {"year": "1957", "text": "Project Vanguard: A launchpad explosion of Vanguard TV3 thwarts the first United States attempt to launch a satellite into Earth orbit.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Project_Vanguard\" title=\"Project Vanguard\">Project Vanguard</a>: A <a href=\"https://wikipedia.org/wiki/Launch_pad\" title=\"Launch pad\">launchpad</a> explosion of <a href=\"https://wikipedia.org/wiki/Vanguard_TV3\" class=\"mw-redirect\" title=\"Vanguard TV3\">Vanguard TV3</a> thwarts the first United States attempt to launch a <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> into Earth orbit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Vanguard\" title=\"Project Vanguard\">Project Vanguard</a>: A <a href=\"https://wikipedia.org/wiki/Launch_pad\" title=\"Launch pad\">launchpad</a> explosion of <a href=\"https://wikipedia.org/wiki/Vanguard_TV3\" class=\"mw-redirect\" title=\"Vanguard TV3\">Vanguard TV3</a> thwarts the first United States attempt to launch a <a href=\"https://wikipedia.org/wiki/Satellite\" title=\"Satellite\">satellite</a> into Earth orbit.", "links": [{"title": "Project Vanguard", "link": "https://wikipedia.org/wiki/Project_Vanguard"}, {"title": "Launch pad", "link": "https://wikipedia.org/wiki/Launch_pad"}, {"title": "Vanguard TV3", "link": "https://wikipedia.org/wiki/Vanguard_TV3"}, {"title": "Satellite", "link": "https://wikipedia.org/wiki/Satellite"}]}, {"year": "1967", "text": "<PERSON> performs the first human heart transplant in the United States.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first human heart transplant in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first human heart transplant in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "Altamont Free Concert: At a free concert performed by the Rolling Stones, eighteen-year old <PERSON> is stabbed to death by Hells Angels security guards.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Altamont_Free_Concert\" title=\"Altamont Free Concert\">Altamont Free Concert</a>: At a free concert performed by the <a href=\"https://wikipedia.org/wiki/Rolling_Stones\" class=\"mw-redirect\" title=\"Rolling Stones\">Rolling Stones</a>, eighteen-year old <PERSON> is stabbed to death by <a href=\"https://wikipedia.org/wiki/Hells_Angels\" title=\"Hells Angels\">Hells Angels</a> security guards.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Altamont_Free_Concert\" title=\"Altamont Free Concert\">Altamont Free Concert</a>: At a free concert performed by the <a href=\"https://wikipedia.org/wiki/Rolling_Stones\" class=\"mw-redirect\" title=\"Rolling Stones\">Rolling Stones</a>, eighteen-year old <PERSON> is stabbed to death by <a href=\"https://wikipedia.org/wiki/Hells_Angels\" title=\"Hells Angels\">Hells Angels</a> security guards.", "links": [{"title": "Altamont Free Concert", "link": "https://wikipedia.org/wiki/Altamont_Free_Concert"}, {"title": "Rolling Stones", "link": "https://wikipedia.org/wiki/Rolling_Stones"}, {"title": "Hells Angels", "link": "https://wikipedia.org/wiki/Hells_Angels"}]}, {"year": "1971", "text": "Pakistan severs diplomatic relations with India, initiating the Indo-Pakistani War of 1971.", "html": "1971 - Pakistan severs diplomatic relations with India, initiating the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War of 1971</a>.", "no_year_html": "Pakistan severs diplomatic relations with India, initiating the <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War of 1971</a>.", "links": [{"title": "Indo-Pakistani War of 1971", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971"}]}, {"year": "1973", "text": "The Twenty-fifth Amendment: The United States House of Representatives votes 387-35 to confirm <PERSON> as Vice President of the United States. (On November 27, the Senate confirmed him 92-3.)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">The Twenty-fifth Amendment</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> votes 387-35 to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>. (On <a href=\"https://wikipedia.org/wiki/November_27\" title=\"November 27\">November 27</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a> confirmed him 92-3.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution\" title=\"Twenty-fifth Amendment to the United States Constitution\">The Twenty-fifth Amendment</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> votes 387-35 to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>. (On <a href=\"https://wikipedia.org/wiki/November_27\" title=\"November 27\">November 27</a>, the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senate</a> confirmed him 92-3.)", "links": [{"title": "Twenty-fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twenty-fifth_Amendment_to_the_United_States_Constitution"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "November 27", "link": "https://wikipedia.org/wiki/November_27"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1975", "text": "The Troubles: Fleeing from the police, a Provisional IRA unit takes a British couple hostage in their flat on Balcombe Street, London, beginning a six-day siege.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Fleeing from the police, a <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> unit takes a British couple hostage in their flat on Balcombe Street, London, beginning a <a href=\"https://wikipedia.org/wiki/Balcombe_Street_siege\" title=\"Balcombe Street siege\">six-day siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Fleeing from the police, a <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> unit takes a British couple hostage in their flat on Balcombe Street, London, beginning a <a href=\"https://wikipedia.org/wiki/Balcombe_Street_siege\" title=\"Balcombe Street siege\">six-day siege</a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Balcombe Street siege", "link": "https://wikipedia.org/wiki/Balcombe_Street_siege"}]}, {"year": "1977", "text": "South Africa grants independence to Bophuthatswana, although it is not recognized by any other country.", "html": "1977 - South Africa grants independence to <a href=\"https://wikipedia.org/wiki/Bophuthatswana\" title=\"Bophuthatswana\">Bophuthatswana</a>, although it is not recognized by any other country.", "no_year_html": "South Africa grants independence to <a href=\"https://wikipedia.org/wiki/Bophuthatswana\" title=\"Bophuthatswana\">Bophuthatswana</a>, although it is not recognized by any other country.", "links": [{"title": "Bophuthatswana", "link": "https://wikipedia.org/wiki/Bophuthatswana"}]}, {"year": "1978", "text": "Spain ratifies the Spanish Constitution of 1978 in a referendum.", "html": "1978 - Spain ratifies the <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1978\" class=\"mw-redirect\" title=\"Spanish Constitution of 1978\">Spanish Constitution of 1978</a> in a referendum.", "no_year_html": "Spain ratifies the <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1978\" class=\"mw-redirect\" title=\"Spanish Constitution of 1978\">Spanish Constitution of 1978</a> in a referendum.", "links": [{"title": "Spanish Constitution of 1978", "link": "https://wikipedia.org/wiki/Spanish_Constitution_of_1978"}]}, {"year": "1982", "text": "The Troubles: The Irish National Liberation Army bombs a pub frequented by British soldiers in Ballykelly, Northern Ireland, killing  eleven soldiers and six civilians.", "html": "1982 - The Troubles: The <a href=\"https://wikipedia.org/wiki/Irish_National_Liberation_Army\" title=\"Irish National Liberation Army\">Irish National Liberation Army</a> <a href=\"https://wikipedia.org/wiki/Droppin_Well_bombing\" title=\"Droppin Well bombing\">bombs a pub</a> frequented by British soldiers in <a href=\"https://wikipedia.org/wiki/Ballykelly,_County_Londonderry\" title=\"Ballykelly, County Londonderry\">Ballykelly, Northern Ireland</a>, killing eleven soldiers and six civilians.", "no_year_html": "The Troubles: The <a href=\"https://wikipedia.org/wiki/Irish_National_Liberation_Army\" title=\"Irish National Liberation Army\">Irish National Liberation Army</a> <a href=\"https://wikipedia.org/wiki/Droppin_Well_bombing\" title=\"Droppin Well bombing\">bombs a pub</a> frequented by British soldiers in <a href=\"https://wikipedia.org/wiki/Ballykelly,_County_Londonderry\" title=\"Ballykelly, County Londonderry\">Ballykelly, Northern Ireland</a>, killing eleven soldiers and six civilians.", "links": [{"title": "Irish National Liberation Army", "link": "https://wikipedia.org/wiki/Irish_National_Liberation_Army"}, {"title": "Droppin Well bombing", "link": "https://wikipedia.org/wiki/Droppin_Well_bombing"}, {"title": "Ballykelly, County Londonderry", "link": "https://wikipedia.org/wiki/Ball<PERSON>lly,_County_Londonderry"}]}, {"year": "1989", "text": "The École Polytechnique massacre (or Montreal Massacre): <PERSON>, an anti-feminist gunman, murders 14 young women at the École Polytechnique in Montreal.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/%C3%89cole_Polytechnique_massacre\" title=\"École Polytechnique massacre\">École Polytechnique massacre</a> (or Montreal Massacre): <a href=\"https://wikipedia.org/wiki/<PERSON>_L%C3%A9pine\" title=\"<PERSON>\"><PERSON></a>, an anti-feminist gunman, murders 14 young women at the <a href=\"https://wikipedia.org/wiki/%C3%89cole_Polytechnique_de_Montr%C3%A9al\" class=\"mw-redirect\" title=\"École Polytechnique de Montréal\">École Polytechnique</a> in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/%C3%89cole_Polytechnique_massacre\" title=\"École Polytechnique massacre\">École Polytechnique massacre</a> (or Montreal Massacre): <a href=\"https://wikipedia.org/wiki/<PERSON>_L%C3%A9pine\" title=\"<PERSON>\"><PERSON></a>, an anti-feminist gunman, murders 14 young women at the <a href=\"https://wikipedia.org/wiki/%C3%89cole_Polytechnique_de_Montr%C3%A9al\" class=\"mw-redirect\" title=\"École Polytechnique de Montréal\">École Polytechnique</a> in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "links": [{"title": "École Polytechnique massacre", "link": "https://wikipedia.org/wiki/%C3%89cole_Polytechnique_massacre"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marc_L%C3%A9pine"}, {"title": "École Polytechnique de Montréal", "link": "https://wikipedia.org/wiki/%C3%89cole_Polytechnique_de_Montr%C3%A9al"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1990", "text": "A military jet of the Italian Air Force, abandoned by its pilot after an on-board fire, crashed into a high school near Bologna, Italy, killing 12 students and injuring 88 other people.", "html": "1990 - A military jet of the <a href=\"https://wikipedia.org/wiki/Italian_Air_Force\" title=\"Italian Air Force\">Italian Air Force</a>, abandoned by its pilot after an on-board fire, <a href=\"https://wikipedia.org/wiki/1990_Italian_Air_Force_MB-326_crash\" title=\"1990 Italian Air Force MB-326 crash\">crashed into a high school</a> near <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, Italy, killing 12 students and injuring 88 other people.", "no_year_html": "A military jet of the <a href=\"https://wikipedia.org/wiki/Italian_Air_Force\" title=\"Italian Air Force\">Italian Air Force</a>, abandoned by its pilot after an on-board fire, <a href=\"https://wikipedia.org/wiki/1990_Italian_Air_Force_MB-326_crash\" title=\"1990 Italian Air Force MB-326 crash\">crashed into a high school</a> near <a href=\"https://wikipedia.org/wiki/Bologna\" title=\"Bologna\">Bologna</a>, Italy, killing 12 students and injuring 88 other people.", "links": [{"title": "Italian Air Force", "link": "https://wikipedia.org/wiki/Italian_Air_Force"}, {"title": "1990 Italian Air Force MB-326 crash", "link": "https://wikipedia.org/wiki/1990_Italian_Air_Force_MB-326_crash"}, {"title": "Bologna", "link": "https://wikipedia.org/wiki/Bologna"}]}, {"year": "1991", "text": "Yugoslav Wars: In Croatia, forces of the Serb-dominated Yugoslav People's Army (JNA) heaviest bombardment of Dubrovnik during a siege of seven months.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: In <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, forces of the Serb-dominated <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> (JNA) heaviest bombardment of <a href=\"https://wikipedia.org/wiki/Dubrovnik\" title=\"Dubrovnik\">Dubrovnik</a> during a siege of seven months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: In <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a>, forces of the Serb-dominated <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> (JNA) heaviest bombardment of <a href=\"https://wikipedia.org/wiki/Dubrovnik\" title=\"Dubrovnik\">Dubrovnik</a> during a siege of seven months.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Yugoslav People's Army", "link": "https://wikipedia.org/wiki/Yugoslav_People%27s_Army"}, {"title": "Dubrovnik", "link": "https://wikipedia.org/wiki/Dubrovnik"}]}, {"year": "1992", "text": "The Babri Masjid in Ayodhya, India, is demolished, leading to widespread riots causing the death of over 1,500 people.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Babri_Masjid\" title=\"Babri Masjid\">Babri Masjid</a> in <a href=\"https://wikipedia.org/wiki/Ayodhya\" title=\"Ayodhya\">Ayodhya</a>, India, is <a href=\"https://wikipedia.org/wiki/Demolition_of_the_Babri_Masjid\" title=\"Demolition of the Babri Masjid\">demolished</a>, leading to widespread riots causing the death of over 1,500 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Babri_Masjid\" title=\"Babri Masjid\">Babri Masjid</a> in <a href=\"https://wikipedia.org/wiki/Ayodhya\" title=\"Ayodhya\">Ayodhya</a>, India, is <a href=\"https://wikipedia.org/wiki/Demolition_of_the_Babri_Masjid\" title=\"Demolition of the Babri Masjid\">demolished</a>, leading to widespread riots causing the death of over 1,500 people.", "links": [{"title": "Babri Masjid", "link": "https://wikipedia.org/wiki/Babri_Masjid"}, {"title": "Ayodhya", "link": "https://wikipedia.org/wiki/Ayodhya"}, {"title": "Demolition of the Babri Masjid", "link": "https://wikipedia.org/wiki/Demolition_of_the_Babri_Masjid"}]}, {"year": "1995", "text": "The United States Food and Drug Administration approves Saquinavir, the first protease inhibitor to treat HIV/AIDS. Within 2 years of its approval, annual deaths from AIDS in the United States fall from over 50,000 to approximately 18,000.", "html": "1995 - The United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> approves <a href=\"https://wikipedia.org/wiki/Saquinavir\" title=\"Saquinavir\"><PERSON><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Protease_inhibitor_(pharmacology)\" title=\"Protease inhibitor (pharmacology)\">protease inhibitor</a> to treat <a href=\"https://wikipedia.org/wiki/HIV/AIDS\" title=\"HIV/AIDS\">HIV/AIDS</a>. Within 2 years of its approval, annual deaths from AIDS in the United States fall from over 50,000 to approximately 18,000.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Food_and_Drug_Administration\" title=\"Food and Drug Administration\">Food and Drug Administration</a> approves <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>avir\" title=\"Saquinavir\"><PERSON><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Protease_inhibitor_(pharmacology)\" title=\"Protease inhibitor (pharmacology)\">protease inhibitor</a> to treat <a href=\"https://wikipedia.org/wiki/HIV/AIDS\" title=\"HIV/AIDS\">HIV/AIDS</a>. Within 2 years of its approval, annual deaths from AIDS in the United States fall from over 50,000 to approximately 18,000.", "links": [{"title": "Food and Drug Administration", "link": "https://wikipedia.org/wiki/Food_and_Drug_Administration"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r"}, {"title": "Protease inhibitor (pharmacology)", "link": "https://wikipedia.org/wiki/Protease_inhibitor_(pharmacology)"}, {"title": "HIV/AIDS", "link": "https://wikipedia.org/wiki/HIV/AIDS"}]}, {"year": "1998", "text": "in Venezuela, <PERSON> is victorious in presidential elections.", "html": "1998 - in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is victorious in presidential elections.", "no_year_html": "in <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is victorious in presidential elections.", "links": [{"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}]}, {"year": "1999", "text": "A&M Records, Inc. v. Napster, Inc.: The Recording Industry Association of America sues the peer-to-peer file-sharing service Napster, alleging copyright infringement.", "html": "1999 - <i><a href=\"https://wikipedia.org/wiki/A%26M_Records,_Inc._v._Napster,_Inc._(2000)\" class=\"mw-redirect\" title=\"A&amp;M Records, Inc. v. Napster, Inc. (2000)\">A&amp;M Records, Inc. v. Napster, Inc.</a></i>: The <a href=\"https://wikipedia.org/wiki/Recording_Industry_Association_of_America\" title=\"Recording Industry Association of America\">Recording Industry Association of America</a> sues the <a href=\"https://wikipedia.org/wiki/Peer-to-peer\" title=\"Peer-to-peer\">peer-to-peer</a> <a href=\"https://wikipedia.org/wiki/File_sharing\" title=\"File sharing\">file-sharing</a> service <a href=\"https://wikipedia.org/wiki/Napster\" title=\"Napster\"><PERSON>ps<PERSON></a>, alleging <a href=\"https://wikipedia.org/wiki/Copyright_infringement\" title=\"Copyright infringement\">copyright infringement</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/A%26M_Records,_Inc._v._Napster,_Inc._(2000)\" class=\"mw-redirect\" title=\"A&amp;M Records, Inc. v. Napster, Inc. (2000)\">A&amp;M Records, Inc. v. Napster, Inc.</a></i>: The <a href=\"https://wikipedia.org/wiki/Recording_Industry_Association_of_America\" title=\"Recording Industry Association of America\">Recording Industry Association of America</a> sues the <a href=\"https://wikipedia.org/wiki/Peer-to-peer\" title=\"Peer-to-peer\">peer-to-peer</a> <a href=\"https://wikipedia.org/wiki/File_sharing\" title=\"File sharing\">file-sharing</a> service <a href=\"https://wikipedia.org/wiki/Napster\" title=\"Napster\"><PERSON>ps<PERSON></a>, alleging <a href=\"https://wikipedia.org/wiki/Copyright_infringement\" title=\"Copyright infringement\">copyright infringement</a>.", "links": [{"title": "A&M Records, Inc. v. Napster, Inc. (2000)", "link": "https://wikipedia.org/wiki/A%26M_Records,_Inc._v._Napster,_Inc._(2000)"}, {"title": "Recording Industry Association of America", "link": "https://wikipedia.org/wiki/Recording_Industry_Association_of_America"}, {"title": "Peer-to-peer", "link": "https://wikipedia.org/wiki/Peer-to-peer"}, {"title": "File sharing", "link": "https://wikipedia.org/wiki/File_sharing"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Napster"}, {"title": "Copyright infringement", "link": "https://wikipedia.org/wiki/Copyright_infringement"}]}, {"year": "2005", "text": "An Iranian Air Force C-130 military transport aircraft crashes into a ten-floor apartment building in a residential area of Tehran, killing all 94 on board and 12 more on the ground.", "html": "2005 - An <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130</a> military transport aircraft <a href=\"https://wikipedia.org/wiki/2005_Iranian_Air_Force_C-130_crash\" title=\"2005 Iranian Air Force C-130 crash\">crashes into a ten-floor apartment building</a> in a residential area of <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, killing all 94 on board and 12 more on the ground.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force\" title=\"Islamic Republic of Iran Air Force\">Iranian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130</a> military transport aircraft <a href=\"https://wikipedia.org/wiki/2005_Iranian_Air_Force_C-130_crash\" title=\"2005 Iranian Air Force C-130 crash\">crashes into a ten-floor apartment building</a> in a residential area of <a href=\"https://wikipedia.org/wiki/Tehran\" title=\"Tehran\">Tehran</a>, killing all 94 on board and 12 more on the ground.", "links": [{"title": "Islamic Republic of Iran Air Force", "link": "https://wikipedia.org/wiki/Islamic_Republic_of_Iran_Air_Force"}, {"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}, {"title": "2005 Iranian Air Force C-130 crash", "link": "https://wikipedia.org/wiki/2005_Iranian_Air_Force_C-130_crash"}, {"title": "Tehran", "link": "https://wikipedia.org/wiki/Tehran"}]}, {"year": "2006", "text": "NASA reveals photographs taken by Mars Global Surveyor suggesting the presence of liquid water on Mars.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> reveals photographs taken by <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a> suggesting the presence of liquid <a href=\"https://wikipedia.org/wiki/Water_on_Mars\" title=\"Water on Mars\">water on Mars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> reveals photographs taken by <a href=\"https://wikipedia.org/wiki/Mars_Global_Surveyor\" title=\"Mars Global Surveyor\">Mars Global Surveyor</a> suggesting the presence of liquid <a href=\"https://wikipedia.org/wiki/Water_on_Mars\" title=\"Water on Mars\">water on Mars</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Mars Global Surveyor", "link": "https://wikipedia.org/wiki/Mars_Global_Surveyor"}, {"title": "Water on Mars", "link": "https://wikipedia.org/wiki/Water_on_Mars"}]}, {"year": "2015", "text": "Venezuelan parliamentary election: For the first time in 17 years, the United Socialist Party of Venezuela loses its majority in parliament.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/2015_Venezuelan_parliamentary_election\" title=\"2015 Venezuelan parliamentary election\">Venezuelan parliamentary election</a>: For the first time in 17 years, the <a href=\"https://wikipedia.org/wiki/United_Socialist_Party_of_Venezuela\" title=\"United Socialist Party of Venezuela\">United Socialist Party of Venezuela</a> loses its majority in parliament.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2015_Venezuelan_parliamentary_election\" title=\"2015 Venezuelan parliamentary election\">Venezuelan parliamentary election</a>: For the first time in 17 years, the <a href=\"https://wikipedia.org/wiki/United_Socialist_Party_of_Venezuela\" title=\"United Socialist Party of Venezuela\">United Socialist Party of Venezuela</a> loses its majority in parliament.", "links": [{"title": "2015 Venezuelan parliamentary election", "link": "https://wikipedia.org/wiki/2015_Venezuelan_parliamentary_election"}, {"title": "United Socialist Party of Venezuela", "link": "https://wikipedia.org/wiki/United_Socialist_Party_of_Venezuela"}]}, {"year": "2017", "text": "<PERSON>'s administration officially announces the recognition of Jerusalem as the capital of Israel.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s administration officially announces <a href=\"https://wikipedia.org/wiki/United_States_recognition_of_Jerusalem_as_Israeli_capital\" class=\"mw-redirect\" title=\"United States recognition of Jerusalem as Israeli capital\">the recognition of Jerusalem as the capital of Israel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s administration officially announces <a href=\"https://wikipedia.org/wiki/United_States_recognition_of_Jerusalem_as_Israeli_capital\" class=\"mw-redirect\" title=\"United States recognition of Jerusalem as Israeli capital\">the recognition of Jerusalem as the capital of Israel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States recognition of Jerusalem as Israeli capital", "link": "https://wikipedia.org/wiki/United_States_recognition_of_Jerusalem_as_Israeli_capital"}]}], "Births": [{"year": "846", "text": "<PERSON>, Arabian 11th of the Twelve Imams (d. 874)", "html": "846 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arabian 11th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (d. 874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arabian 11th of <a href=\"https://wikipedia.org/wiki/The_Twelve_Imams\" class=\"mw-redirect\" title=\"The Twelve Imams\">the Twelve Imams</a> (d. 874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Twelve Imams", "link": "https://wikipedia.org/wiki/The_Twelve_Imams"}]}, {"year": "1285", "text": "<PERSON> of Castile (d. 1312)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a> (d. 1312)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_IV_of_Castile\" title=\"<PERSON> IV of Castile\"><PERSON> IV of Castile</a> (d. 1312)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_Castile"}]}, {"year": "1421", "text": "<PERSON> of England (d. 1471)", "html": "1421 - <a href=\"https://wikipedia.org/wiki/Henry_VI_of_England\" title=\"Henry VI of England\"><PERSON> of England</a> (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" title=\"Henry VI of England\"><PERSON> VI of England</a> (d. 1471)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1478", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian courtier, diplomat, and author (d. 1529)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_Castiglione\" title=\"Balda<PERSON><PERSON> Castiglione\"><PERSON><PERSON><PERSON><PERSON></a>, Italian courtier, diplomat, and author (d. 1529)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baldassa<PERSON>_Castiglione\" title=\"Balda<PERSON>re Castiglione\"><PERSON><PERSON><PERSON><PERSON></a>, Italian courtier, diplomat, and author (d. 1529)", "links": [{"title": "Balda<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Balda<PERSON><PERSON>_Castiglione"}]}, {"year": "1520", "text": "<PERSON>, queen of Poland (d. 1551)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Radziwi%C5%82%C5%82\" title=\"<PERSON>\"><PERSON></a>, queen of Poland (d. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Radziwi%C5%82%C5%82\" title=\"<PERSON>\"><PERSON></a>, queen of Poland (d. 1551)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barbara_Radziwi%C5%82%C5%82"}]}, {"year": "1545", "text": "<PERSON><PERSON>, Dutch historian and noble (d. 1604)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and noble (d. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch historian and noble (d. 1604)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1586", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian astronomer and physicist (d. 1670)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astronomer and physicist (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian astronomer and physicist (d. 1670)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_<PERSON><PERSON><PERSON>"}]}, {"year": "1592", "text": "<PERSON>, 1st Duke of Newcastle (d. 1676)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a> (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a> (d. 1676)", "links": [{"title": "<PERSON>, 1st Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle"}]}, {"year": "1608", "text": "<PERSON>, 1st Duke of Albemarle, English general and politician, Lord Lieutenant of Ireland (d. 1670)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON>, 1st Duke of Albemarle</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON>, 1st Duke of Albemarle</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1670)", "links": [{"title": "<PERSON>, 1st Duke of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1637", "text": "<PERSON>, English courtier and politician, 4th Colonial Governor of New York (d. 1714)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and politician, 4th <a href=\"https://wikipedia.org/wiki/Colonial_Governor_of_New_York\" class=\"mw-redirect\" title=\"Colonial Governor of New York\">Colonial Governor of New York</a> (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and politician, 4th <a href=\"https://wikipedia.org/wiki/Colonial_Governor_of_New_York\" class=\"mw-redirect\" title=\"Colonial Governor of New York\">Colonial Governor of New York</a> (d. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colonial Governor of New York", "link": "https://wikipedia.org/wiki/Colonial_Governor_of_New_York"}]}, {"year": "1640", "text": "<PERSON>, French historian and author (d. 1723)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and author (d. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, Maltese sculptor and painter (d. 1703)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor and painter (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese sculptor and painter (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON> of Savoy (d. 1712)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/Marie_Ad%C3%A9la%C3%AFde_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9la%C3%AFde_of_Savoy\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a> (d. 1712)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/Marie_Ad%C3%A9la%C3%AFde_of_Savoy"}]}, {"year": "1721", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French minister and politician (d. 1794)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Chr%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>n_de_Malesherbes\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Lamoignon de Malesherbes\"><PERSON><PERSON><PERSON><PERSON><PERSON> de Malesherbes</a>, French minister and politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Ch<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>n_de_Malesherbes\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Lamoignon de Malesherbes\"><PERSON><PERSON><PERSON><PERSON><PERSON> de Malesherbes</a>, French minister and politician (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>bes", "link": "https://wikipedia.org/wiki/Guillaume-Chr%C3%A9<PERSON>_<PERSON>_<PERSON>n_de_Malesherbes"}]}, {"year": "1721", "text": "<PERSON>, Scottish philologist and linguist (d. 1809)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philologist and linguist (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish philologist and linguist (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, British colonial administrator of India (d. 1818)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonial administrator of India (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British colonial administrator of India (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, American jurist and politician (d. 1844)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON>, French physicist and chemist (d. 1850)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON> of the Netherlands (d. 1849)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> II of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands\" title=\"<PERSON> II of the Netherlands\"><PERSON> of the Netherlands</a> (d. 1849)", "links": [{"title": "<PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Netherlands"}]}, {"year": "1803", "text": "<PERSON> of Saxony (d. 1829)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a> (d. 1829)", "links": [{"title": "<PERSON> of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_of_Saxony"}]}, {"year": "1805", "text": "<PERSON>, English-Australian politician, 4th Premier of South Australia (d. 1876)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1876)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1812", "text": "<PERSON>, English businessman and philanthropist (d. 1884)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, German-English philologist and orientalist (d. 1900)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/Max_M%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German-English philologist and orientalist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_M%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German-English philologist and orientalist (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_M%C3%BCller"}]}, {"year": "1827", "text": "<PERSON>, Australian biscuit manufacturer and founder of A<PERSON>tt's Biscuits (d. 1901)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biscuit_manufacturer)\" title=\"<PERSON> (biscuit manufacturer)\"><PERSON></a>, Australian biscuit manufacturer and founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Biscuits\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Biscuits\"><PERSON><PERSON><PERSON>'s Biscuits</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(biscuit_manufacturer)\" title=\"<PERSON> (biscuit manufacturer)\"><PERSON></a>, Australian biscuit manufacturer and founder of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Biscuits\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Biscuits\"><PERSON><PERSON><PERSON>'s Biscuits</a> (d. 1901)", "links": [{"title": "<PERSON> (biscuit manufacturer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(biscuit_manufacturer)"}, {"title": "<PERSON><PERSON><PERSON>'s Biscuits", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Biscuits"}]}, {"year": "1833", "text": "<PERSON>, American colonel (d. 1916)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, German chemist (d. 1910)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and soldier (d. 1870)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and soldier (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter and soldier (d. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Bazille"}]}, {"year": "1848", "text": "<PERSON>, Austrian astronomer (d. 1925)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian astronomer (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian astronomer (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, German field marshal (d. 1945)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German field marshal (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German field marshal (d. 1945)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Czech-Austrian botanist and academic (d. 1937)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian botanist and academic (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian botanist and academic (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON>, Indian historian and scholar (d. 1931)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian and scholar (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian and scholar (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American chemist and engineer (d. 1914)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American chemist and engineer (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON>\"><PERSON></a>, American chemist and engineer (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1946)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>_Hart\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American golfer and pilot (d. 1946)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and pilot (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and pilot (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, English mystic and author (d. 1941)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and author (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and author (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, German-American businessman, co-founded the Duesenberg Automobile & Motors Company (d. 1932)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Duesenberg\">Duesenberg Automobile &amp; Motors Company</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Duesenberg\">Duesenberg Automobile &amp; Motors Company</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Duesenberg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>berg"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Mexican politician (d. 1968)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Elvia_Carrillo_Puerto\" title=\"Elvia Carrillo Puerto\"><PERSON><PERSON></a>, Mexican politician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvia_Carrillo_Puerto\" title=\"Elvia Carrillo Puerto\"><PERSON><PERSON></a>, Mexican politician (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elvia_Carrillo_Puerto"}]}, {"year": "1882", "text": "<PERSON>, Australian cricketer (d. 1954)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Warren_<PERSON>ley"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, American author, playwright, and academic (d. 1973)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Cornelia_Meigs\" title=\"Cornelia Meigs\"><PERSON><PERSON><PERSON></a>, American author, playwright, and academic (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelia_Meigs\" title=\"Cornelia Meigs\"><PERSON><PERSON><PERSON></a>, American author, playwright, and academic (d. 1973)", "links": [{"title": "Cornelia <PERSON>", "link": "https://wikipedia.org/wiki/Cornelia_Meigs"}]}, {"year": "1886", "text": "<PERSON>, American soldier, author, and poet (d. 1918)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and poet (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, author, and poet (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, British actress (d. 1983)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American pianist and composer (d. 1960)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (d. 1960)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1888", "text": "<PERSON>, English actor, director, and screenwriter (d. 1949)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hay\"><PERSON></a>, English actor, director, and screenwriter (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Welsh occultist, psychologist, and author (d. 1946)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fortune\"><PERSON></a>, Welsh occultist, psychologist, and author (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dion Fortune\"><PERSON></a>, Welsh occultist, psychologist, and author (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic (d. 1951)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1890", "text": "<PERSON>, German painter and illustrator (d. 1955)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, English-Italian captain, poet, and author (d. 1969)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian captain, poet, and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian captain, poet, and author (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American admiral (d. 1984)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Homer_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Homer_N<PERSON>_<PERSON>\" title=\"Homer N<PERSON>\"><PERSON> <PERSON></a>, American admiral (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English author and poet (d. 1978)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American songwriter (d. 1983)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German-American photographer and journalist (d. 1995)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer and journalist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American photographer and journalist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Scottish-Australian politician, 37th Premier of Victoria (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Scottish-Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Scottish-Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1977)", "links": [{"title": "<PERSON> (Victorian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Victorian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Swedish sociologist and economist, Nobel Prize laureate (d. 1987)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish sociologist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, American actress, writer, and director (d. 1964)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, writer, and director (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, writer, and director (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American actress (d. 1974)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American photographer and academic (d. 1990)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American baseball player and manager (d. 1946)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, French-American journalist and pianist (d. 2007)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American journalist and pianist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American journalist and pianist (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%88ve_<PERSON><PERSON>e"}]}, {"year": "1905", "text": "<PERSON>, American journalist and author (d. 2001)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" class=\"mw-redirect\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (d. 2001)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1907", "text": "<PERSON>, American logician (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON></a>, American logician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, American logician (d. 1989)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1908", "text": "<PERSON><PERSON>, Austrian-American mathematician (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>eit<PERSON>\"><PERSON><PERSON></a>, Austrian-American mathematician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Freitag\"><PERSON><PERSON></a>, Austrian-American mathematician (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Freitag"}]}, {"year": "1908", "text": "<PERSON>, Swiss lawyer and politician, 69th President of the Swiss Confederation (d. 2003)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 69th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 69th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1908", "text": "<PERSON>, American gangster (d. 1934)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Baby_Face_Nelson\" title=\"Baby Face Nelson\"><PERSON> Face <PERSON></a>, American gangster (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Face_Nelson\" title=\"Baby Face Nelson\"><PERSON> Face <PERSON></a>, American gangster (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian runner (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Szab%C3%B3_(middle-distance_runner)\" title=\"<PERSON><PERSON><PERSON><PERSON> (middle-distance runner)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Szab%C3%B3_(middle-distance_runner)\" title=\"<PERSON><PERSON><PERSON><PERSON> (middle-distance runner)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (middle-distance runner)", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Szab%C3%B3_(middle-distance_runner)"}]}, {"year": "1909", "text": "<PERSON><PERSON>, American religious leader (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jeffs\"><PERSON><PERSON></a>, American religious leader (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jeffs\"><PERSON><PERSON></a>, American religious leader (d. 2002)", "links": [{"title": "<PERSON><PERSON> Jeffs", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jeff<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian cricketer and sportscaster (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American historian, author, and academic (d. 1971)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American swimmer and actress (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/1912\" title=\"1912\">1912</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1912\" title=\"1912\">1912</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and actress (d. 2004)", "links": [{"title": "1912", "link": "https://wikipedia.org/wiki/1912"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German-American pianist, conductor, and radio host (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist, conductor, and radio host (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pianist, conductor, and radio host (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English cricketer (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Russian captain and pilot (d. 1943)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian captain and pilot (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian captain and pilot (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic educator and politician, 3rd President of Iceland (d. 1982)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Kristj%C3%A1n_Eldj%C3%A1rn\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kristj%C3%A1n_Eldj%C3%A1rn\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristj%C3%A1n_Eldj%C3%A1rn"}, {"title": "President of Iceland", "link": "https://wikipedia.org/wiki/President_of_Iceland"}]}, {"year": "1916", "text": "<PERSON>, American songwriter and producer (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American minister and colonel (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and colonel (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and colonel (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Lebanese lawyer and politician (d. 1977)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Canadian-American businessman, co-founded <PERSON><PERSON><PERSON><PERSON> (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Polish Holocaust survivor (d. 2019)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Biterman\" title=\"Tauba Biterman\"><PERSON><PERSON></a>, Polish Holocaust survivor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Biterman\" title=\"Tauba Biterman\"><PERSON><PERSON></a>, Polish Holocaust survivor (d. 2019)", "links": [{"title": "Tauba Biterman", "link": "https://wikipedia.org/wiki/Tauba_Biterman"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Canadian-American figure skater and coach (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American figure skater and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American figure skater and coach (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Belgian-born philosopher, literary critic and theorist (d. 1983)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-born philosopher, literary critic and theorist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-born philosopher, literary critic and theorist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American pianist and composer (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English sportscaster and producer (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1921", "text": "<PERSON>, American football player and coach (d. 2003)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Italian lawyer, pianist, and composer (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer, pianist, and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian lawyer, pianist, and composer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English captain, Victoria Cross recipient (d. 1944)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1922", "text": "<PERSON>, American soldier and politician (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1973)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American shot putter and discus thrower (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and discus thrower (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American shot putter and discus thrower (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor, dancer, and singer (d. 1980)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and singer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Van\"><PERSON></a>, American actor, dancer, and singer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, French journalist and radio host", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, German-Austrian cellist and conductor (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian cellist and conductor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian cellist and conductor (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American comic book illustrator (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book illustrator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comic book illustrator (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Swiss director, producer, and screenwriter (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss director, producer, and screenwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss director, producer, and screenwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Zambian banker and politician, 3rd Prime Minister of Zambia (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian banker and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian banker and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zambia\" title=\"Prime Minister of Zambia\">Prime Minister of Zambia</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Zambia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Zambia"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter and actor (d. 1996)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Zeki_M%C3%BCren\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeki_M%C3%BCren\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter and actor (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeki_M%C3%BCren"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author, screenwriter, and critic (d. 2007)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, screenwriter, and critic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, screenwriter, and critic (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(writer)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Polish composer and academic (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Henryk_G%C3%B3recki\" title=\"<PERSON><PERSON> G<PERSON>\"><PERSON><PERSON></a>, Polish composer and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henryk_G%C3%B3recki\" title=\"<PERSON><PERSON> Gó<PERSON>i\"><PERSON><PERSON></a>, Polish composer and academic (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Henryk_G%C3%B3recki"}]}, {"year": "1933", "text": "<PERSON>, American general", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American wrestler, sportscaster, and actor (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, sportscaster, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, sportscaster, and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian actor, singer, and politician (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, singer, and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, singer, and politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English saxophonist and composer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, English saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, English saxophonist and composer", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(jazz_musician)"}]}, {"year": "1936", "text": "<PERSON>, American evangelist and author", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American writer and comedian", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Ecuadorian-American soccer player (d. 2006)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian-American soccer player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian-American soccer player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Belgian-American actor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Italian politician and sports administrator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician and sports administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician and sports administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franco_Carraro"}]}, {"year": "1940", "text": "<PERSON>, Canadian lawyer and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American visual effects designer and cinematographer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American visual effects designer and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American visual effects designer and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American country singer-songwriter and actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American sculptor and illustrator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American murderer (d. 1991)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American academic and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Austrian author and playwright, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American guitarist, keyboard player, and songwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, keyboard player, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, keyboard player, and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer (d. 2008)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON>)\" title=\"<PERSON> (<PERSON>)\"><PERSON></a>, English singer-songwriter, keyboard player, and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON>)\" title=\"<PERSON> (<PERSON>)\"><PERSON></a>, English singer-songwriter, keyboard player, and producer (d. 2008)", "links": [{"title": "<PERSON> (<PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_(<PERSON>_<PERSON>)"}]}, {"year": "1943", "text": "<PERSON>, English rock singer-songwriter and music producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Keith <PERSON>\"><PERSON></a>, English rock singer-songwriter and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Keith West\"><PERSON></a>, English rock singer-songwriter and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Keith_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter, record producer, music entrepreneur, television/radio presenter, and convicted sex offender", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, English singer-songwriter, record producer, music entrepreneur, television/radio presenter, and convicted sex offender", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, English singer-songwriter, record producer, music entrepreneur, television/radio presenter, and convicted sex offender", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Indian director, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American soul/funk singer-songwriter, musician, and producer (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Beverly\" title=\"Frankie Beverly\"><PERSON></a>, American soul/funk singer-songwriter, musician, and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frankie_Beverly\" title=\"Frankie Beverly\"><PERSON></a>, American soul/funk singer-songwriter, musician, and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Dutch footballer and manager (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian businessman and politician, 9th Canadian Minister of Foreign Affairs", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 9th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Canadian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Dutch-South African painter and author (d. 2005)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/He<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-South African painter and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-South African painter and author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Czech-American bassist and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Mir<PERSON><PERSON>_Vitou%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American bassist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Vitou%C5%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American bassist and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslav_Vitou%C5%A1"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Central African politician, Prime Minister of the Central African Republic (d. 2014)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Central African politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Central African politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of the Central African Republic", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic"}]}, {"year": "1948", "text": "<PERSON>, American businessman and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Finnish racing driver", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American author, playwright, and educator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, playwright, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, playwright, and educator", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter (d. 1986)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American author and cartoonist (d. 2007)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and cartoonist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English cricketer and umpire", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French hurdler and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French hurdler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French hurdler and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Japanese pianist, composer, and conductor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Baroness <PERSON> of Coatdyke, Scottish journalist and politician, Secretary of State for Scotland", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_Coatdyke\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Coatdyke\"><PERSON>, Baroness <PERSON> of Coatdyke</a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_Coatdyke\" class=\"mw-redirect\" title=\"<PERSON>, Baroness Li<PERSON> of Coatdyke\"><PERSON>, Baroness <PERSON> of Coatdyke</a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>, Baroness <PERSON> of Coatdyke", "link": "https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON><PERSON>_of_Coatdyke"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1951", "text": "<PERSON>, English ballerina and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Caribbean-English boxer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean-English boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, French author and critic (d. 1999)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9hal\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9hal\" title=\"<PERSON>\"><PERSON></a>, French author and critic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Br%C3%A9hal"}]}, {"year": "1952", "text": "<PERSON>, American computer programmer and entrepreneur; founded Craigslist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and entrepreneur; founded <a href=\"https://wikipedia.org/wiki/Craigslist\" title=\"Craigslist\">Craigslist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer and entrepreneur; founded <a href=\"https://wikipedia.org/wiki/Craigslist\" title=\"Craigslist\">Craigslist</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Craigslist", "link": "https://wikipedia.org/wiki/Craigslist"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Japanese illustrator (d. 2010)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Shi<PERSON>_Sat%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shi<PERSON>_<PERSON>t%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese illustrator (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shio_Sat%C5%8D"}]}, {"year": "1953", "text": "<PERSON>, English journalist (d. 2011)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian cricketer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English academic and politician, Minister of State for Europe", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Italian painter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, musician, and music producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and music producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and music producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Scottish educator and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English drummer, songwriter, and producer (d. 2025)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer, songwriter, and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian cricketer, rugby league player, and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, rugby league player, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, rugby league player, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English footballer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1955", "text": "<PERSON>, American actor, comedian, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Italian mountaineer and guide", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and guide", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and guide", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American guitarist, songwriter, and producer (d. 1982)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (d. 1999)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American politician, 56th Governor of New York", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 56th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 56th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1957", "text": "<PERSON>, American basketball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English animator, director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nick Park\"><PERSON></a>, English animator, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nick_Park\" title=\"Nick Park\"><PERSON></a>, English animator, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nick_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American computer scientist and academic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Japanese game programmer and businessman (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>a\"><PERSON><PERSON></a>, Japanese game programmer and businessman (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Iwata\"><PERSON><PERSON></a>, Japanese game programmer and businessman (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1959", "text": "<PERSON>, English computer scientist and engineer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese journalist and photographer (d. 2018)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese journalist and photographer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese journalist and photographer (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American drummer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American musician (d. 1996)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, German race car driver", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter, musician, author, DJ, and radio presenter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician, author, DJ, and radio presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, musician, author, DJ, and radio presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Danish actor and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Estonian painter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Mall_Nukke\" title=\"Mall Nukke\"><PERSON></a>, Estonian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mall_Nukke\" title=\"Mall Nukke\"><PERSON></a>, Estonian painter", "links": [{"title": "Mall Nukke", "link": "https://wikipedia.org/wiki/Mall_Nukke"}]}, {"year": "1965", "text": "<PERSON>, Scottish footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Swiss triathlete", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss triathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish golfer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American businesswoman and engineer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Cuban boxer (d. 2012)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Arnaldo_Mesa\" title=\"Arnaldo Mesa\"><PERSON><PERSON><PERSON></a>, Cuban boxer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arnaldo_Mesa\" title=\"Arnaldo Mesa\"><PERSON><PERSON><PERSON></a>, Cuban boxer (d. 2012)", "links": [{"title": "Arnaldo Mesa", "link": "https://wikipedia.org/wiki/Arnaldo_Mesa"}]}, {"year": "1968", "text": "<PERSON>, Norwegian author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>g%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Norwegian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>g%C3%A5rd\" title=\"<PERSON>\"><PERSON></a>, Norwegian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Knausg%C3%A5rd"}]}, {"year": "1968", "text": "<PERSON>, Iranian writer and political theorist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian writer and political theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian writer and political theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Swedish singer-songwriter, keyboard player, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American lawyer and politician, 6th Mayor of the District of Columbia", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Mayor_of_the_District_of_Columbia\" title=\"Mayor of the District of Columbia\">Mayor of the District of Columbia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Mayor_of_the_District_of_Columbia\" title=\"Mayor of the District of Columbia\">Mayor of the District of Columbia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of the District of Columbia", "link": "https://wikipedia.org/wiki/Mayor_of_the_District_of_Columbia"}]}, {"year": "1970", "text": "<PERSON>, English politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American swimmer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Cuban baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Dutch tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese singer and voice actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer and voice actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Dutch field hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American activist (d. 1990)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English scientist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English scientist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American lawyer and politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American mixed martial artist and boxer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mixed martial artist and boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English actor, director, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Mexican footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Adrian_<PERSON>c%C3%ADa_Arias\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Arias\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adrian_Garc%C3%ADa_Arias"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English cricketer, coach, and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Irish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Fak\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Fak\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%9Fak"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Brazilian basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9s_Pinto\" title=\"Adriana Mois<PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9s_Pinto\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adriana_Mois%C3%A9s_Pinto"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Argentine rugby player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American golfer and coach (d. 2014)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Cameroonian-French boxer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian-French boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian-French boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Spanish cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Zimbabwean cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Scottish race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Syn<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress and musician", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "Princess <PERSON>, Duchess of Värmland", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_V%C3%A4rmland\" title=\"Princess <PERSON>, Duchess of Värmland\">Princess <PERSON>, Duchess of Värmland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_V%C3%A4rmland\" title=\"Princess <PERSON>, Duchess of Värmland\">Princess <PERSON>, Duchess of Värmland</a>", "links": [{"title": "Princess <PERSON>, Duchess of Värmland", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_V%C3%A4rmland"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English race car driver (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2013)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1986", "text": "<PERSON>, American ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(outfielder)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Estonian singer and violinist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian singer and violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian singer and violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese voice actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Austrian tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Serbian taekwondo athlete", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>di%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian taekwondo athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian taekwondo athlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milica_Mandi%C4%87"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1992", "text": "<PERSON>, Russian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Congolese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>sombalonga\" title=\"<PERSON><PERSON><PERSON> Assombalong<PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sombalong<PERSON>\" title=\"<PERSON><PERSON><PERSON> Assombalong<PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Britt_<PERSON>so<PERSON><PERSON>a"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jasprit_Bumrah\" title=\"Jasprit Bumrah\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jasp<PERSON>_Bumrah\" title=\"Jasprit Bumrah\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jasp<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Cuban technician, known for a child custody and immigration case held in 2000", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban technician, known for a child custody and immigration case held in 2000", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban technician, known for a child custody and immigration case held in 2000", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eli%C3%A1n_Gonz%C3%A1lez"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Australian-Samoan rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tau<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tau<PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Greek-Nigerian basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Nigerian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-Nigerian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Wakatakakage_Atsushi\" title=\"Wakatakakage Atsushi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wakatakakage_Atsushi\" title=\"Wakatakakage Atsushi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "Wakatakakage Atsushi", "link": "https://wikipedia.org/wiki/Wakatakakage_Atsushi"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Iyer\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Iyer\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shrey<PERSON>_<PERSON>yer"}]}, {"year": "1995", "text": "A Boogie wit da <PERSON><PERSON>, American rapper and singer-songwriter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/A_Boogie_wit_da_Hoodie\" title=\"A Boogie wit da Hoodie\">A Boogie wit da Hoodie</a>, American rapper and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A_Boogie_wit_da_Hoodie\" title=\"A Boogie wit da Hoodie\">A Boogie wit da Hoodie</a>, American rapper and singer-songwriter", "links": [{"title": "A Boogie wit da Hoodie", "link": "https://wikipedia.org/wiki/A_<PERSON>_wit_da_<PERSON>ie"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Italian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/David<PERSON>_<PERSON>abria"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actress and singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Latvian figure skater", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Angel%C4%ABna_Ku%C4%8Dva%C4%BCska\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angel%C4%ABna_Ku%C4%8Dva%C4%BCska\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Angel%C4%ABna_Ku%C4%8Dva%C4%BCska"}]}], "Deaths": [{"year": "343", "text": "<PERSON> <PERSON>, Greek bishop and saint (b. 270)", "html": "343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Saint <PERSON>\">Saint <PERSON></a>, Greek bishop and saint (b. 270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Saint <PERSON>\">Saint <PERSON></a>, Greek bishop and saint (b. 270)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nicholas"}]}, {"year": "735", "text": "<PERSON> of Japan (b. 676)", "html": "735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a> of Japan (b. 676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Prince <PERSON>\">Prince <PERSON></a> of Japan (b. 676)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "762", "text": "<PERSON>, Arab rebel leader (b. 710)", "html": "762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab rebel leader (b. 710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Arab rebel leader (b. 710)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1185", "text": "<PERSON><PERSON><PERSON> of Portugal (b. 1109)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> I of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1109)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON><PERSON><PERSON> I of Portugal\"><PERSON><PERSON><PERSON> of Portugal</a> (b. 1109)", "links": [{"title": "<PERSON><PERSON><PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1305", "text": "<PERSON>, Metropolitan of Kyiv", "html": "1305 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Metropolitan_of_all_Rus\" class=\"mw-redirect\" title=\"<PERSON>, Metropolitan of all Rus\"><PERSON>, Metropolitan of Kyiv</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Metropolitan_of_all_Rus\" class=\"mw-redirect\" title=\"<PERSON>, Metropolitan of all Rus\"><PERSON>, Metropolitan of Kyiv</a>", "links": [{"title": "<PERSON>, Metropolitan of all Rus", "link": "https://wikipedia.org/wiki/<PERSON>,_Metropolitan_of_all_Rus"}]}, {"year": "1306", "text": "<PERSON>, 5th Earl of Norfolk (b. 1270)", "html": "1306 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Norfolk\" title=\"<PERSON>, 5th Earl of Norfolk\"><PERSON>, 5th Earl of Norfolk</a> (b. 1270)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Norfolk\" title=\"<PERSON>, 5th Earl of Norfolk\"><PERSON>, 5th Earl of Norfolk</a> (b. 1270)", "links": [{"title": "<PERSON>, 5th Earl of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Norfolk"}]}, {"year": "1352", "text": "<PERSON> (b. 1291)", "html": "1352 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VI\" title=\"<PERSON> Clement VI\">Pope <PERSON> VI</a> (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_VI\" title=\"<PERSON> Clement VI\"><PERSON> VI</a> (b. 1291)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON>, Dutch painter (b. 1495)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (b. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1616", "text": "<PERSON>, Moroccan writer, judge and mathematician (b. 1552)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan writer, judge and mathematician (b. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan writer, judge and mathematician (b. 1552)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1618", "text": "<PERSON>, French cardinal (b. 1556)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1556)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1658", "text": "<PERSON><PERSON><PERSON>, Spanish priest and author (b. 1601)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Baltasar_Graci%C3%A1n\" title=\"Baltasar Gracián\"><PERSON><PERSON><PERSON></a>, Spanish priest and author (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baltasar_Graci%C3%A1n\" title=\"Baltasar Gracián\"><PERSON>lta<PERSON></a>, Spanish priest and author (b. 1601)", "links": [{"title": "Baltasar <PERSON>", "link": "https://wikipedia.org/wiki/Baltasar_Graci%C3%A1n"}]}, {"year": "1675", "text": "<PERSON>, English priest, scholar, and academic (b. 1602)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, scholar, and academic (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest, scholar, and academic (b. 1602)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON><PERSON><PERSON>, Queen consort of <PERSON> (b. 1630)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Gonzaga_(1630%E2%80%931686)\" title=\"<PERSON><PERSON><PERSON> (1630-1686)\"><PERSON><PERSON><PERSON></a>, Queen consort of <PERSON> (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1630%E2%80%931686)\" title=\"<PERSON><PERSON><PERSON> (1630-1686)\"><PERSON><PERSON><PERSON></a>, Queen consort of <PERSON> (b. 1630)", "links": [{"title": "<PERSON><PERSON><PERSON> (1630-1686)", "link": "https://wikipedia.org/wiki/Eleonora_Gonzaga_(1630%E2%80%931686)"}]}, {"year": "1716", "text": "<PERSON><PERSON>, Dutch priest and composer (b. 1642)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/Benedictus_Buns\" title=\"Benedictus Buns\"><PERSON><PERSON></a>, Dutch priest and composer (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benedict<PERSON>_Buns\" title=\"Benedictus Buns\"><PERSON><PERSON></a>, Dutch priest and composer (b. 1642)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Buns"}]}, {"year": "1718", "text": "<PERSON>, English poet and playwright (b. 1674)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English poet and playwright (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English poet and playwright (b. 1674)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1746", "text": "<PERSON> <PERSON><PERSON><PERSON>, Scottish poet and songwriter (b. 1665)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lady <PERSON><PERSON>\">Lady <PERSON><PERSON><PERSON></a>, Scottish poet and songwriter (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lady G<PERSON><PERSON>\">Lady <PERSON><PERSON><PERSON></a>, Scottish poet and songwriter (b. 1665)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>llie"}]}, {"year": "1771", "text": "<PERSON>, Italian anatomist and pathologist (b. 1682)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist and pathologist (b. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist and pathologist (b. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French painter (b. 1699)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>rdin\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>rdin\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French painter (b. 1699)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>-Si<PERSON>%C3%A9<PERSON>_<PERSON><PERSON>n"}]}, {"year": "1788", "text": "<PERSON>, English bishop (b. 1714)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English ornithologist and entomologist (b. 1789)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and entomologist (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and entomologist (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, French physiologist and academic (b. 1794)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and academic (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physiologist and academic (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, German linguist and academic (b. 1821)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and academic (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German linguist and academic (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Greek painter and educator (b. 1814)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and educator (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek painter and educator (b. 1814)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, American businessman (b. 1814)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Era<PERSON><PERSON>_<PERSON>_Bigelow\" title=\"<PERSON><PERSON><PERSON>elow\"><PERSON><PERSON><PERSON></a>, American businessman (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bigelow\" title=\"<PERSON><PERSON><PERSON> Bigelow\"><PERSON><PERSON><PERSON></a>, American businessman (b. 1814)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bigelow"}]}, {"year": "1882", "text": "<PERSON>, Swiss businessman and politician, founded Credit Suisse (b. 1819)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman and politician, founded <a href=\"https://wikipedia.org/wiki/Credit_Suisse\" title=\"Credit Suisse\">Credit Suisse</a> (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman and politician, founded <a href=\"https://wikipedia.org/wiki/Credit_Suisse\" title=\"Credit Suisse\">Credit Suisse</a> (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Credit Suisse", "link": "https://wikipedia.org/wiki/Credit_Suisse"}]}, {"year": "1882", "text": "<PERSON>, English novelist, essayist, and short story writer  (b. 1815)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, and short story writer (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist, essayist, and short story writer (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American general and politician, President of the Confederate States of America (b. 1808)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America\" title=\"President of the Confederate States of America\">President of the Confederate States of America</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Confederate States of America", "link": "https://wikipedia.org/wiki/President_of_the_Confederate_States_of_America"}]}, {"year": "1892", "text": "<PERSON>, German engineer and businessman, founded the Siemens Company (b. 1816)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Siemens\" title=\"Siemens\">Siemens Company</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Siemens\" title=\"Siemens\">Siemens Company</a> (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Siemens", "link": "https://wikipedia.org/wiki/Siemens"}]}, {"year": "1918", "text": "<PERSON>, Russian chemist (b. 1851)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian chemist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian chemist (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Ottoman politician, 280th Grand Vizier of the Ottoman Empire (b. 1865)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, 280th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman politician, 280th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1924", "text": "<PERSON>-<PERSON>, American author and screenwriter (b. 1863)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Irish-Australian politician, 29th Premier of Tasmania (b. 1870)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1870)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1951", "text": "<PERSON>, American journalist and publisher, founded The New Yorker (b. 1892)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, founded <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The New Yorker", "link": "https://wikipedia.org/wiki/The_New_Yorker"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American baseball player and manager (b. 1874)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian economist and politician, 1st Indian Minister of Justice (b. 1891)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian economist and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)\" title=\"Ministry of Law and Justice (India)\">Indian Minister of Justice</a> (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian economist and politician, 1st <a href=\"https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)\" title=\"Ministry of Law and Justice (India)\">Indian Minister of Justice</a> (b. 1891)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Law and Justice (India)", "link": "https://wikipedia.org/wiki/Ministry_of_Law_and_Justice_(India)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Martinique-French psychiatrist and author (b. 1925)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Fanon\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON>-French psychiatrist and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Fanon\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON>-French psychiatrist and author (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>z_Fanon"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Dutch footballer and architect (b. 1895)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and architect (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and architect (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress and singer (b. 1934)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Soviet naval officer (b. 1904)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(officer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (officer)\"><PERSON><PERSON></a>, Soviet naval officer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(officer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (officer)\"><PERSON><PERSON></a>, Soviet naval officer (b. 1904)", "links": [{"title": "<PERSON><PERSON> (officer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(officer)"}]}, {"year": "1976", "text": "<PERSON>, Brazilian lawyer and politician, 24th President of Brazil (b. 1918)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Brazilian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Go<PERSON>t"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1980", "text": "<PERSON>, French engineer and businessman, co-founded DB (b. 1911)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/DB_(car)\" title=\"DB (car)\">DB</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/DB_(car)\" title=\"DB (car)\">DB</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DB (car)", "link": "https://wikipedia.org/wiki/DB_(car)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Kenyan activist and politician (b. 1927)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan activist and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan activist and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, French singer and actress (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and actress (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer and actress (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Pakistani poet, historian, and politician (b. 1914)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet, historian, and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet, historian, and politician (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and puppeteer (b. 1917)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and puppeteer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and puppeteer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (b. 1893)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1893)", "links": [{"title": "B<PERSON><PERSON> Grimes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American serial killer, arsonist, and cannibal (b. 1938)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer, arsonist, and cannibal (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer, arsonist, and cannibal (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1936)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress (b. 1902)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American pianist and composer (b. 1902)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ain"}]}, {"year": "1989", "text": "<PERSON>, American actor, singer, and producer (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, singer, and producer (b. 1912)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter and guitarist (b. 1948)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter and guitarist (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Malaysian lawyer and politician, 1st Prime Minister of Malaysia (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a> (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}]}, {"year": "1991", "text": "<PERSON>, English nurse (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English economist and statistician, Nobel Prize laureate (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and statistician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and statistician, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1993", "text": "<PERSON>, American actor (b. 1908)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1908)", "links": [{"title": "Don <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German footballer and manager (b. 1922)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Italian actor and director (b. 1933)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor and director (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actor and director (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Volont%C3%A9"}]}, {"year": "1996", "text": "<PERSON>, American businessman (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Dutch swimmer (b. 1918)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, French sculptor and educator (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and educator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and educator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>ni"}]}, {"year": "2000", "text": "<PERSON>, German-American actor (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Pakistani singer-songwriter and poet (b. 1942)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani singer-songwriter and poet (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani singer-songwriter and poet (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American football player and coach (b. 1923)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American priest and activist (b. 1923)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and activist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Guatemalan general and politician, President of Guatemala (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">President of Guatemala</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Luxembourger cyclist (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Charly G<PERSON>\"><PERSON><PERSON></a>, Luxembourger cyclist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cha<PERSON>_<PERSON>\" title=\"Charly G<PERSON>\"><PERSON><PERSON></a>, Luxembourger cyclist (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aul"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Malaysian-Singaporean union leader and politician, 3rd President of Singapore (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian-Singaporean union leader and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian-Singaporean union leader and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "2005", "text": "<PERSON>, South African singer (b. 1942)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South African singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, South African singer (b. 1942)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2005", "text": "<PERSON>, American general (b. 1912)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, New Zealand director and producer (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, New Zealand director and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, New Zealand director and producer (b. 1922)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "2010", "text": "<PERSON>, American-Canadian journalist and actor (b. 1953)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist and actor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American singer-songwriter and producer (b. 1940)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Equatoguinean engineer and politician, Prime Minister of Equatorial Guinea (b. 1961)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Biteo_Boric%C3%B3\" title=\"Miguel Abia Biteo Boricó\"><PERSON></a>, Equatoguinean engineer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Equatorial Guinea\">Prime Minister of Equatorial Guinea</a> (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Biteo_Boric%C3%B3\" title=\"Miguel Abia Biteo Boricó\"><PERSON></a>, Equatoguinean engineer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea\" class=\"mw-redirect\" title=\"Prime Minister of Equatorial Guinea\">Prime Minister of Equatorial Guinea</a> (b. 1961)", "links": [{"title": "Miguel <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Biteo_Boric%C3%B3"}, {"title": "Prime Minister of Equatorial Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Equatorial_Guinea"}]}, {"year": "2012", "text": "<PERSON>, Guyanese author, poet, and playwright (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese author, poet, and playwright (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese author, poet, and playwright (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Taiwanese banker and businessman (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese banker and businessman (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese banker and businessman (b. 1933)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2012", "text": "<PERSON><PERSON>, English guitarist (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English guitarist (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Uruguayan lawyer and politician, Minister of Foreign Affairs of Uruguay (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Uruguayan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Uruguay)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Uruguay)\">Minister of Foreign Affairs of Uruguay</a> (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Uruguayan lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Uruguay)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Uruguay)\">Minister of Foreign Affairs of Uruguay</a> (b. 1963)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}, {"title": "Minister of Foreign Affairs (Uruguay)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Uruguay)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, French poet and critic (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and critic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet and critic (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English pianist and composer (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player and coach (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._<PERSON>\" title=\"M. K. Turk\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M._<PERSON>._<PERSON>rk\" title=\"M. K. Turk\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1942)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German-American video game designer, created the Magnavox Odyssey (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Magnavox_Odyssey\" title=\"Magnavox Odyssey\">Magnavox Odyssey</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American video game designer, created the <a href=\"https://wikipedia.org/wiki/Magnavox_Odyssey\" title=\"Magnavox Odyssey\">Magnavox Odyssey</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Magnavox Odyssey", "link": "https://wikipedia.org/wiki/Magnavox_Odyssey"}]}, {"year": "2014", "text": "<PERSON>, American wrestler and manager (b. 1962)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American golfer (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (b. 1923)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "2014", "text": "<PERSON>, English-American photographer and journalist (b. 1981)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American photographer and journalist (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American photographer and journalist (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Taiwanese actor, director, and politician (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese actor, director, and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese actor, director, and politician (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese general and politician (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, British actor (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, British actor (b. 1934)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2016", "text": "<PERSON>, British actor (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian fashion model and television personality (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fashion model and television personality (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian fashion model and television personality (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}