{"date": "September 24", "url": "https://wikipedia.org/wiki/September_24", "data": {"Events": [{"year": "787", "text": "Second Council of Nicaea: The council assembles at the church of Hagia Sophia.", "html": "787 - <a href=\"https://wikipedia.org/wiki/Second_Council_of_Nicaea\" title=\"Second Council of Nicaea\">Second Council of Nicaea</a>: The council assembles at the church of Hagia Sophia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Council_of_Nicaea\" title=\"Second Council of Nicaea\">Second Council of Nicaea</a>: The council assembles at the church of Hagia Sophia.", "links": [{"title": "Second Council of Nicaea", "link": "https://wikipedia.org/wiki/Second_Council_of_Nicaea"}]}, {"year": "1568", "text": "Spanish naval forces defeat an English fleet, under the command of <PERSON>, at the Battle of San Juan de Ulúa near Veracruz.", "html": "1568 - Spanish naval forces defeat an English fleet, under the command of <PERSON>, at the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_de_Ul%C3%BAa_(1568)\" title=\"Battle of San Juan de Ulúa (1568)\">Battle of San Juan de Ulúa</a> near Veracruz.", "no_year_html": "Spanish naval forces defeat an English fleet, under the command of <PERSON>, at the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Juan_de_Ul%C3%BAa_(1568)\" title=\"Battle of San Juan de Ulúa (1568)\">Battle of San Juan de Ulúa</a> near Veracruz.", "links": [{"title": "Battle of San Juan de Ulúa (1568)", "link": "https://wikipedia.org/wiki/Battle_of_San_Juan_<PERSON>_<PERSON>%C3%BAa_(1568)"}]}, {"year": "1645", "text": "The Battle of Rowton Heath in England is a Parliamentarian victory over a Royalist army commanded in person by King <PERSON>.", "html": "1645 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Rowton_Heath\" title=\"Battle of Rowton Heath\">Battle of Rowton Heath</a> in England is a Parliamentarian victory over a Royalist army commanded in person by King <PERSON>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Rowton_Heath\" title=\"Battle of Rowton Heath\">Battle of Rowton Heath</a> in England is a Parliamentarian victory over a Royalist army commanded in person by King <PERSON>.", "links": [{"title": "Battle of Rowton Heath", "link": "https://wikipedia.org/wiki/Battle_of_Rowton_Heath"}]}, {"year": "1674", "text": "Second Tantrik Coronation of <PERSON><PERSON>.", "html": "1674 - Second Tantrik Coronation of <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shiva<PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "Second Tantrik Coronation of <a href=\"https://wikipedia.org/wiki/Shivaji\" title=\"Shivaji\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1789", "text": "The United States Congress passes the Judiciary Act, creating the office of the Attorney General and federal judiciary system and ordering the composition of the Supreme Court.", "html": "1789 - The United States Congress passes the <a href=\"https://wikipedia.org/wiki/Judiciary_Act_of_1789\" title=\"Judiciary Act of 1789\">Judiciary Act</a>, creating the office of the Attorney General and federal judiciary system and ordering the composition of the Supreme Court.", "no_year_html": "The United States Congress passes the <a href=\"https://wikipedia.org/wiki/Judiciary_Act_of_1789\" title=\"Judiciary Act of 1789\">Judiciary Act</a>, creating the office of the Attorney General and federal judiciary system and ordering the composition of the Supreme Court.", "links": [{"title": "Judiciary Act of 1789", "link": "https://wikipedia.org/wiki/Judiciary_Act_of_1789"}]}, {"year": "1830", "text": "A revolutionary committee of notables forms the Provisional Government of Belgium.", "html": "1830 - A revolutionary committee of notables forms the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Belgium\" title=\"Provisional Government of Belgium\">Provisional Government of Belgium</a>.", "no_year_html": "A revolutionary committee of notables forms the <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_Belgium\" title=\"Provisional Government of Belgium\">Provisional Government of Belgium</a>.", "links": [{"title": "Provisional Government of Belgium", "link": "https://wikipedia.org/wiki/Provisional_Government_of_Belgium"}]}, {"year": "1841", "text": "The Sultanate of Brunei cedes Sarawak to <PERSON>.", "html": "1841 - The <a href=\"https://wikipedia.org/wiki/Sultanate_of_Brunei\" class=\"mw-redirect\" title=\"Sultanate of Brunei\">Sultanate of Brunei</a> cedes <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sarawak\" class=\"mw-redirect\" title=\"Kingdom of Sarawak\">Sarawak</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sultanate_of_Brunei\" class=\"mw-redirect\" title=\"Sultanate of Brunei\">Sultanate of Brunei</a> cedes <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sarawak\" class=\"mw-redirect\" title=\"Kingdom of Sarawak\">Sarawak</a> to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Sultanate of Brunei", "link": "https://wikipedia.org/wiki/Sultanate_of_Brunei"}, {"title": "Kingdom of Sarawak", "link": "https://wikipedia.org/wiki/Kingdom_of_Sarawak"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "Mexican-American War: General <PERSON> captures Monterrey.", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: General <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Monterrey\" title=\"Battle of Monterrey\">captures Monterrey</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: General <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Monterrey\" title=\"Battle of Monterrey\">captures Monterrey</a>.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Battle of Monterrey", "link": "https://wikipedia.org/wiki/Battle_of_Monterrey"}]}, {"year": "1852", "text": "The first powered, passenger-carrying airship, the Giffard dirigible, travels 17 miles (27 km) from Paris to Trappes.", "html": "1852 - The first powered, passenger-carrying airship, the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dirigible\" title=\"Giffard dirigible\">G<PERSON>ard dirigible</a>, travels 17 miles (27 km) from Paris to Trappes.", "no_year_html": "The first powered, passenger-carrying airship, the <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_dirigible\" title=\"Giffard dirigible\">Giffard dirigible</a>, travels 17 miles (27 km) from Paris to Trappes.", "links": [{"title": "<PERSON><PERSON><PERSON> dirigible", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dirigible"}]}, {"year": "1853", "text": "Admiral <PERSON><PERSON> formally takes possession of New Caledonia in the name of France.", "html": "1853 - Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> formally takes possession of <a href=\"https://wikipedia.org/wiki/New_Caledonia\" title=\"New Caledonia\">New Caledonia</a> in the name of France.", "no_year_html": "Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> formally takes possession of <a href=\"https://wikipedia.org/wiki/New_Caledonia\" title=\"New Caledonia\">New Caledonia</a> in the name of France.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New Caledonia", "link": "https://wikipedia.org/wiki/New_Caledonia"}]}, {"year": "1869", "text": "Black Friday (1869): Gold prices plummet after United States President <PERSON> orders the Treasury to sell large quantities of gold after <PERSON> and <PERSON> plot to control the market.", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Black_Friday_(1869)\" title=\"Black Friday (1869)\">Black Friday (1869)</a>: Gold prices plummet after United States President <PERSON> orders the Treasury to sell large quantities of gold after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> plot to control the market.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Friday_(1869)\" title=\"Black Friday (1869)\">Black Friday (1869)</a>: Gold prices plummet after United States President <PERSON> orders the Treasury to sell large quantities of gold after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> plot to control the market.", "links": [{"title": "Black Friday (1869)", "link": "https://wikipedia.org/wiki/Black_Friday_(1869)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (financier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)"}]}, {"year": "1875", "text": "The 1864 play <PERSON> Cobblers by <PERSON><PERSON><PERSON> is premiered for the first time in Oulu, Finland.", "html": "1875 - The 1864 play <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lers\" title=\"Heath Cobblers\"><PERSON> Cobblers</a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is premiered for the first time in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu, Finland</a>.", "no_year_html": "The 1864 play <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cobblers\"><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is premiered for the first time in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"<PERSON>ulu\">Oulu, Finland</a>.", "links": [{"title": "<PERSON> Cobblers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lers"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}]}, {"year": "1877", "text": "The Battle of Shiroyama is a decisive victory of the Imperial Japanese Army over the Satsuma Rebellion.", "html": "1877 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Shiroyama\" title=\"Battle of Shiroyama\">Battle of Shiroyama</a> is a decisive victory of the Imperial Japanese Army over the Satsuma Rebellion.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Shiroyama\" title=\"Battle of Shiroyama\">Battle of Shiroyama</a> is a decisive victory of the Imperial Japanese Army over the Satsuma Rebellion.", "links": [{"title": "Battle of Shiroyama", "link": "https://wikipedia.org/wiki/Battle_of_Shiroyama"}]}, {"year": "1890", "text": "The Church of Jesus Christ of Latter-day Saints officially renounces polygamy.", "html": "1890 - The Church of Jesus Christ of Latter-day Saints officially <a href=\"https://wikipedia.org/wiki/1890_Manifesto\" title=\"1890 Manifesto\">renounces</a> polygamy.", "no_year_html": "The Church of Jesus Christ of Latter-day Saints officially <a href=\"https://wikipedia.org/wiki/1890_Manifesto\" title=\"1890 Manifesto\">renounces</a> polygamy.", "links": [{"title": "1890 Manifesto", "link": "https://wikipedia.org/wiki/1890_Manifesto"}]}, {"year": "1906", "text": "U.S. President <PERSON> proclaims Devils Tower in Wyoming as the nation's first National Monument.", "html": "1906 - U.S. President <PERSON> proclaims <a href=\"https://wikipedia.org/wiki/Devils_Tower_National_Monument\" class=\"mw-redirect\" title=\"Devils Tower National Monument\">Devils Tower</a> in Wyoming as the nation's first National Monument.", "no_year_html": "U.S. President <PERSON> proclaims <a href=\"https://wikipedia.org/wiki/Devils_Tower_National_Monument\" class=\"mw-redirect\" title=\"Devils Tower National Monument\">Devils Tower</a> in Wyoming as the nation's first National Monument.", "links": [{"title": "Devils Tower National Monument", "link": "https://wikipedia.org/wiki/Devils_Tower_National_Monument"}]}, {"year": "1906", "text": "Racial tensions exacerbated by rumors lead to the Atlanta Race Riot, further increasing racial segregation.", "html": "1906 - Racial tensions exacerbated by rumors lead to the <a href=\"https://wikipedia.org/wiki/Atlanta_Race_Riot\" class=\"mw-redirect\" title=\"Atlanta Race Riot\">Atlanta Race Riot</a>, further increasing racial segregation.", "no_year_html": "Racial tensions exacerbated by rumors lead to the <a href=\"https://wikipedia.org/wiki/Atlanta_Race_Riot\" class=\"mw-redirect\" title=\"Atlanta Race Riot\">Atlanta Race Riot</a>, further increasing racial segregation.", "links": [{"title": "Atlanta Race Riot", "link": "https://wikipedia.org/wiki/Atlanta_Race_Riot"}]}, {"year": "1911", "text": "His Majesty's Airship No. 1, Britain's first rigid airship, is wrecked by strong winds before her maiden flight at Barrow-in-Furness.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/HMA_No._1\" title=\"HMA No. 1\">His Majesty's Airship No. 1</a>, Britain's first rigid airship, is wrecked by strong winds before her maiden flight at Barrow-in-Furness.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMA_No._1\" title=\"HMA No. 1\">His Majesty's Airship No. 1</a>, Britain's first rigid airship, is wrecked by strong winds before her maiden flight at Barrow-in-Furness.", "links": [{"title": "HMA No. 1", "link": "https://wikipedia.org/wiki/HMA_No._1"}]}, {"year": "1929", "text": "<PERSON> performs the first flight without a window, proving that full instrument flying from take off to landing is possible.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first flight without a window, proving that full instrument flying from take off to landing is possible.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs the first flight without a window, proving that full instrument flying from take off to landing is possible.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON> and Dr. <PERSON> agree to the Poona Pact, which reserved seats in the Indian provincial legislatures for the \"Depressed Classes\" (Untouchables).", "html": "1932 - <PERSON> and Dr<PERSON> agree to the <a href=\"https://wikipedia.org/wiki/Poona_Pact\" title=\"Poona Pact\">Poona Pact</a>, which reserved seats in the Indian provincial legislatures for the \"Depressed Classes\" (Untouchables).", "no_year_html": "<PERSON> and Dr<PERSON> agree to the <a href=\"https://wikipedia.org/wiki/Poona_Pact\" title=\"Poona Pact\">Poona Pact</a>, which reserved seats in the Indian provincial legislatures for the \"Depressed Classes\" (Untouchables).", "links": [{"title": "Poona Pact", "link": "https://wikipedia.org/wiki/Poona_Pact"}]}, {"year": "1935", "text": "<PERSON> and <PERSON><PERSON> produce the first rodeo ever held outdoors under electric lights.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> and <PERSON><PERSON></a> produce the first rodeo ever held outdoors under electric lights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> and <PERSON><PERSON></a> produce the first rodeo ever held outdoors under electric lights.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "Cathay Pacific Airways is founded in Hong Kong.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Cathay_Pacific\" title=\"Cathay Pacific\">Cathay Pacific Airways</a> is founded in Hong Kong.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cathay_Pacific\" title=\"Cathay Pacific\">Cathay Pacific Airways</a> is founded in Hong Kong.", "links": [{"title": "Cathay Pacific", "link": "https://wikipedia.org/wiki/Cathay_Pacific"}]}, {"year": "1946", "text": "The top-secret <PERSON><PERSON><PERSON><PERSON> Report on the Soviet Union is delivered to President <PERSON>.", "html": "1946 - The top-secret <a href=\"https://wikipedia.org/wiki/X_Article\" title=\"X Article\"><PERSON><PERSON> Report</a> on the Soviet Union is delivered to President <PERSON>.", "no_year_html": "The top-secret <a href=\"https://wikipedia.org/wiki/X_Article\" title=\"X Article\"><PERSON><PERSON> Report</a> on the Soviet Union is delivered to President <PERSON>.", "links": [{"title": "X Article", "link": "https://wikipedia.org/wiki/X_Article"}]}, {"year": "1948", "text": "The Honda Motor Company is founded.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda Motor Company</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda Motor Company</a> is founded.", "links": [{"title": "Honda", "link": "https://wikipedia.org/wiki/Honda"}]}, {"year": "1950", "text": "The eastern United States is covered by a thick haze from the Chinchaga fire in western Canada.", "html": "1950 - The eastern United States is covered by a thick haze from the <a href=\"https://wikipedia.org/wiki/Chinchaga_fire\" title=\"Chinchaga fire\">Chinchaga fire</a> in western Canada.", "no_year_html": "The eastern United States is covered by a thick haze from the <a href=\"https://wikipedia.org/wiki/Chinchaga_fire\" title=\"Chinchaga fire\">Chinchaga fire</a> in western Canada.", "links": [{"title": "Chinchaga fire", "link": "https://wikipedia.org/wiki/Chinchaga_fire"}]}, {"year": "1954", "text": "AEC Routemaster, the iconic London bus was introduced.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/AEC_Routemaster\" title=\"AEC Routemaster\">AEC Routemaster</a>, the iconic <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> bus was introduced.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AEC_Routemaster\" title=\"AEC Routemaster\">AEC Routemaster</a>, the iconic <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a> bus was introduced.", "links": [{"title": "AEC Routemaster", "link": "https://wikipedia.org/wiki/AEC_Routemaster"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1957", "text": "President <PERSON> sends the 101st Airborne Division to Little Rock, Arkansas, to enforce desegregation.", "html": "1957 - President <PERSON> sends the 101st Airborne Division to Little Rock, Arkansas, to enforce <a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">desegregation</a>.", "no_year_html": "President <PERSON> sends the 101st Airborne Division to Little Rock, Arkansas, to enforce <a href=\"https://wikipedia.org/wiki/Little_Rock_Nine\" title=\"Little Rock Nine\">desegregation</a>.", "links": [{"title": "Little Rock Nine", "link": "https://wikipedia.org/wiki/Little_Rock_Nine"}]}, {"year": "1959", "text": "TAI Flight 307 crashes during takeoff from Bordeaux-Mérignac Airport in Bordeaux, Nouvelle-Aquitaine, France, killing 55 people.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/TAI_Flight_307\" title=\"TAI Flight 307\">TAI Flight 307</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bordeaux%E2%80%93M%C3%A9rignac_Airport\" title=\"Bordeaux-Mérignac Airport\">Bordeaux-Mérignac Airport</a> in <a href=\"https://wikipedia.org/wiki/Bordeaux\" title=\"Bordeaux\">Bordeaux</a>, <a href=\"https://wikipedia.org/wiki/Nouvelle-Aquitaine\" title=\"Nouvelle-Aquitaine\">Nouvelle-Aquitaine</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, killing 55 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAI_Flight_307\" title=\"TAI Flight 307\">TAI Flight 307</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Bordeaux%E2%80%93M%C3%A9rignac_Airport\" title=\"Bordeaux-Mérignac Airport\">Bordeaux-Mérignac Airport</a> in <a href=\"https://wikipedia.org/wiki/Bordeaux\" title=\"Bordeaux\">Bordeaux</a>, <a href=\"https://wikipedia.org/wiki/Nouvelle-Aquitaine\" title=\"Nouvelle-Aquitaine\">Nouvelle-Aquitaine</a>, <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, killing 55 people.", "links": [{"title": "TAI Flight 307", "link": "https://wikipedia.org/wiki/TAI_Flight_307"}, {"title": "Bordeaux-Mérignac Airport", "link": "https://wikipedia.org/wiki/Bordeaux%E2%80%93M%C3%A9rignac_Airport"}, {"title": "Bordeaux", "link": "https://wikipedia.org/wiki/Bordeaux"}, {"title": "Nouvelle-Aquitaine", "link": "https://wikipedia.org/wiki/Nouvelle-Aquitaine"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}]}, {"year": "1960", "text": "USS Enterprise, the world's first nuclear-powered aircraft carrier, is launched.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)\" title=\"USS Enterprise (CVN-65)\">USS <i>Enterprise</i></a>, the world's first nuclear-powered aircraft carrier, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)\" title=\"USS Enterprise (CVN-65)\">USS <i>Enterprise</i></a>, the world's first nuclear-powered aircraft carrier, is launched.", "links": [{"title": "USS Enterprise (CVN-65)", "link": "https://wikipedia.org/wiki/USS_Enterprise_(CVN-65)"}]}, {"year": "1972", "text": "Japan Airlines Flight 472 lands at Juhu Aerodrome instead of Santacruz Airport in Bombay, India.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_472_(1972)\" class=\"mw-redirect\" title=\"Japan Airlines Flight 472 (1972)\">Japan Airlines Flight 472</a> lands at Juhu Aerodrome instead of Santacruz Airport in Bombay, India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_472_(1972)\" class=\"mw-redirect\" title=\"Japan Airlines Flight 472 (1972)\">Japan Airlines Flight 472</a> lands at Juhu Aerodrome instead of Santacruz Airport in Bombay, India.", "links": [{"title": "Japan Airlines Flight 472 (1972)", "link": "https://wikipedia.org/wiki/Japan_Airlines_Flight_472_(1972)"}]}, {"year": "1973", "text": "Guinea-Bissau declares its independence from Portugal.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> declares its independence from Portugal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> declares its independence from Portugal.", "links": [{"title": "Guinea-Bissau", "link": "https://wikipedia.org/wiki/Guinea-Bissau"}]}, {"year": "1975", "text": "Southwest Face expedition members become the first persons to reach the summit of Mount Everest by any of its faces, instead of using a ridge route.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/1975_British_Mount_Everest_Southwest_Face_expedition\" title=\"1975 British Mount Everest Southwest Face expedition\">Southwest Face expedition</a> members become the first persons to reach the summit of Mount Everest by any of its faces, instead of using a ridge route.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1975_British_Mount_Everest_Southwest_Face_expedition\" title=\"1975 British Mount Everest Southwest Face expedition\">Southwest Face expedition</a> members become the first persons to reach the summit of Mount Everest by any of its faces, instead of using a ridge route.", "links": [{"title": "1975 British Mount Everest Southwest Face expedition", "link": "https://wikipedia.org/wiki/1975_British_Mount_Everest_Southwest_Face_expedition"}]}, {"year": "1993", "text": "The Cambodian monarchy is restored, with <PERSON><PERSON><PERSON> as king.", "html": "1993 - The Cambodian monarchy is restored, with <a href=\"https://wikipedia.org/wiki/Norod<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as king.", "no_year_html": "The Cambodian monarchy is restored, with <a href=\"https://wikipedia.org/wiki/Norod<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as king.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}]}, {"year": "1996", "text": "Representatives of 71 nations sign the Comprehensive Nuclear-Test-Ban Treaty at the United Nations.", "html": "1996 - Representatives of 71 nations sign the <a href=\"https://wikipedia.org/wiki/Comprehensive_Nuclear-Test-Ban_Treaty\" title=\"Comprehensive Nuclear-Test-Ban Treaty\">Comprehensive Nuclear-Test-Ban Treaty</a> at the United Nations.", "no_year_html": "Representatives of 71 nations sign the <a href=\"https://wikipedia.org/wiki/Comprehensive_Nuclear-Test-Ban_Treaty\" title=\"Comprehensive Nuclear-Test-Ban Treaty\">Comprehensive Nuclear-Test-Ban Treaty</a> at the United Nations.", "links": [{"title": "Comprehensive Nuclear-Test-Ban Treaty", "link": "https://wikipedia.org/wiki/Comprehensive_Nuclear-Test-Ban_Treaty"}]}, {"year": "2005", "text": "Hurricane <PERSON> makes landfall in the United States, devastating portions of southwestern Louisiana and extreme southeastern Texas.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Hurricane_Rita\" title=\"Hurricane Rita\">Hurricane Rita</a> makes landfall in the United States, devastating portions of southwestern Louisiana and extreme southeastern <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Rita\" title=\"Hurricane Rita\">Hurricane Rita</a> makes landfall in the United States, devastating portions of southwestern Louisiana and extreme southeastern <a href=\"https://wikipedia.org/wiki/Texas\" title=\"Texas\">Texas</a>.", "links": [{"title": "Hurricane Rita", "link": "https://wikipedia.org/wiki/Hurricane_Rita"}, {"title": "Texas", "link": "https://wikipedia.org/wiki/Texas"}]}, {"year": "2007", "text": "Between 30,000 and 100,000 people take part in anti-government protests in Yangon, Burma, the largest in 20 years.", "html": "2007 - Between 30,000 and 100,000 people take part in <a href=\"https://wikipedia.org/wiki/Saffron_Revolution\" title=\"Saffron Revolution\">anti-government protests</a> in Yangon, Burma, the largest in 20 years.", "no_year_html": "Between 30,000 and 100,000 people take part in <a href=\"https://wikipedia.org/wiki/Saffron_Revolution\" title=\"Saffron Revolution\">anti-government protests</a> in Yangon, Burma, the largest in 20 years.", "links": [{"title": "Saffron Revolution", "link": "https://wikipedia.org/wiki/Saffron_Revolution"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON> resigns as president of South Africa.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Thab<PERSON>_<PERSON>\" title=\"<PERSON>hab<PERSON>\"><PERSON><PERSON><PERSON></a> resigns as president of South Africa.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thab<PERSON>_<PERSON>\" title=\"<PERSON>hab<PERSON>\"><PERSON><PERSON><PERSON></a> resigns as president of South Africa.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thab<PERSON>_<PERSON><PERSON>i"}]}, {"year": "2009", "text": "The G20 summit begins in Pittsburgh with 30 global leaders in attendance.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/2009_G-20_Pittsburgh_summit\" class=\"mw-redirect\" title=\"2009 G-20 Pittsburgh summit\">G20 summit</a> begins in Pittsburgh with 30 global leaders in attendance.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2009_G-20_Pittsburgh_summit\" class=\"mw-redirect\" title=\"2009 G-20 Pittsburgh summit\">G20 summit</a> begins in Pittsburgh with 30 global leaders in attendance.", "links": [{"title": "2009 G-20 Pittsburgh summit", "link": "https://wikipedia.org/wiki/2009_G-20_Pittsburgh_summit"}]}, {"year": "2009", "text": "SA Airlink Flight 8911 crashes near Durban International Airport in Durban, South Africa, killing the captain and injuring the rest of the crew.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/SA_Airlink_Flight_8911\" class=\"mw-redirect\" title=\"SA Airlink Flight 8911\">SA Airlink Flight 8911</a> crashes near <a href=\"https://wikipedia.org/wiki/Durban_International_Airport\" title=\"Durban International Airport\">Durban International Airport</a> in <a href=\"https://wikipedia.org/wiki/Durban\" title=\"Durban\">Durban</a>, <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, killing the captain and injuring the rest of the crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SA_Airlink_Flight_8911\" class=\"mw-redirect\" title=\"SA Airlink Flight 8911\">SA Airlink Flight 8911</a> crashes near <a href=\"https://wikipedia.org/wiki/Durban_International_Airport\" title=\"Durban International Airport\">Durban International Airport</a> in <a href=\"https://wikipedia.org/wiki/Durban\" title=\"Durban\">Durban</a>, <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, killing the captain and injuring the rest of the crew.", "links": [{"title": "SA Airlink Flight 8911", "link": "https://wikipedia.org/wiki/SA_Airlink_Flight_8911"}, {"title": "Durban International Airport", "link": "https://wikipedia.org/wiki/Durban_International_Airport"}, {"title": "Durban", "link": "https://wikipedia.org/wiki/Durban"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}]}, {"year": "2013", "text": "A 7.7-magnitude earthquake strikes southern Pakistan, killing at least 327 people.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">7.7-magnitude</a> <a href=\"https://wikipedia.org/wiki/2013_Balochistan_earthquakes\" title=\"2013 Balochistan earthquakes\">earthquake strikes</a> southern Pakistan, killing at least 327 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">7.7-magnitude</a> <a href=\"https://wikipedia.org/wiki/2013_Balochistan_earthquakes\" title=\"2013 Balochistan earthquakes\">earthquake strikes</a> southern Pakistan, killing at least 327 people.", "links": [{"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "2013 Balochistan earthquakes", "link": "https://wikipedia.org/wiki/2013_Balochistan_earthquakes"}]}, {"year": "2014", "text": "The Mars Orbiter Mission makes India the first Asian nation to reach Mars orbit, and the first nation in the world to do so in its first attempt.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Mars_Orbiter_Mission\" title=\"Mars Orbiter Mission\">Mars Orbiter Mission</a> makes India the first Asian nation to reach Mars orbit, and the first nation in the world to do so in its first attempt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mars_Orbiter_Mission\" title=\"Mars Orbiter Mission\">Mars Orbiter Mission</a> makes India the first Asian nation to reach Mars orbit, and the first nation in the world to do so in its first attempt.", "links": [{"title": "Mars Orbiter Mission", "link": "https://wikipedia.org/wiki/Mars_Orbiter_Mission"}]}, {"year": "2015", "text": "At least 1,100 people are killed and another 934 wounded after a stampede during the Hajj in Saudi Arabia.", "html": "2015 - At least 1,100 people are killed and another 934 wounded after <a href=\"https://wikipedia.org/wiki/2015_Mina_disaster\" class=\"mw-redirect\" title=\"2015 Mina disaster\">a stampede</a> during the Hajj in Saudi Arabia.", "no_year_html": "At least 1,100 people are killed and another 934 wounded after <a href=\"https://wikipedia.org/wiki/2015_Mina_disaster\" class=\"mw-redirect\" title=\"2015 Mina disaster\">a stampede</a> during the Hajj in Saudi Arabia.", "links": [{"title": "2015 Mina disaster", "link": "https://wikipedia.org/wiki/2015_Mina_disaster"}]}, {"year": "2023", "text": "NASA's OSIRIS-REx capsule containing samples from the asteroid 101955 <PERSON><PERSON> successfully lands back on Earth.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/OSIRIS-REx\" title=\"OSIRIS-REx\">OSIRIS-REx</a> capsule containing samples from the asteroid <a href=\"https://wikipedia.org/wiki/101955_Bennu\" title=\"101955 Bennu\">101955 Bennu</a> successfully lands back on Earth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/OSIRIS-REx\" title=\"OSIRIS-REx\">OSIRIS-REx</a> capsule containing samples from the asteroid <a href=\"https://wikipedia.org/wiki/101955_Bennu\" title=\"101955 Bennu\">101955 Bennu</a> successfully lands back on Earth.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "OSIRIS-REx", "link": "https://wikipedia.org/wiki/OSIRIS-REx"}, {"title": "101955 <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/101955_<PERSON>nu"}]}], "Births": [{"year": "15", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (d. 69)", "html": "15 - <a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 69)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 69)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}]}, {"year": "936", "text": "<PERSON><PERSON><PERSON>, Buyid king (d. 983)", "html": "936 - <a href=\"https://wikipedia.org/wiki/%27Adu<PERSON>_<PERSON>-<PERSON>\" title=\"'<PERSON><PERSON>\">'<PERSON><PERSON></a>, <PERSON><PERSON> king (d. 983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%27Adu<PERSON>_<PERSON>\" title=\"'<PERSON><PERSON>\">'<PERSON><PERSON></a>, <PERSON><PERSON> king (d. 983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%27Adu<PERSON>_<PERSON>"}]}, {"year": "1301", "text": "<PERSON>, 1st Earl of Stafford, English soldier (d. 1372)", "html": "1301 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Stafford\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl <PERSON> Stafford\"><PERSON>, 1st Earl of Stafford</a>, English soldier (d. 1372)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_<PERSON>_Stafford\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Stafford\"><PERSON>, 1st Earl of Stafford</a>, English soldier (d. 1372)", "links": [{"title": "<PERSON>, 1st Earl of Stafford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Stafford"}]}, {"year": "1418", "text": "<PERSON> of Cyprus, Duchess of Savoy (probable; d. 1462)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/Anne_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a>, Duchess of Savoy (probable; d. 1462)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a>, Duchess of Savoy (probable; d. 1462)", "links": [{"title": "Anne of Cyprus", "link": "https://wikipedia.org/wiki/Anne_of_Cyprus"}]}, {"year": "1433", "text": "<PERSON><PERSON> of Amarsar, Rajput chieftain (d. 1488)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Amarsar\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Amarsar\"><PERSON><PERSON> of Amarsar</a>, Rajput chieftain (d. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Amarsar\" class=\"mw-redirect\" title=\"<PERSON><PERSON> of Amarsar\"><PERSON><PERSON> of Amarsar</a>, Rajput chieftain (d. 1488)", "links": [{"title": "<PERSON><PERSON> of Amarsar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Amarsar"}]}, {"year": "1473", "text": "<PERSON>, German Knight and landowner (d. 1528)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Fr<PERSON>\"><PERSON></a>, German Knight and landowner (d. 1528)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Knight and landowner (d. 1528)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1501", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian mathematician, physician, and astrologer (d. 1576)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"G<PERSON>lamo <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician, physician, and astrologer (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Gerolamo <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian mathematician, physician, and astrologer (d. 1576)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>lamo_<PERSON>ano"}]}, {"year": "1534", "text": "<PERSON>, fourth Sikh Guru (d. 1581)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth <a href=\"https://wikipedia.org/wiki/Sikh_Guru\" class=\"mw-redirect\" title=\"Sikh Guru\">Sikh Guru</a> (d. 1581)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth <a href=\"https://wikipedia.org/wiki/Sikh_Guru\" class=\"mw-redirect\" title=\"Sikh Guru\">Sikh Guru</a> (d. 1581)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sikh Guru", "link": "https://wikipedia.org/wiki/Sikh_Guru"}]}, {"year": "1564", "text": "<PERSON>, English sailor and navigator (d. 1620)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(sailor,_born_1564)\" class=\"mw-redirect\" title=\"<PERSON> (sailor, born 1564)\"><PERSON></a>, English sailor and navigator (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sailor,_born_1564)\" class=\"mw-redirect\" title=\"<PERSON> (sailor, born 1564)\"><PERSON></a>, English sailor and navigator (d. 1620)", "links": [{"title": "<PERSON> (sailor, born 1564)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sailor,_born_1564)"}]}, {"year": "1583", "text": "<PERSON><PERSON>, Bohemian general (d. 1634)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON> von Wall<PERSON>\"><PERSON><PERSON> von <PERSON></a>, Bohemian general (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_von_<PERSON>\" title=\"<PERSON><PERSON> von <PERSON>\"><PERSON><PERSON> von <PERSON></a>, Bohemian general (d. 1634)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON>, Dutch mathematician and politician (d. 1672)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and politician (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician and politician (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON><PERSON><PERSON>, French composer (d. 1688)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer (d. 1688)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1705", "text": "Count <PERSON>, Austrian field marshal (d. 1766)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian field marshal (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian field marshal (d. 1766)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, English historian, author, and politician (d. 1797)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and politician (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and politician (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, American Continental Army officer, jurist, and politician, 4th Chief Justice of the United States Supreme Court (d. 1835)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Continental Army officer, jurist, and politician, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Chief Justice of the United States Supreme Court\">Chief Justice of the United States Supreme Court</a> (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Continental Army officer, jurist, and politician, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States_Supreme_Court\" class=\"mw-redirect\" title=\"Chief Justice of the United States Supreme Court\">Chief Justice of the United States Supreme Court</a> (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States Supreme Court", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States_Supreme_Court"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, German-Danish composer and conductor (d. 1817)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/F.L.%C3%86._Kunzen\" class=\"mw-redirect\" title=\"F.L.<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Danish composer and conductor (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F.L.%C3%86._<PERSON>nz<PERSON>\" class=\"mw-redirect\" title=\"F.L.<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Danish composer and conductor (d. 1817)", "links": [{"title": "F.L.<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F.L.%C3%86._<PERSON><PERSON><PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, French sculptor and educator (d. 1875)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and educator (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and educator (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Ukrainian-Russian mathematician and physicist (d. 1862)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and physicist (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and physicist (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON><PERSON>, French paleontologist and geologist (d. 1868)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Archiac\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French paleontologist and geologist (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Archiac\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French paleontologist and geologist (d. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_d%27Archiac"}]}, {"year": "1812", "text": "<PERSON>, British poet and writer of musical scores (d. 1845)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British poet and writer of musical scores (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British poet and writer of musical scores (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Spanish poet and philosopher (d. 1901)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_de_Campoamor_y_Campoosorio\" title=\"<PERSON> Campoamor y Campoosorio\"><PERSON> y Campo<PERSON></a>, Spanish poet and philosopher (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_de_Campoamor_y_Campoosorio\" title=\"<PERSON> de Campoamor y Campoosorio\"><PERSON> y Campoosorio</a>, Spanish poet and philosopher (d. 1901)", "links": [{"title": "Ramón de Campoamor y Campoosorio", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_de_Campoamor_y_Campoosorio"}]}, {"year": "1829", "text": "<PERSON>, American jurist and politician, Secretary of State of Texas (d. 1885)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_West\" title=\"Charles <PERSON> West\"><PERSON></a>, American jurist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Texas\" title=\"Secretary of State of Texas\">Secretary of State of Texas</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_S._West\" title=\"Charles <PERSON> West\"><PERSON></a>, American jurist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_of_Texas\" title=\"Secretary of State of Texas\">Secretary of State of Texas</a> (d. 1885)", "links": [{"title": "Charles <PERSON>", "link": "https://wikipedia.org/wiki/Charles_S._West"}, {"title": "Secretary of State of Texas", "link": "https://wikipedia.org/wiki/Secretary_of_State_of_Texas"}]}, {"year": "1845", "text": "<PERSON>, Estonian philologist and author (d. 1905)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and author (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian philologist and author (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, American businessman and politician, 45th Governor of Massachusetts (d. 1939)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1859", "text": "<PERSON>, German cellist and composer (d. 1933)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist and composer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist and composer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian activist (d. 1936)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian activist (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1870", "text": "<PERSON>, French chemist and engineer, invented Neon lighting (d. 1960)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">Neon lighting</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Neon_lighting\" title=\"Neon lighting\">Neon lighting</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Neon lighting", "link": "https://wikipedia.org/wiki/Neon_lighting"}]}, {"year": "1871", "text": "<PERSON><PERSON>, English tennis player, golfer, and archer (d. 1960)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English tennis player, golfer, and archer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English tennis player, golfer, and archer (d. 1960)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 7th State Elder of Estonia (d. 1941)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/State_Elder_of_Estonia\" title=\"State Elder of Estonia\">State Elder of Estonia</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/State_Elder_of_Estonia\" title=\"State Elder of Estonia\">State Elder of Estonia</a> (d. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "State Elder of Estonia", "link": "https://wikipedia.org/wiki/State_Elder_of_Estonia"}]}, {"year": "1873", "text": "<PERSON> Mercedes <PERSON>, Cuban pianist and composer (d. 1957)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Adam_de_Ar%C3%B3stegui\" title=\"<PERSON> de las Mercedes Adam <PERSON> Aróstegui\"><PERSON></a>, Cuban pianist and composer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Adam_de_Ar%C3%B3stegui\" title=\"<PERSON> de las Mercedes Adam <PERSON> Aróstegui\"><PERSON></a>, Cuban pianist and composer (d. 1957)", "links": [{"title": "<PERSON> de las Mercedes Adam <PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%ADa_de_las_Mercedes_Adam_de_Ar%C3%B3stegui"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Swiss author and poet (d. 1947)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author and poet (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American super-centenarian, oldest verified American person ever (d. 1999)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American super-centenarian, oldest verified American person ever (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American super-centenarian, oldest verified American person ever (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, French tennis player (d. 1978)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>is"}]}, {"year": "1883", "text": "<PERSON>, American businessman, founded Mars, Incorporated (d. 1934)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Franklin Clarence Mars\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Mars,_Incorporated\" class=\"mw-redirect\" title=\"Mars, Incorporated\">Mars, Incorporated</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Franklin Clarence Mars\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Mars,_Incorporated\" class=\"mw-redirect\" title=\"Mars, Incorporated\">Mars, Incorporated</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mars, Incorporated", "link": "https://wikipedia.org/wiki/Mars,_Incorporated"}]}, {"year": "1883", "text": "<PERSON>, Scottish-American high jumper and coach (d. 1951)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American high jumper and coach (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American high jumper and coach (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, French cyclist (d. 1963)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Turkish general and politician, 2nd President of Turkey (d. 1973)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON>smet İnönü\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON>smet İnönü\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (d. 1973)", "links": [{"title": "İsmet İnönü", "link": "https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1884", "text": "<PERSON>, German weapons designer and engineer (d. 1953)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German weapons designer and engineer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German weapons designer and engineer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Estonian pianist, composer, and educator (d. 1963)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist, composer, and educator (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian pianist, composer, and educator (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Cuban baseball player, coach, and manager (d. 1977)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Cuban baseball player, coach, and manager (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)\" title=\"<PERSON> (catcher)\"><PERSON></a>, Cuban baseball player, coach, and manager (d. 1977)", "links": [{"title": "<PERSON> (catcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(catcher)"}]}, {"year": "1890", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author and playwright (d. 1971)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and playwright (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and playwright (d. 1971)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Canadian agronomist and politician, 15th Premier of Québec (d. 1956)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Ad%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian agronomist and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Québec</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian agronomist and politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Quebec_premiers\" class=\"mw-redirect\" title=\"List of Quebec premiers\">Premier of Québec</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ad%C3%A9<PERSON>_<PERSON>bout"}, {"title": "List of Quebec premiers", "link": "https://wikipedia.org/wiki/List_of_Quebec_premiers"}]}, {"year": "1893", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1929)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lemon_<PERSON>\" title=\"Blind Lemon Jefferson\"><PERSON></a>, American singer-songwriter and guitarist (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Lemon_<PERSON>\" title=\"Blind Lemon Jefferson\"><PERSON></a>, American singer-songwriter and guitarist (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lemon_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Scottish-American golfer and sportscaster (d. 1968)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American golfer and sportscaster (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Arm<PERSON>\"><PERSON></a>, Scottish-American golfer and sportscaster (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actor, singer, and screenwriter (d. 1979)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French physician and physiologist, Nobel Prize laureate (d. 1988)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand\" title=\"<PERSON>\"><PERSON></a>, French physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand\" title=\"<PERSON>\"><PERSON></a>, French physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Fr%C3%A9d%C3%A9ric_Cournand"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1896", "text": "<PERSON><PERSON>, American novelist and short story writer (d. 1940)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and short story writer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and short story writer (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Australian pharmacologist and pathologist, Nobel Prize laureate (d. 1968)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pharmacologist and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pharmacologist and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1898", "text": "<PERSON>, American astronomer (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Australian painter (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, British politician (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British politician (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American cartoonist (d. 1955)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fisher\"><PERSON></a>, American cartoonist (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Austrian neurologist and psychologist (d. 2001)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neurologist and psychologist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian neurologist and psychologist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Spanish-American physician and biochemist, Nobel Prize laureate (d. 1993)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Severo_Ochoa\" title=\"<PERSON><PERSON><PERSON>cho<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_<PERSON>cho<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish-American physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sever<PERSON>_Ochoa"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1906", "text": "<PERSON>, Canadian sociologist and academic (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sociologist and academic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sociologist and academic (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Polish footballer (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>wrot"}]}, {"year": "1907", "text": "<PERSON>, American pianist, composer, and songwriter (d. 1979)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ben_Oakland\" title=\"Ben Oakland\"><PERSON></a>, American pianist, composer, and songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ben_Oakland\" title=\"Ben Oakland\"><PERSON></a>, American pianist, composer, and songwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ben_Oakland"}]}, {"year": "1909", "text": "<PERSON>, Polish historian and architect (d. 1966)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%82ek\" title=\"<PERSON>\"><PERSON></a>, Polish historian and architect (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C5%82ek\" title=\"<PERSON>\"><PERSON></a>, Polish historian and architect (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Anton<PERSON>_Cio%C5%82ek"}]}, {"year": "1910", "text": "<PERSON>, Belgian-French actor (d. 1976)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French actor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Soviet politician (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet politician (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American author (d. 1998)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American singer (d. 2014)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Chinese-American film director (d. 1970)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American film director (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American film director (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian politician, 18th Governor-General of Australia (d. 1991)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor-general)\" title=\"<PERSON> (governor-general)\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 1991)", "links": [{"title": "<PERSON> (governor-general)", "link": "https://wikipedia.org/wiki/<PERSON>(governor-general)"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Polish pianist, composer, and conductor (d. 1991)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist, composer, and conductor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pianist, composer, and conductor (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American businesswoman and author (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Am<PERSON>\" title=\"<PERSON> Amonette\"><PERSON></a>, American businesswoman and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Am<PERSON>\" title=\"<PERSON> Amonette\"><PERSON></a>, American businesswoman and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Indian-born American theoretical chemist who developed the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> model (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-born American theoretical chemist who developed the <a href=\"https://wikipedia.org/wiki/Dewar%E2%80%93Chatt%E2%80%93Duncanson_model\" title=\"<PERSON><PERSON>-<PERSON><PERSON>-<PERSON> model\"><PERSON><PERSON>-<PERSON><PERSON>-<PERSON> model</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-born American theoretical chemist who developed the <a href=\"https://wikipedia.org/wiki/Dewar%E2%80%93Chatt%E2%80%93Dun<PERSON>on_model\" title=\"<PERSON><PERSON>-<PERSON><PERSON>-<PERSON> model\"><PERSON><PERSON>-<PERSON><PERSON>-<PERSON> model</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> model", "link": "https://wikipedia.org/wiki/Dewar%E2%80%93Chatt%E2%80%93Duncanson_model"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American actress (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dley"}]}, {"year": "1920", "text": "<PERSON>, American soldier and pilot, Medal of Honor recipient (d. 1945)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1920", "text": "<PERSON>, Guyanese-American author, poet, and playwright (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American author, poet, and playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese-American author, poet, and playwright (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Iraqi-Israeli rabbi and scholar (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-Israeli rabbi and scholar (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iraqi-Israeli rabbi and scholar (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American sportscaster and journalist (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English-American actress, singer, and dancer (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress, singer, and dancer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress, singer, and dancer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Italian actor and singer (d. 1967)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Etto<PERSON>_<PERSON>\" title=\"<PERSON>tto<PERSON> Ba<PERSON>ini\"><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ba<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor and singer (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American director, producer, and screenwriter (d. 2023)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress and singer (d. 1998)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English actor and playwright (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor and playwright (d. 2012)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American trumpet player and composer (d. 1950)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Navarro\"><PERSON><PERSON></a>, American trumpet player and composer (d. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Hungarian-American mathematician (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Ukrainian gymnast (d. 2020)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Egyptian-Greek actress (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Greek actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-Greek actress (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Indian physiologist and academic (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physiologist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physiologist and academic (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English-American actor and singer (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American clarinet player, saxophonist, and flute player (d. 1991)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American clarinet player, saxophonist, and flute player (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, American clarinet player, saxophonist, and flute player (d. 1991)", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(jazz_musician)"}]}, {"year": "1930", "text": "<PERSON>, American illustrator (d. 1985)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Polish poet and author (d. 1998)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>rupi%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>i%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Krupi%C5%84ski"}]}, {"year": "1930", "text": "<PERSON>, Maltese-English actor (d. 1977)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-English actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese-English actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Filipino politician and diplomat (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and diplomat (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician and diplomat (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American captain, engineer, and astronaut (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, engineer, and astronaut (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, engineer, and astronaut (d. 2018)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>(astronaut)"}]}, {"year": "1931", "text": "<PERSON>, Scottish painter and printmaker (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and printmaker (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and printmaker (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American lawyer and politician (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English journalist and author", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English singer and actor (d. 1999)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English race car driver (d. 1977)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Argentinian-Italian footballer and manager (d. 1998)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer and manager (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian footballer and manager (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German politician, Minister-President of Hesse (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Hesse\" class=\"mw-redirect\" title=\"List of Ministers-President of Hesse\">Minister-President of Hesse</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers-President_of_Hesse\" class=\"mw-redirect\" title=\"List of Ministers-President of Hesse\">Minister-President of Hesse</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Ministers-President of Hesse", "link": "https://wikipedia.org/wiki/List_of_Ministers-President_of_Hesse"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American drummer (d. 1996)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Scottish footballer and manager (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer and manager (d. 2018)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1934", "text": "<PERSON>, English-Scottish author and screenwriter (d. 1995)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-Scottish author and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" class=\"mw-redirect\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-Scottish author and screenwriter (d. 1995)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1934", "text": "<PERSON>, English art dealer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English art dealer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English art dealer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English painter, designer, and academic (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, designer, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter, designer, and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American singer and guitarist (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, German politician and diplomat, 7th Secretary General of NATO (d. 1994)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W%C3%B6rner\" title=\"<PERSON>\"><PERSON></a>, German politician and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rner\" title=\"<PERSON>\"><PERSON></a>, German politician and diplomat, 7th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manfred_W%C3%B6rner"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1934", "text": "<PERSON>, American director, screenwriter, and producer (d. 2015)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indian businessman (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American puppeteer, director, producer, and screenwriter, created <PERSON> Muppets (d. 1990)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, director, producer, and screenwriter, created <a href=\"https://wikipedia.org/wiki/The_Muppets\" title=\"The Muppets\">The Muppets</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer, director, producer, and screenwriter, created <a href=\"https://wikipedia.org/wiki/The_Muppets\" title=\"The Muppets\">The Muppets</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Muppets", "link": "https://wikipedia.org/wiki/The_Muppets"}]}, {"year": "1938", "text": "<PERSON>, American saxophonist, flute player, and producer (d. 1993)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist, flute player, and producer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American saxophonist, flute player, and producer (d. 1993)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1939", "text": "<PERSON>, American trombonist and producer (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trombonist and producer (d. 2014)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Israeli journalist (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli journalist (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, French ufologist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French ufologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON>\"><PERSON></a>, French ufologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1940", "text": "<PERSON>, French author (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yves <PERSON>\"><PERSON></a>, French author (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yves_<PERSON>\" title=\"Yves <PERSON>\"><PERSON></a>, French author (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2011)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1941", "text": "<PERSON>, American singer, photographer, and activist (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, photographer, and activist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, photographer, and activist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Irish poet and academic (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish poet and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish poet and academic (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eavan_Boland"}]}, {"year": "1944", "text": "Sven<PERSON><PERSON>, Danish bodybuilder and stuntman", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish bodybuilder and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish bodybuilder and stuntman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, <PERSON>'s 1967 Miss September and 1968 Playmate of the Year.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Victoria_Vetri\" title=\"Victoria Vetri\"><PERSON></a>, <PERSON>'s 1967 Miss September and 1968 Playmate of the Year.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Vetri\" title=\"Victoria Vetri\"><PERSON></a>, <PERSON>'s 1967 Miss September and 1968 Playmate of the Year.", "links": [{"title": "Victoria Vetri", "link": "https://wikipedia.org/wiki/Victoria_Vetri"}]}, {"year": "1945", "text": "<PERSON>, American journalist and author (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American comics creator and musician (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comics creator and musician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comics creator and musician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English composer, conductor, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American guitarist and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player, coach, and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Greenlandic educator and politician, 2nd Prime Minister of Greenland", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greenlandic educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Greenlandic educator and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greenland\" title=\"Prime Minister of Greenland\">Prime Minister of Greenland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Greenland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greenland"}]}, {"year": "1946", "text": "<PERSON>, Cuban pianist and songwriter (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Welsh-English cricketer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Chilean astronomer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean astronomer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American painter (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian-American actor and screenwriter (d. 1998)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and screenwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor and screenwriter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, New Zealand-Australian singer-songwriter and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Porter\"><PERSON></a>, New Zealand-Australian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, South African politician, Speaker of the National Assembly of South Africa", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_South_Africa\" title=\"Speaker of the National Assembly of South Africa\">Speaker of the National Assembly of South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_South_Africa\" title=\"Speaker of the National Assembly of South Africa\">Speaker of the National Assembly of South Africa</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>leka_Mbete"}, {"title": "Speaker of the National Assembly of South Africa", "link": "https://wikipedia.org/wiki/Speaker_of_the_National_Assembly_of_South_Africa"}]}, {"year": "1949", "text": "<PERSON>, Swedish cardinal", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian cricketer, coach, and sportscaster", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author, poet, and playwright", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American scholar and diplomat, United States Ambassador to Malta", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Malta\" class=\"mw-redirect\" title=\"United States Ambassador to Malta\">United States Ambassador to Malta</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Malta\" class=\"mw-redirect\" title=\"United States Ambassador to Malta\">United States Ambassador to Malta</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Malta", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Malta"}]}, {"year": "1952", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 1999)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Italian footballer and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian businessman and politician, President of Friuli Venezia Giulia", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Friuli-Venezia_Giulia\" class=\"mw-redirect\" title=\"List of Presidents of Friuli-Venezia Giulia\">President of Friuli Venezia Giulia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Friuli-Venezia_Giulia\" class=\"mw-redirect\" title=\"List of Presidents of Friuli-Venezia Giulia\">President of Friuli Venezia Giulia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Presidents of Friuli-Venezia Giulia", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Friuli-Venezia_Giulia"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American baseball player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Cypriot-English businessman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot-English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American puppeteer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian singer-songwriter, producer, and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sky\"><PERSON></a>, Canadian singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>g<PERSON>ber"}]}, {"year": "1961", "text": "<PERSON>, American screenwriter and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)"}]}, {"year": "1961", "text": "<PERSON>, Canadian actor, director, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English footballer, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English director and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>pple"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Canadian-American actress and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian painter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>al%C4%81ns\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>al%C4%81ns\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian painter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilgvars_Zal%C4%81ns"}]}, {"year": "1963", "text": "<PERSON>, Australian rugby player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English journalist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Cuban-American baseball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Estonian lawyer and politician, Estonian Minister of the Interior", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)\" class=\"mw-redirect\" title=\"Minister of the Interior (Estonia)\">Estonian Minister of the Interior</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marko_Pomerants"}, {"title": "Minister of the Interior (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Estonia)"}]}, {"year": "1964", "text": "<PERSON>, Dutch fashion designer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English chef and television host", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian saxophonist and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Nj%C3%A5l_%C3%98lnes\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nj%C3%A5l_%C3%98lnes\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian saxophonist and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nj%C3%A5l_%C3%98lnes"}]}, {"year": "1965", "text": "<PERSON>, American singer and bass player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American drummer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Indian voice actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian voice actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Irish-Canadian philosopher, author, and blogger", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian philosopher, author, and blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian philosopher, author, and blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American journalist and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, English economist, author, and academic", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English economist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English economist, author, and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tz"}]}, {"year": "1969", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English author, director, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>ham<PERSON>_<PERSON>rif\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ham<PERSON>_<PERSON>rif\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>f"}]}, {"year": "1969", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 2003)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American paleontologist and entomologist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and entomologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and entomologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gel"}]}, {"year": "1971", "text": "<PERSON>, American businessman and author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English drummer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, British politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player and sportscaster", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Scottish rower", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(infielder)"}]}, {"year": "1976", "text": "<PERSON>, Angolan basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Angolan basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Angolan basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American wrestler and businesswoman", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese voice actress and singer (d. 2013)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Yakku<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yakku<PERSON>_<PERSON>\" title=\"Yak<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Vahu<PERSON>_<PERSON>ahtram%C3%A4e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>ahtram%C3%A4e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vahu<PERSON>_Vahtram%C3%A4e"}]}, {"year": "1977", "text": "<PERSON>, German footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>r_Gbaja-Biamila\" title=\"Kabeer Gbaja-Biamila\"><PERSON><PERSON><PERSON>-Biam<PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>r_Gbaja-Biamila\" title=\"Kabeer Gbaja-Biamila\"><PERSON><PERSON><PERSON>-Biam<PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kabeer_Gbaja-Biamila"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Dutch archer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Wiet<PERSON>_van_<PERSON>\" title=\"<PERSON>iet<PERSON> van Alten\"><PERSON><PERSON><PERSON></a>, Dutch archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiet<PERSON>_van_<PERSON>\" title=\"Wiet<PERSON> van Alten\"><PERSON><PERSON><PERSON></a>, Dutch archer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wiet<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/F%C3%A1bio_Aur%C3%A9lio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A1bio_Aur%C3%A9lio\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A1bio_Aur%C3%A9lio"}]}, {"year": "1979", "text": "<PERSON>, South Korean singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Italian cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>en"}]}, {"year": "1980", "text": "<PERSON>, English cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Victoria_Pendleton\" title=\"Victoria Pendleton\"><PERSON></a>, English cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Pendleton\" title=\"Victoria Pendleton\"><PERSON></a>, English cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victoria_Pendleton"}]}, {"year": "1980", "text": "<PERSON>, Norwegian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Surov%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Surov%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Surov%C3%BD"}]}, {"year": "1982", "text": "<PERSON>, American gymnast", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American gymnast", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian-New Zealand singer-songwriter and guitarist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Finn\"><PERSON></a>, Australian-New Zealand singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Finn\"><PERSON></a>, Australian-New Zealand singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1985", "text": "<PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Austrian politician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Damon\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Damon\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> Damon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/G%C3%BCrhan_G%C3%BCrsoy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCrhan_G%C3%BCrsoy\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCrhan_G%C3%BCrsoy"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Filipina actress, model, and beauty queen, Miss Universe 2015", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina actress, model, and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_2015\" title=\"Miss Universe 2015\">Miss Universe 2015</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina actress, model, and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_2015\" title=\"Miss Universe 2015\">Miss Universe 2015</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Miss Universe 2015", "link": "https://wikipedia.org/wiki/Miss_Universe_2015"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> B<PERSON>t\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> B<PERSON>fict\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Oriol_Romeu\" title=\"Oriol Romeu\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oriol_Romeu\" title=\"Oriol Romeu\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "Or<PERSON>", "link": "https://wikipedia.org/wiki/Oriol_Romeu"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gg%C3%A8\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gg%C3%A8\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximiliano_Ugg%C3%A8"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American wrestler and mixed martial artist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actor, singer, and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rab<PERSON>yo\" title=\"Tosin Adarabioyo\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tosin Adarabioyo\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tosin_Adarabioyo"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American mass murderer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mass murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mass murderer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/J%C3%A9ssica_<PERSON>_<PERSON>\" title=\"Jéssica Bouzas <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9ssica_<PERSON>_<PERSON>\" title=\"Jéssica Bouzas <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "Jéssica <PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9ssica_<PERSON>_<PERSON>eiro"}]}, {"year": "2003", "text": "<PERSON>, Manx actor", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Manx actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "366", "text": "<PERSON>", "html": "366 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>ius\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "768", "text": "<PERSON><PERSON><PERSON> the <PERSON>, Frankish king (b. 714)", "html": "768 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Short\" title=\"<PERSON><PERSON><PERSON> the Short\"><PERSON><PERSON><PERSON> the Short</a>, Frankish king (b. 714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Short\" title=\"<PERSON><PERSON><PERSON> the Short\"><PERSON><PERSON><PERSON> the Short</a>, Frankish king (b. 714)", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Short"}]}, {"year": "887", "text": "<PERSON>, general of the Tang Dynasty (b. 821)", "html": "887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty (b. 821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty (b. 821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1054", "text": "<PERSON> of Reichenau, German composer, mathematician, and astronomer (b. 1013)", "html": "1054 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reichenau\" title=\"<PERSON> of Reichenau\"><PERSON> of Reichenau</a>, German composer, mathematician, and astronomer (b. 1013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reichena<PERSON>\" title=\"<PERSON> of Reichenau\"><PERSON> of Reichenau</a>, German composer, mathematician, and astronomer (b. 1013)", "links": [{"title": "<PERSON> Reichenau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reich<PERSON>"}]}, {"year": "1120", "text": "<PERSON><PERSON> <PERSON>, Duke of Bavaria (b. 1072)", "html": "1120 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON> <PERSON>, Duke of Bavaria\"><PERSON><PERSON> <PERSON>, Duke of Bavaria</a> (b. 1072)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON><PERSON> <PERSON>, Duke of Bavaria\"><PERSON><PERSON> <PERSON>, Duke of Bavaria</a> (b. 1072)", "links": [{"title": "<PERSON><PERSON> <PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_of_Bavaria"}]}, {"year": "1143", "text": "<PERSON> of Germany (b. 1072)", "html": "1143 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Waiblingen\" title=\"<PERSON> of Waiblingen\"><PERSON> of Germany</a> (b. 1072)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Waiblingen\" title=\"<PERSON> of Waiblingen\"><PERSON> of Germany</a> (b. 1072)", "links": [{"title": "<PERSON> of Waiblingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1143", "text": "<PERSON> II", "html": "1143 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_II\" title=\"Pope Innocent II\">Pope Innocent II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_II\" title=\"Pope Innocent II\">Pope <PERSON> II</a>", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_II"}]}, {"year": "1180", "text": "<PERSON>, Byzantine emperor (b. 1118)", "html": "1180 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1118)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1118)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1218", "text": "<PERSON> of Knaresborough (b. 1160)", "html": "1218 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Knaresborough\" title=\"<PERSON> of Knaresborough\"><PERSON> of Knaresborough</a> (b. 1160)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Knaresborough\" title=\"<PERSON> of Knaresborough\"><PERSON> of Knaresborough</a> (b. 1160)", "links": [{"title": "<PERSON> of Knaresborough", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1228", "text": "<PERSON> the First-Crowned, Serbian king (b. 1165)", "html": "1228 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_First-Crowned\" title=\"<PERSON> the First-Crowned\"><PERSON> the First-Crowned</a>, Serbian king (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_First-Crowned\" title=\"<PERSON> the First-Crowned\"><PERSON> the First-Crowned</a>, Serbian king (b. 1165)", "links": [{"title": "<PERSON> the First-Crowned", "link": "https://wikipedia.org/wiki/<PERSON>_the_First-Crowned"}]}, {"year": "1270", "text": "<PERSON> Montfort, Lord of Castres", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Montfort,_Lord_of_Castres\" title=\"<PERSON> Montfort, Lord of Castres\"><PERSON> Montfort, Lord of Castres</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mont<PERSON>,_Lord_of_Castres\" title=\"<PERSON> Montfort, Lord of Castres\"><PERSON> Montfort, Lord of Castres</a>", "links": [{"title": "<PERSON> Montfort, Lord of Castres", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Lord_of_Castres"}]}, {"year": "1275", "text": "<PERSON>, 2nd Earl of Hereford, English politician, Lord High Constable of England (b. 1208)", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Hereford\" title=\"<PERSON>, 2nd Earl of Hereford\"><PERSON>, 2nd Earl of Hereford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1208)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Hereford\" title=\"<PERSON>, 2nd Earl of Hereford\"><PERSON>, 2nd Earl of Hereford</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1208)", "links": [{"title": "<PERSON>, 2nd Earl of Hereford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Hereford"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1435", "text": "<PERSON><PERSON><PERSON> of Bavaria (b. 1370)", "html": "1435 - <a href=\"https://wikipedia.org/wiki/Isabeau_of_Bavaria\" title=\"Isabeau of Bavaria\">Isabeau of Bavaria</a> (b. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabeau_of_Bavaria\" title=\"Isabeau of Bavaria\"><PERSON><PERSON><PERSON> of Bavaria</a> (b. 1370)", "links": [{"title": "Isabeau of Bavaria", "link": "https://wikipedia.org/wiki/Isabeau_of_Bavaria"}]}, {"year": "1459", "text": "<PERSON> of Pomerania, King of Norway, Denmark and Sweden (b. 1382)", "html": "1459 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON> of Pomerania\"><PERSON> of Pomerania</a>, King of Norway, Denmark and Sweden (b. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Pomerania\" class=\"mw-redirect\" title=\"<PERSON> of Pomerania\"><PERSON> of Pomerania</a>, King of Norway, Denmark and Sweden (b. 1382)", "links": [{"title": "Eric of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pomerania"}]}, {"year": "1494", "text": "<PERSON><PERSON><PERSON>, Italian poet and scholar (b. 1454)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and scholar (b. 1454)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and scholar (b. 1454)", "links": [{"title": "Poliziano", "link": "https://wikipedia.org/wiki/Poliziano"}]}, {"year": "1534", "text": "<PERSON>, Lithuanian prince (b. c. 1470)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian prince (b. c. 1470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian prince (b. c. 1470)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1541", "text": "<PERSON><PERSON><PERSON>, German-Swiss physician, botanist, and chemist (b. 1493)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/Paracelsus\" title=\"Paracelsus\"><PERSON><PERSON><PERSON></a>, German-Swiss physician, botanist, and chemist (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paracelsus\" title=\"Paracelsus\"><PERSON><PERSON><PERSON></a>, German-Swiss physician, botanist, and chemist (b. 1493)", "links": [{"title": "Paracelsus", "link": "https://wikipedia.org/wiki/Paracelsus"}]}, {"year": "1545", "text": "<PERSON> of Mainz, German cardinal (b. 1490)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mainz\" class=\"mw-redirect\" title=\"<PERSON> of Mainz\"><PERSON> Mainz</a>, German cardinal (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Albert_of_Mainz\" class=\"mw-redirect\" title=\"<PERSON> of Mainz\"><PERSON> Mainz</a>, German cardinal (b. 1490)", "links": [{"title": "<PERSON> of Mainz", "link": "https://wikipedia.org/wiki/Albert_<PERSON>_Mainz"}]}, {"year": "1562", "text": "<PERSON>, 4th Earl of Kent, English politician (b. 1495)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Kent\" title=\"<PERSON>, 4th Earl of Kent\"><PERSON>, 4th Earl of Kent</a>, English politician (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Kent\" title=\"<PERSON>, 4th Earl of Kent\"><PERSON>, 4th Earl of Kent</a>, English politician (b. 1495)", "links": [{"title": "<PERSON>, 4th Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Kent"}]}, {"year": "1572", "text": "<PERSON><PERSON><PERSON><PERSON>, last of the Incas", "html": "1572 - <a href=\"https://wikipedia.org/wiki/T%C3%BApac_Amaru\" title=\"Túpac Amaru\">Tú<PERSON><PERSON></a>, last of the Incas", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%BApac_Amaru\" title=\"Túpac Amaru\">Tú<PERSON><PERSON></a>, last of the Incas", "links": [{"title": "Túpac <PERSON>", "link": "https://wikipedia.org/wiki/T%C3%BApac_Amaru"}]}, {"year": "1605", "text": "<PERSON>, Portuguese composer and educator (b. 1547)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and educator (b. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese composer and educator (b. 1547)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, Polish commander (b. 1560)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish commander (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish commander (b. 1560)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1646", "text": "<PERSON><PERSON>, Portuguese composer and educator (b. 1565)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bo\" title=\"<PERSON><PERSON> Lobo\"><PERSON><PERSON></a>, Portuguese composer and educator (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>bo\" title=\"<PERSON><PERSON> Lobo\"><PERSON><PERSON></a>, Portuguese composer and educator (b. 1565)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lobo"}]}, {"year": "1655", "text": "<PERSON>, Landgrave of Hesse-Eschwege (b. 1617)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Hesse-Eschwege\" title=\"<PERSON>, Landgrave of Hesse-Eschwege\"><PERSON>, Landgrave of Hesse-Eschwege</a> (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Hesse-Eschwege\" title=\"<PERSON>, Landgrave of Hesse-Eschwege\"><PERSON>, Landgrave of Hesse-Eschwege</a> (b. 1617)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Eschwege", "link": "https://wikipedia.org/wiki/<PERSON>,_Land<PERSON>_of_Hesse-Eschwege"}]}, {"year": "1707", "text": "<PERSON>, Italian poet and author (b. 1642)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1732", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1654)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>igen\" title=\"Emperor <PERSON>ige<PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>n\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1654)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>igen"}]}, {"year": "1742", "text": "<PERSON>, German mathematician, astronomer, and cartographer (b. 1684)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and cartographer (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and cartographer (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, English engraver (b. 1751)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engraver (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> She<PERSON>\"><PERSON></a>, English engraver (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, leader of the United Irishmen executed during the Irish Rebellion of 1798", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> executed during the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irishmen</a> executed during the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}, {"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}]}, {"year": "1802", "text": "<PERSON>, Russian author and critic (b. 1749)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and critic (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1834", "text": "<PERSON> of Brazil (b. 1798)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"Pedro I of Brazil\"><PERSON> of Brazil</a> (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"<PERSON> of Brazil\"><PERSON> of Brazil</a> (b. 1798)", "links": [{"title": "<PERSON> of Brazil", "link": "https://wikipedia.org/wiki/Pedro_I_of_Brazil"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON>, English painter and poet (b. 1817)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Branwell_Bront%C3%AB\" title=\"Branwell Brontë\"><PERSON><PERSON><PERSON> Bront<PERSON></a>, English painter and poet (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Branwell_Bront%C3%AB\" title=\"Branwell Brontë\"><PERSON><PERSON><PERSON> Brontë</a>, English painter and poet (b. 1817)", "links": [{"title": "Branwell Brontë", "link": "https://wikipedia.org/wiki/Branwell_Bront%C3%AB"}]}, {"year": "1863", "text": "<PERSON>, English businessman, founded <PERSON><PERSON><PERSON><PERSON> (b. 1794)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>ben<PERSON>\" title=\"Deben<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deben<PERSON>s"}]}, {"year": "1889", "text": "<PERSON><PERSON> <PERSON><PERSON>, American general and academic (b. 1821)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American general and academic (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American general and academic (b. 1821)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American balloonist and skydiver (b. 1856)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American balloonist and skydiver (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American balloonist and skydiver (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Irish-American soldier and composer (b. 1829)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American soldier and composer (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American soldier and composer (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Swedish lawyer and politician, 1st Prime Minister of Sweden (b. 1818)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Faroese-Danish physician and author, Nobel Prize laureate (b. 1860)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese-Danish physician and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese-Danish physician and author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai prince (b. 1892)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai prince (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Thai prince (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ul<PERSON>dej"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and politician, 9th Governor of West Virginia (b. 1857)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Governor_of_West_Virginia\" class=\"mw-redirect\" title=\"Governor of West Virginia\">Governor of West Virginia</a> (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of West Virginia", "link": "https://wikipedia.org/wiki/Governor_of_West_Virginia"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and actor (b. 1878)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and actor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English author (b. 1869)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovene priest and journalist (b. 1879)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene priest and journalist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovene priest and journalist (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1938", "text": "<PERSON>, Belarusian-Russian mathematician and academic (b. 1900)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian mathematician and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian mathematician and academic (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German-American film producer, founded Universal Studios (b. 1867)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American film producer, founded <a href=\"https://wikipedia.org/wiki/Universal_Pictures\" title=\"Universal Pictures\">Universal Studios</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American film producer, founded <a href=\"https://wikipedia.org/wiki/Universal_Pictures\" title=\"Universal Pictures\">Universal Studios</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Universal Pictures", "link": "https://wikipedia.org/wiki/Universal_Pictures"}]}, {"year": "1939", "text": "<PERSON>, American fencer (b. 1854)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, American fencer (b. 1854)", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)"}]}, {"year": "1945", "text": "<PERSON>, German physicist and academic, co-invented the Geiger counter (b. 1882)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, co-invented the <a href=\"https://wikipedia.org/wiki/Geiger_counter\" title=\"Gei<PERSON> counter\"><PERSON><PERSON><PERSON> counter</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, co-invented the <a href=\"https://wikipedia.org/wiki/Gei<PERSON>_counter\" title=\"Gei<PERSON> counter\"><PERSON><PERSON><PERSON> counter</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Geiger counter", "link": "https://wikipedia.org/wiki/Geiger_counter"}]}, {"year": "1947", "text": "<PERSON>, American historian and author (b. 1861)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor (b. 1894)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "Princess <PERSON> of Hesse and by Rhine (b. 1863)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine\" title=\"Princess <PERSON> of Hesse and by Rhine\">Princess <PERSON> of Hesse and by Rhine</a> (b. 1863)", "links": [{"title": "Princess <PERSON> of Hesse and by Rhine", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Hesse_and_by_Rhine"}]}, {"year": "1962", "text": "<PERSON>, American actor, director, and screenwriter (b. 1887)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Estonian-Australian wrestler and poet (b. 1887)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian-Australian wrestler and poet (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian-Australian wrestler and poet (b. 1887)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>sto"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Brazilian physician, geographer, and activist (b. 1908)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Josu%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician, geographer, and activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Josu%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician, geographer, and activist (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josu%C3%A9_de_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American businessman and politician, Mayor of Dallas (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Dallas\" class=\"mw-redirect\" title=\"List of mayors of Dallas\">Mayor of Dallas</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Dallas\" class=\"mw-redirect\" title=\"List of mayors of Dallas\">Mayor of Dallas</a> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Dallas", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Dallas"}]}, {"year": "1976", "text": "<PERSON>, Ghanaian composer and educator (b. 1904)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian composer and educator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian composer and educator (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American journalist and author (b. 1912)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American journalist and author (b. 1912)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1978", "text": "<PERSON>, German chemist and physicist (b. 1896)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_No<PERSON>ack\" title=\"<PERSON> Noddack\"><PERSON></a>, German chemist and physicist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_No<PERSON>\" title=\"<PERSON> Noddack\"><PERSON></a>, German chemist and physicist (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ddack"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German general and politician (b. 1897)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German general and politician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German general and politician (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Estonian-Brazilian director, producer, and cinematographer (b. 1896)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Brazilian director, producer, and cinematographer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Brazilian director, producer, and cinematographer (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actress and dancer (b. 1910)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actress (b. 1914)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (b. 1914)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>(actress)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Polish-English footballer (b. 1906)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English footballer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English footballer (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>wrot"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1899)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1899)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American children's book writer, poet, and illustrator (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Dr<PERSON>_<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON></a>, American children's book writer, poet, and illustrator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, American children's book writer, poet, and illustrator (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1957)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian physicist and academic (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American mountaineer, photographer, and scholar (b. 1932)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, American mountaineer, photographer, and scholar (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, American mountaineer, photographer, and scholar (b. 1932)", "links": [{"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter (b. 1931)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Zeki_M%C3%BCren\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zeki_M%C3%BCren\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zeki_M%C3%BCren"}]}, {"year": "1998", "text": "<PERSON>, American composer and screenwriter (b. 1942)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and screenwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and screenwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Chadian politician (b. 1953)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Youssouf_Togo%C3%AFmi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chadian politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Youssouf_Togo%C3%AFmi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chadian politician (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Youssouf_Togo%C3%AFmi"}]}, {"year": "2002", "text": "<PERSON>, American football player (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American singer and radio host (b. 1924)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and radio host (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and radio host (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, French author and screenwriter (b. 1935)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and screenwriter (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>n"}]}, {"year": "2006", "text": "<PERSON>, PIRA volunteer, lawyer, and politician (b. 1953)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA</a> volunteer, lawyer, and politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/PIRA\" class=\"mw-redirect\" title=\"PIRA\">PIRA</a> volunteer, lawyer, and politician (b. 1953)", "links": [{"title": "<PERSON> (Irish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Irish_politician)"}, {"title": "PIRA", "link": "https://wikipedia.org/wiki/PIRA"}]}, {"year": "2006", "text": "<PERSON>, Canadian soldier and runner (b. 1909)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and runner (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier and runner (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American screenwriter and author (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and coach (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Canadian author (b. 1975)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian author (b. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Russian engineer and politician, Vice President of the Soviet Union (b. 1937)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian engineer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Vice President of the Soviet Union</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian engineer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Vice President of the Soviet Union</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}]}, {"year": "2012", "text": "<PERSON>, French cyclist (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Polish-Canadian painter and educator (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian painter and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Mexican lawyer and politician, Governor of Oaxaca (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Oaxaca\" title=\"Governor of Oaxaca\">Governor of Oaxaca</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Oaxaca\" title=\"Governor of Oaxaca\">Governor of Oaxaca</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_V%C3%<PERSON><PERSON><PERSON>_Col<PERSON>ares"}, {"title": "Governor of Oaxaca", "link": "https://wikipedia.org/wiki/Governor_of_Oaxaca"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian architect and urban planner (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and urban planner (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and urban planner (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Ukrainian-Russian psychiatrist and author (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian psychiatrist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian psychiatrist and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-Hong Kong journalist and author (b. 1912)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English-Hong Kong journalist and author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English-Hong Kong journalist and author (b. 1912)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Kazakh general and politician (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Sagadat_Nurmagam<PERSON>ov\" title=\"Sagada<PERSON> Nurmagambetov\"><PERSON><PERSON><PERSON></a>, Kazakh general and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sagadat_N<PERSON>gam<PERSON>ov\" title=\"Sagada<PERSON> Nurmagambetov\"><PERSON><PERSON><PERSON></a>, Kazakh general and politician (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>dat_N<PERSON>gam<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1984)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1984)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2014", "text": "<PERSON>, Duchess of Devonshire, English aristocrat, socialite, and author (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON>, Duchess of Devonshire\"><PERSON>, Duchess of Devonshire</a>, English aristocrat, socialite, and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire\" title=\"<PERSON>, Duchess of Devonshire\"><PERSON>, Duchess of Devonshire</a>, English aristocrat, socialite, and author (b. 1920)", "links": [{"title": "<PERSON>, Duchess of Devonshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duchess_of_Devonshire"}]}, {"year": "2014", "text": "<PERSON>, English harpsichord player and conductor, founded the Academy of Ancient Music (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor, founded the <a href=\"https://wikipedia.org/wiki/Academy_of_Ancient_Music\" title=\"Academy of Ancient Music\">Academy of Ancient Music</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and conductor, founded the <a href=\"https://wikipedia.org/wiki/Academy_of_Ancient_Music\" title=\"Academy of Ancient Music\">Academy of Ancient Music</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}, {"title": "Academy of Ancient Music", "link": "https://wikipedia.org/wiki/Academy_of_Ancient_Music"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Estonian physicist, philosopher, and author (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5iv\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian physicist, philosopher, and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5iv\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian physicist, philosopher, and author (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Madis_K%C3%B5iv"}]}, {"year": "2015", "text": "<PERSON>, Australian painter and educator (b. 1914)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(war_artist)\" title=\"<PERSON> (war artist)\"><PERSON></a>, Australian painter and educator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(war_artist)\" title=\"<PERSON> (war artist)\"><PERSON></a>, Australian painter and educator (b. 1914)", "links": [{"title": "<PERSON> (war artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(war_artist)"}]}, {"year": "2015", "text": "<PERSON>, Chinese archaeologist and academic (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese archaeologist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese archaeologist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wang_Zhongshu"}]}, {"year": "2016", "text": "<PERSON>, Welsh footballer (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Russian footballer (b. 1979)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer (b. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Australian researcher, author and biologist (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian researcher, author and biologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian researcher, author and biologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1953)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, American accordionist and bandleader (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Buckwheat_Zydeco\" title=\"Buckwheat Zydeco\">Buckw<PERSON> Zydeco</a>, American accordionist and bandleader (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buckwheat_Zydeco\" title=\"Buckwheat Zydeco\">Buckw<PERSON> Zydeco</a>, American accordionist and bandleader (b. 1947)", "links": [{"title": "Buckwheat Zydeco", "link": "https://wikipedia.org/wiki/Buckwheat_Zydeco"}]}, {"year": "2020", "text": "<PERSON>, Australian cricketer, coach and commentator (b. 1961)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer, coach and commentator (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer, coach and commentator (b. 1961)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American jazz saxophonist (b. 1940)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American jazz saxophonist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American jazz saxophonist (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}