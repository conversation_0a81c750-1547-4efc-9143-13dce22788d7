{"date": "February 23", "url": "https://wikipedia.org/wiki/February_23", "data": {"Events": [{"year": "303", "text": "Roman emperor <PERSON><PERSON><PERSON><PERSON> orders the destruction of the Christian church in Nicomedia, beginning eight years of Di<PERSON><PERSON>ianic Persecution.", "html": "303 - <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Di<PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a> orders the destruction of the Christian church in <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a>, beginning eight years of <a href=\"https://wikipedia.org/wiki/Diocletianic_Persecution\" title=\"Diocletianic Persecution\">Di<PERSON><PERSON>ianic Persecution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/Di<PERSON><PERSON>ian\" title=\"<PERSON><PERSON><PERSON>ian\"><PERSON><PERSON><PERSON><PERSON></a> orders the destruction of the Christian church in <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a>, beginning eight years of <a href=\"https://wikipedia.org/wiki/Diocletianic_Persecution\" title=\"Diocletianic Persecution\">Di<PERSON><PERSON>ian<PERSON> Persecution</a>.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "Nicomedia", "link": "https://wikipedia.org/wiki/Nicomedia"}, {"title": "Diocletianic Persecution", "link": "https://wikipedia.org/wiki/Di<PERSON>letian<PERSON>_Persecution"}]}, {"year": "532", "text": "Byzantine emperor <PERSON><PERSON> <PERSON> lays the foundation stone of a new Orthodox Christian basilica in Constantinople - the Hagia Sophia.", "html": "532 - <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"Justinian I\"><PERSON><PERSON> I</a> lays the foundation stone of a new <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Orthodox Christian</a> <a href=\"https://wikipedia.org/wiki/Basilica\" title=\"Basilica\">basilica</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> - the <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_I\" title=\"Justinian I\"><PERSON><PERSON> I</a> lays the foundation stone of a new <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Orthodox Christian</a> <a href=\"https://wikipedia.org/wiki/Basilica\" title=\"Basilica\">basilica</a> in <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> - the <a href=\"https://wikipedia.org/wiki/Hagia_Sophia\" title=\"Hagia Sophia\">Hagia Sophia</a>.", "links": [{"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Eastern Orthodox Church", "link": "https://wikipedia.org/wiki/Eastern_Orthodox_Church"}, {"title": "Basilica", "link": "https://wikipedia.org/wiki/Basilica"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "Hagia Sophia", "link": "https://wikipedia.org/wiki/Hagia_Sophia"}]}, {"year": "628", "text": "<PERSON><PERSON><PERSON> <PERSON>, last Sasanian shah of Iran, is overthrown.", "html": "628 - <a href=\"https://wikipedia.org/wiki/Khosrow_II\" title=\"Khosrow II\"><PERSON><PERSON><PERSON> <PERSON></a>, last Sasanian shah of Iran, is overthrown.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>row_II\" title=\"Khosrow II\"><PERSON><PERSON><PERSON> <PERSON></a>, last Sasanian shah of Iran, is overthrown.", "links": [{"title": "Khosrow II", "link": "https://wikipedia.org/wiki/K<PERSON><PERSON>_II"}]}, {"year": "705", "text": "Empress <PERSON> abdicates the throne, restoring the Tang dynasty.", "html": "705 - Empress <a href=\"https://wikipedia.org/wiki/Wu_Z<PERSON>an\" title=\"<PERSON> Z<PERSON>an\"><PERSON></a> abdicates the throne, restoring the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>.", "no_year_html": "Empress <a href=\"https://wikipedia.org/wiki/Wu_Z<PERSON>\" title=\"<PERSON>an\"><PERSON></a> abdicates the throne, restoring the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wu_Zetian"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "1455", "text": "Traditionally the date of publication of the Gutenberg Bible, the first Western book printed with movable type.", "html": "1455 - Traditionally the date of publication of the <a href=\"https://wikipedia.org/wiki/Gutenberg_Bible\" title=\"Gutenberg Bible\">Gutenberg Bible</a>, the first <a href=\"https://wikipedia.org/wiki/Western_culture\" title=\"Western culture\">Western</a> <a href=\"https://wikipedia.org/wiki/Book\" title=\"Book\">book</a> printed with <a href=\"https://wikipedia.org/wiki/Movable_type\" title=\"Movable type\">movable type</a>.", "no_year_html": "Traditionally the date of publication of the <a href=\"https://wikipedia.org/wiki/Gutenberg_Bible\" title=\"Gutenberg Bible\">Gutenberg Bible</a>, the first <a href=\"https://wikipedia.org/wiki/Western_culture\" title=\"Western culture\">Western</a> <a href=\"https://wikipedia.org/wiki/Book\" title=\"Book\">book</a> printed with <a href=\"https://wikipedia.org/wiki/Movable_type\" title=\"Movable type\">movable type</a>.", "links": [{"title": "Gutenberg Bible", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bible"}, {"title": "Western culture", "link": "https://wikipedia.org/wiki/Western_culture"}, {"title": "Book", "link": "https://wikipedia.org/wiki/Book"}, {"title": "Movable type", "link": "https://wikipedia.org/wiki/Movable_type"}]}, {"year": "1725", "text": "<PERSON><PERSON> <PERSON><PERSON> leads his Tafel-Music Shepherd Cantata for the birthday of <PERSON>, Duke of Saxe-Weissenfels.", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads his <i><a href=\"https://wikipedia.org/wiki/Tafelmusik\" title=\"Tafelmusik\"><PERSON><PERSON>l-<PERSON></a></i> <i><a href=\"https://wikipedia.org/wiki/Entfliehet,_verschwindet,_entweichet,_ihr_<PERSON><PERSON>,_BWV_249a\" title=\"Entfliehet, verschwindet, entweichet, ihr Sorgen, BWV 249a\">Shepherd Cantata</a></i> for the birthday of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe<PERSON>Weissenfels\" title=\"<PERSON>, Duke of Saxe-Weissenfels\"><PERSON>, Duke of Saxe-Weissenfels</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads his <i><a href=\"https://wikipedia.org/wiki/Tafelmusik\" title=\"Tafelmusik\">Ta<PERSON>l-Music</a></i> <i><a href=\"https://wikipedia.org/wiki/Entfliehet,_verschwindet,_entweichet,_ihr_<PERSON><PERSON>,_BWV_249a\" title=\"Entfliehet, verschwindet, entweichet, ihr Sorgen, BWV 249a\">Shepherd Cantata</a></i> for the birthday of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe-Weissenfels\" title=\"<PERSON>, Duke of Saxe-Weissenfels\"><PERSON>, Duke of Saxe-Weissenfels</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tafelmusik", "link": "https://wikipedia.org/wiki/Tafelmusik"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON>, en<PERSON><PERSON><PERSON>, <PERSON><PERSON>, BWV 249a", "link": "https://wikipedia.org/wiki/Entf<PERSON><PERSON><PERSON>,_versch<PERSON><PERSON>,_ent<PERSON><PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>,_<PERSON>_249a"}, {"title": "<PERSON>, Duke of Saxe-Weissenfels", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxe-Weissenfels"}]}, {"year": "1763", "text": "Berbice slave uprising in Guyana: The first major slave revolt in South America.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Berbice_slave_uprising\" class=\"mw-redirect\" title=\"Berbice slave uprising\">Berbice slave uprising</a> in <a href=\"https://wikipedia.org/wiki/Guyana\" title=\"Guyana\">Guyana</a>: The first major slave revolt in South America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Berbice_slave_uprising\" class=\"mw-redirect\" title=\"Berbice slave uprising\">Berbice slave uprising</a> in <a href=\"https://wikipedia.org/wiki/Guyana\" title=\"Guyana\">Guyana</a>: The first major slave revolt in South America.", "links": [{"title": "Berbice slave uprising", "link": "https://wikipedia.org/wiki/Berbice_slave_uprising"}, {"title": "Guyana", "link": "https://wikipedia.org/wiki/Guyana"}]}, {"year": "1778", "text": "American Revolutionary War: Baron <PERSON> arrives at Valley Forge, Pennsylvania, to help train the Continental Army.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Baron <PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Valley_Forge,_Pennsylvania\" title=\"Valley Forge, Pennsylvania\">Valley Forge, Pennsylvania</a>, to help train the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Baron <PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Valley_Forge,_Pennsylvania\" title=\"Valley Forge, Pennsylvania\">Valley Forge, Pennsylvania</a>, to help train the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Valley Forge, Pennsylvania", "link": "https://wikipedia.org/wiki/Valley_Forge,_Pennsylvania"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}]}, {"year": "1820", "text": "Cato Street Conspiracy: A plot to murder all the British cabinet ministers is exposed and the conspirators arrested.", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Cato_Street_Conspiracy\" title=\"Cato Street Conspiracy\">Cato Street Conspiracy</a>: A plot to murder all the British cabinet ministers is exposed and the conspirators arrested.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cato_Street_Conspiracy\" title=\"Cato Street Conspiracy\">Cato Street Conspiracy</a>: A plot to murder all the British cabinet ministers is exposed and the conspirators arrested.", "links": [{"title": "Cato Street Conspiracy", "link": "https://wikipedia.org/wiki/Cato_Street_Conspiracy"}]}, {"year": "1836", "text": "Texas Revolution: The Siege of the Alamo (prelude to the Battle of the Alamo) begins in San Antonio, Texas.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_the_Alamo\" title=\"Siege of the Alamo\">Siege of the Alamo</a> (prelude to the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Alamo\" title=\"Battle of the Alamo\">Battle of the Alamo</a>) begins in <a href=\"https://wikipedia.org/wiki/San_Antonio,_Texas\" class=\"mw-redirect\" title=\"San Antonio, Texas\">San Antonio, Texas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Texas_Revolution\" title=\"Texas Revolution\">Texas Revolution</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_the_Alamo\" title=\"Siege of the Alamo\">Siege of the Alamo</a> (prelude to the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Alamo\" title=\"Battle of the Alamo\">Battle of the Alamo</a>) begins in <a href=\"https://wikipedia.org/wiki/San_Antonio,_Texas\" class=\"mw-redirect\" title=\"San Antonio, Texas\">San Antonio, Texas</a>.", "links": [{"title": "Texas Revolution", "link": "https://wikipedia.org/wiki/Texas_Revolution"}, {"title": "Siege of the Alamo", "link": "https://wikipedia.org/wiki/Siege_of_the_Alamo"}, {"title": "Battle of the Alamo", "link": "https://wikipedia.org/wiki/Battle_of_the_Alamo"}, {"title": "San Antonio, Texas", "link": "https://wikipedia.org/wiki/San_Antonio,_Texas"}]}, {"year": "1847", "text": "Mexican-American War: Battle of Buena Vista: In Mexico, American troops under future president General <PERSON> defeat Mexican General <PERSON>.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Buena_Vista\" title=\"Battle of Buena Vista\">Battle of Buena Vista</a>: In Mexico, American troops under future president General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat Mexican General <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Buena_Vista\" title=\"Battle of Buena Vista\">Battle of Buena Vista</a>: In Mexico, American troops under future president General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat Mexican General <a href=\"https://wikipedia.org/wiki/Antonio_L%C3%B3pez_de_Santa_Anna\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Battle of Buena Vista", "link": "https://wikipedia.org/wiki/Battle_of_Buena_Vista"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_L%C3%B3<PERSON><PERSON>_<PERSON>_Santa_Anna"}]}, {"year": "1854", "text": "The official independence of the Orange Free State, South Africa is declared.", "html": "1854 - The official independence of the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>, South Africa is declared.", "no_year_html": "The official independence of the <a href=\"https://wikipedia.org/wiki/Orange_Free_State\" title=\"Orange Free State\">Orange Free State</a>, South Africa is declared.", "links": [{"title": "Orange Free State", "link": "https://wikipedia.org/wiki/Orange_Free_State"}]}, {"year": "1861", "text": "President-elect <PERSON> arrives secretly in Washington, D.C., after the thwarting of an alleged assassination plot in Baltimore, Maryland.", "html": "1861 - President-elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives secretly in Washington, D.C., after the thwarting of an alleged <a href=\"https://wikipedia.org/wiki/Baltimore_Plot\" title=\"Baltimore Plot\">assassination plot</a> in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>, <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>.", "no_year_html": "President-elect <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives secretly in Washington, D.C., after the thwarting of an alleged <a href=\"https://wikipedia.org/wiki/Baltimore_Plot\" title=\"Baltimore Plot\">assassination plot</a> in <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>, <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Baltimore Plot", "link": "https://wikipedia.org/wiki/Baltimore_Plot"}, {"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}]}, {"year": "1870", "text": "Reconstruction Era: Post-U.S. Civil War military control of Mississippi ends and it is readmitted to the Union.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: Post-<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">U.S. Civil War</a> military control of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> ends and it is readmitted to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: Post-<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">U.S. Civil War</a> military control of <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> ends and it is readmitted to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "links": [{"title": "Reconstruction Era", "link": "https://wikipedia.org/wiki/Reconstruction_Era"}, {"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1883", "text": "Alabama becomes the first U.S. state to enact an anti-trust law.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> becomes the first <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> to enact an <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">anti-trust law</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> becomes the first <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> to enact an <a href=\"https://wikipedia.org/wiki/Competition_law\" title=\"Competition law\">anti-trust law</a>.", "links": [{"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Competition law", "link": "https://wikipedia.org/wiki/Competition_law"}]}, {"year": "1885", "text": "Sino-French War: French Army gains an important victory in the Battle of Đồng Đăng in the Tonkin region of Vietnam.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: French Army gains an important victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_%C4%90%E1%BB%93ng_%C4%90%C4%83ng_(1885)\" title=\"Battle of Đồng Đăng (1885)\">Battle of Đồng Đăng</a> in the <a href=\"https://wikipedia.org/wiki/Tonkin\" title=\"Tonkin\">Tonkin</a> region of Vietnam.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: French Army gains an important victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_%C4%90%E1%BB%93ng_%C4%90%C4%83ng_(1885)\" title=\"Battle of Đồng Đăng (1885)\">Battle of Đồng Đăng</a> in the <a href=\"https://wikipedia.org/wiki/Tonkin\" title=\"Tonkin\">Tonkin</a> region of Vietnam.", "links": [{"title": "Sino-French War", "link": "https://wikipedia.org/wiki/Sino-French_War"}, {"title": "Battle of Đồng Đăng (1885)", "link": "https://wikipedia.org/wiki/Battle_of_%C4%90%E1%BB%93ng_%C4%90%C4%83ng_(1885)"}, {"title": "Tonkin", "link": "https://wikipedia.org/wiki/Tonkin"}]}, {"year": "1886", "text": "<PERSON> produced the first samples of aluminium from the electrolysis of aluminium oxide, after several years of intensive work. He was assisted in this project by his older sister, <PERSON>.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles <PERSON> Hall\"><PERSON></a> produced the first samples of <a href=\"https://wikipedia.org/wiki/Aluminium\" title=\"Aluminium\">aluminium</a> from the electrolysis of aluminium oxide, after several years of intensive work. He was assisted in this project by his older sister, <a href=\"https://wikipedia.org/wiki/Julia_<PERSON>_Hall\" title=\"Julia Brainerd Hall\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Charles Martin Hall\"><PERSON></a> produced the first samples of <a href=\"https://wikipedia.org/wiki/Aluminium\" title=\"Aluminium\">aluminium</a> from the electrolysis of aluminium oxide, after several years of intensive work. He was assisted in this project by his older sister, <a href=\"https://wikipedia.org/wiki/Julia_Brain<PERSON>_Hall\" title=\"Julia Brainerd Hall\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Aluminium", "link": "https://wikipedia.org/wiki/Aluminium"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "The French Riviera is hit by a large earthquake, killing around 2,000.", "html": "1887 - The <a href=\"https://wikipedia.org/wiki/French_Riviera\" title=\"French Riviera\">French Riviera</a> is hit by a <a href=\"https://wikipedia.org/wiki/1887_Liguria_earthquake\" title=\"1887 Liguria earthquake\">large earthquake</a>, killing around 2,000.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Riviera\" title=\"French Riviera\">French Riviera</a> is hit by a <a href=\"https://wikipedia.org/wiki/1887_Liguria_earthquake\" title=\"1887 Liguria earthquake\">large earthquake</a>, killing around 2,000.", "links": [{"title": "French Riviera", "link": "https://wikipedia.org/wiki/French_Riviera"}, {"title": "1887 Liguria earthquake", "link": "https://wikipedia.org/wiki/1887_Liguria_earthquake"}]}, {"year": "1898", "text": "<PERSON><PERSON> is imprisoned in France after writing J'Accuse…!, a letter accusing the French government of antisemitism and wrongfully imprisoning Captain <PERSON>.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Zola\" title=\"É<PERSON>\"><PERSON><PERSON></a> is imprisoned in France after writing <i><a href=\"https://wikipedia.org/wiki/J%27Accuse%E2%80%A6!\" class=\"mw-redirect\" title=\"J'Accuse…!\">J'Accuse…!</a></i>, a letter accusing the French government of <a href=\"https://wikipedia.org/wiki/Antisemitism\" title=\"Antisemitism\">antisemitism</a> and wrongfully imprisoning <a href=\"https://wikipedia.org/wiki/Captain_(OF-2)\" class=\"mw-redirect\" title=\"Captain (OF-2)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>ola\" title=\"Émile <PERSON>\"><PERSON><PERSON></a> is imprisoned in France after writing <i><a href=\"https://wikipedia.org/wiki/J%27Accuse%E2%80%A6!\" class=\"mw-redirect\" title=\"J'Accuse…!\">J'Accuse…!</a></i>, a letter accusing the French government of <a href=\"https://wikipedia.org/wiki/Antisemitism\" title=\"Antisemitism\">antisemitism</a> and wrongfully imprisoning <a href=\"https://wikipedia.org/wiki/Captain_(OF-2)\" class=\"mw-redirect\" title=\"Captain (OF-2)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Zola"}, {"title": "J'Accuse…!", "link": "https://wikipedia.org/wiki/J%27Accuse%E2%80%A6!"}, {"title": "Antisemitism", "link": "https://wikipedia.org/wiki/Antisemitism"}, {"title": "Captain (OF-2)", "link": "https://wikipedia.org/wiki/Captain_(OF-2)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "Second Boer War: During the Battle of the Tugela Heights, the first British attempt to take Hart's Hill fails.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Tugela_Heights\" title=\"Battle of the Tugela Heights\">Battle of the Tugela Heights</a>, the first British attempt to take Hart's Hill fails.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: During the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Tugela_Heights\" title=\"Battle of the Tugela Heights\">Battle of the Tugela Heights</a>, the first British attempt to take Hart's Hill fails.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of the Tugela Heights", "link": "https://wikipedia.org/wiki/Battle_of_the_Tugela_Heights"}]}, {"year": "1903", "text": "Cuba leases Guantánamo Bay to the United States \"in perpetuity\".", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> leases <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantánamo Bay</a> to the United States \"in perpetuity\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> leases <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantánamo Bay</a> to the United States \"in perpetuity\".", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Guantanamo Bay Naval Base", "link": "https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base"}]}, {"year": "1905", "text": "Chicago attorney <PERSON> and three other businessmen meet for lunch to form the Rotary Club, the world's first service club.", "html": "1905 - Chicago attorney <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and three other businessmen meet for lunch to form the <a href=\"https://wikipedia.org/wiki/Rotary_Club\" class=\"mw-redirect\" title=\"Rotary Club\">Rotary Club</a>, the world's first <a href=\"https://wikipedia.org/wiki/Service_club\" title=\"Service club\">service club</a>.", "no_year_html": "Chicago attorney <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and three other businessmen meet for lunch to form the <a href=\"https://wikipedia.org/wiki/Rotary_Club\" class=\"mw-redirect\" title=\"Rotary Club\">Rotary Club</a>, the world's first <a href=\"https://wikipedia.org/wiki/Service_club\" title=\"Service club\">service club</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Rotary Club", "link": "https://wikipedia.org/wiki/Rotary_Club"}, {"title": "Service club", "link": "https://wikipedia.org/wiki/Service_club"}]}, {"year": "1909", "text": "The AEA Silver Dart makes the first powered flight in Canada and the British Empire.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/AEA_Silver_Dart\" title=\"AEA Silver Dart\">AEA Silver Dart</a> makes the first powered flight in Canada and the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/AEA_Silver_Dart\" title=\"AEA Silver Dart\">AEA Silver Dart</a> makes the first powered flight in Canada and the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a>.", "links": [{"title": "AEA Silver Dart", "link": "https://wikipedia.org/wiki/AEA_Silver_Dart"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}]}, {"year": "1917", "text": "First demonstrations in Saint Petersburg, Russia. The beginning of the February Revolution (March 8 in the Gregorian calendar).", "html": "1917 - First demonstrations in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia. The beginning of the <a href=\"https://wikipedia.org/wiki/February_Revolution\" title=\"February Revolution\">February Revolution</a> (March 8 in the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>).", "no_year_html": "First demonstrations in <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>, Russia. The beginning of the <a href=\"https://wikipedia.org/wiki/February_Revolution\" title=\"February Revolution\">February Revolution</a> (March 8 in the <a href=\"https://wikipedia.org/wiki/Gregorian_calendar\" title=\"Gregorian calendar\">Gregorian calendar</a>).", "links": [{"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}, {"title": "February Revolution", "link": "https://wikipedia.org/wiki/February_Revolution"}, {"title": "Gregorian calendar", "link": "https://wikipedia.org/wiki/Gregorian_calendar"}]}, {"year": "1927", "text": "U.S. President <PERSON> signs a bill by Congress establishing the Federal Radio Commission (later replaced by the Federal Communications Commission) which was to regulate the use of radio frequencies in the United States.", "html": "1927 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill by <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> establishing the <a href=\"https://wikipedia.org/wiki/Federal_Radio_Commission\" title=\"Federal Radio Commission\">Federal Radio Commission</a> (later replaced by the <a href=\"https://wikipedia.org/wiki/Federal_Communications_Commission\" title=\"Federal Communications Commission\">Federal Communications Commission</a>) which was to regulate the use of radio frequencies in the United States.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill by <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> establishing the <a href=\"https://wikipedia.org/wiki/Federal_Radio_Commission\" title=\"Federal Radio Commission\">Federal Radio Commission</a> (later replaced by the <a href=\"https://wikipedia.org/wiki/Federal_Communications_Commission\" title=\"Federal Communications Commission\">Federal Communications Commission</a>) which was to regulate the use of radio frequencies in the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Federal Radio Commission", "link": "https://wikipedia.org/wiki/Federal_Radio_Commission"}, {"title": "Federal Communications Commission", "link": "https://wikipedia.org/wiki/Federal_Communications_Commission"}]}, {"year": "1927", "text": "German theoretical physicist <PERSON> writes a letter to fellow physicist <PERSON>, in which he describes his uncertainty principle for the first time.", "html": "1927 - German <a href=\"https://wikipedia.org/wiki/Theoretical_physics\" title=\"Theoretical physics\">theoretical physicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes a letter to fellow physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in which he describes his <a href=\"https://wikipedia.org/wiki/Uncertainty_principle\" title=\"Uncertainty principle\">uncertainty principle</a> for the first time.", "no_year_html": "German <a href=\"https://wikipedia.org/wiki/Theoretical_physics\" title=\"Theoretical physics\">theoretical physicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> writes a letter to fellow physicist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, in which he describes his <a href=\"https://wikipedia.org/wiki/Uncertainty_principle\" title=\"Uncertainty principle\">uncertainty principle</a> for the first time.", "links": [{"title": "Theoretical physics", "link": "https://wikipedia.org/wiki/Theoretical_physics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Uncertainty principle", "link": "https://wikipedia.org/wiki/Uncertainty_principle"}]}, {"year": "1934", "text": "<PERSON> becomes King of Belgium.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON></a> becomes King of <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Belgium\" title=\"Leopold III of Belgium\"><PERSON></a> becomes King of <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Leopold_III_of_Belgium"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}]}, {"year": "1941", "text": "Plutonium is first produced and isolated by Dr<PERSON>.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">Plutonium</a> is first produced and isolated by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Plutonium\" title=\"Plutonium\">Plutonium</a> is first produced and isolated by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Plutonium", "link": "https://wikipedia.org/wiki/Plutonium"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "World War II: Japanese submarines fire artillery shells at the coastline near Santa Barbara, California.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> submarines <a href=\"https://wikipedia.org/wiki/Bombardment_of_Ellwood\" title=\"Bombardment of Ellwood\">fire artillery shells</a> at the coastline near <a href=\"https://wikipedia.org/wiki/Santa_Barbara,_California\" title=\"Santa Barbara, California\">Santa Barbara, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> submarines <a href=\"https://wikipedia.org/wiki/Bombardment_of_Ellwood\" title=\"Bombardment of Ellwood\">fire artillery shells</a> at the coastline near <a href=\"https://wikipedia.org/wiki/Santa_Barbara,_California\" title=\"Santa Barbara, California\">Santa Barbara, California</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Bombardment of Ellwood", "link": "https://wikipedia.org/wiki/Bombardment_of_Ellwood"}, {"title": "Santa Barbara, California", "link": "https://wikipedia.org/wiki/Santa_Barbara,_California"}]}, {"year": "1943", "text": "The Cavan Orphanage fire kills thirty-five girls and an elderly cook.", "html": "1943 - The <a href=\"https://wikipedia.org/wiki/Cavan_Orphanage_fire\" title=\"Cavan Orphanage fire\">Cavan Orphanage fire</a> kills thirty-five girls and an elderly cook.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cavan_Orphanage_fire\" title=\"Cavan Orphanage fire\">Cavan Orphanage fire</a> kills thirty-five girls and an elderly cook.", "links": [{"title": "Cavan Orphanage fire", "link": "https://wikipedia.org/wiki/Cavan_Orphanage_fire"}]}, {"year": "1943", "text": "Greek Resistance: The United Panhellenic Organization of Youth is founded in Greece.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Greek_Resistance\" class=\"mw-redirect\" title=\"Greek Resistance\">Greek Resistance</a>: The <a href=\"https://wikipedia.org/wiki/United_Panhellenic_Organization_of_Youth\" title=\"United Panhellenic Organization of Youth\">United Panhellenic Organization of Youth</a> is founded in Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_Resistance\" class=\"mw-redirect\" title=\"Greek Resistance\">Greek Resistance</a>: The <a href=\"https://wikipedia.org/wiki/United_Panhellenic_Organization_of_Youth\" title=\"United Panhellenic Organization of Youth\">United Panhellenic Organization of Youth</a> is founded in Greece.", "links": [{"title": "Greek Resistance", "link": "https://wikipedia.org/wiki/Greek_Resistance"}, {"title": "United Panhellenic Organization of Youth", "link": "https://wikipedia.org/wiki/United_Panhellenic_Organization_of_Youth"}]}, {"year": "1944", "text": "The Soviet Union begins the forced deportation of the Chechen and Ingush people from the North Caucasus to Central Asia.", "html": "1944 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins the <a href=\"https://wikipedia.org/wiki/Operation_Lentil_(Caucasus)\" class=\"mw-redirect\" title=\"Operation Lentil (Caucasus)\">forced deportation</a> of the <a href=\"https://wikipedia.org/wiki/Chechens\" title=\"Chechens\">Chechen</a> and <a href=\"https://wikipedia.org/wiki/Ingush_people\" title=\"Ingush people\">Ingush people</a> from the <a href=\"https://wikipedia.org/wiki/North_Caucasus\" title=\"North Caucasus\">North Caucasus</a> to Central Asia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins the <a href=\"https://wikipedia.org/wiki/Operation_Lentil_(Caucasus)\" class=\"mw-redirect\" title=\"Operation Lentil (Caucasus)\">forced deportation</a> of the <a href=\"https://wikipedia.org/wiki/Chechens\" title=\"Chechens\">Chechen</a> and <a href=\"https://wikipedia.org/wiki/Ingush_people\" title=\"Ingush people\">Ingush people</a> from the <a href=\"https://wikipedia.org/wiki/North_Caucasus\" title=\"North Caucasus\">North Caucasus</a> to Central Asia.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Operation Lentil (Caucasus)", "link": "https://wikipedia.org/wiki/Operation_Lentil_(Caucasus)"}, {"title": "Chechens", "link": "https://wikipedia.org/wiki/Chechens"}, {"title": "Ingush people", "link": "https://wikipedia.org/wiki/Ingush_people"}, {"title": "North Caucasus", "link": "https://wikipedia.org/wiki/North_Caucasus"}]}, {"year": "1945", "text": "World War II: During the Battle of Iwo Jima, a group of United States Marines reach the top of Mount Suribachi on the island and are photographed raising the American flag.", "html": "1945 - World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>, a group of <a href=\"https://wikipedia.org/wiki/United_States_Marine\" class=\"mw-redirect\" title=\"United States Marine\">United States Marines</a> reach the top of <a href=\"https://wikipedia.org/wiki/Mount_Suribachi\" title=\"Mount Suribachi\">Mount Suribachi</a> on the island and are <a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima\" title=\"Raising the Flag on Iwo Jima\">photographed raising the American flag</a>.", "no_year_html": "World War II: During the <a href=\"https://wikipedia.org/wiki/Battle_of_Iwo_Jima\" title=\"Battle of Iwo Jima\">Battle of Iwo Jima</a>, a group of <a href=\"https://wikipedia.org/wiki/United_States_Marine\" class=\"mw-redirect\" title=\"United States Marine\">United States Marines</a> reach the top of <a href=\"https://wikipedia.org/wiki/Mount_Suribachi\" title=\"Mount Suribachi\">Mount Suribachi</a> on the island and are <a href=\"https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima\" title=\"Raising the Flag on Iwo Jima\">photographed raising the American flag</a>.", "links": [{"title": "Battle of Iwo Jima", "link": "https://wikipedia.org/wiki/Battle_of_Iwo_Jima"}, {"title": "United States Marine", "link": "https://wikipedia.org/wiki/United_States_Marine"}, {"title": "Mount Suribachi", "link": "https://wikipedia.org/wiki/Mount_Suribachi"}, {"title": "Raising the Flag on Iwo Jima", "link": "https://wikipedia.org/wiki/Raising_the_Flag_on_Iwo_Jima"}]}, {"year": "1945", "text": "World War II: The 11th Airborne Division, with Filipino guerrillas, free all 2,147 captives of the Los Baños internment camp, in what General <PERSON> later would refer to as \"the textbook airborne operation for all ages and all armies.\"", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/11th_Airborne_Division_(United_States)\" class=\"mw-redirect\" title=\"11th Airborne Division (United States)\">11th Airborne Division</a>, with <a href=\"https://wikipedia.org/wiki/Filipinos\" title=\"Filipinos\">Filipino</a> guerrillas, free all 2,147 captives of the <a href=\"https://wikipedia.org/wiki/Los_Ba%C3%B1os,_Laguna\" title=\"Los Baños, Laguna\">Los Baños</a> internment camp, in what General <PERSON> later would refer to as \"the textbook airborne operation for all ages and all armies.\"", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/11th_Airborne_Division_(United_States)\" class=\"mw-redirect\" title=\"11th Airborne Division (United States)\">11th Airborne Division</a>, with <a href=\"https://wikipedia.org/wiki/Filipinos\" title=\"Filipinos\">Filipino</a> guerrillas, free all 2,147 captives of the <a href=\"https://wikipedia.org/wiki/Los_Ba%C3%B1os,_Laguna\" title=\"Los Baños, Laguna\">Los Baños</a> internment camp, in what General <PERSON> later would refer to as \"the textbook airborne operation for all ages and all armies.\"", "links": [{"title": "11th Airborne Division (United States)", "link": "https://wikipedia.org/wiki/11th_Airborne_Division_(United_States)"}, {"title": "Filipinos", "link": "https://wikipedia.org/wiki/Filipinos"}, {"title": "Los Baños, Laguna", "link": "https://wikipedia.org/wiki/Los_Ba%C3%B1os,_Laguna"}]}, {"year": "1945", "text": "World War II: The capital of the Philippines, Manila, is liberated by combined Filipino and American forces.", "html": "1945 - World War II: The capital of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, is liberated by combined Filipino and American forces.", "no_year_html": "World War II: The capital of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>, is liberated by combined Filipino and American forces.", "links": [{"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}]}, {"year": "1945", "text": "World War II: Capitulation of German garrison in Poznań. The city is liberated by Soviet and Polish forces.", "html": "1945 - World War II: Capitulation of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> garrison in <a href=\"https://wikipedia.org/wiki/Pozna%C5%84\" title=\"Poznań\">Poznań</a>. The city is <a href=\"https://wikipedia.org/wiki/Battle_of_Pozna%C5%84_(1945)\" title=\"Battle of Poznań (1945)\">liberated</a> by <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> and <a href=\"https://wikipedia.org/wiki/History_of_Poland_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"History of Poland (1939-45)\">Polish</a> forces.", "no_year_html": "World War II: Capitulation of <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> garrison in <a href=\"https://wikipedia.org/wiki/Pozna%C5%84\" title=\"Poznań\">Poznań</a>. The city is <a href=\"https://wikipedia.org/wiki/Battle_of_Pozna%C5%84_(1945)\" title=\"Battle of Poznań (1945)\">liberated</a> by <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> and <a href=\"https://wikipedia.org/wiki/History_of_Poland_(1939%E2%80%9345)\" class=\"mw-redirect\" title=\"History of Poland (1939-45)\">Polish</a> forces.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Poznań", "link": "https://wikipedia.org/wiki/Pozna%C5%84"}, {"title": "Battle of Poznań (1945)", "link": "https://wikipedia.org/wiki/Battle_of_Pozna%C5%84_(1945)"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "History of Poland (1939-45)", "link": "https://wikipedia.org/wiki/History_of_Poland_(1939%E2%80%9345)"}]}, {"year": "1945", "text": "World War II: The German town of Pforzheim is annihilated in a raid by 379 British bombers.", "html": "1945 - World War II: The German town of <a href=\"https://wikipedia.org/wiki/Pforzheim\" title=\"Pforzheim\">Pforzheim</a> is <a href=\"https://wikipedia.org/wiki/Bombing_of_Pforzheim_in_World_War_II\" title=\"Bombing of Pforzheim in World War II\">annihilated</a> in a raid by 379 British bombers.", "no_year_html": "World War II: The German town of <a href=\"https://wikipedia.org/wiki/Pforzheim\" title=\"Pforzheim\">Pforzheim</a> is <a href=\"https://wikipedia.org/wiki/Bombing_of_Pforzheim_in_World_War_II\" title=\"Bombing of Pforzheim in World War II\">annihilated</a> in a raid by 379 British bombers.", "links": [{"title": "Pforzheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rzheim"}, {"title": "Bombing of Pforzheim in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Pforzheim_in_World_War_II"}]}, {"year": "1945", "text": "American Airlines Flight 009 crashes near Rural Retreat, Virginia, killing 17.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_009\" title=\"American Airlines Flight 009\">American Airlines Flight 009</a> crashes near <a href=\"https://wikipedia.org/wiki/Rural_Retreat,_Virginia\" title=\"Rural Retreat, Virginia\">Rural Retreat, Virginia</a>, killing 17.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_009\" title=\"American Airlines Flight 009\">American Airlines Flight 009</a> crashes near <a href=\"https://wikipedia.org/wiki/Rural_Retreat,_Virginia\" title=\"Rural Retreat, Virginia\">Rural Retreat, Virginia</a>, killing 17.", "links": [{"title": "American Airlines Flight 009", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_009"}, {"title": "Rural Retreat, Virginia", "link": "https://wikipedia.org/wiki/Rural_Retreat,_Virginia"}]}, {"year": "1947", "text": "International Organization for Standardization is founded.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/International_Organization_for_Standardization\" title=\"International Organization for Standardization\">International Organization for Standardization</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/International_Organization_for_Standardization\" title=\"International Organization for Standardization\">International Organization for Standardization</a> is founded.", "links": [{"title": "International Organization for Standardization", "link": "https://wikipedia.org/wiki/International_Organization_for_Standardization"}]}, {"year": "1954", "text": "The first mass inoculation of children against polio with the Salk vaccine begins in Pittsburgh.", "html": "1954 - The first mass inoculation of children against <a href=\"https://wikipedia.org/wiki/Poliomyelitis\" class=\"mw-redirect\" title=\"Poliomyelitis\">polio</a> with the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Vaccine\" title=\"Vaccine\">vaccine</a> begins in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "no_year_html": "The first mass inoculation of children against <a href=\"https://wikipedia.org/wiki/Poliomyelitis\" class=\"mw-redirect\" title=\"Poliomyelitis\">polio</a> with the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Vaccine\" title=\"Vaccine\">vaccine</a> begins in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>.", "links": [{"title": "Poliomy<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Poliomyelitis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vaccine", "link": "https://wikipedia.org/wiki/Vaccine"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}]}, {"year": "1958", "text": "Five-time Argentine Formula One champion <PERSON> is kidnapped by rebels involved in the Cuban Revolution, on the eve of the Cuban Grand Prix. He was released the following day after the race.", "html": "1958 - Five-time Argentine <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a> is kidnapped by rebels involved in the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>, on the eve of the <a href=\"https://wikipedia.org/wiki/Cuban_Grand_Prix\" title=\"Cuban Grand Prix\">Cuban Grand Prix</a>. He was released the following day after the race.", "no_year_html": "Five-time Argentine <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> <a href=\"https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions\" title=\"List of Formula One World Drivers' Champions\">champion</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a> is kidnapped by rebels involved in the <a href=\"https://wikipedia.org/wiki/Cuban_Revolution\" title=\"Cuban Revolution\">Cuban Revolution</a>, on the eve of the <a href=\"https://wikipedia.org/wiki/Cuban_Grand_Prix\" title=\"Cuban Grand Prix\">Cuban Grand Prix</a>. He was released the following day after the race.", "links": [{"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "List of Formula One World Drivers' Champions", "link": "https://wikipedia.org/wiki/List_of_Formula_One_World_Drivers%27_Champions"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cuban Revolution", "link": "https://wikipedia.org/wiki/Cuban_Revolution"}, {"title": "Cuban Grand Prix", "link": "https://wikipedia.org/wiki/Cuban_Grand_Prix"}]}, {"year": "1966", "text": "In Syria, Ba'ath Party member <PERSON><PERSON> leads an intra-party military coup that replaces the previous government of General <PERSON><PERSON>, also a Baathist.", "html": "1966 - In <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> member <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Salah Jadid\"><PERSON><PERSON></a> leads an intra-party military <a href=\"https://wikipedia.org/wiki/Coup\" class=\"mw-redirect\" title=\"Coup\">coup</a> that <a href=\"https://wikipedia.org/wiki/1966_Syrian_coup_d%27%C3%A9tat\" title=\"1966 Syrian coup d'état\">replaces</a> the previous government of General <a href=\"https://wikipedia.org/wiki/Am<PERSON>_al-<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, also a Baathist.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/Ba%27ath_Party\" title=\"Ba'ath Party\">Ba'ath Party</a> member <a href=\"https://wikipedia.org/wiki/Sal<PERSON>_<PERSON>\" title=\"Salah Jadid\"><PERSON><PERSON></a> leads an intra-party military <a href=\"https://wikipedia.org/wiki/Coup\" class=\"mw-redirect\" title=\"Coup\">coup</a> that <a href=\"https://wikipedia.org/wiki/1966_Syrian_coup_d%27%C3%A9tat\" title=\"1966 Syrian coup d'état\">replaces</a> the previous government of General <a href=\"https://wikipedia.org/wiki/Am<PERSON>_al-<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, also a Baathist.", "links": [{"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Ba'ath Party", "link": "https://wikipedia.org/wiki/Ba%27ath_Party"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Coup", "link": "https://wikipedia.org/wiki/Coup"}, {"title": "1966 Syrian coup d'état", "link": "https://wikipedia.org/wiki/1966_Syrian_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1971", "text": "Operation Lam Son 719: South Vietnamese General <PERSON> was killed in a helicopter crash en route to taking control of the faltering campaign.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Operation_Lam_Son_719\" title=\"Operation Lam Son 719\">Operation Lam Son 719</a>: South Vietnamese General <a href=\"https://wikipedia.org/wiki/<PERSON>_Cao_Tri\" class=\"mw-redirect\" title=\"Do Cao Tri\"><PERSON></a> was killed in a helicopter crash en route to taking control of the faltering campaign.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Lam_Son_719\" title=\"Operation Lam Son 719\">Operation Lam Son 719</a>: South Vietnamese General <a href=\"https://wikipedia.org/wiki/Do_Cao_Tri\" class=\"mw-redirect\" title=\"Do Cao Tri\"><PERSON></a> was killed in a helicopter crash en route to taking control of the faltering campaign.", "links": [{"title": "Operation Lam Son 719", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_719"}, {"title": "Do <PERSON> Tri", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tri"}]}, {"year": "1974", "text": "The Symbionese Liberation Army demands $4 million more to release kidnap victim <PERSON>.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Symbionese_Liberation_Army\" title=\"Symbionese Liberation Army\">Symbionese Liberation Army</a> demands $4 million more to release kidnap victim <a href=\"https://wikipedia.org/wiki/<PERSON>_Hearst\" title=\"<PERSON> Hearst\"><PERSON> Hearst</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Symbionese_Liberation_Army\" title=\"Symbionese Liberation Army\">Symbionese Liberation Army</a> demands $4 million more to release kidnap victim <a href=\"https://wikipedia.org/wiki/<PERSON>_Hearst\" title=\"<PERSON> Hearst\"><PERSON> Hearst</a>.", "links": [{"title": "Symbionese Liberation Army", "link": "https://wikipedia.org/wiki/Symbionese_Liberation_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>st"}]}, {"year": "1980", "text": "Iran hostage crisis: Supreme Leader <PERSON><PERSON><PERSON><PERSON> states that Iran's parliament will decide the fate of the American embassy hostages.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader</a> <a href=\"https://wikipedia.org/wiki/Ayatollah\" title=\"Ayatollah\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_Khomeini\" title=\"<PERSON><PERSON><PERSON><PERSON>homeini\"><PERSON><PERSON><PERSON><PERSON></a> states that Iran's parliament will decide the fate of the American embassy hostages.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: <a href=\"https://wikipedia.org/wiki/Supreme_Leader_of_Iran\" title=\"Supreme Leader of Iran\">Supreme Leader</a> <a href=\"https://wikipedia.org/wiki/Ayatollah\" title=\"Ayatollah\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>homeini\" title=\"<PERSON><PERSON><PERSON><PERSON>homeini\"><PERSON><PERSON><PERSON><PERSON></a> states that Iran's parliament will decide the fate of the American embassy hostages.", "links": [{"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Supreme Leader of Iran", "link": "https://wikipedia.org/wiki/Supreme_Leader_of_Iran"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}]}, {"year": "1981", "text": "In Spain, <PERSON> attempts a coup d'état by capturing the Spanish Congress of Deputies.", "html": "1981 - In Spain, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts <a href=\"https://wikipedia.org/wiki/23-F\" class=\"mw-redirect\" title=\"23-F\">a coup d'état</a> by capturing the <a href=\"https://wikipedia.org/wiki/Congress_of_Deputies_(Spain)\" class=\"mw-redirect\" title=\"Congress of Deputies (Spain)\">Spanish Congress of Deputies</a>.", "no_year_html": "In Spain, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attempts <a href=\"https://wikipedia.org/wiki/23-F\" class=\"mw-redirect\" title=\"23-F\">a coup d'état</a> by capturing the <a href=\"https://wikipedia.org/wiki/Congress_of_Deputies_(Spain)\" class=\"mw-redirect\" title=\"Congress of Deputies (Spain)\">Spanish Congress of Deputies</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "23-F", "link": "https://wikipedia.org/wiki/23-F"}, {"title": "Congress of Deputies (Spain)", "link": "https://wikipedia.org/wiki/Congress_of_Deputies_(Spain)"}]}, {"year": "1983", "text": "The United States Environmental Protection Agency announces its intent to buy out and evacuate the dioxin-contaminated community of Times Beach, Missouri.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/United_States_Environmental_Protection_Agency\" title=\"United States Environmental Protection Agency\">United States Environmental Protection Agency</a> announces its intent to buy out and evacuate the <a href=\"https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins\" title=\"Polychlorinated dibenzodioxins\">dioxin</a>-contaminated community of <a href=\"https://wikipedia.org/wiki/Times_Beach,_Missouri\" title=\"Times Beach, Missouri\">Times Beach, Missouri</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Environmental_Protection_Agency\" title=\"United States Environmental Protection Agency\">United States Environmental Protection Agency</a> announces its intent to buy out and evacuate the <a href=\"https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins\" title=\"Polychlorinated dibenzodioxins\">dioxin</a>-contaminated community of <a href=\"https://wikipedia.org/wiki/Times_Beach,_Missouri\" title=\"Times Beach, Missouri\">Times Beach, Missouri</a>.", "links": [{"title": "United States Environmental Protection Agency", "link": "https://wikipedia.org/wiki/United_States_Environmental_Protection_Agency"}, {"title": "Polychlorinated dibenzodioxins", "link": "https://wikipedia.org/wiki/Polychlorinated_dibenzodioxins"}, {"title": "Times Beach, Missouri", "link": "https://wikipedia.org/wiki/Times_Beach,_Missouri"}]}, {"year": "1987", "text": "Supernova 1987a is seen in the Large Magellanic Cloud.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Supernova_1987a\" class=\"mw-redirect\" title=\"Supernova 1987a\">Supernova 1987a</a> is seen in the <a href=\"https://wikipedia.org/wiki/Large_Magellanic_Cloud\" title=\"Large Magellanic Cloud\">Large Magellanic Cloud</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Supernova_1987a\" class=\"mw-redirect\" title=\"Supernova 1987a\">Supernova 1987a</a> is seen in the <a href=\"https://wikipedia.org/wiki/Large_Magellanic_Cloud\" title=\"Large Magellanic Cloud\">Large Magellanic Cloud</a>.", "links": [{"title": "Supernova 1987a", "link": "https://wikipedia.org/wiki/Supernova_1987a"}, {"title": "Large Magellanic Cloud", "link": "https://wikipedia.org/wiki/Large_Magellanic_Cloud"}]}, {"year": "1988", "text": "<PERSON> begins the Anfal genocide against Kurds and Assyrians in northern Iraq.", "html": "1988 - <PERSON> begins the <a href=\"https://wikipedia.org/wiki/Anfal_genocide\" class=\"mw-redirect\" title=\"Anfal genocide\">Anfal genocide</a> against Kurds and Assyrians in northern Iraq.", "no_year_html": "<PERSON> begins the <a href=\"https://wikipedia.org/wiki/Anfal_genocide\" class=\"mw-redirect\" title=\"Anfal genocide\">Anfal genocide</a> against Kurds and Assyrians in northern Iraq.", "links": [{"title": "Anfal genocide", "link": "https://wikipedia.org/wiki/Anfal_genocide"}]}, {"year": "1991", "text": "In Thailand, General <PERSON><PERSON><PERSON> leads a bloodless coup d'état, deposing Prime Minister <PERSON><PERSON><PERSON>.", "html": "1991 - In <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, General <a href=\"https://wikipedia.org/wiki/Sunthorn_Kongsompong\" title=\"Sunthorn Kongsompong\">Sunthorn Kongsompong</a> leads a bloodless <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, <a href=\"https://wikipedia.org/wiki/1991_Thai_coup_d%27%C3%A9tat\" title=\"1991 Thai coup d'état\">deposing</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>van\" title=\"Chatic<PERSON> Cho<PERSON>havan\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>, General <a href=\"https://wikipedia.org/wiki/Sunthorn_Kongsompong\" title=\"Sunthorn Kongsompong\">Sunthorn Kongsompong</a> leads a bloodless <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, <a href=\"https://wikipedia.org/wiki/1991_Thai_coup_d%27%C3%A9tat\" title=\"1991 Thai coup d'état\">deposing</a> Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cho<PERSON>van\" title=\"Cha<PERSON><PERSON> Cho<PERSON>havan\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "Sunthorn Kongsompong", "link": "https://wikipedia.org/wiki/Sunthorn_Kongsompong"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "1991 Thai coup d'état", "link": "https://wikipedia.org/wiki/1991_Thai_coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>van"}]}, {"year": "1998", "text": "In the United States, tornadoes in central Florida destroy or damage 2,600 structures and kill 42 people.", "html": "1998 - In the United States, <a href=\"https://wikipedia.org/wiki/1998_Kissimmee_tornado_outbreak\" title=\"1998 Kissimmee tornado outbreak\">tornadoes in central Florida</a> destroy or damage 2,600 structures and kill 42 people.", "no_year_html": "In the United States, <a href=\"https://wikipedia.org/wiki/1998_Kissimmee_tornado_outbreak\" title=\"1998 Kissimmee tornado outbreak\">tornadoes in central Florida</a> destroy or damage 2,600 structures and kill 42 people.", "links": [{"title": "1998 Kissimmee tornado outbreak", "link": "https://wikipedia.org/wiki/1998_Kissimmee_tornado_outbreak"}]}, {"year": "1999", "text": "Kurdish rebel leader <PERSON> is charged with treason in Ankara, Turkey.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96calan\" title=\"<PERSON>\"><PERSON></a> is charged with <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kurds\" title=\"Kurds\">Kurdish</a> rebel leader <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96calan\" title=\"<PERSON>\"><PERSON></a> is charged with <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a>, Turkey.", "links": [{"title": "Kurds", "link": "https://wikipedia.org/wiki/Kurds"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%96calan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}, {"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}]}, {"year": "1999", "text": "An avalanche buries the town of Galtür, Austria, killing 31.", "html": "1999 - An <a href=\"https://wikipedia.org/wiki/Avalanche\" title=\"Avalanche\">avalanche</a> <a href=\"https://wikipedia.org/wiki/1999_Galt%C3%BCr_avalanche\" title=\"1999 Galtür avalanche\">buries the town</a> of <a href=\"https://wikipedia.org/wiki/Galt%C3%BCr\" title=\"Galtür\">Galtür</a>, Austria, killing 31.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Avalanche\" title=\"Avalanche\">avalanche</a> <a href=\"https://wikipedia.org/wiki/1999_Galt%C3%BCr_avalanche\" title=\"1999 Galtür avalanche\">buries the town</a> of <a href=\"https://wikipedia.org/wiki/Galt%C3%BCr\" title=\"Galtür\">Galtür</a>, Austria, killing 31.", "links": [{"title": "Avalanche", "link": "https://wikipedia.org/wiki/Avalanche"}, {"title": "1999 Galtür avalanche", "link": "https://wikipedia.org/wiki/1999_Galt%C3%BCr_avalanche"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Galt%C3%BCr"}]}, {"year": "2007", "text": "A train derails on an evening express service near Grayrigg, Cumbria, England, killing one person and injuring 88. This results in hundreds of points being checked over the UK after a few similar accidents.", "html": "2007 - A <a href=\"https://wikipedia.org/wiki/<PERSON>rig<PERSON>_derailment\" title=\"Grayrigg derailment\">train derails</a> on an evening express service near <a href=\"https://wikipedia.org/wiki/Grayrigg\" title=\"<PERSON>rigg\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cumbria\" title=\"Cumbria\">Cumbria</a>, England, killing one person and injuring 88. This results in hundreds of points being checked over the UK after a few similar accidents.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_derailment\" title=\"Grayrigg derailment\">train derails</a> on an evening express service near <a href=\"https://wikipedia.org/wiki/Grayrigg\" title=\"Grayrigg\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Cumbria\" title=\"Cumbria\">Cumbria</a>, England, killing one person and injuring 88. This results in hundreds of points being checked over the UK after a few similar accidents.", "links": [{"title": "<PERSON><PERSON><PERSON> derailment", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_derailment"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rigg"}, {"title": "Cumbria", "link": "https://wikipedia.org/wiki/Cumbria"}]}, {"year": "2008", "text": "A United States Air Force B-2 Spirit bomber crashes on Guam, marking  the first operational loss of a B-2.", "html": "2008 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/B-2_Spirit\" class=\"mw-redirect\" title=\"B-2 Spirit\">B-2 Spirit</a> bomber <a href=\"https://wikipedia.org/wiki/2008_Andersen_Air_Force_Base_B-2_accident\" title=\"2008 Andersen Air Force Base B-2 accident\">crashes on Guam</a>, marking the first operational loss of a B-2.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/B-2_Spirit\" class=\"mw-redirect\" title=\"B-2 Spirit\">B-2 Spirit</a> bomber <a href=\"https://wikipedia.org/wiki/2008_Andersen_Air_Force_Base_B-2_accident\" title=\"2008 Andersen Air Force Base B-2 accident\">crashes on Guam</a>, marking the first operational loss of a B-2.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "B-2 Spirit", "link": "https://wikipedia.org/wiki/B-2_<PERSON>"}, {"title": "2008 Andersen Air Force Base B-2 accident", "link": "https://wikipedia.org/wiki/2008_<PERSON>_Air_Force_Base_B-2_accident"}]}, {"year": "2010", "text": "Unknown criminals pour more than .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}2+1⁄2 million liters of diesel oil and other hydrocarbons into the river Lambro, in northern Italy, sparking an environmental disaster.", "html": "2010 - Unknown criminals pour more than <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">2<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million liters of <a href=\"https://wikipedia.org/wiki/Diesel_oil\" class=\"mw-redirect\" title=\"Diesel oil\">diesel oil</a> and other <a href=\"https://wikipedia.org/wiki/Hydrocarbon\" title=\"Hydrocarbon\">hydrocarbons</a> into the river <a href=\"https://wikipedia.org/wiki/Lambro\" title=\"Lambro\">Lambro</a>, in northern Italy, sparking an <a href=\"https://wikipedia.org/wiki/Environmental_disaster\" title=\"Environmental disaster\">environmental disaster</a>.", "no_year_html": "Unknown criminals pour more than <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">2<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million liters of <a href=\"https://wikipedia.org/wiki/Diesel_oil\" class=\"mw-redirect\" title=\"Diesel oil\">diesel oil</a> and other <a href=\"https://wikipedia.org/wiki/Hydrocarbon\" title=\"Hydrocarbon\">hydrocarbons</a> into the river <a href=\"https://wikipedia.org/wiki/Lambro\" title=\"Lambro\">Lambro</a>, in northern Italy, sparking an <a href=\"https://wikipedia.org/wiki/Environmental_disaster\" title=\"Environmental disaster\">environmental disaster</a>.", "links": [{"title": "Diesel oil", "link": "https://wikipedia.org/wiki/Diesel_oil"}, {"title": "Hydrocarbon", "link": "https://wikipedia.org/wiki/Hydrocarbon"}, {"title": "Lambro", "link": "https://wikipedia.org/wiki/Lambro"}, {"title": "Environmental disaster", "link": "https://wikipedia.org/wiki/Environmental_disaster"}]}, {"year": "2012", "text": "A series of attacks across Iraq leave at least 83 killed and more than 250 injured.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/23_February_2012_Iraq_attacks\" title=\"23 February 2012 Iraq attacks\">series of attacks</a> across <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> leave at least 83 killed and more than 250 injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/23_February_2012_Iraq_attacks\" title=\"23 February 2012 Iraq attacks\">series of attacks</a> across <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> leave at least 83 killed and more than 250 injured.", "links": [{"title": "23 February 2012 Iraq attacks", "link": "https://wikipedia.org/wiki/23_February_2012_Iraq_attacks"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2017", "text": "The Turkish-backed Free Syrian Army captures Al-Bab from ISIL.", "html": "2017 - The Turkish-backed <a href=\"https://wikipedia.org/wiki/Free_Syrian_Army\" title=\"Free Syrian Army\">Free Syrian Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_al-Bab\" title=\"Battle of al-Bab\">captures Al-Ba<PERSON></a> from <a href=\"https://wikipedia.org/wiki/ISIL\" class=\"mw-redirect\" title=\"ISIL\">ISIL</a>.", "no_year_html": "The Turkish-backed <a href=\"https://wikipedia.org/wiki/Free_Syrian_Army\" title=\"Free Syrian Army\">Free Syrian Army</a> <a href=\"https://wikipedia.org/wiki/Battle_of_al-Bab\" title=\"Battle of al-Bab\">captures Al-Bab</a> from <a href=\"https://wikipedia.org/wiki/ISIL\" class=\"mw-redirect\" title=\"ISIL\">ISIL</a>.", "links": [{"title": "Free Syrian Army", "link": "https://wikipedia.org/wiki/Free_Syrian_Army"}, {"title": "Battle of al-Bab", "link": "https://wikipedia.org/wiki/Battle_of_al-Bab"}, {"title": "ISIL", "link": "https://wikipedia.org/wiki/ISIL"}]}, {"year": "2019", "text": "Atlas Air Flight 3591, a Boeing 767 freighter, crashes into Trinity Bay near Anahuac, Texas, killing all three people on board.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Atlas_Air_Flight_3591\" title=\"Atlas Air Flight 3591\">Atlas Air Flight 3591</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_767\" title=\"Boeing 767\">Boeing 767</a> freighter, crashes into <a href=\"https://wikipedia.org/wiki/Trinity_Bay_(Texas)\" title=\"Trinity Bay (Texas)\">Trinity Bay</a> near <a href=\"https://wikipedia.org/wiki/Anahuac,_Texas\" title=\"Anahuac, Texas\">Anahuac, Texas</a>, killing all three people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atlas_Air_Flight_3591\" title=\"Atlas Air Flight 3591\">Atlas Air Flight 3591</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_767\" title=\"Boeing 767\">Boeing 767</a> freighter, crashes into <a href=\"https://wikipedia.org/wiki/Trinity_Bay_(Texas)\" title=\"Trinity Bay (Texas)\">Trinity Bay</a> near <a href=\"https://wikipedia.org/wiki/Anahuac,_Texas\" title=\"Anahuac, Texas\">Anahuac, Texas</a>, killing all three people on board.", "links": [{"title": "Atlas Air Flight 3591", "link": "https://wikipedia.org/wiki/Atlas_Air_Flight_3591"}, {"title": "Boeing 767", "link": "https://wikipedia.org/wiki/Boeing_767"}, {"title": "Trinity Bay (Texas)", "link": "https://wikipedia.org/wiki/Trinity_Bay_(Texas)"}, {"title": "Anahuac, Texas", "link": "https://wikipedia.org/wiki/Anahuac,_Texas"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, a 25-year-old African-American citizen, is shot and murdered by three white men after visiting a house under construction while jogging at a neighborhood in Satilla Shores near Brunswick in Glynn County, Georgia.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Murder of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a 25-year-old African-American citizen, is shot and murdered by three white men after visiting a house under construction while jogging at a neighborhood in Satilla Shores near <a href=\"https://wikipedia.org/wiki/Brunswick,_Georgia\" title=\"Brunswick, Georgia\">Brunswick</a> in <a href=\"https://wikipedia.org/wiki/Glynn_County,_Georgia\" title=\"Glynn County, Georgia\">Glynn County, Georgia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Murder of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, a 25-year-old African-American citizen, is shot and murdered by three white men after visiting a house under construction while jogging at a neighborhood in Satilla Shores near <a href=\"https://wikipedia.org/wiki/Brunswick,_Georgia\" title=\"Brunswick, Georgia\">Brunswick</a> in <a href=\"https://wikipedia.org/wiki/Glynn_County,_Georgia\" title=\"Glynn County, Georgia\">Glynn County, Georgia</a>.", "links": [{"title": "Murder of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Brunswick, Georgia", "link": "https://wikipedia.org/wiki/Brunswick,_Georgia"}, {"title": "Glynn County, Georgia", "link": "https://wikipedia.org/wiki/Glynn_County,_Georgia"}]}, {"year": "2021", "text": "Four simultaneous prison riots leave at least 62 people dead in Ecuador.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/February_2021_Ecuadorian_prison_riots\" title=\"February 2021 Ecuadorian prison riots\">Four simultaneous prison riots</a> leave at least 62 people dead in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/February_2021_Ecuadorian_prison_riots\" title=\"February 2021 Ecuadorian prison riots\">Four simultaneous prison riots</a> leave at least 62 people dead in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "links": [{"title": "February 2021 Ecuadorian prison riots", "link": "https://wikipedia.org/wiki/February_2021_Ecuadorian_prison_riots"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}]}], "Births": [{"year": "1133", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> caliph (d. 1154)", "html": "1133 - <a href=\"https://wikipedia.org/wiki/Al-Zafir\" title=\"Al-Zafir\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (d. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Zafir\" title=\"Al-Zafir\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> caliph (d. 1154)", "links": [{"title": "Al-Z<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>afir"}]}, {"year": "1417", "text": "<PERSON> (d. 1471)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Paul <PERSON>\"><PERSON> II</a> (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul II\"><PERSON> II</a> (d. 1471)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1417", "text": "<PERSON>, Duke of Bavaria (d. 1479)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (d. 1479)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1443", "text": "<PERSON>, Hungarian king (d. 1490)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian king (d. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian king (d. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1529", "text": "<PERSON><PERSON><PERSON>, Italian historian (d. 1568)", "html": "1529 - <a href=\"https://wikipedia.org/wiki/<PERSON>of<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian historian (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian historian (d. 1568)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onofrio_Panvinio"}]}, {"year": "1539", "text": "<PERSON> of Legnica, thrice Duke of Legnica (d. 1588)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Legnica\" title=\"<PERSON> of Legnica\"><PERSON> of Legnica</a>, thrice Duke of Legnica (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Legnica\" title=\"<PERSON> of Legnica\"><PERSON> of Legnica</a>, thrice Duke of Legnica (d. 1588)", "links": [{"title": "<PERSON> of Legnica", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Legnica"}]}, {"year": "1539", "text": "<PERSON><PERSON>, Empress of the Mughal Empire (d. 1612)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/Sal<PERSON>_Sultan_<PERSON>\" title=\"Salima Sultan Be<PERSON>\"><PERSON><PERSON></a>, Empress of the Mughal Empire (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sal<PERSON>_Sultan_<PERSON>\" title=\"Salima Sultan Be<PERSON>\"><PERSON><PERSON></a>, Empress of the Mughal Empire (d. 1612)", "links": [{"title": "Salima <PERSON>", "link": "https://wikipedia.org/wiki/Sal<PERSON>_Sultan_Begum"}]}, {"year": "1583", "text": "<PERSON><PERSON><PERSON>, French mathematician, astrologer, and astronomer (d. 1656)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(mathematician)\" title=\"<PERSON><PERSON><PERSON> (mathematician)\"><PERSON><PERSON><PERSON></a>, French mathematician, astrologer, and astronomer (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(mathematician)\" title=\"<PERSON><PERSON><PERSON> (mathematician)\"><PERSON><PERSON><PERSON></a>, French mathematician, astrologer, and astronomer (d. 1656)", "links": [{"title": "<PERSON><PERSON><PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(mathematician)"}]}, {"year": "1592", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1663)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/Balthazar_Gerbier\" title=\"Balthazar Gerbier\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balthazar_<PERSON>bie<PERSON>\" title=\"Balthazar Gerbier\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1663)", "links": [{"title": "Balthazar G<PERSON>r", "link": "https://wikipedia.org/wiki/Balt<PERSON>_<PERSON>r"}]}, {"year": "1606", "text": "<PERSON> of Nassau-Siegen, officer in the Dutch Army (d. 1674)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Siegen\" title=\"<PERSON> of Nassau-Siegen\"><PERSON> of Nassau-Siegen</a>, officer in the Dutch Army (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Nassau-Siegen\" title=\"<PERSON> of Nassau-Siegen\"><PERSON> of Nassau-Siegen</a>, officer in the Dutch Army (d. 1674)", "links": [{"title": "<PERSON> of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Nassau-Siegen"}]}, {"year": "1633", "text": "<PERSON>, English diarist and politician (d. 1703)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist and politician (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist and politician (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, Japanese shōgun (d. 1709)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>oshi\" title=\"Tokugawa Tsunayoshi\"><PERSON></a>, Japanese shōgun (d. 1709)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>oshi\" title=\"Tokugawa Tsunayoshi\"><PERSON></a>, Japanese shōgun (d. 1709)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1680", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Canadian politician, 2nd Colonial Governor of Louisiana (d. 1767)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON>_<PERSON>_Bien<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON> Bienville\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>, Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (d. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON>_<PERSON>_Bienville\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON> Bienville\"><PERSON><PERSON><PERSON>, <PERSON><PERSON></a>, Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana\" title=\"List of colonial governors of Louisiana\">Colonial Governor of Louisiana</a> (d. 1767)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of colonial governors of Louisiana", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Louisiana"}]}, {"year": "1685", "text": "<PERSON>, German-English organist and composer (d. 1759)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Handel\"><PERSON></a>, German-English organist and composer (d. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Handel\"><PERSON></a>, German-English organist and composer (d. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, Welsh-English minister and philosopher (d. 1791)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English minister and philosopher (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English minister and philosopher (d. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, German banker and businessman (d. 1812)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and businessman (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German banker and businessman (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Mexican politician and general (d. 1854)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and general (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%AD<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and general (d. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Joaqu%C3%ADn_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Finnish poet, physicist and meteorologist (d. 1848)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet, physicist and meteorologist (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet, physicist and meteorologist (d. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1915)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">He<PERSON><PERSON></a>, Dutch painter (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, German philosopher and author (d. 1906)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, Swiss businessman, founded The Ritz Hotel, London and Hôtel Ritz Paris (d. 1918)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Ritz\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded <a href=\"https://wikipedia.org/wiki/The_Ritz_Hotel,_London\" title=\"The Ritz Hotel, London\">The Ritz Hotel, London</a> and <a href=\"https://wikipedia.org/wiki/H%C3%B4tel_Ritz_Paris\" title=\"Hôtel Ritz Paris\">Hôtel Ritz Paris</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Ritz\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded <a href=\"https://wikipedia.org/wiki/The_Ritz_Hotel,_London\" title=\"The Ritz Hotel, London\">The Ritz Hotel, London</a> and <a href=\"https://wikipedia.org/wiki/H%C3%B4tel_Ritz_Paris\" title=\"Hôtel Ritz Paris\">Hôtel Ritz Paris</a> (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Ritz"}, {"title": "The Ritz Hotel, London", "link": "https://wikipedia.org/wiki/The_Ritz_Hotel,_London"}, {"title": "Hôtel Ritz Paris", "link": "https://wikipedia.org/wiki/H%C3%B4tel_Ritz_Paris"}]}, {"year": "1868", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American sociologist, historian, and activist (d. 1963)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American sociologist, historian, and activist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American sociologist, historian, and activist (d. 1963)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Swedish actress, singer, and director (d. 1947)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress, singer, and director (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress, singer, and director (d. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Chinese journalist, philosopher, and scholar (d. 1929)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist, philosopher, and scholar (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese journalist, philosopher, and scholar (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Estonian lawyer and politician, 1st President of Estonia (d. 1956)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ts\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ts\" title=\"<PERSON>\"><PERSON></a>, Estonian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Estonia\" title=\"President of Estonia\">President of Estonia</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Konstantin_P%C3%A4ts"}, {"title": "President of Estonia", "link": "https://wikipedia.org/wiki/President_of_Estonia"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Ukrainian painter and theorist (d. 1935)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian painter and theorist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian painter and theorist (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, German-Swiss psychiatrist and philosopher (d. 1969)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss psychiatrist and philosopher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss psychiatrist and philosopher (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American painter (d. 1962)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wiggins\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wiggins\" title=\"<PERSON>ig<PERSON>\"><PERSON></a>, American painter (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wiggins"}]}, {"year": "1884", "text": "<PERSON>, Polish biochemist (d. 1967)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Funk\"><PERSON></a>, Polish biochemist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Funk\"><PERSON></a>, Polish biochemist (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress and director (d. 1957)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and director (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress and director (d. 1957)", "links": [{"title": "Musidora", "link": "https://wikipedia.org/wiki/Musidora"}]}, {"year": "1889", "text": "<PERSON>, English-American actor (d. 1975)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American director, cinematographer, and producer (d. 1949)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, cinematographer, and producer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, cinematographer, and producer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American captain, pilot, and politician, 60th Governor of New Hampshire (d. 1947)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and politician, 60th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1892", "text": "<PERSON>, English actress (d. 1995)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American journalist and writer (d. 1950)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and writer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and writer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Australian rugby league player and coach (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, German author and poet (d. 1974)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4stner\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_K%C3%A4stner"}]}, {"year": "1899", "text": "<PERSON>, American director and screenwriter (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English director and screenwriter (d. 1980)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American journalist and historian (d. 1993)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian lawyer and politician, 20th Prime Minister of Australia (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1915", "text": "<PERSON>, American actor and director (d. 1979)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director (d. 1979)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1915", "text": "<PERSON>, American general and pilot (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Irish footballer and manager (d. 1995)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Canadian lawyer and politician (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paul_G%C3%A<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Uruguayan jurist and politician, President of Uruguay (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Uruguay", "link": "https://wikipedia.org/wiki/President_of_Uruguay"}]}, {"year": "1923", "text": "<PERSON>, English footballer (d. 2000)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1923)\" title=\"<PERSON> (footballer, born 1923)\"><PERSON></a>, English footballer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1923)\" title=\"<PERSON> (footballer, born 1923)\"><PERSON></a>, English footballer (d. 2000)", "links": [{"title": "<PERSON> (footballer, born 1923)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1923)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Greek judge and politician, 176th Prime Minister of Greece (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek judge and politician, 176th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek judge and politician, 176th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1923", "text": "<PERSON>, American football player (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American fighter pilot (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fighter pilot (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fighter pilot (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author (d. 1991)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, South-African-American physicist and academic, Nobel Prize laureate (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South-African-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South-African-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1925", "text": "<PERSON>, American lawyer and politician (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, French soprano and actress (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gine_C<PERSON>pin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soprano and actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gine_C<PERSON>pin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soprano and actress (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gine_Crespin"}]}, {"year": "1927", "text": "<PERSON>, Guyanese activist and publisher (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese activist and publisher (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese activist and publisher (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German racing driver", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Russian colonel, physician, and astronaut (d. 1990)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian colonel, physician, and astronaut (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian colonel, physician, and astronaut (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "Patriarch <PERSON><PERSON> of Moscow (d. 2008)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_<PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON>_<PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON> of Moscow</a> (d. 2008)", "links": [{"title": "Patriarch <PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II_of_Moscow"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American baseball player and coach (d. 1980)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"El<PERSON> Howard\"><PERSON><PERSON></a>, American baseball player and coach (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Howard\"><PERSON><PERSON></a>, American baseball player and coach (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English-American author, poet, and academic (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer,_born_1930)\" title=\"<PERSON> (writer, born 1930)\"><PERSON></a>, English-American author, poet, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer,_born_1930)\" title=\"<PERSON> (writer, born 1930)\"><PERSON></a>, English-American author, poet, and academic (d. 2015)", "links": [{"title": "<PERSON> (writer, born 1930)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer,_born_1930)"}]}, {"year": "1931", "text": "<PERSON>, American painter and sculptor (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American actress and producer (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American football player, coach, and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American broadcast journalist (d. 2019)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a>, American broadcast journalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chase\"><PERSON></a>, American broadcast journalist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American director, producer, and screenwriter (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress (d. 1992)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1939", "text": "<PERSON>, American basketball player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American football player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player and coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American golfer (d. 2018)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 2018)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1944", "text": "<PERSON>, English author and educator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, German keyboard player and composer (d. 2001)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German keyboard player and composer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (d. 2014)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, South African cleric and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cleric and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cleric and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Danish politician, Speaker of the Danish Parliament", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pia_Kj%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Folketing\" class=\"mw-redirect\" title=\"List of Speakers of the Folketing\">Speaker of the Danish Parliament</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pia_Kj%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, <a href=\"https://wikipedia.org/wiki/List_of_Speakers_of_the_Folketing\" class=\"mw-redirect\" title=\"List of Speakers of the Folketing\">Speaker of the Danish Parliament</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pia_Kj%C3%A6rsgaard"}, {"title": "List of Speakers of the Folketing", "link": "https://wikipedia.org/wiki/List_of_Speakers_of_the_Folketing"}]}, {"year": "1947", "text": "<PERSON>, Swiss chef and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English director and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)"}]}, {"year": "1948", "text": "<PERSON>, English footballer (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and bass player (d. 2020)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Argentine author and translator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Aira\" title=\"<PERSON>\"><PERSON></a>, Argentine author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>a\" title=\"<PERSON>\"><PERSON></a>, Argentine author and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Aira"}]}, {"year": "1949", "text": "<PERSON>, Canadian engineer, astronaut, and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer, astronaut, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian engineer, astronaut, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American philosopher and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Welsh bass guitarist and composer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh bass guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Welsh bass guitarist and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1951", "text": "<PERSON>, American tennis player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter of Jewish melodies (d. 2011)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter of Jewish melodies (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter of Jewish melodies (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>\" <PERSON>, American football player and boxer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ed_%22Too_Tall%22_<PERSON>\" title='<PERSON> \"Too <PERSON>\" Jones'><PERSON> \"Too <PERSON>\" <PERSON></a>, American football player and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed_%22Too_Tall%22_<PERSON>\" title='<PERSON> \"Too <PERSON>\" Jones'><PERSON> \"Too <PERSON>\" <PERSON></a>, American football player and boxer", "links": [{"title": "<PERSON> \"Too Tall\" Jones", "link": "https://wikipedia.org/wiki/Ed_%22Too_Tall%22_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American guitarist and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Hong Kong singer-songwriter, guitarist, and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Japanese racing driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Sri Lankan physician and academic (d. 1989)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gam<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>anagama\"><PERSON><PERSON></a>, Sri Lankan physician and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Thiranagama\"><PERSON><PERSON></a>, Sri Lankan physician and academic (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1954", "text": "<PERSON>, Ukrainian captain and politician, 3rd President of Ukraine", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian captain and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ukraine\" title=\"President of Ukraine\">President of Ukraine</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Ukraine", "link": "https://wikipedia.org/wiki/President_of_Ukraine"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American basketball player and coach (d. 2015)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>lip_<PERSON>\" title=\"<PERSON>lip <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lip <PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flip_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American-British author", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Scottish politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American serial killer (d. 2004)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer-songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American engineer and astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Scottish soldier and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish soldier and politician", "links": [{"title": "<PERSON>-Grainger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>ger"}]}, {"year": "1959", "text": "<PERSON>, Irish singer and actress (d. 2025)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer and actress (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Emperor of Japan", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Emperor_of_Japan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor of Japan\"><PERSON><PERSON><PERSON><PERSON>, Emperor of Japan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Emperor_of_Japan\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Emperor of Japan\"><PERSON><PERSON><PERSON><PERSON>, Emperor of Japan</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Emperor of Japan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Emperor_of_Japan"}]}, {"year": "1962", "text": "<PERSON>, American guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Polish journalist and politician, 11th Minister of Foreign Affairs of Poland", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Rados%C5%82aw_Si<PERSON>ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Poland\">Minister of Foreign Affairs of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rados%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Poland\">Minister of Foreign Affairs of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rados%C5%82aw_<PERSON><PERSON><PERSON>"}, {"title": "Minister of Foreign Affairs of Poland", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland"}]}, {"year": "1964", "text": "<PERSON>, Norwegian guitarist and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American businessman", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Czech-Monacan tennis player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech-Monacan tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech-Monacan tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sukov%C3%A1"}]}, {"year": "1967", "text": "<PERSON>, American golfer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, New Zealand golfer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English journalist and television news presenter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and television news presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and television news presenter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American fashion designer and businessman, founded FUBU", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer and businessman, founded <a href=\"https://wikipedia.org/wiki/FUBU\" title=\"FUBU\">FUBU</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer and businessman, founded <a href=\"https://wikipedia.org/wiki/FUBU\" title=\"FUBU\">FUBU</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "FUBU", "link": "https://wikipedia.org/wiki/FUBU"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Bhagyash<PERSON>\" title=\"<PERSON>hagyash<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bhagyash<PERSON>\" title=\"<PERSON>hagyash<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hagyashree"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American actress and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Swedish golfer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English model and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Messenger\" title=\"<PERSON> Messenger\"><PERSON></a>, English model and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Messenger\" title=\"<PERSON> Messenger\"><PERSON></a>, English model and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Melinda_Messenger"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American soccer player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soccer player", "links": [{"title": "Joe<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Italian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> St<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alessandro_<PERSON>urba"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> White\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> White\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American-Polish basketball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Polish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Polish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, South African rugby player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American songwriter and playwright", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Scottish actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian skier", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0migun-V%C3%A4hi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C5%A0migun-V%C3%A4hi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristina_%C5%A0migun-V%C3%A4hi"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Puerto Rican singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Residente\" title=\"Residente\"><PERSON><PERSON></a>, Puerto Rican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Residente\" title=\"Residente\"><PERSON><PERSON></a>, Puerto Rican singer-songwriter", "links": [{"title": "<PERSON>e", "link": "https://wikipedia.org/wiki/Residente"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player (d. 2003)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2003)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ad"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Indian actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American comedian, actor, producer, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Egyptian footballer, manager and sportscaster", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Egyptian footballer, manager and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Egyptian footballer, manager and sportscaster", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thompson"}]}, {"year": "1986", "text": "<PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Concei%C3%A7%C3%A3o\" class=\"mw-redirect\" title=\"<PERSON> Con<PERSON>ção\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emerson_Concei%C3%A7%C3%A3o\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emerson_Concei%C3%A7%C3%A3o"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American football player and coach", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Swedish singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ab-Soul\" title=\"Ab-Soul\"><PERSON><PERSON>-<PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ab-Soul\" title=\"Ab-Soul\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "Ab-Soul", "link": "https://wikipedia.org/wiki/Ab-Soul"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Trinidadian-American singer-songwriter and producer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Theophilus_London\" title=\"Theophilus London\"><PERSON><PERSON><PERSON></a>, Trinidadian-American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theophilus_London\" title=\"Theophilus London\"><PERSON>phi<PERSON></a>, Trinidadian-American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theophilus_London"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Gait%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Gait%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Gait%C3%A1n"}]}, {"year": "1989", "text": "<PERSON>, American ice dancer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_Pied\" title=\"<PERSON><PERSON><PERSON><PERSON>d\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_Pied\" title=\"<PERSON><PERSON><PERSON><PERSON>d\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_Pied"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Wilin_Rosario\" title=\"Wilin <PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilin_<PERSON>\" title=\"Wilin <PERSON>\"><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilin_Rosario"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Casemiro"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Kyria<PERSON>_<PERSON>ulos\" title=\"<PERSON>yr<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kyria<PERSON>_<PERSON>ulos\" title=\"<PERSON>yria<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kyria<PERSON>_Papadopoulos"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Australian actress and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Weaving\" title=\"Samara Weaving\"><PERSON><PERSON></a>, Australian actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Weaving\" title=\"Samara Weaving\"><PERSON><PERSON></a>, Australian actress and model", "links": [{"title": "Samara Weaving", "link": "https://wikipedia.org/wiki/Samara_Weaving"}]}, {"year": "1994", "text": "<PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Dakota_Fanning\" title=\"Dakota Fanning\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dakota_Fanning\" title=\"Dakota Fanning\"><PERSON></a>, American actress", "links": [{"title": "Dakota Fanning", "link": "https://wikipedia.org/wiki/Dakota_Fanning"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ri"}]}, {"year": "1995", "text": "<PERSON>, Canadian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%27A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Dutch hurdler and sprinter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch hurdler and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch hurdler and sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "2002", "text": "<PERSON>, English actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "715", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> caliph (b. 668)", "html": "715 - <a href=\"https://wikipedia.org/wiki/Al-Walid_I\" title=\"Al-Walid I\"><PERSON>-<PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Walid_I\" title=\"Al-Walid I\">Al-<PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 668)", "links": [{"title": "Al-<PERSON><PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/Al-Walid_I"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}]}, {"year": "908", "text": "<PERSON>, Shatuo military governor during the Tang dynasty in China (b. 856)", "html": "908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Keyong\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Shatuo\" title=\"Shatuo\">Shat<PERSON></a> military governor during the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> in <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> (b. 856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Keyong\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Shatuo\" title=\"Shatuo\">Shatuo</a> military governor during the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> in <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> (b. 856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ong"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shatuo"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "943", "text": "<PERSON>, Count of Vermandois, (b. 884)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Vermandois\" title=\"<PERSON>, Count of Vermandois\"><PERSON>, Count of Vermandois</a>, (b. 884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Vermandois\" title=\"<PERSON>, Count of Vermandois\"><PERSON>, Count of Vermandois</a>, (b. 884)", "links": [{"title": "<PERSON>, Count of Vermandois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "943", "text": "<PERSON>, prince of Tao-Klarjeti (Georgia)", "html": "943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Klarjeti\" title=\"<PERSON> of Klarjeti\"><PERSON> I</a>, prince of <a href=\"https://wikipedia.org/wiki/Principality_of_Tao-Klarjeti\" class=\"mw-redirect\" title=\"Principality of Tao-Klarjeti\">Tao-Klarjeti</a> (<a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Klarjeti\" title=\"<PERSON> of Klarjeti\"><PERSON> I</a>, prince of <a href=\"https://wikipedia.org/wiki/Principality_of_Tao-Klarjeti\" class=\"mw-redirect\" title=\"Principality of Tao-Klarjeti\">Tao-Klarjeti</a> (<a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a>)", "links": [{"title": "<PERSON> of Klarjeti", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Principality of Tao-Klarjeti", "link": "https://wikipedia.org/wiki/Principality_of_Tao-<PERSON>larjet<PERSON>"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}]}, {"year": "1011", "text": "<PERSON><PERSON><PERSON>, German archbishop (b. 940)", "html": "1011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop (b. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archbishop (b. 940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Will<PERSON>s"}]}, {"year": "1100", "text": "Emperor <PERSON><PERSON><PERSON> of Song (b. 1076)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Song (b. 1076)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Song (b. 1076)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1270", "text": "<PERSON> France (b. 1225)", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(saint)\" class=\"mw-redirect\" title=\"<PERSON> of France (saint)\"><PERSON> of France</a> (b. 1225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_France_(saint)\" class=\"mw-redirect\" title=\"<PERSON> of France (saint)\"><PERSON> of France</a> (b. 1225)", "links": [{"title": "<PERSON> of France (saint)", "link": "https://wikipedia.org/wiki/<PERSON>_of_France_(saint)"}]}, {"year": "1447", "text": "<PERSON>, Duke of Gloucester (b. 1390)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Gloucester\" title=\"<PERSON>, Duke of Gloucester\"><PERSON>, Duke of Gloucester</a> (b. 1390)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Gloucester\" title=\"<PERSON>, Duke of Gloucester\"><PERSON>, Duke of Gloucester</a> (b. 1390)", "links": [{"title": "<PERSON>, Duke of Gloucester", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_of_Gloucester"}]}, {"year": "1447", "text": "<PERSON> (b. 1383)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON> <PERSON> IV</a> (b. 1383)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> IV</a> (b. 1383)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "Emperor <PERSON><PERSON> of Ming (b. 1427)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Ming\" title=\"Emperor <PERSON><PERSON> of Ming\">Emperor <PERSON><PERSON> of Ming</a> (b. 1427)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Ming\" title=\"Emperor <PERSON> of Ming\">Emperor <PERSON><PERSON> of Ming</a> (b. 1427)", "links": [{"title": "Emperor <PERSON><PERSON> of Ming", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Ming"}]}, {"year": "1473", "text": "<PERSON>, Duke of Gelderland (b. 1410)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Gelderland\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Gelderland\"><PERSON>, Duke of Gelderland</a> (b. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Gelderland\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Gelderland\"><PERSON>, Duke of Gelderland</a> (b. 1410)", "links": [{"title": "<PERSON>, Duke of Gelderland", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Gelderland"}]}, {"year": "1526", "text": "<PERSON>, Spanish Viceroy of the Indies (b. c. 1479)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Diego_Col%C3%B3n\" class=\"mw-redirect\" title=\"Diego <PERSON>\"><PERSON></a>, Spanish Viceroy of the Indies (b. c. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Col%C3%B3n\" class=\"mw-redirect\" title=\"Diego Colón\"><PERSON></a>, Spanish Viceroy of the Indies (b. c. 1479)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Col%C3%B3n"}]}, {"year": "1554", "text": "<PERSON>, 1st Duke of Suffolk, English politician, Lord Lieutenant of Leicestershire (b. 1515)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Leicestershire\" title=\"Lord Lieutenant of Leicestershire\">Lord Lieutenant of Leicestershire</a> (b. 1515)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Suffolk\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Leicestershire\" title=\"Lord Lieutenant of Leicestershire\">Lord Lieutenant of Leicestershire</a> (b. 1515)", "links": [{"title": "<PERSON>, 1st Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Suffolk"}, {"title": "Lord Lieutenant of Leicestershire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Leicestershire"}]}, {"year": "1603", "text": "<PERSON>, Italian philosopher, physician, and botanist (b. 1519)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, physician, and botanist (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher, physician, and botanist (b. 1519)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON><PERSON>, French mathematician (b. 1540)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/Franciscus_Vieta\" class=\"mw-redirect\" title=\"Franciscus Vieta\"><PERSON><PERSON></a>, French mathematician (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franciscus_Vieta\" class=\"mw-redirect\" title=\"Franciscus Vieta\"><PERSON><PERSON></a>, French mathematician (b. 1540)", "links": [{"title": "<PERSON><PERSON>a", "link": "https://wikipedia.org/wiki/Franciscus_Vieta"}]}, {"year": "1620", "text": "<PERSON>, English politician (b. 1543)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English politician (b. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English politician (b. 1543)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}]}, {"year": "1704", "text": "<PERSON>, French organist and composer (b. 1653)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (b. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON><PERSON>, Polish king (b. 1677)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish king (b. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish king (b. 1677)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanis%C5%82aw_Leszczy%C5%84ski"}]}, {"year": "1781", "text": "<PERSON>, Founding Father of the United States (b. 1716)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Pennsylvania_politician)\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a>, Founding Father of the United States (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Pennsylvania_politician)\" title=\"<PERSON> (Pennsylvania politician)\"><PERSON></a>, Founding Father of the United States (b. 1716)", "links": [{"title": "<PERSON> (Pennsylvania politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Pennsylvania_politician)"}]}, {"year": "1792", "text": "<PERSON>, English painter and academic (b. 1723)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1723)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1723)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, English poet (b. 1795)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON>, Brazilian politician, twice Minister of Finance, brother of <PERSON> and <PERSON><PERSON><PERSON><PERSON> (b. 1775)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_de_Andrada\" title=\"<PERSON><PERSON>rada\"><PERSON><PERSON></a>, Brazilian politician, twice <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Economy_(Brazil)\" class=\"mw-redirect\" title=\"Ministry of the Economy (Brazil)\">Minister of Finance</a>, brother of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%A1cio_de_Andrada_e_Silva\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>_de_Andrada\" title=\"Antô<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_de_Andrada\" title=\"<PERSON><PERSON> Andrada\"><PERSON><PERSON></a>, Brazilian politician, twice <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Economy_(Brazil)\" class=\"mw-redirect\" title=\"Ministry of the Economy (Brazil)\">Minister of Finance</a>, brother of <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%A1<PERSON>_de_Andrada_e_Silva\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_Ribe<PERSON>_de_Andrada\" title=\"Antô<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1775)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ministry of the Economy (Brazil)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Economy_(Brazil)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%<PERSON><PERSON>_<PERSON>_And<PERSON>_e_Silva"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, American politician, 6th President of the United States (b. 1767)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1855", "text": "<PERSON>, German mathematician, astronomer, and physicist (b. 1777)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and physicist (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and physicist (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish poet and playwright (b. 1812)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Zygmunt_Krasi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and playwright (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zygmunt_Krasi%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and playwright (b. 1812)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zygmunt_Krasi%C5%84ski"}]}, {"year": "1871", "text": "<PERSON>, Finnish medical reformer (b. 1827)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish medical reformer (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish medical reformer (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Prussian soldier and politician, 10th Minister President of Prussia (b. 1803)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian soldier and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (b. 1803)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister President of Prussia", "link": "https://wikipedia.org/wiki/Minister_President_of_Prussia"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON>, German composer and educator (b. 1828)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Wold<PERSON>r_Bargiel\" title=\"Wold<PERSON><PERSON> Bargiel\"><PERSON><PERSON><PERSON><PERSON></a>, German composer and educator (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wold<PERSON>r_Bargiel\" title=\"<PERSON>old<PERSON><PERSON> Bar<PERSON>l\"><PERSON><PERSON><PERSON><PERSON></a>, German composer and educator (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wold<PERSON>r_Bargiel"}]}, {"year": "1900", "text": "<PERSON>, English poet, novelist, and short story writer (b. 1867)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, and short story writer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, novelist, and short story writer (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German surgeon and academic (b. 1823)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz (b. 1882)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz"}]}, {"year": "1930", "text": "<PERSON><PERSON>, German SA officer (b. 1907)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a> officer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a> officer (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sturmabteilung", "link": "https://wikipedia.org/wiki/Sturmabteilung"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Australian soprano and actress (b. 1861)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soprano and actress (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian soprano and actress (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English composer and academic (b. 1857)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Belgian-American chemist and engineer (b. 1863)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American chemist and engineer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American chemist and engineer (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Baekeland"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese general (b. 1885)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese general (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Irish-American publisher and educator (b. 1866)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American publisher and educator (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American publisher and educator (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French poet and playwright (b. 1868)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet and playwright (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English actor and comedian (b. 1890)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress and producer (b. 1933)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress and producer (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, 2nd King of Saudi Arabia (b. 1902)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Saud_of_Saudi_Arabia\" title=\"<PERSON><PERSON> of Saudi Arabia\"><PERSON><PERSON> bin <PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/King_of_Saudi_Arabia\" title=\"King of Saudi Arabia\">King of Saudi Arabia</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saud_of_Saudi_Arabia\" title=\"<PERSON><PERSON> of Saudi Arabia\"><PERSON><PERSON> bin <PERSON></a>, 2nd <a href=\"https://wikipedia.org/wiki/King_of_Saudi_Arabia\" title=\"King of Saudi Arabia\">King of Saudi Arabia</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/Saud_of_Saudi_Arabia"}, {"title": "King of Saudi Arabia", "link": "https://wikipedia.org/wiki/King_of_Saudi_Arabia"}]}, {"year": "1973", "text": "<PERSON>, American physician and physiologist, Nobel Prize laureate (b. 1895)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1895)", "links": [{"title": "Dickinson W<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1974", "text": "<PERSON>, American composer and screenwriter (b. 1895)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and screenwriter (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and screenwriter (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON> <PERSON><PERSON>, English painter (b. 1887)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/L._<PERSON><PERSON>_<PERSON>ry\" title=\"L. S. Lowry\"><PERSON><PERSON> <PERSON><PERSON></a>, English painter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L._S._Lowry\" title=\"L. S. Lowry\"><PERSON><PERSON> <PERSON><PERSON></a>, English painter (b. 1887)", "links": [{"title": "L. S. Lowry", "link": "https://wikipedia.org/wiki/L._<PERSON>._<PERSON>ry"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Canadian businessman and politician, 25th Premier of British Columbia (b. 1900)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Canadian businessman and politician, 25th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (b. 1900)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1983", "text": "<PERSON>, English organist and composer (b. 1892)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Salvadoran engineer and politician, President of El Salvador (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Napole%C3%B3n_Du<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran engineer and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Napole%C3%B3n_Du<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran engineer and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Napole%C3%B3n_Duarte"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "1995", "text": "<PERSON>, English veterinarian and author (b. 1916)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English veterinarian and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English veterinarian and author (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American drummer, composer, and producer (b. 1945)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer, composer, and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer, composer, and producer (b. 1945)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1998", "text": "<PERSON>, American actor and director (b. 1924)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American wrestler (b. 1965)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/The_Renegade_(wrestler)\" title=\"<PERSON> Renegade (wrestler)\">The Renegade</a>, American wrestler (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Renegade_(wrestler)\" title=\"The Renegade (wrestler)\">The Renegade</a>, American wrestler (b. 1965)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/The_Renegade_(wrestler)"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Israeli singer-songwriter and actress (b. 1957)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Of<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actress (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actress (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ofra_Haza"}]}, {"year": "2000", "text": "<PERSON>, English footballer and manager (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American bass player, songwriter, and producer (b. 1955)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American sociologist and academic (b. 1910)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Indian director, producer, screenwriter, and actor (b. 1934)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hindi_film_maker)\" class=\"mw-redirect\" title=\"<PERSON> (Hindi film maker)\"><PERSON></a>, Indian director, producer, screenwriter, and actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hindi_film_maker)\" class=\"mw-redirect\" title=\"<PERSON> (Hindi film maker)\"><PERSON></a>, Indian director, producer, screenwriter, and actor (b. 1934)", "links": [{"title": "<PERSON> (Hindi film maker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hindi_film_maker)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Indian politician, Indian Minister of External Affairs (b. 1918)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "Minister of External Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)"}]}, {"year": "2006", "text": "<PERSON>, Bangladeshi academic and former Minister of Foreign Affairs (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi academic and former Minister of Foreign Affairs (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi academic and former Minister of Foreign Affairs (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Spanish footballer (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zarra\" title=\"Telmo Zarra\"><PERSON><PERSON></a>, Spanish footballer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Zarra\" title=\"Telmo Zarra\"><PERSON><PERSON></a>, Spanish footballer (b. 1921)", "links": [{"title": "Tel<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Zarra"}]}, {"year": "2007", "text": "<PERSON>, the oldest female resident of Belarus not registered by the Guinness Book of Records (b. 1888)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the oldest female resident of <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> not registered by the <i><a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness Book of Records</a></i> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the oldest female resident of <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> not registered by the <i><a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness Book of Records</a></i> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}, {"title": "Guinness World Records", "link": "https://wikipedia.org/wiki/Guinness_World_Records"}]}, {"year": "2007", "text": "<PERSON>, English footballer (b. 1941)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1941)\" title=\"<PERSON> (footballer, born 1941)\"><PERSON></a>, English footballer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1941)\" title=\"<PERSON> (footballer, born 1941)\"><PERSON></a>, English footballer (b. 1941)", "links": [{"title": "<PERSON> (footballer, born 1941)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1941)"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Slovenian economist and politician, 2nd President of Slovenia (b. 1950)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Slovenia\" title=\"President of Slovenia\">President of Slovenia</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian economist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Slovenia\" title=\"President of Slovenia\">President of Slovenia</a> (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>z_Drnov%C5%A1ek"}, {"title": "President of Slovenia", "link": "https://wikipedia.org/wiki/President_of_Slovenia"}]}, {"year": "2008", "text": "<PERSON>, Belgian racing driver and journalist (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and journalist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, Belgian racing driver and journalist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "2010", "text": "<PERSON>, Cuban plumber and activist (b. 1967)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Orlando_Zapata\" title=\"Orlando Zapata\"><PERSON></a>, Cuban plumber and activist (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Zapata\" title=\"Orlando Zapata\"><PERSON></a>, Cuban plumber and activist (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Zapata"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Indian religious leader, founded <PERSON><PERSON><PERSON> (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Srivastava\"><PERSON><PERSON><PERSON></a>, Indian religious leader, founded <a href=\"https://wikipedia.org/wiki/Saha<PERSON>_Yoga\" title=\"<PERSON><PERSON>ja Yoga\"><PERSON><PERSON><PERSON></a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Srivastava\"><PERSON><PERSON><PERSON></a>, Indian religious leader, founded <a href=\"https://wikipedia.org/wiki/Saha<PERSON>_Yoga\" title=\"Sahaja Yoga\"><PERSON><PERSON><PERSON> Yoga</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>va"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yoga"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American physicist and mathematician (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish sociologist and activist (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Kazi<PERSON>rz_%C5%BBygulski\" title=\"<PERSON><PERSON><PERSON><PERSON>yguls<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish sociologist and activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kazi<PERSON>rz_%C5%BBygulski\" title=\"<PERSON><PERSON><PERSON><PERSON>gu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish sociologist and activist (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kazi<PERSON>rz_%C5%BBygulski"}]}, {"year": "2013", "text": "<PERSON>, American soldier and politician, 18th Lieutenant Governor of Delaware (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 18th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Delaware\" title=\"Lieutenant Governor of Delaware\">Lieutenant Governor of Delaware</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 18th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Delaware\" title=\"Lieutenant Governor of Delaware\">Lieutenant Governor of Delaware</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eugene_Bookhammer"}, {"title": "Lieutenant Governor of Delaware", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Delaware"}]}, {"year": "2013", "text": "<PERSON>, Holocaust survivor, Holocaust historian, Yiddish writer, lecturer and editor (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Holocaust survivor, Holocaust historian, Yiddish writer, lecturer and editor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Holocaust survivor, Holocaust historian, Yiddish writer, lecturer and editor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Belgian cardinal (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cardinal (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cardinal (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian lawyer and academic (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r\" title=\"<PERSON><PERSON> Sarkar\"><PERSON><PERSON></a>, Indian lawyer and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rkar\" title=\"<PERSON><PERSON> Sarkar\"><PERSON><PERSON></a>, Indian lawyer and academic (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lot<PERSON>_Sarkar"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Czech-English Holocaust survivor, pianist and educator (b. 1903)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivor</a>, pianist and educator (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English <a href=\"https://wikipedia.org/wiki/Holocaust_survivor\" class=\"mw-redirect\" title=\"Holocaust survivor\">Holocaust survivor</a>, pianist and educator (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Holocaust survivor", "link": "https://wikipedia.org/wiki/Holocaust_survivor"}]}, {"year": "2014", "text": "<PERSON>, American soldier, academic, and politician (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, academic, and politician (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian-English journalist and author (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and author (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Pakistani lawyer and judge, Chief Justice of Pakistan (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Pakistan", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Pakistan"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON> \"<PERSON>, American soldier and politician (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/W._E._%22Bill%22_<PERSON><PERSON>\" title='<PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON>'><PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON></a>, American soldier and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._E._%22Bill%22_<PERSON><PERSON>\" title='<PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON>'><PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON></a>, American soldier and politician (b. 1925)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_%22Bill%22_<PERSON>s"}]}, {"year": "2016", "text": "<PERSON>, German television host and author (b. 1937)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German television host and author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German television host and author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actress (b. 1929)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Saudi Arabian politician (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American politician, 40th Governor of Wisconsin (b. 1936)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 40th <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "2023", "text": "<PERSON>, English football commentator (b. 1945)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football commentator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football commentator (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Eurasian eagle-owl (b. 2010)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>la<PERSON>_(owl)\" title=\"<PERSON><PERSON><PERSON> (owl)\">Flaco</a>, <a href=\"https://wikipedia.org/wiki/Eurasian_eagle-owl\" title=\"Eurasian eagle-owl\">Eurasian eagle-owl</a> (b. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(owl)\" title=\"<PERSON><PERSON><PERSON> (owl)\"><PERSON>la<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Eurasian_eagle-owl\" title=\"Eurasian eagle-owl\">Eurasian eagle-owl</a> (b. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON> (owl)", "link": "https://wikipedia.org/wiki/<PERSON>la<PERSON>_(owl)"}, {"title": "Eurasian eagle-owl", "link": "https://wikipedia.org/wiki/Eurasian_eagle-owl"}]}, {"year": "2025", "text": "<PERSON>, American attorney (b. 1931)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American singer, composer and producer (b. 1951)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, composer and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, composer and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American sports commentator (b. 1956)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Al_T<PERSON>utwig\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, American sports commentator (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Trautwig\" title=\"Al T<PERSON>utwig\"><PERSON></a>, American sports commentator (b. 1956)", "links": [{"title": "Al Trautwig", "link": "https://wikipedia.org/wiki/Al_Trautwig"}]}]}}