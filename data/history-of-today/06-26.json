{"date": "June 26", "url": "https://wikipedia.org/wiki/June_26", "data": {"Events": [{"year": "4", "text": "<PERSON> adopts <PERSON><PERSON><PERSON>.", "html": "4 - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON></a> adopts <a href=\"https://wikipedia.org/wiki/Tiberius\" title=\"Tiber<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> adopts <a href=\"https://wikipedia.org/wiki/Tiberius\" title=\"Tiber<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tiberius"}]}, {"year": "221", "text": "Roman emperor <PERSON><PERSON><PERSON><PERSON> adopts his cousin <PERSON> as his heir and receives the title of Caesar.", "html": "221 - <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> adopts his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> as his heir and receives the title of <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Roman emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> adopts his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> as his heir and receives the title of <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a>.", "links": [{"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON>aga<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elagabalus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}]}, {"year": "363", "text": "Roman emperor <PERSON> is killed during the retreat from the Sasanian Empire.", "html": "363 - Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> is killed during the retreat from the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a>.", "no_year_html": "Roman emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> is killed during the retreat from the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a>.", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}]}, {"year": "684", "text": "<PERSON> <PERSON> is the last pope to require confirmation by the Byzantine emperor before taking office.", "html": "684 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_II\" title=\"Pope Benedict II\">Pope <PERSON> II</a> is the last pope to require confirmation by the Byzantine emperor before taking office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict II\">Pope <PERSON> II</a> is the last pope to require confirmation by the Byzantine emperor before taking office.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "699", "text": "<PERSON> <PERSON>, a Japanese mystic and apothecary who will later be regarded as the founder of a folk religion Shugendō, is banished to Izu Ōshima.", "html": "699 - <a href=\"https://wikipedia.org/wiki/En_no_Ozuno\" class=\"mw-redirect\" title=\"En no Ozuno\"><PERSON> no Ozuno</a>, a Japanese mystic and apothecary who will later be regarded as the founder of a folk religion <a href=\"https://wikipedia.org/wiki/Shugend%C5%8D\" title=\"Shugendō\">Shugend<PERSON></a>, is banished to <a href=\"https://wikipedia.org/wiki/Izu_%C5%8Cshima\" title=\"Izu Ōshima\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En_no_Ozuno\" class=\"mw-redirect\" title=\"En no Ozuno\">En no Ozuno</a>, a Japanese mystic and apothecary who will later be regarded as the founder of a folk religion <a href=\"https://wikipedia.org/wiki/Shugend%C5%8D\" title=\"Shugendō\">Shugendō</a>, is banished to <a href=\"https://wikipedia.org/wiki/Izu_%C5%8Cshima\" title=\"<PERSON><PERSON> Ōshima\"><PERSON><PERSON></a>.", "links": [{"title": "En no Ozuno", "link": "https://wikipedia.org/wiki/En_no_Ozuno"}, {"title": "Shugendō", "link": "https://wikipedia.org/wiki/Shugend%C5%8D"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Izu_%C5%8Cshima"}]}, {"year": "1243", "text": "Mongols defeat the Seljuk Turks at the Battle of Köse Dağ.", "html": "1243 - Mongols defeat the <a href=\"https://wikipedia.org/wiki/Seljuk_Turks\" class=\"mw-redirect\" title=\"Seljuk Turks\">Seljuk Turks</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6se_Da%C4%9F\" title=\"Battle of Köse Dağ\">Battle of Köse Dağ</a>.", "no_year_html": "Mongols defeat the <a href=\"https://wikipedia.org/wiki/Seljuk_Turks\" class=\"mw-redirect\" title=\"Seljuk Turks\">Seljuk Turks</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B6se_Da%C4%9F\" title=\"Battle of Köse Dağ\">Battle of Köse Dağ</a>.", "links": [{"title": "Seljuk Turks", "link": "https://wikipedia.org/wiki/Seljuk_Turks"}, {"title": "Battle of Köse Dağ", "link": "https://wikipedia.org/wiki/Battle_of_K%C3%B6se_Da%C4%9F"}]}, {"year": "1295", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> II crowned king of Poland, following Ducal period. The white eagle is added to the Polish coat of arms.", "html": "1295 - <a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> crowned <a href=\"https://wikipedia.org/wiki/King_of_Poland\" class=\"mw-redirect\" title=\"King of Poland\">king of Poland</a>, following Ducal period. The white eagle is added to the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Poland\" title=\"Coat of arms of Poland\">Polish coat of arms</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Przemys%C5%82_II\" title=\"Przemysł II\">Przemysł II</a> crowned <a href=\"https://wikipedia.org/wiki/King_of_Poland\" class=\"mw-redirect\" title=\"King of Poland\">king of Poland</a>, following Ducal period. The white eagle is added to the <a href=\"https://wikipedia.org/wiki/Coat_of_arms_of_Poland\" title=\"Coat of arms of Poland\">Polish coat of arms</a>.", "links": [{"title": "Przemysł II", "link": "https://wikipedia.org/wiki/Przemys%C5%82_II"}, {"title": "King of Poland", "link": "https://wikipedia.org/wiki/King_of_Poland"}, {"title": "Coat of arms of Poland", "link": "https://wikipedia.org/wiki/Coat_of_arms_of_Poland"}]}, {"year": "1407", "text": "<PERSON> becomes Grand Master of the Teutonic Knights.", "html": "1407 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Grand_Master_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Grand Master of the Teutonic Knights\">Grand Master of the Teutonic Knights</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Grand_Master_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Grand Master of the Teutonic Knights\">Grand Master of the Teutonic Knights</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Grand Master of the Teutonic Knights", "link": "https://wikipedia.org/wiki/Grand_Master_of_the_Teutonic_Knights"}]}, {"year": "1409", "text": "Western Schism: The Roman Catholic Church is led into a double schism as <PERSON><PERSON> is crowned Pope <PERSON> after the Council of Pisa, joining <PERSON> <PERSON> in Rome and <PERSON> <PERSON> in Avignon.", "html": "1409 - <a href=\"https://wikipedia.org/wiki/Western_Schism\" title=\"Western Schism\">Western Schism</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a> is led into a double <a href=\"https://wikipedia.org/wiki/Schism_(religion)\" class=\"mw-redirect\" title=\"Schism (religion)\">schism</a> as <PERSON><PERSON> is crowned <a href=\"https://wikipedia.org/wiki/Pope_Alexander_V\" class=\"mw-redirect\" title=\"Pope Alexander V\">Pope <PERSON> V</a> after the <a href=\"https://wikipedia.org/wiki/Council_of_Pisa\" title=\"Council of Pisa\">Council of Pisa</a>, joining <a href=\"https://wikipedia.org/wiki/Pope_Gregory_XII\" title=\"Pope Gregory XII\">Pope Gregory <PERSON></a> in Rome and <a href=\"https://wikipedia.org/wiki/Antipope_<PERSON>_XIII\" title=\"Antipope Benedict <PERSON>\">Pope <PERSON></a> in <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Western_Schism\" title=\"Western Schism\">Western Schism</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholic Church</a> is led into a double <a href=\"https://wikipedia.org/wiki/Schism_(religion)\" class=\"mw-redirect\" title=\"Schism (religion)\">schism</a> as <PERSON><PERSON> is crowned <a href=\"https://wikipedia.org/wiki/Pope_Alexander_V\" class=\"mw-redirect\" title=\"Pope Alexander V\">Pope <PERSON> V</a> after the <a href=\"https://wikipedia.org/wiki/Council_of_Pisa\" title=\"Council of Pisa\">Council of Pisa</a>, joining <a href=\"https://wikipedia.org/wiki/Pope_Gregory_XII\" title=\"Pope Gregory XII\">Pope Gregory XII</a> in Rome and <a href=\"https://wikipedia.org/wiki/Antipope_<PERSON>_XIII\" title=\"Antipope Benedict <PERSON>\">Pope <PERSON></a> in <a href=\"https://wikipedia.org/wiki/Avignon\" title=\"Avignon\">Avignon</a>.", "links": [{"title": "Western Schism", "link": "https://wikipedia.org/wiki/Western_Schism"}, {"title": "Roman Catholic Church", "link": "https://wikipedia.org/wiki/Roman_Catholic_Church"}, {"title": "Schism (religion)", "link": "https://wikipedia.org/wiki/Schism_(religion)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Council of Pisa", "link": "https://wikipedia.org/wiki/Council_of_Pisa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Antipope <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Avignon", "link": "https://wikipedia.org/wiki/Avignon"}]}, {"year": "1460", "text": "War of the Roses: <PERSON>, 16th Earl of Warwick, and <PERSON>, Earl of March, land in England with a rebel army and march on London.", "html": "1460 - <a href=\"https://wikipedia.org/wiki/War_of_the_Roses\" class=\"mw-redirect\" title=\"War of the Roses\">War of the Roses</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_16th_Earl_<PERSON>_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON>, Earl of March</a>, land in England with a rebel army and march on London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Roses\" class=\"mw-redirect\" title=\"War of the Roses\">War of the Roses</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_16th_Earl_<PERSON>_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON>, Earl of March</a>, land in England with a rebel army and march on London.", "links": [{"title": "War of the Roses", "link": "https://wikipedia.org/wiki/War_of_the_Roses"}, {"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1483", "text": "<PERSON> becomes King of England.", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a> becomes King of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> III</a> becomes King of England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1522", "text": "Ottomans begin the second Siege of Rhodes.", "html": "1522 - Ottomans begin the second <a href=\"https://wikipedia.org/wiki/Siege_of_Rhodes_(1522)\" title=\"Siege of Rhodes (1522)\">Siege of Rhodes</a>.", "no_year_html": "Ottomans begin the second <a href=\"https://wikipedia.org/wiki/Siege_of_Rhodes_(1522)\" title=\"Siege of Rhodes (1522)\">Siege of Rhodes</a>.", "links": [{"title": "Siege of Rhodes (1522)", "link": "https://wikipedia.org/wiki/Siege_of_Rhodes_(1522)"}]}, {"year": "1541", "text": "<PERSON> is assassinated in Lima by the son of his former companion and later antagonist, <PERSON> the younger. <PERSON><PERSON> is later caught and executed.", "html": "1541 - <a href=\"https://wikipedia.org/wiki/Francisco_Pi<PERSON>\" title=\"Francisco Pizar<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima</a> by the son of his former companion and later antagonist, <a href=\"https://wikipedia.org/wiki/Diego_de_Almagro\" title=\"Diego de Almagro\"><PERSON></a> the younger. <PERSON><PERSON> is later caught and executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco<PERSON>\" title=\"Francisco Pizar<PERSON>\"><PERSON></a> is assassinated in <a href=\"https://wikipedia.org/wiki/Lima\" title=\"Lima\">Lima</a> by the son of his former companion and later antagonist, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Diego de Almagro\"><PERSON></a> the younger. <PERSON><PERSON> is later caught and executed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}, {"title": "Lima", "link": "https://wikipedia.org/wiki/Lima"}, {"title": "Diego <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "Livonian campaign of <PERSON> begins.", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Livonian_campaign_of_<PERSON>_<PERSON>%C3%A1thory\" title=\"Livonian campaign of <PERSON>\">Livonian campaign of <PERSON></a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Livonian_campaign_of_<PERSON>_<PERSON>%C3%A1thory\" title=\"Livonian campaign of <PERSON>\">Livonian campaign of <PERSON></a> begins.", "links": [{"title": "Livonian campaign of <PERSON>", "link": "https://wikipedia.org/wiki/Livonian_campaign_of_<PERSON>_B%C3%A1thory"}]}, {"year": "1718", "text": "<PERSON>, <PERSON><PERSON> of Russia, <PERSON> the <PERSON>'s son, mysteriously dies after being sentenced to death by his father for plotting against him.", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON>, Tsarevich of Russia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>'s son, mysteriously dies after being sentenced to death by his father for plotting against him.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Tsarevich_of_Russia\" title=\"<PERSON>, Tsarevich of Russia\"><PERSON>, Tsarevich of Russia</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>'s son, mysteriously dies after being sentenced to death by his father for plotting against him.", "links": [{"title": "<PERSON>, <PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>evich_of_Russia"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1723", "text": "After a siege and bombardment by cannon, Baku surrenders to the Russians.", "html": "1723 - After a siege and bombardment by cannon, <a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1722%E2%80%931723)\" title=\"Russo-Persian War (1722-1723)\">Baku surrenders</a> to the Russians.", "no_year_html": "After a siege and bombardment by cannon, <a href=\"https://wikipedia.org/wiki/Russo-Persian_War_(1722%E2%80%931723)\" title=\"Russo-Persian War (1722-1723)\">Baku surrenders</a> to the Russians.", "links": [{"title": "Russo-Persian War (1722-1723)", "link": "https://wikipedia.org/wiki/Russo-Persian_War_(1722%E2%80%931723)"}]}, {"year": "1740", "text": "A combined force of Spanish, free blacks and allied Indians defeat a British garrison at the Siege of Fort Mose near St. Augustine during the War of Jenkins' Ear.", "html": "1740 - A combined force of Spanish, free blacks and allied Indians defeat a British garrison at the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Mose\" title=\"Siege of Fort Mose\">Siege of Fort Mose</a> near <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> during the <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>.", "no_year_html": "A combined force of Spanish, free blacks and allied Indians defeat a British garrison at the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Mose\" title=\"Siege of Fort Mose\">Siege of Fort Mose</a> near <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> during the <a href=\"https://wikipedia.org/wiki/War_of_Jenkins%27_Ear\" title=\"War of Jenkins' Ear\">War of Jenkins' Ear</a>.", "links": [{"title": "Siege of Fort Mose", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Mose"}, {"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}, {"title": "War of Jenkins' Ear", "link": "https://wikipedia.org/wiki/War_of_Jenkins%27_Ear"}]}, {"year": "1794", "text": "French Revolutionary Wars: Battle of Fleurus marks the first successful military use of aircraft and turns the tide of the War of the First Coalition.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fleurus_(1794)\" title=\"Battle of Fleurus (1794)\">Battle of Fleurus</a> marks the first successful military use of aircraft and turns the tide of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fleurus_(1794)\" title=\"Battle of Fleurus (1794)\">Battle of Fleurus</a> marks the first successful military use of aircraft and turns the tide of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of Fleurus (1794)", "link": "https://wikipedia.org/wiki/Battle_of_Fleurus_(1794)"}, {"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}]}, {"year": "1830", "text": "<PERSON> becomes king of Britain and Hanover.", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> IV of the United Kingdom\"><PERSON> IV</a> becomes king of Britain and Hanover.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> IV of the United Kingdom\"><PERSON> IV</a> becomes king of Britain and Hanover.", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}]}, {"year": "1843", "text": "Treaty of Nanking comes into effect, Hong Kong Island is ceded to the British \"in perpetuity\".", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Nanking\" title=\"Treaty of Nanking\">Treaty of Nanking</a> comes into effect, <a href=\"https://wikipedia.org/wiki/Hong_Kong_Island\" title=\"Hong Kong Island\">Hong Kong Island</a> is ceded to the British \"in perpetuity\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Nanking\" title=\"Treaty of Nanking\">Treaty of Nanking</a> comes into effect, <a href=\"https://wikipedia.org/wiki/Hong_Kong_Island\" title=\"Hong Kong Island\">Hong Kong Island</a> is ceded to the British \"in perpetuity\".", "links": [{"title": "Treaty of Nanking", "link": "https://wikipedia.org/wiki/Treaty_of_Nanking"}, {"title": "Hong Kong Island", "link": "https://wikipedia.org/wiki/Hong_Kong_Island"}]}, {"year": "1848", "text": "End of the June Days Uprising in Paris.", "html": "1848 - End of the <a href=\"https://wikipedia.org/wiki/June_Days_Uprising\" class=\"mw-redirect\" title=\"June Days Uprising\">June Days Uprising</a> in Paris.", "no_year_html": "End of the <a href=\"https://wikipedia.org/wiki/June_Days_Uprising\" class=\"mw-redirect\" title=\"June Days Uprising\">June Days Uprising</a> in Paris.", "links": [{"title": "June Days Uprising", "link": "https://wikipedia.org/wiki/June_Days_Uprising"}]}, {"year": "1857", "text": "The first investiture of the Victoria Cross in Hyde Park, London.", "html": "1857 - The first investiture of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> in <a href=\"https://wikipedia.org/wiki/Hyde_Park,_London\" title=\"Hyde Park, London\">Hyde Park</a>, London.", "no_year_html": "The first investiture of the <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> in <a href=\"https://wikipedia.org/wiki/Hyde_Park,_London\" title=\"Hyde Park, London\">Hyde Park</a>, London.", "links": [{"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}, {"title": "Hyde Park, London", "link": "https://wikipedia.org/wiki/Hyde_Park,_London"}]}, {"year": "1886", "text": "<PERSON> isolated elemental Fluorine for the first time.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> isolated elemental <a href=\"https://wikipedia.org/wiki/Fluorine\" title=\"Fluorine\">Fluorine</a> for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> isolated elemental <a href=\"https://wikipedia.org/wiki/Fluorine\" title=\"Fluorine\">Fluorine</a> for the first time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fluorine", "link": "https://wikipedia.org/wiki/Fluorine"}]}, {"year": "1889", "text": "Bangui is founded by <PERSON> and <PERSON> in what was then the upper reaches of the French Congo.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Bangui\" title=\"Bangui\">Bangui</a> is founded by <PERSON> and <PERSON> in what was then the upper reaches of the <a href=\"https://wikipedia.org/wiki/French_Congo\" title=\"French Congo\">French Congo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangui\" title=\"Bangui\">Bangui</a> is founded by <PERSON> and <PERSON> in what was then the upper reaches of the <a href=\"https://wikipedia.org/wiki/French_Congo\" title=\"French Congo\">French Congo</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bangui"}, {"title": "French Congo", "link": "https://wikipedia.org/wiki/French_Congo"}]}, {"year": "1906", "text": "The first Grand Prix motor race is held at Le Mans.", "html": "1906 - The first <a href=\"https://wikipedia.org/wiki/1906_French_Grand_Prix\" title=\"1906 French Grand Prix\">Grand Prix</a> motor race is held at <a href=\"https://wikipedia.org/wiki/Le_Mans\" title=\"Le Mans\">Le Mans</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/1906_French_Grand_Prix\" title=\"1906 French Grand Prix\">Grand Prix</a> motor race is held at <a href=\"https://wikipedia.org/wiki/Le_Mans\" title=\"Le Mans\">Le Mans</a>.", "links": [{"title": "1906 French Grand Prix", "link": "https://wikipedia.org/wiki/1906_French_Grand_Prix"}, {"title": "Le Mans", "link": "https://wikipedia.org/wiki/<PERSON>_Mans"}]}, {"year": "1909", "text": "The Science Museum in London comes into existence as an independent entity.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/Science_Museum,_London\" title=\"Science Museum, London\">Science Museum</a> in London comes into existence as an independent entity.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Science_Museum,_London\" title=\"Science Museum, London\">Science Museum</a> in London comes into existence as an independent entity.", "links": [{"title": "Science Museum, London", "link": "https://wikipedia.org/wiki/Science_Museum,_London"}]}, {"year": "1917", "text": "World War I: The American Expeditionary Forces begin to arrive in France. They will first enter combat in the Battle of Hamel on July 4.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/American_Expeditionary_Forces\" title=\"American Expeditionary Forces\">American Expeditionary Forces</a> begin to arrive in France. They will first enter combat in the Battle of Hamel on July 4.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/American_Expeditionary_Forces\" title=\"American Expeditionary Forces\">American Expeditionary Forces</a> begin to arrive in France. They will first enter combat in the Battle of Hamel on July 4.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "American Expeditionary Forces", "link": "https://wikipedia.org/wiki/American_Expeditionary_Forces"}]}, {"year": "1918", "text": "World War I: Allied forces under <PERSON> and <PERSON> defeat Imperial German forces under <PERSON>, German Crown Prince in the Battle of Belleau Wood.", "html": "1918 - World War I: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"James Harbord\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Imperial German forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince\" title=\"<PERSON>, German Crown Prince\"><PERSON>, German Crown Prince</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"James Harbord\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">Imperial German forces</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince\" title=\"<PERSON>, German Crown Prince\"><PERSON>, German Crown Prince</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a>.", "links": [{"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "<PERSON>, German Crown Prince", "link": "https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince"}, {"title": "Battle of Belleau Wood", "link": "https://wikipedia.org/wiki/Battle_of_Belleau_Wood"}]}, {"year": "1924", "text": "The American occupation of the Dominican Republic ends after eight years.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)\" class=\"mw-redirect\" title=\"United States occupation of the Dominican Republic (1916-24)\">American occupation of the Dominican Republic</a> ends after eight years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)\" class=\"mw-redirect\" title=\"United States occupation of the Dominican Republic (1916-24)\">American occupation of the Dominican Republic</a> ends after eight years.", "links": [{"title": "United States occupation of the Dominican Republic (1916-24)", "link": "https://wikipedia.org/wiki/United_States_occupation_of_the_Dominican_Republic_(1916%E2%80%9324)"}]}, {"year": "1927", "text": "The Cyclone roller coaster opens on Coney Island.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/The_Cyclone\" class=\"mw-redirect\" title=\"The Cyclone\">The Cyclone</a> roller coaster opens on <a href=\"https://wikipedia.org/wiki/Coney_Island\" title=\"Coney Island\">Coney Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Cyclone\" class=\"mw-redirect\" title=\"The Cyclone\">The Cyclone</a> roller coaster opens on <a href=\"https://wikipedia.org/wiki/Coney_Island\" title=\"Coney Island\">Coney Island</a>.", "links": [{"title": "The Cyclone", "link": "https://wikipedia.org/wiki/The_Cyclone"}, {"title": "Coney Island", "link": "https://wikipedia.org/wiki/Coney_Island"}]}, {"year": "1934", "text": "United States President <PERSON> signs the Federal Credit Union Act, which establishes credit unions.", "html": "1934 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Federal_Credit_Union_Act\" title=\"Federal Credit Union Act\">Federal Credit Union Act</a>, which establishes credit unions.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Federal_Credit_Union_Act\" title=\"Federal Credit Union Act\">Federal Credit Union Act</a>, which establishes credit unions.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Federal Credit Union Act", "link": "https://wikipedia.org/wiki/Federal_Credit_Union_Act"}]}, {"year": "1936", "text": "Initial flight of the Focke-Wulf Fw 61, the first practical helicopter.", "html": "1936 - Initial flight of the <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_61\" title=\"Focke-Wulf Fw 61\">Focke-Wulf Fw 61</a>, the first practical helicopter.", "no_year_html": "Initial flight of the <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_61\" title=\"Focke-Wulf Fw 61\">Focke-Wulf Fw 61</a>, the first practical helicopter.", "links": [{"title": "Focke-Wulf <PERSON> 61", "link": "https://wikipedia.org/wiki/Focke-<PERSON><PERSON>_<PERSON>w_61"}]}, {"year": "1940", "text": "World War II: Under the Molotov-Ribbentrop Pact, the Soviet Union presents an ultimatum to Romania requiring it to cede Bessarabia and the northern part of Bukovina.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Under the <a href=\"https://wikipedia.org/wiki/Molotov%E2%80%93Ribbentrop_Pact\" title=\"Molotov-Ribbentrop Pact\">Molotov-Ribbentrop Pact</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> presents an <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_Bessarabia_and_Northern_Bukovina#Soviet_ultimatum\" title=\"Soviet occupation of Bessarabia and Northern Bukovina\">ultimatum</a> to <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> requiring it to cede <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> and the northern part of <a href=\"https://wikipedia.org/wiki/Bukovina\" title=\"Bukovina\">Bukovina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Under the <a href=\"https://wikipedia.org/wiki/Molotov%E2%80%93Ribbentrop_Pact\" title=\"Molotov-Ribbentrop Pact\">Molotov-Ribbentrop Pact</a>, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> presents an <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_Bessarabia_and_Northern_Bukovina#Soviet_ultimatum\" title=\"Soviet occupation of Bessarabia and Northern Bukovina\">ultimatum</a> to <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> requiring it to cede <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> and the northern part of <a href=\"https://wikipedia.org/wiki/Bukovina\" title=\"Bukovina\">Bukovina</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Molotov-Ribbentrop Pact", "link": "https://wikipedia.org/wiki/Molotov%E2%80%93Ribbentrop_Pact"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Soviet occupation of Bessarabia and Northern Bukovina", "link": "https://wikipedia.org/wiki/Soviet_occupation_of_Bessarabia_and_Northern_Bukovina#Soviet_ultimatum"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Bessarabia", "link": "https://wikipedia.org/wiki/Bessarabia"}, {"title": "Bukovina", "link": "https://wikipedia.org/wiki/Bukovina"}]}, {"year": "1941", "text": "World War II: Soviet planes bomb Kassa, Hungary (now Košice, Slovakia), giving Hungary the impetus to declare war the next day.", "html": "1941 - World War II: Soviet planes <a href=\"https://wikipedia.org/wiki/Bombing_of_Kassa\" title=\"Bombing of Kassa\">bomb Kassa, Hungary</a> (now <a href=\"https://wikipedia.org/wiki/Ko%C5%A1ice\" title=\"Košice\">Košice</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>), giving Hungary the impetus to declare war the next day.", "no_year_html": "World War II: Soviet planes <a href=\"https://wikipedia.org/wiki/Bombing_of_Kassa\" title=\"Bombing of Kassa\">bomb Kassa, Hungary</a> (now <a href=\"https://wikipedia.org/wiki/Ko%C5%A1ice\" title=\"Košice\">Košice</a>, <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>), giving Hungary the impetus to declare war the next day.", "links": [{"title": "Bombing of Kassa", "link": "https://wikipedia.org/wiki/Bombing_of_Kassa"}, {"title": "Košice", "link": "https://wikipedia.org/wiki/Ko%C5%A1ice"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}]}, {"year": "1942", "text": "The first flight of the Grumman F6F Hellcat.", "html": "1942 - The first flight of the <a href=\"https://wikipedia.org/wiki/Grumman_F6F_Hellcat\" title=\"Grumman F6F Hellcat\">Grumman F6F Hellcat</a>.", "no_year_html": "The first flight of the <a href=\"https://wikipedia.org/wiki/Grumman_F6F_Hellcat\" title=\"Grumman F6F Hellcat\">Grumman F6F Hellcat</a>.", "links": [{"title": "Grumman F6F Hellcat", "link": "https://wikipedia.org/wiki/Grumman_F6F_Hellcat"}]}, {"year": "1944", "text": "World War II: San Marino, a neutral state, is mistakenly bombed by the RAF based on faulty information, leading to 35 civilian deaths.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, a neutral state, is mistakenly bombed by the <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> based on faulty information, leading to 35 civilian deaths.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/San_Marino\" title=\"San Marino\">San Marino</a>, a neutral state, is mistakenly bombed by the <a href=\"https://wikipedia.org/wiki/RAF\" class=\"mw-redirect\" title=\"RAF\">RAF</a> based on faulty information, leading to 35 civilian deaths.", "links": [{"title": "San Marino", "link": "https://wikipedia.org/wiki/San_Marino"}, {"title": "RAF", "link": "https://wikipedia.org/wiki/RAF"}]}, {"year": "1944", "text": "World War II: The Battle of Osuchy in Osuchy, Poland, one of the largest battles between Nazi Germany and Polish resistance forces, ends with the defeat of the latter.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Osuchy\" title=\"Battle of Osuchy\">Battle of Osuchy</a> in <a href=\"https://wikipedia.org/wiki/Osuchy\" title=\"Osuchy\">Osuchy</a>, Poland, one of the largest battles between Nazi Germany and <a href=\"https://wikipedia.org/wiki/Polish_resistance_movement_in_World_War_II\" title=\"Polish resistance movement in World War II\">Polish resistance forces</a>, ends with the defeat of the latter.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Osuchy\" title=\"Battle of Osuchy\">Battle of Osuchy</a> in <a href=\"https://wikipedia.org/wiki/Osuchy\" title=\"Osuchy\">Osuchy</a>, Poland, one of the largest battles between Nazi Germany and <a href=\"https://wikipedia.org/wiki/Polish_resistance_movement_in_World_War_II\" title=\"Polish resistance movement in World War II\">Polish resistance forces</a>, ends with the defeat of the latter.", "links": [{"title": "Battle of Osuchy", "link": "https://wikipedia.org/wiki/Battle_of_Osuchy"}, {"title": "Osuchy", "link": "https://wikipedia.org/wiki/Osuchy"}, {"title": "Polish resistance movement in World War II", "link": "https://wikipedia.org/wiki/Polish_resistance_movement_in_World_War_II"}]}, {"year": "1945", "text": "The United Nations Charter is signed by 50 Allied nations in San Francisco, California.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/United_Nations_Charter\" class=\"mw-redirect\" title=\"United Nations Charter\">United Nations Charter</a> is signed by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">50 Allied nations</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco, California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Nations_Charter\" class=\"mw-redirect\" title=\"United Nations Charter\">United Nations Charter</a> is signed by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">50 Allied nations</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco, California</a>.", "links": [{"title": "United Nations Charter", "link": "https://wikipedia.org/wiki/United_Nations_Charter"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1948", "text": "Cold War: The first supply flights are made in response to the Berlin Blockade.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The first supply flights are made in response to the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The first supply flights are made in response to the <a href=\"https://wikipedia.org/wiki/Berlin_Blockade\" title=\"Berlin Blockade\">Berlin Blockade</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Berlin Blockade", "link": "https://wikipedia.org/wiki/Berlin_Blockade"}]}, {"year": "1948", "text": "<PERSON> files the original patent for the grown-junction transistor, the first bipolar junction transistor.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> files the original patent for the <a href=\"https://wikipedia.org/wiki/Grown-junction_transistor\" title=\"Grown-junction transistor\">grown-junction transistor</a>, the first <a href=\"https://wikipedia.org/wiki/Bipolar_junction_transistor\" title=\"Bipolar junction transistor\">bipolar junction transistor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> files the original patent for the <a href=\"https://wikipedia.org/wiki/Grown-junction_transistor\" title=\"Grown-junction transistor\">grown-junction transistor</a>, the first <a href=\"https://wikipedia.org/wiki/Bipolar_junction_transistor\" title=\"Bipolar junction transistor\">bipolar junction transistor</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Grown-junction transistor", "link": "https://wikipedia.org/wiki/Grown-junction_transistor"}, {"title": "Bipolar junction transistor", "link": "https://wikipedia.org/wiki/Bipolar_junction_transistor"}]}, {"year": "1948", "text": "<PERSON>'s short story <PERSON> Lottery is published in The New Yorker magazine.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s short story <i><a href=\"https://wikipedia.org/wiki/The_Lottery\" title=\"The Lottery\">The Lottery</a></i> is published in <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> magazine.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s short story <i><a href=\"https://wikipedia.org/wiki/The_Lottery\" title=\"The Lottery\">The Lottery</a></i> is published in <i><a href=\"https://wikipedia.org/wiki/The_New_Yorker\" title=\"The New Yorker\">The New Yorker</a></i> magazine.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Lottery", "link": "https://wikipedia.org/wiki/The_Lottery"}, {"title": "The New Yorker", "link": "https://wikipedia.org/wiki/The_New_Yorker"}]}, {"year": "1952", "text": "The Pan-Malayan Labour Party is founded in Malaya, as a union of statewide labour parties.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Pan-Malayan_Labour_Party\" class=\"mw-redirect\" title=\"Pan-Malayan Labour Party\">Pan-Malayan Labour Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Malaya</a>, as a union of statewide labour parties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pan-Malayan_Labour_Party\" class=\"mw-redirect\" title=\"Pan-Malayan Labour Party\">Pan-Malayan Labour Party</a> is founded in <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Malaya</a>, as a union of statewide labour parties.", "links": [{"title": "Pan-Malayan Labour Party", "link": "https://wikipedia.org/wiki/Pan-Malayan_Labour_Party"}, {"title": "Federation of Malaya", "link": "https://wikipedia.org/wiki/Federation_of_Malaya"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, head of MVD, is arrested by <PERSON><PERSON> and other members of the Politburo.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, head of <a href=\"https://wikipedia.org/wiki/MVD\" class=\"mw-redirect\" title=\"MVD\">MVD</a>, is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and other members of the <a href=\"https://wikipedia.org/wiki/Politburo\" title=\"Politburo\">Politburo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lavrentiy_Beria\" title=\"Lavrentiy Beria\"><PERSON><PERSON><PERSON><PERSON></a>, head of <a href=\"https://wikipedia.org/wiki/MVD\" class=\"mw-redirect\" title=\"MVD\">MVD</a>, is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and other members of the <a href=\"https://wikipedia.org/wiki/Politburo\" title=\"Politburo\">Politburo</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lavrentiy_Beria"}, {"title": "MVD", "link": "https://wikipedia.org/wiki/MVD"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Politburo", "link": "https://wikipedia.org/wiki/Politburo"}]}, {"year": "1955", "text": "The South African Congress Alliance adopts the Freedom Charter at the Congress of the People in Kliptown.", "html": "1955 - The South African Congress Alliance adopts the <a href=\"https://wikipedia.org/wiki/Freedom_Charter\" title=\"Freedom Charter\">Freedom Charter</a> at the <a href=\"https://wikipedia.org/wiki/Congress_of_the_People_(1955)\" title=\"Congress of the People (1955)\">Congress of the People</a> in <a href=\"https://wikipedia.org/wiki/Kliptown\" title=\"Kliptown\">Kliptown</a>.", "no_year_html": "The South African Congress Alliance adopts the <a href=\"https://wikipedia.org/wiki/Freedom_Charter\" title=\"Freedom Charter\">Freedom Charter</a> at the <a href=\"https://wikipedia.org/wiki/Congress_of_the_People_(1955)\" title=\"Congress of the People (1955)\">Congress of the People</a> in <a href=\"https://wikipedia.org/wiki/Kliptown\" title=\"Kliptown\">Kliptown</a>.", "links": [{"title": "Freedom Charter", "link": "https://wikipedia.org/wiki/Freedom_Charter"}, {"title": "Congress of the People (1955)", "link": "https://wikipedia.org/wiki/Congress_of_the_People_(1955)"}, {"title": "Kliptown", "link": "https://wikipedia.org/wiki/Kliptown"}]}, {"year": "1959", "text": "Swedish boxer <PERSON><PERSON><PERSON> becomes world champion of heavy weight boxing, by defeating American <PERSON> on technical knockout after two minutes and three seconds in the third round at Yankee Stadium.", "html": "1959 - Swedish boxer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes world champion of heavy weight boxing, by defeating American <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on technical knockout after two minutes and three seconds in the third round at <a href=\"https://wikipedia.org/wiki/Yankee_Stadium\" title=\"Yankee Stadium\">Yankee Stadium</a>.", "no_year_html": "Swedish boxer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes world champion of heavy weight boxing, by defeating American <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> on technical knockout after two minutes and three seconds in the third round at <a href=\"https://wikipedia.org/wiki/Yankee_Stadium\" title=\"Yankee Stadium\">Yankee Stadium</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Yankee Stadium", "link": "https://wikipedia.org/wiki/Yankee_Stadium"}]}, {"year": "1960", "text": "The former British Protectorate of British Somaliland gains its independence as Somaliland.", "html": "1960 - The former British Protectorate of <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a> gains its independence as <a href=\"https://wikipedia.org/wiki/State_of_Somaliland\" title=\"State of Somaliland\">Somaliland</a>.", "no_year_html": "The former British Protectorate of <a href=\"https://wikipedia.org/wiki/British_Somaliland\" title=\"British Somaliland\">British Somaliland</a> gains its independence as <a href=\"https://wikipedia.org/wiki/State_of_Somaliland\" title=\"State of Somaliland\">Somaliland</a>.", "links": [{"title": "British Somaliland", "link": "https://wikipedia.org/wiki/British_Somaliland"}, {"title": "State of Somaliland", "link": "https://wikipedia.org/wiki/State_of_Somaliland"}]}, {"year": "1960", "text": "Madagascar gains its independence from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Madagascar\" title=\"Madagascar\">Madagascar</a> gains its independence from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madagascar\" title=\"Madagascar\">Madagascar</a> gains its independence from France.", "links": [{"title": "Madagascar", "link": "https://wikipedia.org/wiki/Madagascar"}]}, {"year": "1963", "text": "Cold War: U.S. President <PERSON> gave his \"Ich bin ein Berliner\" speech, underlining the support of the United States for democratic West Germany shortly after Soviet-supported East Germany erected the Berlin Wall.", "html": "1963 - Cold War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gave his \"<a href=\"https://wikipedia.org/wiki/Ich_bin_ein_<PERSON>\" title=\"Ich bin ein Berliner\">Ich bin ein <PERSON></a>\" speech, underlining the support of the United States for democratic <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> shortly after <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a>-supported <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> erected the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "no_year_html": "Cold War: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gave his \"<a href=\"https://wikipedia.org/wiki/Ich_bin_ein_<PERSON>er\" title=\"Ich bin ein <PERSON>\">Ich bin ein <PERSON></a>\" speech, underlining the support of the United States for democratic <a href=\"https://wikipedia.org/wiki/West_Germany\" title=\"West Germany\">West Germany</a> shortly after <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a>-supported <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> erected the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>ch bin ein <PERSON>", "link": "https://wikipedia.org/wiki/Ich_bin_e<PERSON>_<PERSON>er"}, {"title": "West Germany", "link": "https://wikipedia.org/wiki/West_Germany"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "1967", "text": "<PERSON><PERSON> (later <PERSON>) made a cardinal by Pope <PERSON>.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ojty%C5%82a\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (later <PERSON>) made a cardinal by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ojty%C5%82a\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (later <PERSON>) made a cardinal by <a href=\"https://wikipedia.org/wiki/<PERSON>_Paul_<PERSON>\" title=\"Pope Paul VI\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wojty%C5%82a"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "The Universal Product Code is scanned for the first time to sell a package of <PERSON><PERSON><PERSON>'s chewing gum at the Marsh Supermarket in Troy, Ohio.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Universal_Product_Code\" title=\"Universal Product Code\">Universal Product Code</a> is scanned for the first time to sell a package of Wrigley's <a href=\"https://wikipedia.org/wiki/Chewing_gum\" title=\"Chewing gum\">chewing gum</a> at the Marsh Supermarket in <a href=\"https://wikipedia.org/wiki/Troy,_Ohio\" title=\"Troy, Ohio\">Troy, Ohio</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Universal_Product_Code\" title=\"Universal Product Code\">Universal Product Code</a> is scanned for the first time to sell a package of Wrigley's <a href=\"https://wikipedia.org/wiki/Chewing_gum\" title=\"Chewing gum\">chewing gum</a> at the Marsh Supermarket in <a href=\"https://wikipedia.org/wiki/Troy,_Ohio\" title=\"Troy, Ohio\">Troy, Ohio</a>.", "links": [{"title": "Universal Product Code", "link": "https://wikipedia.org/wiki/Universal_Product_Code"}, {"title": "Chewing gum", "link": "https://wikipedia.org/wiki/Chewing_gum"}, {"title": "Troy, Ohio", "link": "https://wikipedia.org/wiki/Troy,_Ohio"}]}, {"year": "1975", "text": "Two FBI agents and a member of the American Indian Movement are killed in a shootout on the Pine Ridge Indian Reservation in South Dakota; <PERSON> is later convicted of the murders in a controversial trial.", "html": "1975 - Two <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents and a member of the <a href=\"https://wikipedia.org/wiki/American_Indian_Movement\" title=\"American Indian Movement\">American Indian Movement</a> are killed in a shootout on the <a href=\"https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation\" title=\"Pine Ridge Indian Reservation\">Pine Ridge Indian Reservation</a> in <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is later convicted of the murders in a controversial trial.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents and a member of the <a href=\"https://wikipedia.org/wiki/American_Indian_Movement\" title=\"American Indian Movement\">American Indian Movement</a> are killed in a shootout on the <a href=\"https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation\" title=\"Pine Ridge Indian Reservation\">Pine Ridge Indian Reservation</a> in <a href=\"https://wikipedia.org/wiki/South_Dakota\" title=\"South Dakota\">South Dakota</a>; <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is later convicted of the murders in a controversial trial.", "links": [{"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}, {"title": "American Indian Movement", "link": "https://wikipedia.org/wiki/American_Indian_Movement"}, {"title": "Pine Ridge Indian Reservation", "link": "https://wikipedia.org/wiki/Pine_Ridge_Indian_Reservation"}, {"title": "South Dakota", "link": "https://wikipedia.org/wiki/South_Dakota"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON> held his final concert in Indianapolis, Indiana at Market Square Arena.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> held his final concert in Indianapolis, Indiana at Market Square Arena.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a> held his final concert in Indianapolis, Indiana at Market Square Arena.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "Air Canada Flight 189, flying to Toronto, overruns the runway and crashes into the Etobicoke Creek ravine. Two of the 107 passengers on board perish.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_189\" title=\"Air Canada Flight 189\">Air Canada Flight 189</a>, flying to Toronto, overruns the runway and crashes into the <a href=\"https://wikipedia.org/wiki/Etobicoke_Creek\" title=\"Etobicoke Creek\">Etobicoke Creek</a> <a href=\"https://wikipedia.org/wiki/Ravine\" title=\"Ravine\">ravine</a>. Two of the 107 passengers on board perish.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Canada_Flight_189\" title=\"Air Canada Flight 189\">Air Canada Flight 189</a>, flying to Toronto, overruns the runway and crashes into the <a href=\"https://wikipedia.org/wiki/Etobicoke_Creek\" title=\"Etobicoke Creek\">Etobicoke Creek</a> <a href=\"https://wikipedia.org/wiki/Ravine\" title=\"Ravine\">ravine</a>. Two of the 107 passengers on board perish.", "links": [{"title": "Air Canada Flight 189", "link": "https://wikipedia.org/wiki/Air_Canada_Flight_189"}, {"title": "Etobicoke Creek", "link": "https://wikipedia.org/wiki/Etobicoke_Creek"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ravine"}]}, {"year": "1981", "text": "Dan-Air Flight 240, flying to East Midlands Airport, crashes in Nailstone, Leicestershire. All three crew members perish.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_240\" title=\"Dan-Air Flight 240\">Dan-Air Flight 240</a>, flying to <a href=\"https://wikipedia.org/wiki/East_Midlands_Airport\" title=\"East Midlands Airport\">East Midlands Airport</a>, crashes in <a href=\"https://wikipedia.org/wiki/Nailstone\" title=\"Nailstone\">Nailstone</a>, <a href=\"https://wikipedia.org/wiki/Leicestershire\" title=\"Leicestershire\">Leicestershire</a>. All three crew members perish.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dan-Air_Flight_240\" title=\"Dan-Air Flight 240\">Dan-Air Flight 240</a>, flying to <a href=\"https://wikipedia.org/wiki/East_Midlands_Airport\" title=\"East Midlands Airport\">East Midlands Airport</a>, crashes in <a href=\"https://wikipedia.org/wiki/Nailstone\" title=\"Nailstone\">Nailstone</a>, <a href=\"https://wikipedia.org/wiki/Leicestershire\" title=\"Leicestershire\">Leicestershire</a>. All three crew members perish.", "links": [{"title": "Dan-Air Flight 240", "link": "https://wikipedia.org/wiki/Dan-Air_Flight_240"}, {"title": "East Midlands Airport", "link": "https://wikipedia.org/wiki/East_Midlands_Airport"}, {"title": "Nailstone", "link": "https://wikipedia.org/wiki/Nailstone"}, {"title": "Leicestershire", "link": "https://wikipedia.org/wiki/Leicestershire"}]}, {"year": "1988", "text": "The first crash of an Airbus A320 occurs when Air France Flight 296Q crashes at Mulhouse-Habsheim Airfield in Habsheim, France, during an air show, killing three of the 136 people on board.", "html": "1988 - The first crash of an <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a> occurs when <a href=\"https://wikipedia.org/wiki/Air_France_Flight_296Q\" title=\"Air France Flight 296Q\">Air France Flight 296Q</a> crashes at <a href=\"https://wikipedia.org/wiki/Mulhouse%E2%80%93Habsheim_Airfield\" title=\"Mulhouse-Habsheim Airfield\">Mulhouse-Habsheim Airfield</a> in <a href=\"https://wikipedia.org/wiki/Habsheim\" title=\"Habsheim\">Habsheim</a>, France, during an <a href=\"https://wikipedia.org/wiki/Air_show\" title=\"Air show\">air show</a>, killing three of the 136 people on board.", "no_year_html": "The first crash of an <a href=\"https://wikipedia.org/wiki/Airbus_A320_family\" title=\"Airbus A320 family\">Airbus A320</a> occurs when <a href=\"https://wikipedia.org/wiki/Air_France_Flight_296Q\" title=\"Air France Flight 296Q\">Air France Flight 296Q</a> crashes at <a href=\"https://wikipedia.org/wiki/Mulhouse%E2%80%93Habsheim_Airfield\" title=\"Mulhouse-Habsheim Airfield\">Mulhouse-Habsheim Airfield</a> in <a href=\"https://wikipedia.org/wiki/Habsheim\" title=\"Habsheim\">Habsheim</a>, France, during an <a href=\"https://wikipedia.org/wiki/Air_show\" title=\"Air show\">air show</a>, killing three of the 136 people on board.", "links": [{"title": "Airbus A320 family", "link": "https://wikipedia.org/wiki/Airbus_A320_family"}, {"title": "Air France Flight 296Q", "link": "https://wikipedia.org/wiki/Air_France_Flight_296Q"}, {"title": "Mulhouse-Habsheim Airfield", "link": "https://wikipedia.org/wiki/Mulhouse%E2%80%93Habsheim_Airfield"}, {"title": "Habsheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Air show", "link": "https://wikipedia.org/wiki/Air_show"}]}, {"year": "1991", "text": "Yugoslav Wars: The Yugoslav People's Army begins the Ten-Day War in Slovenia.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> begins the <a href=\"https://wikipedia.org/wiki/Ten-Day_War\" title=\"Ten-Day War\">Ten-Day War</a> in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugoslav_Wars\" title=\"Yugoslav Wars\">Yugoslav Wars</a>: The <a href=\"https://wikipedia.org/wiki/Yugoslav_People%27s_Army\" title=\"Yugoslav People's Army\">Yugoslav People's Army</a> begins the <a href=\"https://wikipedia.org/wiki/Ten-Day_War\" title=\"Ten-Day War\">Ten-Day War</a> in <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>.", "links": [{"title": "Yugoslav Wars", "link": "https://wikipedia.org/wiki/Yugoslav_Wars"}, {"title": "Yugoslav People's Army", "link": "https://wikipedia.org/wiki/Yugoslav_People%27s_Army"}, {"title": "Ten-Day War", "link": "https://wikipedia.org/wiki/Ten-Day_War"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}]}, {"year": "1995", "text": "<PERSON><PERSON> bin <PERSON><PERSON><PERSON> deposes his father <PERSON><PERSON><PERSON> bin <PERSON><PERSON>, the Emir of Qatar, in a bloodless coup d'état.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_Khalifa_Al_Thani\" title=\"<PERSON><PERSON> bin Khali<PERSON> Thani\"><PERSON><PERSON> bin <PERSON><PERSON></a> deposes his father <a href=\"https://wikipedia.org/wiki/Khali<PERSON>_bin_Hamad_Al_Thani\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>ad <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">Emir</a> of <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, in a bloodless <a href=\"https://wikipedia.org/wiki/1995_Qatari_coup_d%27%C3%A9tat\" title=\"1995 Qatari coup d'état\">coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_Khalifa_Al_Thani\" title=\"<PERSON><PERSON> bin Khali<PERSON> Thani\"><PERSON><PERSON> bin <PERSON><PERSON></a> deposes his father <a href=\"https://wikipedia.org/wiki/Khali<PERSON>_bin_Hamad_Al_Thani\" title=\"<PERSON><PERSON><PERSON> bin Hamad <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">Emir</a> of <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, in a bloodless <a href=\"https://wikipedia.org/wiki/1995_Qatari_coup_d%27%C3%A9tat\" title=\"1995 Qatari coup d'état\">coup d'état</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> bin <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "Qatar", "link": "https://wikipedia.org/wiki/Qatar"}, {"title": "1995 Qatari coup d'état", "link": "https://wikipedia.org/wiki/1995_Qatari_coup_d%27%C3%A9tat"}]}, {"year": "1997", "text": "The U.S. Supreme Court rules that the Communications Decency Act violates the First Amendment to the United States Constitution.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> violates the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment to the United States Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules that the <a href=\"https://wikipedia.org/wiki/Communications_Decency_Act\" title=\"Communications Decency Act\">Communications Decency Act</a> violates the <a href=\"https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution\" title=\"First Amendment to the United States Constitution\">First Amendment to the United States Constitution</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Communications Decency Act", "link": "https://wikipedia.org/wiki/Communications_Decency_Act"}, {"title": "First Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/First_Amendment_to_the_United_States_Constitution"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON> publishes the first of her <PERSON> novel series, <PERSON> and the Philosopher's Stone in United Kingdom.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> publishes the first of her <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> novel series, <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Philosopher%27s_Stone\" title=\"<PERSON> and the Philosopher's Stone\"><PERSON> and the Philosopher's Stone</a></i> in United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> publishes the first of her <i><a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> novel series, <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Philosopher%27s_Stone\" title=\"<PERSON> and the Philosopher's Stone\"><PERSON> and the Philosopher's Stone</a></i> in United Kingdom.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ling"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> and the Philosopher's Stone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_and_the_Philosopher%27s_<PERSON>"}]}, {"year": "2000", "text": "The Human Genome Project announces the completion of a \"rough draft\" sequence.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/Human_Genome_Project\" title=\"Human Genome Project\">Human Genome Project</a> announces the completion of a \"rough draft\" sequence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Human_Genome_Project\" title=\"Human Genome Project\">Human Genome Project</a> announces the completion of a \"rough draft\" sequence.", "links": [{"title": "Human Genome Project", "link": "https://wikipedia.org/wiki/Human_Genome_Project"}]}, {"year": "2003", "text": "The U.S. Supreme Court rules in Lawrence v. Texas that sex-based sodomy laws are unconstitutional.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Lawrence_v._Texas\" title=\"Lawrence v. Texas\">Lawrence v. Texas</a></i> that sex-based <a href=\"https://wikipedia.org/wiki/Sodomy_law\" title=\"Sodomy law\">sodomy laws</a> are unconstitutional.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Lawrence_v._Texas\" title=\"Lawrence v. Texas\">Lawrence v. Texas</a></i> that sex-based <a href=\"https://wikipedia.org/wiki/Sodomy_law\" title=\"Sodomy law\">sodomy laws</a> are unconstitutional.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON> v. Texas", "link": "https://wikipedia.org/wiki/Lawrence_v._Texas"}, {"title": "Sodomy law", "link": "https://wikipedia.org/wiki/Sodomy_law"}]}, {"year": "2006", "text": "<PERSON>, the first Prime Minister of East Timor, resigns after weeks of political unrest.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Mari_Alkatiri\" title=\"Mari Alkatiri\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_East_Timor\" class=\"mw-redirect\" title=\"Prime Minister of East Timor\">Prime Minister of East Timor</a>, resigns after weeks of political unrest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari_Alkatiri\" title=\"Mari Alkatiri\"><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_East_Timor\" class=\"mw-redirect\" title=\"Prime Minister of East Timor\">Prime Minister of East Timor</a>, resigns after weeks of political unrest.", "links": [{"title": "Mari Alkatiri", "link": "https://wikipedia.org/wiki/Mari_Alkatiri"}, {"title": "Prime Minister of East Timor", "link": "https://wikipedia.org/wiki/Prime_Minister_of_East_Timor"}]}, {"year": "2007", "text": "Pope <PERSON> reinstates the traditional laws of papal election in which a successful candidate must receive two-thirds of the votes.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> reinstates the traditional laws of papal election in which a successful candidate must receive two-thirds of the votes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a> reinstates the traditional laws of papal election in which a successful candidate must receive two-thirds of the votes.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "A suicide bomber dressed as an Iraqi policeman detonates an explosive vest, killing 25 people.", "html": "2008 - A suicide bomber dressed as an Iraqi policeman detonates an explosive vest, <a href=\"https://wikipedia.org/wiki/2008_Karmah_bombing\" title=\"2008 Karmah bombing\">killing 25 people</a>.", "no_year_html": "A suicide bomber dressed as an Iraqi policeman detonates an explosive vest, <a href=\"https://wikipedia.org/wiki/2008_Karmah_bombing\" title=\"2008 Karmah bombing\">killing 25 people</a>.", "links": [{"title": "2008 Karmah bombing", "link": "https://wikipedia.org/wiki/2008_Karmah_bombing"}]}, {"year": "2012", "text": "The Waldo Canyon fire descends into the Mountain Shadows neighborhood in Colorado Springs burning 347 homes in a matter of hours and killing two people.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Waldo_Canyon_Fire\" title=\"Waldo Canyon Fire\">Waldo Canyon fire</a> descends into the Mountain Shadows neighborhood in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs</a> burning 347 homes in a matter of hours and killing two people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Waldo_Canyon_Fire\" title=\"Waldo Canyon Fire\">Waldo Canyon fire</a> descends into the Mountain Shadows neighborhood in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs</a> burning 347 homes in a matter of hours and killing two people.", "links": [{"title": "Waldo Canyon Fire", "link": "https://wikipedia.org/wiki/Waldo_Canyon_Fire"}, {"title": "Colorado Springs, Colorado", "link": "https://wikipedia.org/wiki/Colorado_Springs,_Colorado"}]}, {"year": "2013", "text": "Riots in China's Xinjiang region kill at least 36 people and injure 21 others.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/June_2013_Shanshan_riots\" title=\"June 2013 Shanshan riots\">Riots</a> in China's <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a> region kill at least 36 people and injure 21 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_2013_Shanshan_riots\" title=\"June 2013 Shanshan riots\">Riots</a> in China's <a href=\"https://wikipedia.org/wiki/Xinjiang\" title=\"Xinjiang\">Xinjiang</a> region kill at least 36 people and injure 21 others.", "links": [{"title": "June 2013 Shanshan riots", "link": "https://wikipedia.org/wiki/June_2013_<PERSON><PERSON>_riots"}, {"title": "Xinjiang", "link": "https://wikipedia.org/wiki/Xinjiang"}]}, {"year": "2013", "text": "The U.S. Supreme Court ruled, 5-4, that Section 3 of the Defense of Marriage Act is unconstitutional and in violation of the Fifth Amendment to the United States Constitution.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> <a href=\"https://wikipedia.org/wiki/United_States_v._Windsor\" title=\"United States v. Windsor\">ruled, 5-4</a>, that Section 3 of the <a href=\"https://wikipedia.org/wiki/Defense_of_Marriage_Act\" title=\"Defense of Marriage Act\">Defense of Marriage Act</a> is unconstitutional and in violation of the <a href=\"https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution\" title=\"Fifth Amendment to the United States Constitution\">Fifth Amendment to the United States Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> <a href=\"https://wikipedia.org/wiki/United_States_v._Windsor\" title=\"United States v. Windsor\">ruled, 5-4</a>, that Section 3 of the <a href=\"https://wikipedia.org/wiki/Defense_of_Marriage_Act\" title=\"Defense of Marriage Act\">Defense of Marriage Act</a> is unconstitutional and in violation of the <a href=\"https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution\" title=\"Fifth Amendment to the United States Constitution\">Fifth Amendment to the United States Constitution</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "United States v. Windsor", "link": "https://wikipedia.org/wiki/United_States_v._Windsor"}, {"title": "Defense of Marriage Act", "link": "https://wikipedia.org/wiki/Defense_of_Marriage_Act"}, {"title": "Fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution"}]}, {"year": "2015", "text": "Five different terrorist attacks in France, Tunisia, Somalia, Kuwait, and Syria occurred on what was dubbed Bloody Friday by international media. Upwards of 750 people were either killed or injured in these uncoordinated attacks.", "html": "2015 - Five different terrorist attacks in France, Tunisia, Somalia, Kuwait, and Syria occurred on what was dubbed <a href=\"https://wikipedia.org/wiki/26_June_2015_Islamist_attacks\" title=\"26 June 2015 Islamist attacks\">Bloody Friday</a> by international media. Upwards of 750 people were either killed or injured in these uncoordinated attacks.", "no_year_html": "Five different terrorist attacks in France, Tunisia, Somalia, Kuwait, and Syria occurred on what was dubbed <a href=\"https://wikipedia.org/wiki/26_June_2015_Islamist_attacks\" title=\"26 June 2015 Islamist attacks\">Bloody Friday</a> by international media. Upwards of 750 people were either killed or injured in these uncoordinated attacks.", "links": [{"title": "26 June 2015 Islamist attacks", "link": "https://wikipedia.org/wiki/26_June_2015_Islamist_attacks"}]}, {"year": "2015", "text": "The U.S. Supreme Court ruled, 5-4, that same-sex couples have a constitutional right to marriage under the 14th Amendment to the United States Constitution.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> v<PERSON>\">ruled, 5-4</a>, that <a href=\"https://wikipedia.org/wiki/Same-sex_relationship\" title=\"Same-sex relationship\">same-sex couples</a> have a <a href=\"https://wikipedia.org/wiki/Constitutional_right\" title=\"Constitutional right\">constitutional right</a> to <a href=\"https://wikipedia.org/wiki/Marriage\" title=\"Marriage\">marriage</a> under the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment to the United States Constitution</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> v<PERSON>\">ruled, 5-4</a>, that <a href=\"https://wikipedia.org/wiki/Same-sex_relationship\" title=\"Same-sex relationship\">same-sex couples</a> have a <a href=\"https://wikipedia.org/wiki/Constitutional_right\" title=\"Constitutional right\">constitutional right</a> to <a href=\"https://wikipedia.org/wiki/Marriage\" title=\"Marriage\">marriage</a> under the <a href=\"https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution\" title=\"Fourteenth Amendment to the United States Constitution\">14th Amendment to the United States Constitution</a>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Obergefell v. Hodges", "link": "https://wikipedia.org/wiki/Obergef<PERSON>_v._<PERSON>"}, {"title": "Same-sex relationship", "link": "https://wikipedia.org/wiki/Same-sex_relationship"}, {"title": "Constitutional right", "link": "https://wikipedia.org/wiki/Constitutional_right"}, {"title": "Marriage", "link": "https://wikipedia.org/wiki/Marriage"}, {"title": "Fourteenth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fourteenth_Amendment_to_the_United_States_Constitution"}]}, {"year": "2024", "text": "<PERSON>, founder of WikiLeaks, returns to Australia after pleading guilty to one charge of espionage in a Saipan court and subsequently being released by the United States Department of Justice.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a>, returns to Australia after <a href=\"https://wikipedia.org/wiki/Plea_bargain\" title=\"Plea bargain\">pleading guilty</a> to one charge of <a href=\"https://wikipedia.org/wiki/Espionage_Act_of_1917\" title=\"Espionage Act of 1917\">espionage</a> in a <a href=\"https://wikipedia.org/wiki/District_Court_for_the_Northern_Mariana_Islands\" title=\"District Court for the Northern Mariana Islands\">Saipan court</a> and subsequently being released by the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of <a href=\"https://wikipedia.org/wiki/WikiLeaks\" title=\"WikiLeaks\">WikiLeaks</a>, returns to Australia after <a href=\"https://wikipedia.org/wiki/Plea_bargain\" title=\"Plea bargain\">pleading guilty</a> to one charge of <a href=\"https://wikipedia.org/wiki/Espionage_Act_of_1917\" title=\"Espionage Act of 1917\">espionage</a> in a <a href=\"https://wikipedia.org/wiki/District_Court_for_the_Northern_Mariana_Islands\" title=\"District Court for the Northern Mariana Islands\">Saipan court</a> and subsequently being released by the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Justice\" title=\"United States Department of Justice\">United States Department of Justice</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "WikiLeaks", "link": "https://wikipedia.org/wiki/WikiLeaks"}, {"title": "Plea bargain", "link": "https://wikipedia.org/wiki/Plea_bargain"}, {"title": "Espionage Act of 1917", "link": "https://wikipedia.org/wiki/Espionage_Act_of_1917"}, {"title": "District Court for the Northern Mariana Islands", "link": "https://wikipedia.org/wiki/District_Court_for_the_Northern_Mariana_Islands"}, {"title": "United States Department of Justice", "link": "https://wikipedia.org/wiki/United_States_Department_of_Justice"}]}], "Births": [{"year": "12 BC", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman son of <PERSON><PERSON><PERSON> and <PERSON> the <PERSON> (d. 14)", "html": "12 BC - 12 BC - <a href=\"https://wikipedia.org/wiki/Ag<PERSON>pa_Postumus\" title=\"<PERSON>g<PERSON><PERSON> Postumus\"><PERSON><PERSON><PERSON><PERSON></a>, Roman son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ius_Agrippa\" title=\"<PERSON>ius <PERSON>grippa\"><PERSON>pa</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a> (d. 14)", "no_year_html": "12 BC - <a href=\"https://wikipedia.org/wiki/Ag<PERSON>pa_Postumus\" title=\"<PERSON>g<PERSON><PERSON> Postumus\"><PERSON><PERSON><PERSON><PERSON></a>, Roman son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ius_Agrippa\" title=\"<PERSON>ius Agrippa\"><PERSON>ius <PERSON>rippa</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a> (d. 14)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agrippa_Postumus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_the_<PERSON>"}]}, {"year": "1399", "text": "<PERSON>, Count of Angoulême (d. 1467)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Angoul%C3%AAme\" title=\"<PERSON>, Count of Angoulême\"><PERSON>, Count of Angoulême</a> (d. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Angoul%C3%AAme\" title=\"<PERSON>, Count of Angoulême\"><PERSON>, Count of Angoulême</a> (d. 1467)", "links": [{"title": "<PERSON>, Count of Angoulême", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_of_Ang<PERSON>l%C3%AAme"}]}, {"year": "1467", "text": "<PERSON> of Naples (d. 1496)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Naples\" title=\"<PERSON> of Naples\"><PERSON> of Naples</a> (d. 1496)", "links": [{"title": "<PERSON> of Naples", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Naples"}]}, {"year": "1575", "text": "<PERSON> Brandenburg (d. 1612)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON></a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brandenburg\" title=\"<PERSON> of Brandenburg\"><PERSON> of Brandenburg</a> (d. 1612)", "links": [{"title": "<PERSON> Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, Spanish Jesuit saint (d. 1654)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit saint (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish Jesuit saint (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1600", "text": "<PERSON>, Spanish-born bishop and viceroy of New Spain (d. 1659)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>la<PERSON>x_y_Mendoza\" title=\"<PERSON> Palafox y Mendoza\"><PERSON> y <PERSON></a>, Spanish-born bishop and <a href=\"https://wikipedia.org/wiki/Viceroy\" title=\"Viceroy\">viceroy</a> of <a href=\"https://wikipedia.org/wiki/New_Spain\" title=\"New Spain\">New Spain</a> (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_y_Mendoza\" title=\"<PERSON> Palafox y Mendoza\"><PERSON> y <PERSON></a>, Spanish-born bishop and <a href=\"https://wikipedia.org/wiki/Viceroy\" title=\"Viceroy\">viceroy</a> of <a href=\"https://wikipedia.org/wiki/New_Spain\" title=\"New Spain\">New Spain</a> (d. 1659)", "links": [{"title": "<PERSON> y Mendoza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Viceroy", "link": "https://wikipedia.org/wiki/Viceroy"}, {"title": "New Spain", "link": "https://wikipedia.org/wiki/New_Spain"}]}, {"year": "1681", "text": "<PERSON><PERSON><PERSON><PERSON> of Sweden (d. 1708)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/Hedvig_<PERSON>_of_Sweden\" title=\"Hedvig <PERSON> of Sweden\">Hedvig <PERSON> of Sweden</a> (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hedvig_<PERSON>_of_Sweden\" title=\"Hedvig <PERSON> of Sweden\">Hedvig <PERSON> of Sweden</a> (d. 1708)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Hedvig_Sophia_of_Sweden"}]}, {"year": "1689", "text": "<PERSON>, American pastor and academic (d. 1769)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and academic (d. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and academic (d. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, Swedish chemist and mineralogist (d. 1768)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish chemist and mineralogist (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1699", "text": "<PERSON>, French businesswoman (d. 1777)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9r%C3%A8<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>é<PERSON>\"><PERSON></a>, French businesswoman (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9r%C3%A8<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>ér<PERSON>\"><PERSON></a>, French businesswoman (d. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Th%C3%A9r%C3%A8se_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, English hymn-writer and educator (d. 1751)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymn-writer and educator (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hymn-writer and educator (d. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1703", "text": "<PERSON>, American minister and academic (d. 1767)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (d. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and academic (d. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON> of Sardinia (d. 1796)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Sardinia\" class=\"mw-redirect\" title=\"<PERSON> of Sardinia\"><PERSON> of Sardinia</a> (d. 1796)", "links": [{"title": "<PERSON> of Sardinia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_III_of_Sardinia"}]}, {"year": "1730", "text": "<PERSON>, French astronomer and academic (d. 1817)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (d. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1764", "text": "<PERSON>, Polish politician (d. 1812)", "html": "1764 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%82_%C5%81<PERSON><PERSON>cz<PERSON>ki\" title=\"<PERSON>\"><PERSON></a>, Polish politician (d. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%82_%C5%81<PERSON><PERSON>cz<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish politician (d. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%82_%C5%81<PERSON><PERSON><PERSON><PERSON>ki"}]}, {"year": "1786", "text": "<PERSON><PERSON><PERSON>, Thai poet (d. 1855)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Sunthorn_Phu\" title=\"Sunthorn Phu\"><PERSON><PERSON><PERSON></a>, Thai poet (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sunthorn_Phu\" title=\"Sunthorn Phu\"><PERSON><PERSON><PERSON></a>, Thai poet (d. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sunthorn_<PERSON>u"}]}, {"year": "1796", "text": "<PERSON>, Polish painter and engineer (d. 1847)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%82_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter and engineer (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%82_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter and engineer (d. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%82_<PERSON><PERSON><PERSON>"}]}, {"year": "1798", "text": "<PERSON>, German poet and critic (d. 1873)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON><PERSON><PERSON>, English painter and poet (d. 1848)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Branwell_Bront%C3%AB\" title=\"Branwell Brontë\"><PERSON><PERSON><PERSON> Brontë</a>, English painter and poet (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Branwell_Bront%C3%AB\" title=\"Branwell Brontë\"><PERSON><PERSON><PERSON> Brontë</a>, English painter and poet (d. 1848)", "links": [{"title": "Branwell Brontë", "link": "https://wikipedia.org/wiki/Branwell_Bront%C3%AB"}]}, {"year": "1819", "text": "<PERSON><PERSON>, American general (d. 1893)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_Doubleday\" title=\"Abner Doubleday\"><PERSON><PERSON> Double<PERSON></a>, American general (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Doubleday\" title=\"Abner Doubleday\"><PERSON><PERSON> Doubleday</a>, American general (d. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Doubleday"}]}, {"year": "1821", "text": "<PERSON><PERSON><PERSON>, Argentinian soldier, journalist, and politician, 6th President of Argentina (d. 1906)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian soldier, journalist, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_Mi<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian soldier, journalist, and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_<PERSON>tre"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1824", "text": "<PERSON>, 1st Baron <PERSON>, Irish-Scottish physicist and engineer (d. 1907)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-Scottish physicist and engineer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Irish-Scottish physicist and engineer (d. 1907)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, American journalist and author (d. 1896)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, American soldier and author (d. 1901)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON><PERSON>, Lebanese painter (d. 1930)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Corm\"><PERSON><PERSON></a>, Lebanese painter (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Corm\"><PERSON><PERSON></a>, Lebanese painter (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Canadian lawyer and politician, 8th Prime Minister of Canada (d. 1937)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1865", "text": "<PERSON>, Lithuanian-American historian and author (d. 1959)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American historian and author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American historian and author (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, 5th Earl of Carnarvon, English archaeologist and banker (d. 1923)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Carnarvon\" title=\"<PERSON>, 5th Earl of Carnarvon\"><PERSON>, 5th Earl of Carnarvon</a>, English archaeologist and banker (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Carnarvon\" title=\"<PERSON>, 5th Earl of Carnarvon\"><PERSON>, 5th Earl of Carnarvon</a>, English archaeologist and banker (d. 1923)", "links": [{"title": "<PERSON>, 5th Earl of Carnarvon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Carnarvon"}]}, {"year": "1869", "text": "<PERSON>, Danish journalist and author (d. 1954)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ex%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Danish journalist and author (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Danish journalist and author (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nex%C3%B8"}]}, {"year": "1878", "text": "<PERSON>, German mathematician and logician (d. 1957)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Leopold_L%C3%B6wen<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and logician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_L%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and logician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_L%C3%B6wen<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American actor (d. 1956)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1956)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli linguist, poet, and playwright (d. 1960)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli linguist, poet, and playwright (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli linguist, poet, and playwright (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/Ya%27<PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1892", "text": "<PERSON>, American novelist, essayist, short story writer Nobel Prize laureate (d. 1973)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Buck\"><PERSON></a>, American novelist, essayist, short story writer <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, short story writer <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1893", "text": "<PERSON>, American journalist and news anchor (d. 1989)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and news anchor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and news anchor (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Canadian ice hockey player and politician (d. 1950)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German engineer and businessman (d. 1978)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and businessman (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, US general (d. 1971)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>est<PERSON>_<PERSON><PERSON>er\" title=\"<PERSON>est<PERSON> Puller\"><PERSON><PERSON><PERSON></a>, US general (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>est<PERSON>_<PERSON><PERSON>er\" title=\"Chest<PERSON> Puller\"><PERSON><PERSON><PERSON></a>, US general (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>est<PERSON>_<PERSON><PERSON>er"}]}, {"year": "1899", "text": "Grand Duchess <PERSON> of Russia (d. 1918)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1899-1918)\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)\" class=\"mw-redirect\" title=\"Grand Duchess <PERSON> of Russia (1899-1918)\">Grand Duchess <PERSON> of Russia</a> (d. 1918)", "links": [{"title": "Grand Duchess <PERSON> of Russia (1899-1918)", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia_(1899%E2%80%931918)"}]}, {"year": "1901", "text": "<PERSON>, American lieutenant and politician, 1st United States Secretary of the Air Force (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Air_Force\" title=\"United States Secretary of the Air Force\">United States Secretary of the Air Force</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 1st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Air_Force\" title=\"United States Secretary of the Air Force\">United States Secretary of the Air Force</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Air Force", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Air_Force"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Swiss tenor and educator (d. 2010)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C3%A9nod\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tenor and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>u%C3%A9nod\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tenor and educator (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hugues_Cu%C3%A9nod"}]}, {"year": "1903", "text": "<PERSON> <PERSON>, American singer-songwriter and guitarist (d. 1958)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Big Bill <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter and guitarist (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Big Bill <PERSON>\"><PERSON> <PERSON></a>, American singer-songwriter and guitarist (d. 1958)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Slovak-American actor and singer (d. 1964)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-American actor and singer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovak-American actor and singer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, American author and illustrator (d. 1985)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>ynd_<PERSON>\" title=\"Lynd Ward\"><PERSON><PERSON><PERSON></a>, American author and illustrator (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ynd_<PERSON>\" title=\"Lynd Ward\"><PERSON><PERSON><PERSON></a>, American author and illustrator (d. 1985)", "links": [{"title": "<PERSON>ynd <PERSON>", "link": "https://wikipedia.org/wiki/Lynd_Ward"}]}, {"year": "1906", "text": "<PERSON>, Italian singer (d. 1974)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American sculptor and educator (d. 2008)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and educator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American baseball player (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Debs_Garms\" title=\"Debs Garms\"><PERSON><PERSON></a>, American baseball player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_Garms\" title=\"De<PERSON> Garms\"><PERSON><PERSON></a>, American baseball player (d. 1984)", "links": [{"title": "<PERSON>bs Garms", "link": "https://wikipedia.org/wiki/Debs_Garms"}]}, {"year": "1908", "text": "<PERSON>, Chilean physician and politician, 29th President of Chile (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"<PERSON>\"><PERSON></a>, Chilean physician and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Allende\" title=\"Salvador Allen<PERSON>\"><PERSON></a>, Chilean physician and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Salvador_Allende"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1909", "text": "Colonel <PERSON>, Dutch-American talent manager (d. 1997)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>\" title=\"Colonel <PERSON>\">Colonel <PERSON></a>, Dutch-American talent manager (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>\" title=\"Colonel <PERSON>\">Colonel <PERSON></a>, Dutch-American talent manager (d. 1997)", "links": [{"title": "Colonel <PERSON>", "link": "https://wikipedia.org/wiki/Colonel_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, German-American animator, director, and producer (d. 1985)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American animator, director, and producer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American animator, director, and producer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON> <PERSON>, American golfer and basketball player (d. 1956)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, American golfer and basketball player (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and basketball player (d. 1956)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Polish pilot and engineer (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Bronis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pilot and engineer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bronis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish pilot and engineer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bronis%C5%82aw_%C5%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, French poet, author, and politician (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet, author, and politician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet, author, and politician (d. 2008)", "links": [{"title": "Aimé <PERSON>", "link": "https://wikipedia.org/wiki/Aim%C3%A9_C%C3%A9saire"}]}, {"year": "1913", "text": "<PERSON>, English computer scientist and physicist (d. 2010)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and physicist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and physicist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English author and poet (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Bangladeshi Islamic scholar and teacher (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>_<PERSON>\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Bangladeshi Islamic scholar and teacher (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>_<PERSON>\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Bangladeshi Islamic scholar and teacher (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "Princess <PERSON> of Greece and Denmark, European royalty (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a>, European royalty (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a>, European royalty (d. 2001)", "links": [{"title": "Princess <PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1915", "text": "<PERSON>, American gangster (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English professional footballer (d. 2019)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English professional footballer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American author and poet (d. 2013)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_Zolotow"}]}, {"year": "1916", "text": "<PERSON>, American psychotherapist and author (d. 1988)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Virginia_Satir\" title=\"Virginia Satir\"><PERSON></a>, American psychotherapist and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Satir\" title=\"Virginia Satir\"><PERSON></a>, American psychotherapist and author (d. 1988)", "links": [{"title": "Virginia Satir", "link": "https://wikipedia.org/wiki/Virginia_Satir"}]}, {"year": "1916", "text": "<PERSON>, Italian actor and singer (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Albanian albanologist (d. 2019)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian albanologist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Idr<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian albanologist (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idriz_<PERSON><PERSON>i"}]}, {"year": "1918", "text": "<PERSON>, Polish-born Austrian Jewish musician (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born Austrian Jewish musician (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born Austrian Jewish musician (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American combat fighter pilot (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Raleigh_Rhodes\" title=\"Raleigh Rhodes\"><PERSON></a>, American combat fighter pilot (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Raleigh_Rhodes\" title=\"Raleigh Rhodes\"><PERSON></a>, American combat fighter pilot (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Raleigh_Rhodes"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON>, American entrepreneur and philanthropist (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American entrepreneur and philanthropist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American entrepreneur and philanthropist (d. 2006)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American political scientist and academic (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American pitcher (d. 1983)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pitcher (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pitcher (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American historian (d. 2018)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English art director (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English art director (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English art director (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Canadian-American baseball player, manager, and sportscaster (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American baseball player, manager, and sportscaster (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American baseball player, manager, and sportscaster (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, French-British secret agent (d. 1945)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-British secret agent (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-British secret agent (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American computer scientist (d. 2018)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American computer scientist (d. 2018)", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "1922", "text": "<PERSON>, American author (d. 1989)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, English artist, cartoonist, designer, and restaurateur (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English artist, cartoonist, designer, and restaurateur (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English artist, cartoonist, designer, and restaurateur (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, German conductor (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German conductor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American military historian and author (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military historian and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American military historian and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Greek-French philosopher and author (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and author (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, CIA officer (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, CIA officer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, CIA officer (d. 2017)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON>, Soviet soldier, pilot and cosmonaut (d. 1970)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet soldier, pilot and cosmonaut (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet soldier, pilot and cosmonaut (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German chess player (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chess player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chess player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French resistant and businessman (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French resistant and businessman (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French resistant and businessman (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American fiddler (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, American fiddler (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)\" title=\"<PERSON> (fiddler)\"><PERSON></a>, American fiddler (d. 2011)", "links": [{"title": "<PERSON> (fiddler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fiddler)"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Indian poet (d. 2020)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hat<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhatnagar\"><PERSON><PERSON><PERSON></a>, Indian poet (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ra Bhatnagar\"><PERSON><PERSON><PERSON></a>, Indian poet (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON> Bhatnagar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hat<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Chilean surgeon", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean surgeon", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean surgeon", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_M%C3%B6<PERSON><PERSON><PERSON>_<PERSON>os"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Romanian politician", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Canadian author and poet (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American composer and academic (d. 1996)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese inventor", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese inventor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese inventor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American politician; 5th Governor of Alaska (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician; 5th Governor of Alaska (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician; 5th Governor of Alaska (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian businessman and philanthropist (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Australian soprano and actress (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON><PERSON>\" title=\"June <PERSON><PERSON><PERSON>\">June <PERSON></a>, Australian soprano and actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_B<PERSON><PERSON>\" title=\"June <PERSON><PERSON><PERSON>\">June <PERSON><PERSON></a>, Australian soprano and actress (d. 2005)", "links": [{"title": "June <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/June_Bronhill"}]}, {"year": "1929", "text": "<PERSON>, Latvian-Canadian photographer and author (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Canadian photographer and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Canadian photographer and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American illustrator and graphic designer (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and graphic designer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and graphic designer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American wrestler and trainer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, East German secret police (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, East German secret police (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, East German secret police (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English philosopher and author (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Bahamian politician; Governor-General of the Bahamas", "html": "1932 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian politician; Governor-General of the Bahamas", "no_year_html": "<PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian politician; Governor-General of the Bahamas", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American venture capitalist (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American venture capitalist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American venture capitalist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian conductor (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian conductor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American baseball player (d. 1981)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 1981)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1933", "text": "<PERSON>, English politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American pianist and composer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Goto\" title=\"Toru Goto\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Got<PERSON>\" title=\"Toru Goto\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1935", "text": "<PERSON>, Italian race car driver", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Italian basketball player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Nigerian general (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American basketball player (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Baron <PERSON> of Rogart, Scottish politician (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Rogart\" title=\"<PERSON>, Baron <PERSON> of Rogart\"><PERSON>, Baron <PERSON> of Rogart</a>, Scottish politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Rogart\" title=\"<PERSON>, Baron <PERSON> of Rogart\"><PERSON>, Baron <PERSON> of Rogart</a>, Scottish politician (d. 2020)", "links": [{"title": "<PERSON>, Baron <PERSON> of Rogart", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Rogart"}]}, {"year": "1936", "text": "<PERSON>, American short story writer (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Canadian cardinal (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian cardinal (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American author and poet (d. 2017)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1937", "text": "<PERSON>, American bassist and composer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American sociologist and politician, 7th Governor of Hawaii", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">Governor of Hawaii</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Hawaii\" title=\"Governor of Hawaii\">Governor of Hawaii</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Hawaii", "link": "https://wikipedia.org/wiki/Governor_of_Hawaii"}]}, {"year": "1938", "text": "<PERSON>, American pop-soul singer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American pop-soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American pop-soul singer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1938", "text": "<PERSON>, American climatologist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Gerald_<PERSON>\" title=\"Gerald <PERSON>\"><PERSON></a>, American climatologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gerald_<PERSON>\" title=\"Gerald North\"><PERSON></a>, American climatologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gerald_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American soldier, lawyer, and politician, 64th Governor of Virginia", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 64th <a href=\"https://wikipedia.org/wiki/Governor_of_Virginia\" title=\"Governor of Virginia\">Governor of Virginia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Virginia", "link": "https://wikipedia.org/wiki/Governor_of_Virginia"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Malaysian politician (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian author and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON> <PERSON><PERSON>, American wrestler and manager", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Brazilian singer-songwriter, guitarist, and politician, Brazilian Minister of Culture", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter, guitarist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Brazil)\" title=\"Minister of Culture (Brazil)\">Brazilian Minister of Culture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter, guitarist, and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Culture_(Brazil)\" title=\"Minister of Culture (Brazil)\">Brazilian Minister of Culture</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Culture (Brazil)", "link": "https://wikipedia.org/wiki/Minister_of_Culture_(Brazil)"}]}, {"year": "1943", "text": "<PERSON>, English singer, pianist, and keyboard player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Georgie_Fame\" title=\"Georgie Fame\"><PERSON></a>, English singer, pianist, and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georgie_Fame\" title=\"Georgie Fame\"><PERSON></a>, English singer, pianist, and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georgie_Fame"}]}, {"year": "1943", "text": "<PERSON>, American author and educator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Russian politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Zyuganov\"><PERSON><PERSON><PERSON></a>, Russian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1945", "text": "<PERSON><PERSON> (Dwight <PERSON>), American criminal, black supremacist, pedophile, convicted child molester, and musician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_al-Ma<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> al-Mahdi\"><PERSON><PERSON><PERSON></a> (<PERSON>), American criminal, black supremacist, pedophile, convicted child molester, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_al-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> al<PERSON>Ha<PERSON> al-Mahdi\"><PERSON><PERSON></a> (<PERSON>), American criminal, black supremacist, pedophile, convicted child molester, and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American neuroscientist and pharmacologist (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and pharmacologist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and pharmacologist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American dermatologist and author (d. 2015)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dermatologist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dermatologist and author (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American biologist and geneticist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and geneticist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and geneticist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian cricketer and manager (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Scottish footballer and manager (d. 2023)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Jamaican-English civil rights activist (d. 1979)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English civil rights activist (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Morris\" title=\"<PERSON> Morris\"><PERSON></a>, Jamaican-English civil rights activist (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Morris"}]}, {"year": "1954", "text": "<PERSON>, Spanish footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Clash)\" class=\"mw-redirect\" title=\"<PERSON> (The Clash)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Clash)\" class=\"mw-redirect\" title=\"<PERSON> (The Clash)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (The Clash)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(The_Clash)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Wata<PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Watanabe\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>be"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, interim president of the Central African Republic", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, interim president of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, interim president of the <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_<PERSON>ba-Panza"}, {"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}]}, {"year": "1956", "text": "<PERSON>, English colonel and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English actor and screenwriter (d. 2007)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Canadian surgeon and politician, 31st Premier of Quebec", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian surgeon and politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and musician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Irish politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American cyclist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, American singer-songwriter and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON>n"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Russian-Swiss businessman and philanthropist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American economist and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/The_Sundays\" title=\"The Sundays\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Sundays\" title=\"The Sundays\"><PERSON></a>, English singer-songwriter", "links": [{"title": "The Sundays", "link": "https://wikipedia.org/wiki/The_Sundays"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Tommi_M%C3%A4kinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tommi_M%C3%A4kinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tommi_M%C3%A4kinen"}]}, {"year": "1966", "text": "<PERSON><PERSON>, French actor, director, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, American drummer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_Reil"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Ukrainian high jumper", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian high jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French director and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Icelandic lecturer and politician, 6th President of Iceland", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Gu%C3%B0ni_Th._J%C3%B3hannesson\" title=\"Guðni Th. Jóhannesson\"><PERSON>u<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Icelandic lecturer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gu%C3%B0ni_Th._J%C3%B3hannesson\" title=\"Guðni Th. Jóhannesson\"><PERSON>uð<PERSON> <PERSON>h<PERSON><PERSON></a>, Icelandic lecturer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gu%C3%B0ni_Th._J%C3%B3hannesson"}, {"title": "President of Iceland", "link": "https://wikipedia.org/wiki/President_of_Iceland"}]}, {"year": "1968", "text": "<PERSON>, Italian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English bass player and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian swimmer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Norwegian sprinter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1970", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Kenyan runner", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American record producer, co-founded Murder Inc Records (d. 2025)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Ir<PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Murder_Inc_Records\" class=\"mw-redirect\" title=\"Murder Inc Records\">Murder Inc Records</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\"><PERSON><PERSON><PERSON></a>, American record producer, co-founded <a href=\"https://wikipedia.org/wiki/Murder_Inc_Records\" class=\"mw-redirect\" title=\"Murder Inc Records\">Murder Inc Records</a> (d. 2025)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}, {"title": "Murder Inc Records", "link": "https://wikipedia.org/wiki/Murder_Inc_Records"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1970", "text": "<PERSON>, American actor and playwright", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Zimbabwean footballer (d. 2012)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ovu"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chris_O%27Donnell"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian motorcycle racer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Max_Biaggi\" title=\"Max Biaggi\"><PERSON></a>, Italian motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_Biaggi\" title=\"Max Biaggi\"><PERSON></a>, Italian motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Biaggi"}]}, {"year": "1972", "text": "<PERSON>, Australian long jumper and police officer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jai_<PERSON>\" title=\"Jai Tau<PERSON>\"><PERSON></a>, Australian long jumper and police officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jai Taurima\"><PERSON></a>, Australian long jumper and police officer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jai_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey)"}]}, {"year": "1975", "text": "<PERSON>, English footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Zimbabwean cricketer and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zimbabwean cricketer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American football player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chad_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American political commentator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lewis"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%8D_<PERSON><PERSON>da"}]}, {"year": "1979", "text": "<PERSON>, Argentinian basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Togolese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ham%C3%ADlton_H%C3%AAnio_Ferreira_Calheiros\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hênio Ferreira Calheiros\"><PERSON><PERSON><PERSON> Calheiro<PERSON></a>, Togolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ham%C3%ADlton_H%C3%AAnio_Ferreira_Calheiros\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Hênio Ferreira Calheiros\"><PERSON><PERSON><PERSON> Calheiro<PERSON></a>, Togolese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ham%C3%ADlton_H%C3%AAnio_Ferreira_Calheiros"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1980)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1980)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1980)\" class=\"mw-redirect\" title=\"<PERSON> (footballer born 1980)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer_born_1980)"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, drummer, and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, drummer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, drummer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian sprinter and hurdler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian sprinter and hurdler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natal<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Kond%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nd%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>ko_Kond%C5%8D"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Slovak tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8Dov%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zuzana_Ku%C4%8Dov%C3%A1"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Vin%C3%<PERSON><PERSON>_<PERSON>_Almeida\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vin%C3%<PERSON><PERSON>_<PERSON>_Almeida\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vin%C3%<PERSON><PERSON>_<PERSON>_Almeida"}]}, {"year": "1983", "text": "<PERSON>, South African-English cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ki\" title=\"Toyono<PERSON> Daiki\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Daiki\" title=\"Toyono<PERSON> Daiki\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON> Daiki", "link": "https://wikipedia.org/wiki/Toyonoshima_Daiki"}]}, {"year": "1983", "text": "<PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, Puerto Rican-American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Puerto Rican-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Puerto Rican-American basketball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Gambian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Yankuba_Ceesay\" title=\"Yankuba Ceesay\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yanku<PERSON>_Ceesay\" title=\"Yankuba Ceesay\"><PERSON><PERSON><PERSON></a>, Gambian footballer", "links": [{"title": "Yankuba <PERSON>ay", "link": "https://wikipedia.org/wiki/Yan<PERSON>ba_Ceesay"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dukes\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dukes\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, French singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Indila\" title=\"Indila\"><PERSON><PERSON><PERSON></a>, French singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indila\" title=\"Indila\"><PERSON><PERSON><PERSON></a>, French singer", "links": [{"title": "Indila", "link": "https://wikipedia.org/wiki/Indila"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/P<PERSON>cah_Jeptoo\" title=\"Priscah Jeptoo\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON><PERSON>_Jeptoo\" title=\"P<PERSON>cah Jeptoo\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Priscah_Jeptoo"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Aubrey_Plaza\" title=\"Aubrey Plaza\">Aubrey Plaza</a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aubrey_Plaza\" title=\"Aubrey Plaza\">Aubrey Plaza</a>, American actress", "links": [{"title": "Aubrey Plaza", "link": "https://wikipedia.org/wiki/Aubrey_Plaza"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Bulgarian singer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>slava"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian figure skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/J%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C5%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C5%<PERSON><PERSON><PERSON>_<PERSON>ha"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Tibetan spiritual leader, 17th Karmapa Lama", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ogyen_Tri<PERSON>ley_Do<PERSON>je\" title=\"Ogyen Tri<PERSON>ley Do<PERSON>je\"><PERSON><PERSON><PERSON></a>, Tibetan spiritual leader, 17th <a href=\"https://wikipedia.org/wiki/Karmapa\" title=\"Karmapa\"><PERSON><PERSON><PERSON></a> Lama", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gy<PERSON>_<PERSON>_<PERSON>\" title=\"Ogyen Tri<PERSON>ley Do<PERSON>je\"><PERSON><PERSON><PERSON></a>, Tibetan spiritual leader, 17th <a href=\"https://wikipedia.org/wiki/Karmapa\" title=\"Karmapa\"><PERSON><PERSON><PERSON></a> Lama", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ka<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karmapa"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Colombian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian race car driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian-American actor, comedian, director, producer, writer and social media personality", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, comedian, director, producer, writer and social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, Canadian-American actor, comedian, director, producer, writer and social media personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Oljira\" title=\"<PERSON><PERSON><PERSON> Oljira\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Oljira\" title=\"<PERSON><PERSON><PERSON> Oljira\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>jira"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> Shumpert\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"I<PERSON> Shumpert\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1990", "text": "<PERSON>, Estonian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ema<PERSON>\" title=\"<PERSON><PERSON><PERSON> Chemali\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ema<PERSON>\" title=\"<PERSON><PERSON><PERSON> Chemali\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rules footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rules footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Costa Rican footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, French basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American actress and singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American singer-songwriter, dancer, and actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ariana Grande\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ariana Grande\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, English javelin thrower", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English javelin thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, South Korean singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-rin\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-rin\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-rin\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-rin\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-rin"}]}, {"year": "1997", "text": "<PERSON>, Australian actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English cricketer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_cricketer)\" title=\"<PERSON> (English cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_cricketer)\" title=\"<PERSON> (English cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (English cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_cricketer)"}]}, {"year": "2000", "text": "<PERSON>, American tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "2002", "text": "<PERSON>, American race car driver", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American basketball player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2004)\" title=\"<PERSON><PERSON> (basketball, born 2004)\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2004)\" title=\"<PERSON><PERSON> (basketball, born 2004)\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> (basketball, born 2004)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2004)"}]}, {"year": "2005", "text": "Princess <PERSON> of the Netherlands", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_Netherlands\" title=\"Princess <PERSON> of the Netherlands\">Princess <PERSON> of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_the_Netherlands\" title=\"Princess <PERSON> of the Netherlands\">Princess <PERSON> of the Netherlands</a>", "links": [{"title": "Princess <PERSON> of the Netherlands", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_the_Netherlands"}]}], "Deaths": [{"year": "116 BC", "text": "<PERSON>, king of Egypt", "html": "116 BC - 116 BC - <a href=\"https://wikipedia.org/wiki/Ptolemy_VIII_Physcon\" title=\"Ptolemy VIII Physcon\"><PERSON> VIII</a>, king of Egypt", "no_year_html": "116 BC - <a href=\"https://wikipedia.org/wiki/Ptolemy_VIII_Physcon\" title=\"Ptolemy VIII Physcon\"><PERSON> VIII</a>, king of Egypt", "links": [{"title": "Ptolemy <PERSON> Physcon", "link": "https://wikipedia.org/wiki/Ptolemy_VIII_Physcon"}]}, {"year": "363", "text": "<PERSON> the Apostate, Roman emperor (b. 332)", "html": "363 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON> the Apostate</a>, Roman emperor (b. 332)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON> the Apostate</a>, Roman emperor (b. 332)", "links": [{"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}]}, {"year": "405", "text": "<PERSON><PERSON><PERSON>, bishop of Trent (b. 353)", "html": "405 - <a href=\"https://wikipedia.org/wiki/Vigilius_of_Trent\" title=\"Vigilius of Trent\">Vigilius</a>, bishop of Trent (b. 353)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vigilius_of_Trent\" title=\"Vigilius of Trent\">Vigilius</a>, bishop of Trent (b. 353)", "links": [{"title": "V<PERSON><PERSON> of Trent", "link": "https://wikipedia.org/wiki/Vigilius_of_Trent"}]}, {"year": "822", "text": "<PERSON><PERSON><PERSON>, Japanese Buddhist monk (b. 767)", "html": "822 - <a href=\"https://wikipedia.org/wiki/Saich%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk (b. 767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saich%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist monk (b. 767)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saich%C5%8D"}]}, {"year": "969", "text": "<PERSON>, Egyptian martyr (b. 940)", "html": "969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian martyr (b. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian martyr (b. 940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "985", "text": "<PERSON><PERSON> III, king of León", "html": "985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_of_Le%C3%B3n\" title=\"<PERSON><PERSON> III of León\"><PERSON><PERSON> III</a>, king of León", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_of_Le%C3%B3n\" title=\"<PERSON><PERSON> III of León\"><PERSON><PERSON> III</a>, king of León", "links": [{"title": "<PERSON><PERSON> III of León", "link": "https://wikipedia.org/wiki/Ramiro_III_of_Le%C3%B3n"}]}, {"year": "1090", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bishop of Prague", "html": "1090 - <a href=\"https://wikipedia.org/wiki/<PERSON>arom%C3%<PERSON>r,_Bishop_of_Prague\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Bishop of Prague\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, bishop of Prague", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>arom%C3%ADr,_Bishop_of_Prague\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>, Bishop of Prague\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, bishop of Prague", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Bishop of Prague", "link": "https://wikipedia.org/wiki/Jarom%C3%ADr,_Bishop_of_Prague"}]}, {"year": "1095", "text": "<PERSON>, bishop of Hereford", "html": "1095 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lotharingian\" title=\"<PERSON> the Lotharingian\"><PERSON></a>, bishop of Hereford", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Lotharingian\" title=\"<PERSON> the Lotharingian\"><PERSON></a>, bishop of Hereford", "links": [{"title": "<PERSON> the Lotharingian", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lotharingian"}]}, {"year": "1265", "text": "<PERSON> of Bohemia, duchess of Silesia (b. 1203 or 1204)", "html": "1265 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia,_Duchess_of_Silesia\" title=\"<PERSON> of Bohemia, Duchess of Silesia\"><PERSON> of Bohemia</a>, duchess of Silesia (b. 1203 or 1204)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia,_Duchess_of_Silesia\" title=\"<PERSON> of Bohemia, Duchess of Silesia\"><PERSON> of Bohemia</a>, duchess of Silesia (b. 1203 or 1204)", "links": [{"title": "<PERSON> of Bohemia, Duchess of Silesia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bohemia,_Duchess_of_Silesia"}]}, {"year": "1274", "text": "<PERSON><PERSON> <PERSON>, Persian scientist and writer (b. 1201)", "html": "1274 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Persian scientist and writer (b. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Persian scientist and writer (b. 1201)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1487", "text": "<PERSON>, Byzantine philosopher and scholar (b. 1415)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine philosopher and scholar (b. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine philosopher and scholar (b. 1415)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1541", "text": "<PERSON>, Spanish explorer and politician, Governor of New Castile (b. c. 1471)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/Francisco<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Viceroys_of_Peru\" class=\"mw-redirect\" title=\"List of Viceroys of Peru\">Governor of New Castile</a> (b. c. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish explorer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Viceroys_of_Peru\" class=\"mw-redirect\" title=\"List of Viceroys of Peru\">Governor of New Castile</a> (b. c. 1471)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>"}, {"title": "List of Viceroys of Peru", "link": "https://wikipedia.org/wiki/List_of_Viceroys_of_Peru"}]}, {"year": "1574", "text": "<PERSON>, comte <PERSON>, captain of the Scottish Guard of Henry II of France (b. 1530)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte <PERSON></a>, captain of the Scottish Guard of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte de <PERSON>\"><PERSON>, comte de <PERSON></a>, captain of the Scottish Guard of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_France\" title=\"<PERSON> II of France\"><PERSON> of France</a> (b. 1530)", "links": [{"title": "<PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_comte_<PERSON>_<PERSON>"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1677", "text": "<PERSON>, Italian architect, painter and engraver (b. 1596)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Italian architect, painter and engraver (b. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Italian architect, painter and engraver (b. 1596)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)"}]}, {"year": "1688", "text": "<PERSON>, English philosopher and academic (b. 1617)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1752", "text": "<PERSON><PERSON><PERSON>, Spanish cardinal (b. 1664)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cardinal (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cardinal (b. 1664)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, Austrian field marshal (b. 1705)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian field marshal (b. 1705)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1784", "text": "<PERSON>, American lawyer and politician, 4th Governor of Delaware (b. 1728)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Delaware\" class=\"mw-redirect\" title=\"Governor of Delaware\">Governor of Delaware</a> (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Delaware", "link": "https://wikipedia.org/wiki/Governor_of_Delaware"}]}, {"year": "1793", "text": "<PERSON>, English ornithologist and ecologist (b. 1720)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ecologist (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ecologist (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON>, German linguist and translator (b. 1747)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4hrig\" title=\"<PERSON>\"><PERSON></a>, German linguist and translator (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4hrig\" title=\"<PERSON>\"><PERSON></a>, German linguist and translator (b. 1747)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johannes_J%C3%A4hrig"}]}, {"year": "1798", "text": "<PERSON>, Irish revolutionary (b. 1776)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_Irishmen)\" title=\"<PERSON> (United Irishmen)\"><PERSON></a>, Irish revolutionary (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_Irishmen)\" title=\"<PERSON> (United Irishmen)\"><PERSON></a>, Irish revolutionary (b. 1776)", "links": [{"title": "<PERSON> (United Irishmen)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_Irishmen)"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish poet and politician (b. 1748)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and politician (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and politician (b. 1748)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1810", "text": "<PERSON><PERSON><PERSON>, French inventor, co-invented the hot air balloon (b. 1740)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\"><PERSON><PERSON><PERSON></a>, French inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montgolfier_brothers\" title=\"Montgolfier brothers\"><PERSON><PERSON><PERSON></a>, French inventor, co-invented the <a href=\"https://wikipedia.org/wiki/Hot_air_balloon\" title=\"Hot air balloon\">hot air balloon</a> (b. 1740)", "links": [{"title": "Montgolfier brothers", "link": "https://wikipedia.org/wiki/Montgol<PERSON>r_brothers"}, {"title": "Hot air balloon", "link": "https://wikipedia.org/wiki/Hot_air_balloon"}]}, {"year": "1830", "text": "<PERSON> of the United Kingdom (b. 1762)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> IV of the United Kingdom\"><PERSON> of the United Kingdom</a> (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"George IV of the United Kingdom\"><PERSON> of the United Kingdom</a> (b. 1762)", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}]}, {"year": "1836", "text": "<PERSON>, French soldier and composer (b. 1760)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and composer (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and composer (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, German philosopher and author (b. 1806)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, French lawyer and politician (b. 1809)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%A8s"}]}, {"year": "1878", "text": "<PERSON> of Orléans (b. 1860)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Mercedes_of_Orl%C3%A9ans\" title=\"Mercedes of Orléans\">Mercedes of Orléans</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mercedes_of_Orl%C3%A9ans\" title=\"Mercedes of Orléans\">Mercedes of Orléans</a> (b. 1860)", "links": [{"title": "Mercedes of Orléans", "link": "https://wikipedia.org/wiki/Mercedes_of_Orl%C3%A9ans"}]}, {"year": "1879", "text": "<PERSON>, American general (b. 1821)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(general)\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1821)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(general)"}]}, {"year": "1883", "text": "<PERSON>, Irish-English astronomer, geophysicist, and ornithologist (b. 1788)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English astronomer, geophysicist, and ornithologist (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English astronomer, geophysicist, and ornithologist (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Austrian poet and author (b. 1843)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Prince of Monaco (b. 1848)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (b. 1848)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1927", "text": "<PERSON>, French painter (b. 1841)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American astronomer and academic (b. 1900)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Adelaide_Ames\" title=\"Adelaide Ames\"><PERSON></a>, American astronomer and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adelaide_Ames\" title=\"Adelaide Ames\"><PERSON></a>, American astronomer and academic (b. 1900)", "links": [{"title": "Adelaide Ames", "link": "https://wikipedia.org/wiki/Adelaide_Ames"}]}, {"year": "1938", "text": "<PERSON>, American poet, lawyer and politician (b. 1871)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, lawyer and politician (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, lawyer and politician (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American golfer (b. 1859)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English novelist, poet, and critic (b. 1873)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ford_Madox_Ford\" title=\"Ford Madox Ford\">Ford Madox Ford</a>, English novelist, poet, and critic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Madox_Ford\" title=\"Ford Madox Ford\">Ford Madox Ford</a>, English novelist, poet, and critic (b. 1873)", "links": [{"title": "Ford Madox Ford", "link": "https://wikipedia.org/wiki/Ford_Madox_Ford"}]}, {"year": "1943", "text": "<PERSON>, Austrian biologist and physician, Nobel Prize laureate (b. 1868)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1945", "text": "<PERSON>, Czech lawyer and politician, 3rd President of Czechoslovakia (b. 1872)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1cha\" title=\"<PERSON>\"><PERSON></a>, Czech lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1cha\" title=\"<PERSON>\"><PERSON></a>, Czech lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Czechoslovakia\" class=\"mw-redirect\" title=\"President of Czechoslovakia\">President of Czechoslovakia</a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_H%C3%A1cha"}, {"title": "President of Czechoslovakia", "link": "https://wikipedia.org/wiki/President_of_Czechoslovakia"}]}, {"year": "1946", "text": "<PERSON>, German SS officer (b. 1895)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Max_K%C3%B6gel\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Max_K%C3%B6gel\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_K%C3%B6gel"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Japanese politician, Japanese Minister of Foreign Affairs (b. 1880)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Y%C5%8<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)\" title=\"Ministry of Foreign Affairs (Japan)\">Japanese Minister of Foreign Affairs</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y%C5%8<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)\" title=\"Ministry of Foreign Affairs (Japan)\">Japanese Minister of Foreign Affairs</a> (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y%C5%8<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Japan)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Japan)"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian lawyer and politician, 11th Prime Minister of Canada (b. 1870)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1870)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1949", "text": "<PERSON>, South Korean educator and politician, 13th President of the Provisional Government of the Republic of Korea (b. 1876)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea#List_of_presidents\" title=\"Provisional Government of the Republic of Korea\">President of the Provisional Government of the Republic of Korea</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean educator and politician, 13th <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea#List_of_presidents\" title=\"Provisional Government of the Republic of Korea\">President of the Provisional Government of the Republic of Korea</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional Government of the Republic of Korea", "link": "https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea#List_of_presidents"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, German engineer (b. 1895)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German engineer (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American trumpet player and composer (b. 1930)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American pianist (b. 1931)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Polish-German physician and author (b. 1878)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_D%C3%B6blin\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician and author (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfred_D%C3%B6blin\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician and author (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_D%C3%B6blin"}]}, {"year": "1957", "text": "<PERSON>, English novelist and poet (b. 1909)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian runner and hurdler (b. 1873)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and hurdler (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian runner and hurdler (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Croatian physician and scholar (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%A0tampar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian physician and scholar (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/And<PERSON><PERSON>_%C5%A0tampar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian physician and scholar (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andri<PERSON>_%C5%A0tampar"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American-Canadian businessman (b. 1889)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/L%C3%A9o_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian businessman (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Canadian businessman (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_<PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, French actress and singer (b. 1942)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_Dorl%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_Dorl%C3%A9ac\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_Dorl%C3%A9ac"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Spanish priest and saint (b. 1902)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Josemar%C3%ADa_Escriv%C3%A1\" title=\"<PERSON><PERSON><PERSON>riv<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish priest and saint (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Josemar%C3%ADa_Escriv%C3%A1\" title=\"<PERSON><PERSON><PERSON> Escrivá\"><PERSON><PERSON><PERSON></a>, Spanish priest and saint (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josemar%C3%ADa_Escriv%C3%A1"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Ghanaian soldier and politician, 3rd Head of State of Ghana (b. 1936)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>ri<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana\" title=\"List of heads of state of Ghana\">Head of State of Ghana</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana\" title=\"List of heads of state of Ghana\">Head of State of Ghana</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akwas<PERSON>_<PERSON><PERSON>rifa"}, {"title": "List of heads of state of Ghana", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Ghana"}]}, {"year": "1989", "text": "<PERSON>, Canadian lawyer and politician, 27th Canadian Minister of Public Works (b. 1895)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)\" title=\"Minister of Public Works (Canada)\">Canadian Minister of Public Works</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Public Works (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Public_Works_(Canada)"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Finnish author (b. 1909)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish author (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American wrestler (b. 1921)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (b. 1921)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1993", "text": "<PERSON>, American baseball player and coach (b. 1921)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American political scientist and academic (b. 1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Bangladeshi author and activist (b. 1929)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi author and activist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi author and activist (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Irish journalist (b. 1958)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish linguist and academic (b. 1932)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu\" title=\"Necmettin <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish linguist and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu\" title=\"Necmettin <PERSON>lu\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish linguist and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Necmettin_Hac%C4%B1emino%C4%9Flu"}]}, {"year": "1997", "text": "<PERSON>, American football player and coach (b. 1913)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Turkish businessman and philanthropist (b. 1935)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Hac%C4%B1_Sabanc%C4%B1\" title=\"Hacı Sabancı\"><PERSON><PERSON><PERSON></a>, Turkish businessman and philanthropist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hac%C4%B1_Sabanc%C4%B1\" title=\"Hacı Sabancı\"><PERSON><PERSON><PERSON></a>, Turkish businessman and philanthropist (b. 1935)", "links": [{"title": "Hacı Sabancı", "link": "https://wikipedia.org/wiki/Hac%C4%B1_Sabanc%C4%B1"}]}, {"year": "2001", "text": "<PERSON>, French-Italian soprano (b. 1900)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian soprano (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian soprano (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English-Canadian 11th General of The Salvation Army (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(General_of_The_Salvation_Army)\" title=\"<PERSON> (General of The Salvation Army)\"><PERSON></a>, English-Canadian 11th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(General_of_The_Salvation_Army)\" title=\"<PERSON> (General of The Salvation Army)\"><PERSON></a>, English-Canadian 11th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1913)", "links": [{"title": "<PERSON> (General of The Salvation Army)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(General_of_The_Salvation_Army)"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Cameroon footballer (b. 1975)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>o%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Cameroon footballer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>o%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Cameroon footballer (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>o%C3%A9"}]}, {"year": "2003", "text": "<PERSON>, English soldier and businessman (b. 1915)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and businessman (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and businessman (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American general, lawyer, and politician, 103rd Governor of South Carolina (b. 1902)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thur<PERSON>\" title=\"<PERSON>rom Thurmond\"><PERSON><PERSON></a>, American general, lawyer, and politician, 103rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Thur<PERSON>\" title=\"<PERSON><PERSON> Thurmond\"><PERSON><PERSON></a>, American general, lawyer, and politician, 103rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thurmond"}, {"title": "Governor of South Carolina", "link": "https://wikipedia.org/wiki/Governor_of_South_Carolina"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Estonian poet and translator (b. 1950)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rder\" title=\"<PERSON>tt Arder\"><PERSON><PERSON></a>, Estonian poet and translator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rder\" title=\"<PERSON>tt Arder\"><PERSON><PERSON></a>, Estonian poet and translator (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>r"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Indian film producer, founded Dharma Productions (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film producer, founded <a href=\"https://wikipedia.org/wiki/Dharma_Productions\" title=\"Dharma Productions\">Dharma Productions</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian film producer, founded <a href=\"https://wikipedia.org/wiki/Dharma_Productions\" title=\"Dharma Productions\">Dharma Productions</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>"}, {"title": "Dharma Productions", "link": "https://wikipedia.org/wiki/Dharma_Productions"}]}, {"year": "2004", "text": "<PERSON>, Israeli singer-songwriter (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer-songwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli singer-songwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian basketball player (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nno_<PERSON>ets\" title=\"Tõnn<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nno_Le<PERSON>ets\" title=\"Tõnn<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian basketball player (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nno_Lepmets"}]}, {"year": "2005", "text": "<PERSON>, English journalist and game show host (b. 1943)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and game show host (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Dutch magician (b. 1953)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wonder_(magician)\" title=\"<PERSON> (magician)\"><PERSON></a>, Dutch magician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wonder_(magician)\" title=\"<PERSON> (magician)\"><PERSON></a>, Dutch magician (b. 1953)", "links": [{"title": "<PERSON> (magician)", "link": "https://wikipedia.org/wiki/<PERSON>_Wonder_(magician)"}]}, {"year": "2007", "text": "<PERSON>, Belgian-American fashion designer, founded <PERSON> (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Belgian-American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Belgian-American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1929)", "links": [{"title": "<PERSON> (fashion designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand rugby player (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian engineer and politician, 4th President of Lithuania (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian engineer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Lithuania", "link": "https://wikipedia.org/wiki/President_of_Lithuania"}]}, {"year": "2010", "text": "<PERSON>, Estonian physicist and academic (b. 1912)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American actress (b. 1923)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Dutch footballer and coach (b. 1948)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Swedish diplomat, Swedish Permanent Representative to the United Nations (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sverker_%C3%85str%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_Sweden_to_the_United_Nations\" title=\"Permanent Representative of Sweden to the United Nations\">Swedish Permanent Representative to the United Nations</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sverker_%C3%85str%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_Sweden_to_the_United_Nations\" title=\"Permanent Representative of Sweden to the United Nations\">Swedish Permanent Representative to the United Nations</a> (b. 1915)", "links": [{"title": "Sverker Åström", "link": "https://wikipedia.org/wiki/Sverker_%C3%85str%C3%B6m"}, {"title": "Permanent Representative of Sweden to the United Nations", "link": "https://wikipedia.org/wiki/Permanent_Representative_of_Sweden_to_the_United_Nations"}]}, {"year": "2012", "text": "<PERSON>, American basketball player (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director, producer, and screenwriter (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Filipino director, producer, and screenwriter (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, Filipino director, producer, and screenwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_O%27Hara"}]}, {"year": "2012", "text": "<PERSON>, American actress (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Finnish trumpet player and composer (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish trumpet player and composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish trumpet player and composer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Brazilian-American sergeant and judge (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American sergeant and judge (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-American sergeant and judge (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American politician (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1977)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)\" title=\"<PERSON> (baseball, born 1977)\"><PERSON></a>, American baseball player (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)\" title=\"<PERSON> (baseball, born 1977)\"><PERSON></a>, American baseball player (b. 1977)", "links": [{"title": "<PERSON> (baseball, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)"}]}, {"year": "2013", "text": "<PERSON>, Belgian-American businessman (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American businessman (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-American businessman (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American lawyer, politician, and diplomat, 12th White House Chief of Staff (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "2014", "text": "<PERSON>, American-Canadian football player (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American businessman, co-founded Southwest Airlines (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Roll<PERSON>_King\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Southwest_Airlines\" title=\"Southwest Airlines\">Southwest Airlines</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roll<PERSON>_King\" title=\"<PERSON><PERSON> King\"><PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Southwest_Airlines\" title=\"Southwest Airlines\">Southwest Airlines</a> (b. 1931)", "links": [{"title": "Rollin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Southwest Airlines", "link": "https://wikipedia.org/wiki/Southwest_Airlines"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian-American conductor (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American composer and author (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian journalist and politician, 32nd Prime Minister of Russia (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian journalist and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Russia\" title=\"Prime Minister of Russia\">Prime Minister of Russia</a> (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Russia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Russia"}]}, {"year": "2015", "text": "<PERSON>, American screenwriter and producer (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(TV_producer)\" title=\"<PERSON> (TV producer)\"><PERSON></a>, American screenwriter and producer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(TV_producer)\" title=\"<PERSON> (TV producer)\"><PERSON></a>, American screenwriter and producer (b. 1952)", "links": [{"title": "<PERSON> (TV producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(TV_producer)"}]}, {"year": "2019", "text": "<PERSON>, American reality Television star, <PERSON><PERSON><PERSON> (b. 1967)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)\" class=\"mw-redirect\" title=\"<PERSON> (bounty hunter)\"><PERSON></a>, American reality Television star, <PERSON><PERSON><PERSON> (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)\" class=\"mw-redirect\" title=\"<PERSON> (bounty hunter)\"><PERSON></a>, American reality Television star, <PERSON><PERSON><PERSON> (b. 1967)", "links": [{"title": "<PERSON> (bounty hunter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bounty_hunter)"}]}, {"year": "2020", "text": "<PERSON>, American graphic designer (b. 1929)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American politician (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American artist (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Japanese voice actor (b. 1967)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actor (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}