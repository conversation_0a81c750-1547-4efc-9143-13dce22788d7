{"date": "December 8", "url": "https://wikipedia.org/wiki/December_8", "data": {"Events": [{"year": "395", "text": "Later Yan is defeated by its former vassal Northern Wei at the Battle of Canhe Slope.", "html": "395 - <a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a> is defeated by its former vassal <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Canhe_Slope\" title=\"Battle of Canhe Slope\">Battle of Canhe Slope</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a> is defeated by its former vassal <a href=\"https://wikipedia.org/wiki/Northern_Wei\" title=\"Northern Wei\">Northern Wei</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Canhe_Slope\" title=\"Battle of Canhe Slope\">Battle of Canhe Slope</a>.", "links": [{"title": "Later Yan", "link": "https://wikipedia.org/wiki/Later_Yan"}, {"title": "Northern Wei", "link": "https://wikipedia.org/wiki/Northern_Wei"}, {"title": "Battle of Canhe Slope", "link": "https://wikipedia.org/wiki/Battle_of_Canhe_Slope"}]}, {"year": "757", "text": "The poet <PERSON> returns to Chang'an as a member of Emperor <PERSON><PERSON><PERSON>'s court, after having escaped the city during the An Lushan Rebellion.", "html": "757 - The poet <a href=\"https://wikipedia.org/wiki/<PERSON>_Fu\" title=\"Du Fu\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/<PERSON>%27an\" title=\"Chang'an\"><PERSON>'<PERSON></a> as a member of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\">Emperor <PERSON><PERSON><PERSON></a>'s court, after having escaped the city during the <a href=\"https://wikipedia.org/wiki/An_Lushan_Rebellion\" class=\"mw-redirect\" title=\"An Lushan Rebellion\">An Lushan Rebellion</a>.", "no_year_html": "The poet <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fu\"><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/<PERSON>%27an\" title=\"Chang'an\"><PERSON><PERSON><PERSON></a> as a member of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\">Emperor <PERSON><PERSON><PERSON></a>'s court, after having escaped the city during the <a href=\"https://wikipedia.org/wiki/An_Lushan_Rebellion\" class=\"mw-redirect\" title=\"An Lushan Rebellion\">An Lushan Rebellion</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27an"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang"}, {"title": "An Lushan Rebellion", "link": "https://wikipedia.org/wiki/An_Lushan_Rebellion"}]}, {"year": "877", "text": "<PERSON> (son of <PERSON> the <PERSON>) is crowned king of the West Frankish Kingdom at Compiègne.", "html": "877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer\" title=\"<PERSON> the Stammerer\"><PERSON> the St<PERSON>merer</a> (son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a>) is crowned king of the <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Frankish Kingdom</a> at <a href=\"https://wikipedia.org/wiki/Compi%C3%A8gne\" title=\"Compiègne\">Compiègne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_St<PERSON>mere<PERSON>\" title=\"<PERSON> the Stammerer\"><PERSON> the St<PERSON>merer</a> (son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a>) is crowned king of the <a href=\"https://wikipedia.org/wiki/West_Francia\" title=\"West Francia\">West Frankish Kingdom</a> at <a href=\"https://wikipedia.org/wiki/Compi%C3%A8gne\" title=\"Compiègne\">Compiègne</a>.", "links": [{"title": "<PERSON> Stammerer", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Stammerer"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "West Francia", "link": "https://wikipedia.org/wiki/West_Francia"}, {"title": "Compiègne", "link": "https://wikipedia.org/wiki/Compi%C3%A8gne"}]}, {"year": "1504", "text": "<PERSON> ibn <PERSON> writes his Oran fatwa, arguing for the relaxation of Islamic law requirements for the forcibly converted Muslims in Spain.", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27ah\" title=\"<PERSON>\"><PERSON> ibn <PERSON></a> writes his <a href=\"https://wikipedia.org/wiki/Oran_fatwa\" title=\"Oran fatwa\">Oran fatwa</a>, arguing for the relaxation of <a href=\"https://wikipedia.org/wiki/Fiqh\" title=\"Fiqh\">Islamic law</a> requirements for the <a href=\"https://wikipedia.org/wiki/Forced_conversion_of_Muslims_in_Spain\" class=\"mw-redirect\" title=\"Forced conversion of Muslims in Spain\">forcibly converted</a> Muslims in Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27ah\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> writes his <a href=\"https://wikipedia.org/wiki/Oran_fatwa\" title=\"Oran fatwa\">Oran fatwa</a>, arguing for the relaxation of <a href=\"https://wikipedia.org/wiki/Fiqh\" title=\"Fiqh\">Islamic law</a> requirements for the <a href=\"https://wikipedia.org/wiki/Forced_conversion_of_Muslims_in_Spain\" class=\"mw-redirect\" title=\"Forced conversion of Muslims in Spain\">forcibly converted</a> Muslims in Spain.", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>%27ah"}, {"title": "Oran fatwa", "link": "https://wikipedia.org/wiki/Oran_fatwa"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fiqh"}, {"title": "Forced conversion of Muslims in Spain", "link": "https://wikipedia.org/wiki/Forced_conversion_of_Muslims_in_Spain"}]}, {"year": "1660", "text": "A woman (either <PERSON> or <PERSON>) appears on an English public stage for the first time, in the role of <PERSON><PERSON><PERSON> in a production of <PERSON>'s play <PERSON><PERSON><PERSON>.", "html": "1660 - A woman (either <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> or <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>) appears on an English public stage for the first time, in the role of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in a production of Shakespeare's play <i><a href=\"https://wikipedia.org/wiki/Othello\" title=\"Othello\"><PERSON>the<PERSON></a></i>.", "no_year_html": "A woman (either <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> or <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>) appears on an English public stage for the first time, in the role of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> in a production of <PERSON>'s play <i><a href=\"https://wikipedia.org/wiki/Othello\" title=\"Othello\"><PERSON><PERSON><PERSON></a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Desdemona", "link": "https://wikipedia.org/wiki/Desdemona"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Othello"}]}, {"year": "1851", "text": "Conservative Santiago-based government troops defeat rebels at the Battle of Loncomilla, signaling the end of the 1851 Chilean Revolution.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Pelucones\" title=\"Pelucones\">Conservative</a> <a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>-based government troops defeat rebels at the <a href=\"https://wikipedia.org/wiki/Battle_of_Loncomilla\" title=\"Battle of Loncomilla\">Battle of Loncomilla</a>, signaling the end of the <a href=\"https://wikipedia.org/wiki/1851_Chilean_Revolution\" class=\"mw-redirect\" title=\"1851 Chilean Revolution\">1851 Chilean Revolution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pelucones\" title=\"Pelucones\">Conservative</a> <a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>-based government troops defeat rebels at the <a href=\"https://wikipedia.org/wiki/Battle_of_Loncomilla\" title=\"Battle of Loncomilla\">Battle of Loncomilla</a>, signaling the end of the <a href=\"https://wikipedia.org/wiki/1851_Chilean_Revolution\" class=\"mw-redirect\" title=\"1851 Chilean Revolution\">1851 Chilean Revolution</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pelucones"}, {"title": "Santiago", "link": "https://wikipedia.org/wiki/Santiago"}, {"title": "Battle of Loncomilla", "link": "https://wikipedia.org/wiki/Battle_of_Loncomilla"}, {"title": "1851 Chilean Revolution", "link": "https://wikipedia.org/wiki/1851_Chilean_Revolution"}]}, {"year": "1854", "text": "In his Apostolic constitution <PERSON><PERSON><PERSON><PERSON><PERSON>, Pope <PERSON> proclaims the dogmatic definition of Immaculate Conception, which holds that the Blessed Virgin Mary was conceived free of Original Sin.", "html": "1854 - In his <a href=\"https://wikipedia.org/wiki/Apostolic_constitution\" title=\"Apostolic constitution\">Apostolic constitution</a> <i><a href=\"https://wikipedia.org/wiki/Ineffabilis_Deus\" title=\"Ineffabilis Deus\">Ineffabil<PERSON></a></i>, <a href=\"https://wikipedia.org/wiki/Pope_Pius_IX\" title=\"Pope Pius IX\">Pope Pius <PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Dogmatic_definition\" class=\"mw-redirect\" title=\"Dogmatic definition\">dogmatic definition</a> of <a href=\"https://wikipedia.org/wiki/Immaculate_Conception\" title=\"Immaculate Conception\">Immaculate Conception</a>, which holds that the <a href=\"https://wikipedia.org/wiki/Blessed_Virgin_Mary\" class=\"mw-redirect\" title=\"Blessed Virgin Mary\">Blessed Virgin Mary</a> was conceived free of <a href=\"https://wikipedia.org/wiki/Original_Sin\" class=\"mw-redirect\" title=\"Original Sin\">Original Sin</a>.", "no_year_html": "In his <a href=\"https://wikipedia.org/wiki/Apostolic_constitution\" title=\"Apostolic constitution\">Apostolic constitution</a> <i><a href=\"https://wikipedia.org/wiki/Ineffabilis_Deus\" title=\"Ineffabilis Deus\">Ineffabil<PERSON></a></i>, <a href=\"https://wikipedia.org/wiki/Pope_Pius_IX\" title=\"Pope Pius IX\">Pope Pius <PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Dogmatic_definition\" class=\"mw-redirect\" title=\"Dogmatic definition\">dogmatic definition</a> of <a href=\"https://wikipedia.org/wiki/Immaculate_Conception\" title=\"Immaculate Conception\">Immaculate Conception</a>, which holds that the <a href=\"https://wikipedia.org/wiki/Blessed_Virgin_Mary\" class=\"mw-redirect\" title=\"Blessed Virgin Mary\">Blessed Virgin Mary</a> was conceived free of <a href=\"https://wikipedia.org/wiki/Original_Sin\" class=\"mw-redirect\" title=\"Original Sin\">Original Sin</a>.", "links": [{"title": "Apostolic constitution", "link": "https://wikipedia.org/wiki/Apostolic_constitution"}, {"title": "<PERSON>effa<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ineffabilis_Deus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dogmatic definition", "link": "https://wikipedia.org/wiki/Dogmatic_definition"}, {"title": "Immaculate Conception", "link": "https://wikipedia.org/wiki/Immaculate_Conception"}, {"title": "Blessed Virgin Mary", "link": "https://wikipedia.org/wiki/Blessed_Virgin_Mary"}, {"title": "Original Sin", "link": "https://wikipedia.org/wiki/Original_Sin"}]}, {"year": "1863", "text": "Between two and three thousand churchgoers die during the Church of the Company Fire. Deemed as probably the largest single building fire by number of victims in modern history, it began at the start of a Mass held at the Church of the Society of Jesus in Santiago, Chile, during the celebration of the recently proclaimed Feast of the Immaculate Conception.", "html": "1863 - Between two and three thousand churchgoers die during the <a href=\"https://wikipedia.org/wiki/Church_of_the_Company_Fire\" title=\"Church of the Company Fire\">Church of the Company Fire</a>. Deemed as probably the largest single building fire by number of victims in modern history, it began at the start of a <a href=\"https://wikipedia.org/wiki/Mass_(liturgy)\" title=\"Mass (liturgy)\">Mass</a> held at the Church of the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> in <a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>, <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, during the celebration of the recently proclaimed <a href=\"https://wikipedia.org/wiki/Feast_of_the_Immaculate_Conception\" title=\"Feast of the Immaculate Conception\">Feast of the Immaculate Conception</a>.", "no_year_html": "Between two and three thousand churchgoers die during the <a href=\"https://wikipedia.org/wiki/Church_of_the_Company_Fire\" title=\"Church of the Company Fire\">Church of the Company Fire</a>. Deemed as probably the largest single building fire by number of victims in modern history, it began at the start of a <a href=\"https://wikipedia.org/wiki/Mass_(liturgy)\" title=\"Mass (liturgy)\">Mass</a> held at the Church of the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> in <a href=\"https://wikipedia.org/wiki/Santiago\" title=\"Santiago\">Santiago</a>, <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, during the celebration of the recently proclaimed <a href=\"https://wikipedia.org/wiki/Feast_of_the_Immaculate_Conception\" title=\"Feast of the Immaculate Conception\">Feast of the Immaculate Conception</a>.", "links": [{"title": "Church of the Company Fire", "link": "https://wikipedia.org/wiki/Church_of_the_Company_Fire"}, {"title": "Mass (liturgy)", "link": "https://wikipedia.org/wiki/Mass_(liturgy)"}, {"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}, {"title": "Santiago", "link": "https://wikipedia.org/wiki/Santiago"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "Feast of the Immaculate Conception", "link": "https://wikipedia.org/wiki/Feast_of_the_Immaculate_Conception"}]}, {"year": "1864", "text": "<PERSON> <PERSON> promulgates the encyclical Quanta cura and its appendix, the Syllabus of Errors, outlining the authority of the Catholic Church and condemning various liberal ideas.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IX\" title=\"Pope Pius IX\">Pope <PERSON> IX</a> promulgates the encyclical <i><a href=\"https://wikipedia.org/wiki/Quanta_cura\" title=\"Quanta cura\">Quanta cura</a></i> and its appendix, the <i><a href=\"https://wikipedia.org/wiki/Syllabus_of_Errors\" title=\"Syllabus of Errors\">Syllabus of Errors</a></i>, outlining the authority of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> and condemning various <a href=\"https://wikipedia.org/wiki/Liberalism\" title=\"Liberalism\">liberal</a> ideas.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON> IX</a> promulgates the encyclical <i><a href=\"https://wikipedia.org/wiki/Quanta_cura\" title=\"Quanta cura\">Quanta cura</a></i> and its appendix, the <i><a href=\"https://wikipedia.org/wiki/Syllabus_of_Errors\" title=\"Syllabus of Errors\">Syllabus of Errors</a></i>, outlining the authority of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> and condemning various <a href=\"https://wikipedia.org/wiki/Liberalism\" title=\"Liberalism\">liberal</a> ideas.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Quanta cura", "link": "https://wikipedia.org/wiki/Quanta_cura"}, {"title": "Syllabus of Errors", "link": "https://wikipedia.org/wiki/Syllabus_of_Errors"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Liberalism", "link": "https://wikipedia.org/wiki/Liberalism"}]}, {"year": "1907", "text": "King <PERSON><PERSON><PERSON> of Sweden accedes to the Swedish throne.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Gustaf_V\" title=\"Gustaf V\">King <PERSON><PERSON><PERSON> of Sweden</a> accedes to the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Sweden\" title=\"Monarchy of Sweden\">Swedish throne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustaf_V\" title=\"Gustaf V\">King <PERSON><PERSON><PERSON> of Sweden</a> accedes to the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Sweden\" title=\"Monarchy of Sweden\">Swedish throne</a>.", "links": [{"title": "Gustaf V", "link": "https://wikipedia.org/wiki/Gustaf_V"}, {"title": "Monarchy of Sweden", "link": "https://wikipedia.org/wiki/Monarchy_of_Sweden"}]}, {"year": "1912", "text": "Leaders of the German Empire hold an Imperial War Council to discuss the possibility that war might break out.", "html": "1912 - Leaders of the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a> hold an <a href=\"https://wikipedia.org/wiki/German_Imperial_War_Council_of_8_December_1912\" title=\"German Imperial War Council of 8 December 1912\">Imperial War Council</a> to discuss the possibility that war might break out.", "no_year_html": "Leaders of the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a> hold an <a href=\"https://wikipedia.org/wiki/German_Imperial_War_Council_of_8_December_1912\" title=\"German Imperial War Council of 8 December 1912\">Imperial War Council</a> to discuss the possibility that war might break out.", "links": [{"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "German Imperial War Council of 8 December 1912", "link": "https://wikipedia.org/wiki/German_Imperial_War_Council_of_8_December_1912"}]}, {"year": "1914", "text": "World War I: A squadron of Britain's Royal Navy defeats the Imperial German East Asia Squadron in the Battle of the Falkland Islands in the South Atlantic.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A squadron of Britain's <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the Imperial German <a href=\"https://wikipedia.org/wiki/East_Asia_Squadron\" title=\"East Asia Squadron\">East Asia Squadron</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Falkland_Islands\" title=\"Battle of the Falkland Islands\">Battle of the Falkland Islands</a> in the South Atlantic.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A squadron of Britain's <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the Imperial German <a href=\"https://wikipedia.org/wiki/East_Asia_Squadron\" title=\"East Asia Squadron\">East Asia Squadron</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Falkland_Islands\" title=\"Battle of the Falkland Islands\">Battle of the Falkland Islands</a> in the South Atlantic.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "East Asia Squadron", "link": "https://wikipedia.org/wiki/East_Asia_Squadron"}, {"title": "Battle of the Falkland Islands", "link": "https://wikipedia.org/wiki/Battle_of_the_Falkland_Islands"}]}, {"year": "1922", "text": "Two days after coming into existence, the Irish Free State executes four leaders of the Irish Republican Army: <PERSON>, <PERSON>, <PERSON> and <PERSON>.", "html": "1922 - Two days after coming into existence, the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> executes four leaders of the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%931969)\" title=\"Irish Republican Army (1922-1969)\">Irish Republican Army</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>.", "no_year_html": "Two days after coming into existence, the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a> executes four leaders of the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%931969)\" title=\"Irish Republican Army (1922-1969)\">Irish Republican Army</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>.", "links": [{"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "Irish Republican Army (1922-1969)", "link": "https://wikipedia.org/wiki/Irish_Republican_Army_(1922%E2%80%931969)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(Irish_republican)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}]}, {"year": "1933", "text": "Anarchist insurrection breaks out in Zaragoza, Spain.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933\" title=\"Anarchist insurrection of December 1933\">Anarchist insurrection</a> breaks out in <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933\" title=\"Anarchist insurrection of December 1933\">Anarchist insurrection</a> breaks out in <a href=\"https://wikipedia.org/wiki/Zaragoza\" title=\"Zaragoza\">Zaragoza</a>, <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a>.", "links": [{"title": "Anarchist insurrection of December 1933", "link": "https://wikipedia.org/wiki/Anarchist_insurrection_of_December_1933"}, {"title": "Zaragoza", "link": "https://wikipedia.org/wiki/Zaragoza"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1941", "text": "World War II: U.S. President <PERSON> declares December 7 to be \"a date which will live in infamy\", after which the U.S. declares war on Japan.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/December_7\" title=\"December 7\">December 7</a> to be \"<a href=\"https://wikipedia.org/wiki/Infamy_Speech\" class=\"mw-redirect\" title=\"Infamy Speech\">a date which will live in infamy</a>\", after which <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Japan\" class=\"mw-redirect\" title=\"United States declaration of war upon Japan\">the U.S. declares war on Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares <a href=\"https://wikipedia.org/wiki/December_7\" title=\"December 7\">December 7</a> to be \"<a href=\"https://wikipedia.org/wiki/Infamy_Speech\" class=\"mw-redirect\" title=\"Infamy Speech\">a date which will live in infamy</a>\", after which <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Japan\" class=\"mw-redirect\" title=\"United States declaration of war upon Japan\">the U.S. declares war on Japan</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "December 7", "link": "https://wikipedia.org/wiki/December_7"}, {"title": "Infamy Speech", "link": "https://wikipedia.org/wiki/Infamy_Speech"}, {"title": "United States declaration of war upon Japan", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_upon_Japan"}]}, {"year": "1941", "text": "World War II: Japanese forces simultaneously invade Shanghai International Settlement, Malaya, Thailand, Hong Kong, the Philippines, and the Dutch East Indies. (See December 7 for the concurrent attack on Pearl Harbor in the Western Hemisphere.)", "html": "1941 - World War II: Japanese forces simultaneously invade <a href=\"https://wikipedia.org/wiki/Shanghai_International_Settlement\" title=\"Shanghai International Settlement\">Shanghai International Settlement</a>, <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Malaya\" class=\"mw-redirect\" title=\"Japanese invasion of Malaya\">Malaya</a>, <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Thailand\" title=\"Japanese invasion of Thailand\">Thailand</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Hong_Kong\" title=\"Battle of Hong Kong\">Hong Kong</a>, <a href=\"https://wikipedia.org/wiki/Philippines_campaign_(1941%E2%80%931942)\" title=\"Philippines campaign (1941-1942)\">the Philippines</a>, and <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies_campaign\" title=\"Dutch East Indies campaign\">the Dutch East Indies</a>. (See <a href=\"https://wikipedia.org/wiki/December_7\" title=\"December 7\">December 7</a> for the concurrent <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a> in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>.)", "no_year_html": "World War II: Japanese forces simultaneously invade <a href=\"https://wikipedia.org/wiki/Shanghai_International_Settlement\" title=\"Shanghai International Settlement\">Shanghai International Settlement</a>, <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Malaya\" class=\"mw-redirect\" title=\"Japanese invasion of Malaya\">Malaya</a>, <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Thailand\" title=\"Japanese invasion of Thailand\">Thailand</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Hong_Kong\" title=\"Battle of Hong Kong\">Hong Kong</a>, <a href=\"https://wikipedia.org/wiki/Philippines_campaign_(1941%E2%80%931942)\" title=\"Philippines campaign (1941-1942)\">the Philippines</a>, and <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies_campaign\" title=\"Dutch East Indies campaign\">the Dutch East Indies</a>. (See <a href=\"https://wikipedia.org/wiki/December_7\" title=\"December 7\">December 7</a> for the concurrent <a href=\"https://wikipedia.org/wiki/Attack_on_Pearl_Harbor\" title=\"Attack on Pearl Harbor\">attack on Pearl Harbor</a> in the <a href=\"https://wikipedia.org/wiki/Western_Hemisphere\" title=\"Western Hemisphere\">Western Hemisphere</a>.)", "links": [{"title": "Shanghai International Settlement", "link": "https://wikipedia.org/wiki/Shanghai_International_Settlement"}, {"title": "Japanese invasion of Malaya", "link": "https://wikipedia.org/wiki/Japanese_invasion_of_Malaya"}, {"title": "Japanese invasion of Thailand", "link": "https://wikipedia.org/wiki/Japanese_invasion_of_Thailand"}, {"title": "Battle of Hong Kong", "link": "https://wikipedia.org/wiki/Battle_of_Hong_Kong"}, {"title": "Philippines campaign (1941-1942)", "link": "https://wikipedia.org/wiki/Philippines_campaign_(1941%E2%80%931942)"}, {"title": "Dutch East Indies campaign", "link": "https://wikipedia.org/wiki/Dutch_East_Indies_campaign"}, {"title": "December 7", "link": "https://wikipedia.org/wiki/December_7"}, {"title": "Attack on Pearl Harbor", "link": "https://wikipedia.org/wiki/Attack_on_Pearl_Harbor"}, {"title": "Western Hemisphere", "link": "https://wikipedia.org/wiki/Western_Hemisphere"}]}, {"year": "1943", "text": "World War II: The German 117th Jäger Division destroys the monastery of Mega Spilaio in Greece and executes 22 monks and visitors as part of reprisals that culminated a few days later with the Massacre of Kalavryta.", "html": "1943 - World War II: The German <a href=\"https://wikipedia.org/wiki/117th_J%C3%A4ger_Division_(Wehrmacht)\" class=\"mw-redirect\" title=\"117th Jäger Division (Wehrmacht)\">117th Jäger Division</a> destroys the monastery of <a href=\"https://wikipedia.org/wiki/Mega_Spilaio\" title=\"Mega Spilaio\">Mega Spilaio</a> in Greece and executes 22 monks and visitors as part of <a href=\"https://wikipedia.org/wiki/Reprisals\" class=\"mw-redirect\" title=\"Reprisals\">reprisals</a> that culminated a few days later with the <a href=\"https://wikipedia.org/wiki/Massacre_of_Kalavryta\" class=\"mw-redirect\" title=\"Massacre of Kalavryta\">Massacre of Kalavryta</a>.", "no_year_html": "World War II: The German <a href=\"https://wikipedia.org/wiki/117th_J%C3%A4ger_Division_(Wehrmacht)\" class=\"mw-redirect\" title=\"117th Jäger Division (Wehrmacht)\">117th Jäger Division</a> destroys the monastery of <a href=\"https://wikipedia.org/wiki/Mega_Spilaio\" title=\"Mega Spilaio\">Mega Spilaio</a> in Greece and executes 22 monks and visitors as part of <a href=\"https://wikipedia.org/wiki/Reprisals\" class=\"mw-redirect\" title=\"Reprisals\">reprisals</a> that culminated a few days later with the <a href=\"https://wikipedia.org/wiki/Massacre_of_Kalavryta\" class=\"mw-redirect\" title=\"Massacre of Kalavryta\">Massacre of Kalavryta</a>.", "links": [{"title": "117th Jäger Division (Wehrmacht)", "link": "https://wikipedia.org/wiki/117th_J%C3%A4ger_Division_(Wehrmacht)"}, {"title": "Mega Spilaio", "link": "https://wikipedia.org/wiki/Mega_Spilaio"}, {"title": "Reprisals", "link": "https://wikipedia.org/wiki/Reprisals"}, {"title": "Massacre of Kalavryta", "link": "https://wikipedia.org/wiki/Massacre_of_Kalavryta"}]}, {"year": "1953", "text": "U.S. President <PERSON> delivers his \"Atoms for Peace\" speech, which leads to an American program to supply equipment and information on nuclear power to schools, hospitals, and research institutions around the world.", "html": "1953 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/Atoms_for_Peace\" title=\"Atoms for Peace\">Atoms for Peace</a>\" speech, which leads to an American program to supply equipment and information on nuclear power to schools, hospitals, and research institutions around the world.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his \"<a href=\"https://wikipedia.org/wiki/Atoms_for_Peace\" title=\"Atoms for Peace\">Atoms for Peace</a>\" speech, which leads to an American program to supply equipment and information on nuclear power to schools, hospitals, and research institutions around the world.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Atoms for Peace", "link": "https://wikipedia.org/wiki/Atoms_for_Peace"}]}, {"year": "1955", "text": "The Flag of Europe is adopted by Council of Europe.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Flag_of_Europe\" title=\"Flag of Europe\">Flag of Europe</a> is adopted by <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Flag_of_Europe\" title=\"Flag of Europe\">Flag of Europe</a> is adopted by <a href=\"https://wikipedia.org/wiki/Council_of_Europe\" title=\"Council of Europe\">Council of Europe</a>.", "links": [{"title": "Flag of Europe", "link": "https://wikipedia.org/wiki/Flag_of_Europe"}, {"title": "Council of Europe", "link": "https://wikipedia.org/wiki/Council_of_Europe"}]}, {"year": "1962", "text": "Workers at four New York City newspapers (this later increases to nine) go on strike for 114 days.", "html": "1962 - Workers at four New York City newspapers (this later increases to nine) <a href=\"https://wikipedia.org/wiki/1962%E2%80%9363_New_York_City_newspaper_strike\" class=\"mw-redirect\" title=\"1962-63 New York City newspaper strike\">go on strike</a> for 114 days.", "no_year_html": "Workers at four New York City newspapers (this later increases to nine) <a href=\"https://wikipedia.org/wiki/1962%E2%80%9363_New_York_City_newspaper_strike\" class=\"mw-redirect\" title=\"1962-63 New York City newspaper strike\">go on strike</a> for 114 days.", "links": [{"title": "1962-63 New York City newspaper strike", "link": "https://wikipedia.org/wiki/1962%E2%80%9363_New_York_City_newspaper_strike"}]}, {"year": "1963", "text": "Pan Am Flight 214, a Boeing 707, is struck by lightning and crashes near Elkton, Maryland, killing all 81 people on board.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_214\" title=\"Pan Am Flight 214\">Pan Am Flight 214</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, is struck by <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> and crashes near <a href=\"https://wikipedia.org/wiki/Elkton,_Maryland\" title=\"Elkton, Maryland\">Elkton, Maryland</a>, killing all 81 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_214\" title=\"Pan Am Flight 214\">Pan Am Flight 214</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, is struck by <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> and crashes near <a href=\"https://wikipedia.org/wiki/Elkton,_Maryland\" title=\"Elkton, Maryland\">Elkton, Maryland</a>, killing all 81 people on board.", "links": [{"title": "Pan Am Flight 214", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_214"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "Lightning", "link": "https://wikipedia.org/wiki/Lightning"}, {"title": "Elkton, Maryland", "link": "https://wikipedia.org/wiki/Elkton,_Maryland"}]}, {"year": "1966", "text": "The Greek ship SS Heraklion sinks in a storm in the Aegean Sea, killing over 200.", "html": "1966 - The Greek ship <a href=\"https://wikipedia.org/wiki/SS_Heraklion\" title=\"SS Heraklion\">SS <i>Heraklion</i></a> sinks in a storm in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a>, killing over 200.", "no_year_html": "The Greek ship <a href=\"https://wikipedia.org/wiki/SS_Heraklion\" title=\"SS Heraklion\">SS <i>Heraklion</i></a> sinks in a storm in the <a href=\"https://wikipedia.org/wiki/Aegean_Sea\" title=\"Aegean Sea\">Aegean Sea</a>, killing over 200.", "links": [{"title": "SS Heraklion", "link": "https://wikipedia.org/wiki/SS_Heraklion"}, {"title": "Aegean Sea", "link": "https://wikipedia.org/wiki/Aegean_Sea"}]}, {"year": "1969", "text": "Olympic Airways Flight 954 strikes a mountain outside of Keratea, Greece, killing 90 people in the worst crash of a Douglas DC-6 in history.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Olympic_Airways_Flight_954\" title=\"Olympic Airways Flight 954\">Olympic Airways Flight 954</a> strikes a mountain outside of Keratea, Greece, killing 90 people in the worst crash of a <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a> in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Olympic_Airways_Flight_954\" title=\"Olympic Airways Flight 954\">Olympic Airways Flight 954</a> strikes a mountain outside of Keratea, Greece, killing 90 people in the worst crash of a <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a> in history.", "links": [{"title": "Olympic Airways Flight 954", "link": "https://wikipedia.org/wiki/Olympic_Airways_Flight_954"}, {"title": "Douglas DC-6", "link": "https://wikipedia.org/wiki/Douglas_DC-6"}]}, {"year": "1971", "text": "Indo-Pakistani War: The Indian Navy launches an attack on West Pakistan's port city of Karachi.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Navy\" title=\"Indian Navy\">Indian Navy</a> <a href=\"https://wikipedia.org/wiki/Operation_Python\" title=\"Operation Python\">launches an attack</a> on <a href=\"https://wikipedia.org/wiki/West_Pakistan\" title=\"West Pakistan\">West Pakistan</a>'s port city of <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971\" class=\"mw-redirect\" title=\"Indo-Pakistani War of 1971\">Indo-Pakistani War</a>: The <a href=\"https://wikipedia.org/wiki/Indian_Navy\" title=\"Indian Navy\">Indian Navy</a> <a href=\"https://wikipedia.org/wiki/Operation_Python\" title=\"Operation Python\">launches an attack</a> on <a href=\"https://wikipedia.org/wiki/West_Pakistan\" title=\"West Pakistan\">West Pakistan</a>'s port city of <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "links": [{"title": "Indo-Pakistani War of 1971", "link": "https://wikipedia.org/wiki/Indo-Pakistani_War_of_1971"}, {"title": "Indian Navy", "link": "https://wikipedia.org/wiki/Indian_Navy"}, {"title": "Operation Python", "link": "https://wikipedia.org/wiki/Operation_Python"}, {"title": "West Pakistan", "link": "https://wikipedia.org/wiki/West_Pakistan"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}]}, {"year": "1972", "text": "United Airlines Flight 553, a Boeing 737, crashes after aborting its landing attempt at Chicago Midway International Airport, killing 45. This is the first-ever loss of a Boeing 737.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_553\" class=\"mw-redirect\" title=\"United Airlines Flight 553\">United Airlines Flight 553</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a>, crashes after aborting its landing attempt at <a href=\"https://wikipedia.org/wiki/Chicago_Midway_International_Airport\" class=\"mw-redirect\" title=\"Chicago Midway International Airport\">Chicago Midway International Airport</a>, killing 45. This is the first-ever loss of a Boeing 737.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_553\" class=\"mw-redirect\" title=\"United Airlines Flight 553\">United Airlines Flight 553</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a>, crashes after aborting its landing attempt at <a href=\"https://wikipedia.org/wiki/Chicago_Midway_International_Airport\" class=\"mw-redirect\" title=\"Chicago Midway International Airport\">Chicago Midway International Airport</a>, killing 45. This is the first-ever loss of a Boeing 737.", "links": [{"title": "United Airlines Flight 553", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_553"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "Chicago Midway International Airport", "link": "https://wikipedia.org/wiki/Chicago_Midway_International_Airport"}]}, {"year": "1974", "text": "A plebiscite results in the abolition of monarchy in Greece.", "html": "1974 - A <a href=\"https://wikipedia.org/wiki/Greek_republic_referendum,_1974\" class=\"mw-redirect\" title=\"Greek republic referendum, 1974\">plebiscite</a> results in the abolition of <a href=\"https://wikipedia.org/wiki/Monarchy_of_Greece\" title=\"Monarchy of Greece\">monarchy in Greece</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Greek_republic_referendum,_1974\" class=\"mw-redirect\" title=\"Greek republic referendum, 1974\">plebiscite</a> results in the abolition of <a href=\"https://wikipedia.org/wiki/Monarchy_of_Greece\" title=\"Monarchy of Greece\">monarchy in Greece</a>.", "links": [{"title": "Greek republic referendum, 1974", "link": "https://wikipedia.org/wiki/Greek_republic_referendum,_1974"}, {"title": "Monarchy of Greece", "link": "https://wikipedia.org/wiki/Monarchy_of_Greece"}]}, {"year": "1980", "text": "<PERSON> is murdered by <PERSON> in front of The Dakota in New York City.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\">murdered</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in front of <a href=\"https://wikipedia.org/wiki/The_Dakota\" title=\"The Dakota\">The Dakota</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>\" title=\"Murder of <PERSON>\">murdered</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in front of <a href=\"https://wikipedia.org/wiki/The_Dakota\" title=\"The Dakota\">The Dakota</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Dakota", "link": "https://wikipedia.org/wiki/The_Dakota"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1985", "text": "The South Asian Association for Regional Cooperation, the regional intergovernmental organization and geopolitical union in South Asia, is established.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/South_Asian_Association_for_Regional_Cooperation\" title=\"South Asian Association for Regional Cooperation\">South Asian Association for Regional Cooperation</a>, the regional intergovernmental organization and geopolitical union in South Asia, is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_Asian_Association_for_Regional_Cooperation\" title=\"South Asian Association for Regional Cooperation\">South Asian Association for Regional Cooperation</a>, the regional intergovernmental organization and geopolitical union in South Asia, is established.", "links": [{"title": "South Asian Association for Regional Cooperation", "link": "https://wikipedia.org/wiki/South_Asian_Association_for_Regional_Cooperation"}]}, {"year": "1987", "text": "Cold War: The Intermediate-Range Nuclear Forces Treaty is signed by U.S. President <PERSON> and Soviet leader <PERSON> in the White House.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty\" title=\"Intermediate-Range Nuclear Forces Treaty\">Intermediate-Range Nuclear Forces Treaty</a> is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty\" title=\"Intermediate-Range Nuclear Forces Treaty\">Intermediate-Range Nuclear Forces Treaty</a> is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and Soviet leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Intermediate-Range Nuclear Forces Treaty", "link": "https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1987", "text": "An Israeli army tank transporter kills four Palestinian refugees and injures seven others during a traffic accident at the Erez Crossing on the Israel-Gaza Strip border, which has been cited as one of the events which sparked the First Intifada.", "html": "1987 - An Israeli army tank transporter kills four <a href=\"https://wikipedia.org/wiki/Palestinian_refugee\" class=\"mw-redirect\" title=\"Palestinian refugee\">Palestinian refugees</a> and injures seven others during a traffic accident at the <a href=\"https://wikipedia.org/wiki/Erez_Crossing\" title=\"Erez Crossing\">Erez Crossing</a> on the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Gaza_barrier\" class=\"mw-redirect\" title=\"Israel-Gaza barrier\">Israel-Gaza Strip border</a>, which has been cited as one of the events which sparked the <a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a>.", "no_year_html": "An Israeli army tank transporter kills four <a href=\"https://wikipedia.org/wiki/Palestinian_refugee\" class=\"mw-redirect\" title=\"Palestinian refugee\">Palestinian refugees</a> and injures seven others during a traffic accident at the <a href=\"https://wikipedia.org/wiki/Erez_Crossing\" title=\"Erez Crossing\">Erez Crossing</a> on the <a href=\"https://wikipedia.org/wiki/Israel%E2%80%93Gaza_barrier\" class=\"mw-redirect\" title=\"Israel-Gaza barrier\">Israel-Gaza Strip border</a>, which has been cited as one of the events which sparked the <a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a>.", "links": [{"title": "Palestinian refugee", "link": "https://wikipedia.org/wiki/Palestinian_refugee"}, {"title": "Erez Crossing", "link": "https://wikipedia.org/wiki/Erez_Crossing"}, {"title": "Israel-Gaza barrier", "link": "https://wikipedia.org/wiki/Israel%E2%80%93Gaza_barrier"}, {"title": "First Intifada", "link": "https://wikipedia.org/wiki/First_Intifada"}]}, {"year": "1988", "text": "A United States Air Force A-10 Thunderbolt II crashes into an apartment complex in Remscheid, Germany, killing five people and injuring 50 others.", "html": "1988 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Fairchild_Republic_A-10_Thunderbolt_II\" title=\"Fairchild Republic A-10 Thunderbolt II\">A-10 Thunderbolt II</a> <a href=\"https://wikipedia.org/wiki/1988_Remscheid_A-10_crash\" title=\"1988 Remscheid A-10 crash\">crashes</a> into an apartment complex in <a href=\"https://wikipedia.org/wiki/Remscheid\" title=\"Remscheid\">Remscheid</a>, Germany, killing five people and injuring 50 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Fairchild_Republic_A-10_Thunderbolt_II\" title=\"Fairchild Republic A-10 Thunderbolt II\">A-10 Thunderbolt II</a> <a href=\"https://wikipedia.org/wiki/1988_Remscheid_A-10_crash\" title=\"1988 Remscheid A-10 crash\">crashes</a> into an apartment complex in <a href=\"https://wikipedia.org/wiki/Remscheid\" title=\"Remscheid\">Remscheid</a>, Germany, killing five people and injuring 50 others.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Fairchild Republic A-10 Thunderbolt II", "link": "https://wikipedia.org/wiki/Fairchild_Republic_A-10_Thunderbolt_II"}, {"title": "1988 Remscheid A-10 crash", "link": "https://wikipedia.org/wiki/1988_Remscheid_A-10_crash"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Remscheid"}]}, {"year": "1990", "text": "The Galileo spacecraft flies past Earth for the first time.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\">Galileo spacecraft</a> flies past Earth for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\">Galileo spacecraft</a> flies past Earth for the first time.", "links": [{"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}]}, {"year": "1991", "text": "The leaders of Russia, Belarus and Ukraine sign an agreement dissolving the Soviet Union and establishing the Commonwealth of Independent States.", "html": "1991 - The leaders of Russia, <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> and <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> sign an <a href=\"https://wikipedia.org/wiki/Belovezh_Accords\" class=\"mw-redirect\" title=\"Belovezh Accords\">agreement</a> dissolving the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and establishing the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Independent_States\" title=\"Commonwealth of Independent States\">Commonwealth of Independent States</a>.", "no_year_html": "The leaders of Russia, <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> and <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukraine</a> sign an <a href=\"https://wikipedia.org/wiki/Belovezh_Accords\" class=\"mw-redirect\" title=\"Belovezh Accords\">agreement</a> dissolving the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> and establishing the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Independent_States\" title=\"Commonwealth of Independent States\">Commonwealth of Independent States</a>.", "links": [{"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}, {"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "Belovezh Accords", "link": "https://wikipedia.org/wiki/Belovezh_Accords"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Commonwealth of Independent States", "link": "https://wikipedia.org/wiki/Commonwealth_of_Independent_States"}]}, {"year": "1992", "text": "The Galileo spacecraft flies past Earth for the second time.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\">Galileo spacecraft</a> flies past Earth for the second time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\">Galileo spacecraft</a> flies past Earth for the second time.", "links": [{"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}]}, {"year": "1998", "text": "Eighty-one people are killed by armed groups in Algeria.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Tadjena_massacre\" title=\"Tadjena massacre\">Eighty-one people are killed</a> by armed groups in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tadjena_massacre\" title=\"Tadjena massacre\">Eighty-one people are killed</a> by armed groups in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "Tadjena massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "2001", "text": "A raid conducted by the Internal Security Department (ISD) of Singapore foils a <PERSON><PERSON><PERSON> (JI) plot to bomb foreign embassies in Singapore.", "html": "2001 - A raid conducted by the <a href=\"https://wikipedia.org/wiki/Internal_Security_Department_(Singapore)\" title=\"Internal Security Department (Singapore)\">Internal Security Department (ISD) of Singapore</a> foils a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (JI)</a> <a href=\"https://wikipedia.org/wiki/Singapore_embassies_attack_plot\" title=\"Singapore embassies attack plot\">plot to bomb foreign embassies in Singapore</a>.", "no_year_html": "A raid conducted by the <a href=\"https://wikipedia.org/wiki/Internal_Security_Department_(Singapore)\" title=\"Internal Security Department (Singapore)\">Internal Security Department (ISD) of Singapore</a> foils a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (JI)</a> <a href=\"https://wikipedia.org/wiki/Singapore_embassies_attack_plot\" title=\"Singapore embassies attack plot\">plot to bomb foreign embassies in Singapore</a>.", "links": [{"title": "Internal Security Department (Singapore)", "link": "https://wikipedia.org/wiki/Internal_Security_Department_(Singapore)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Islamiyah"}, {"title": "Singapore embassies attack plot", "link": "https://wikipedia.org/wiki/Singapore_embassies_attack_plot"}]}, {"year": "2004", "text": "The Cusco Declaration is signed in Cusco, Peru, establishing the South American Community of Nations.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Cusco_Declaration\" title=\"Cusco Declaration\">Cusco Declaration</a> is signed in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cusco</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, establishing the <a href=\"https://wikipedia.org/wiki/South_American_Community_of_Nations\" class=\"mw-redirect\" title=\"South American Community of Nations\">South American Community of Nations</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cusco_Declaration\" title=\"Cusco Declaration\">Cusco Declaration</a> is signed in <a href=\"https://wikipedia.org/wiki/Cusco\" title=\"Cusco\">Cusco</a>, <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>, establishing the <a href=\"https://wikipedia.org/wiki/South_American_Community_of_Nations\" class=\"mw-redirect\" title=\"South American Community of Nations\">South American Community of Nations</a>.", "links": [{"title": "Cusco Declaration", "link": "https://wikipedia.org/wiki/Cusco_Declaration"}, {"title": "Cusco", "link": "https://wikipedia.org/wiki/Cusco"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}, {"title": "South American Community of Nations", "link": "https://wikipedia.org/wiki/South_American_Community_of_Nations"}]}, {"year": "2004", "text": "Columbus nightclub shooting: <PERSON> opens fire at the Alrosa Villa nightclub in Columbus, Ohio, killing former Pantera guitarist <PERSON><PERSON><PERSON> and three others before being shot dead by a police officer.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Columbus_nightclub_shooting\" title=\"Columbus nightclub shooting\">Columbus nightclub shooting</a>: <PERSON> opens fire at the Alrosa Villa nightclub in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, killing former <a href=\"https://wikipedia.org/wiki/Pantera\" title=\"Pantera\">Pantera</a> guitarist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Darrell\" title=\"<PERSON><PERSON><PERSON> Darrell\"><PERSON><PERSON><PERSON></a> and three others before being shot dead by a police officer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Columbus_nightclub_shooting\" title=\"Columbus nightclub shooting\">Columbus nightclub shooting</a>: <PERSON> opens fire at the Alrosa Villa nightclub in <a href=\"https://wikipedia.org/wiki/Columbus,_Ohio\" title=\"Columbus, Ohio\">Columbus, Ohio</a>, killing former <a href=\"https://wikipedia.org/wiki/Pantera\" title=\"Pantera\">Pantera</a> guitarist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Darrell\" title=\"<PERSON><PERSON><PERSON> Darrell\"><PERSON><PERSON><PERSON></a> and three others before being shot dead by a police officer.", "links": [{"title": "Columbus nightclub shooting", "link": "https://wikipedia.org/wiki/Columbus_nightclub_shooting"}, {"title": "Columbus, Ohio", "link": "https://wikipedia.org/wiki/Columbus,_Ohio"}, {"title": "Pantera", "link": "https://wikipedia.org/wiki/Pantera"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "Bombings in Baghdad, Iraq kill 127 people and injure 448 others.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/December_2009_Baghdad_bombings\" title=\"December 2009 Baghdad bombings\">Bombings</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad, Iraq</a> kill 127 people and injure 448 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/December_2009_Baghdad_bombings\" title=\"December 2009 Baghdad bombings\">Bombings</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad, Iraq</a> kill 127 people and injure 448 others.", "links": [{"title": "December 2009 Baghdad bombings", "link": "https://wikipedia.org/wiki/December_2009_Baghdad_bombings"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "2010", "text": "With the second launch of the Falcon 9, and the first launch of the Dragon, SpaceX becomes the first private company to successfully launch, orbit and recover a spacecraft.", "html": "2010 - With the <a href=\"https://wikipedia.org/wiki/COTS_Demo_Flight_1\" class=\"mw-redirect\" title=\"COTS Demo Flight 1\">second launch</a> of the <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a>, and the first launch of the <a href=\"https://wikipedia.org/wiki/SpaceX_Dragon_1\" title=\"SpaceX Dragon 1\">Dragon</a>, <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a> becomes the first private company to successfully launch, orbit and recover a <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a>.", "no_year_html": "With the <a href=\"https://wikipedia.org/wiki/COTS_Demo_Flight_1\" class=\"mw-redirect\" title=\"COTS Demo Flight 1\">second launch</a> of the <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a>, and the first launch of the <a href=\"https://wikipedia.org/wiki/SpaceX_Dragon_1\" title=\"SpaceX Dragon 1\">Dragon</a>, <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a> becomes the first private company to successfully launch, orbit and recover a <a href=\"https://wikipedia.org/wiki/Spacecraft\" title=\"Spacecraft\">spacecraft</a>.", "links": [{"title": "COTS Demo Flight 1", "link": "https://wikipedia.org/wiki/COTS_Demo_Flight_1"}, {"title": "Falcon 9", "link": "https://wikipedia.org/wiki/Falcon_9"}, {"title": "SpaceX Dragon 1", "link": "https://wikipedia.org/wiki/SpaceX_Dragon_1"}, {"title": "SpaceX", "link": "https://wikipedia.org/wiki/SpaceX"}, {"title": "Spacecraft", "link": "https://wikipedia.org/wiki/Spacecraft"}]}, {"year": "2010", "text": "The Japanese solar-sail spacecraft IKAROS passes the planet Venus at a distance of about 80,800 km (50,200 mi).", "html": "2010 - The Japanese <a href=\"https://wikipedia.org/wiki/Solar-sail\" class=\"mw-redirect\" title=\"Solar-sail\">solar-sail</a> spacecraft <a href=\"https://wikipedia.org/wiki/IKAROS\" title=\"IKAROS\">IKAROS</a> passes the planet <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> at a distance of about 80,800 km (50,200 mi).", "no_year_html": "The Japanese <a href=\"https://wikipedia.org/wiki/Solar-sail\" class=\"mw-redirect\" title=\"Solar-sail\">solar-sail</a> spacecraft <a href=\"https://wikipedia.org/wiki/IKAROS\" title=\"IKAROS\">IKAROS</a> passes the planet <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> at a distance of about 80,800 km (50,200 mi).", "links": [{"title": "Solar-sail", "link": "https://wikipedia.org/wiki/Solar-sail"}, {"title": "IKAROS", "link": "https://wikipedia.org/wiki/IKAROS"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "2013", "text": "Riots break out in Singapore, after a fatal accident in Little India.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/2013_Little_India_riot\" title=\"2013 Little India riot\">Riots break out</a> in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, after a fatal accident in <a href=\"https://wikipedia.org/wiki/Little_India,_Singapore\" title=\"Little India, Singapore\">Little India</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2013_Little_India_riot\" title=\"2013 Little India riot\">Riots break out</a> in <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singapore</a>, after a fatal accident in <a href=\"https://wikipedia.org/wiki/Little_India,_Singapore\" title=\"Little India, Singapore\">Little India</a>.", "links": [{"title": "2013 Little India riot", "link": "https://wikipedia.org/wiki/2013_Little_India_riot"}, {"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "Little India, Singapore", "link": "https://wikipedia.org/wiki/Little_India,_Singapore"}]}, {"year": "2013", "text": "Metallica performs a show in Antarctica, making them the first band to perform on all seven continents.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Metallica\" title=\"Metallica\">Metallica</a> performs a show in <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>, making them the first band to perform on all seven continents.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metallica\" title=\"Metallica\">Metallica</a> performs a show in <a href=\"https://wikipedia.org/wiki/Antarctica\" title=\"Antarctica\">Antarctica</a>, making them the first band to perform on all seven continents.", "links": [{"title": "Metallica", "link": "https://wikipedia.org/wiki/Metallica"}, {"title": "Antarctica", "link": "https://wikipedia.org/wiki/Antarctica"}]}, {"year": "2019", "text": "First confirmed case of COVID-19 in China.", "html": "2019 - First confirmed case of <a href=\"https://wikipedia.org/wiki/Coronavirus_disease_2019\" class=\"mw-redirect\" title=\"Coronavirus disease 2019\">COVID-19</a> in China.", "no_year_html": "First confirmed case of <a href=\"https://wikipedia.org/wiki/Coronavirus_disease_2019\" class=\"mw-redirect\" title=\"Coronavirus disease 2019\">COVID-19</a> in China.", "links": [{"title": "Coronavirus disease 2019", "link": "https://wikipedia.org/wiki/Coronavirus_disease_2019"}]}, {"year": "2024", "text": "Damascus falls to rebels after Syrian troops withdraw and president <PERSON><PERSON><PERSON> leaves the country as his government collapses. Israel as a result invaded into the buffer zone between Syria and the Israeli-occupied Golan Heights.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Fall_of_Damascus_(2024)\" title=\"Fall of Damascus (2024)\">Damascus falls</a> to rebels after Syrian troops withdraw and president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leaves the country as <a href=\"https://wikipedia.org/wiki/Fall_of_the_Assad_regime\" title=\"Fall of the Assad regime\">his government collapses</a>. Israel as a result <a href=\"https://wikipedia.org/wiki/2024_Israeli_invasion_of_Syria\" class=\"mw-redirect\" title=\"2024 Israeli invasion of Syria\">invaded</a> into the <a href=\"https://wikipedia.org/wiki/Buffer_zone\" title=\"Buffer zone\">buffer zone</a> between Syria and the Israeli-occupied <a href=\"https://wikipedia.org/wiki/Golan_Heights\" title=\"Golan Heights\">Golan Heights</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fall_of_Damascus_(2024)\" title=\"Fall of Damascus (2024)\">Damascus falls</a> to rebels after Syrian troops withdraw and president <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leaves the country as <a href=\"https://wikipedia.org/wiki/Fall_of_the_Assad_regime\" title=\"Fall of the Assad regime\">his government collapses</a>. Israel as a result <a href=\"https://wikipedia.org/wiki/2024_Israeli_invasion_of_Syria\" class=\"mw-redirect\" title=\"2024 Israeli invasion of Syria\">invaded</a> into the <a href=\"https://wikipedia.org/wiki/Buffer_zone\" title=\"Buffer zone\">buffer zone</a> between Syria and the Israeli-occupied <a href=\"https://wikipedia.org/wiki/Golan_Heights\" title=\"Golan Heights\">Golan Heights</a>.", "links": [{"title": "Fall of Damascus (2024)", "link": "https://wikipedia.org/wiki/Fall_of_Damascus_(2024)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Fall of the <PERSON><PERSON> regime", "link": "https://wikipedia.org/wiki/Fall_of_the_Assad_regime"}, {"title": "2024 Israeli invasion of Syria", "link": "https://wikipedia.org/wiki/2024_Israeli_invasion_of_Syria"}, {"title": "Buffer zone", "link": "https://wikipedia.org/wiki/Buffer_zone"}, {"title": "Golan Heights", "link": "https://wikipedia.org/wiki/Golan_Heights"}]}], "Births": [{"year": "65 BC", "text": "<PERSON>, Roman poet (d. 8 BC)", "html": "65 BC - 65 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman poet (d. 8 BC)", "no_year_html": "65 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman poet (d. 8 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1021", "text": "<PERSON>, Chinese economist and chancellor (d. 1086)", "html": "1021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese economist and chancellor (d. 1086)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese economist and chancellor (d. 1086)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1412", "text": "<PERSON><PERSON><PERSON>, Italian lord (d. 1468)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/Astorre_II_Manfredi\" title=\"Astorre II Manfredi\">Astorre II Manfred<PERSON></a>, Italian lord (d. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Astor<PERSON>_II_Manfredi\" title=\"Astorre II Manfredi\"><PERSON><PERSON><PERSON> II Manfred<PERSON></a>, Italian lord (d. 1468)", "links": [{"title": "Astorre II Manfredi", "link": "https://wikipedia.org/wiki/Astor<PERSON>_<PERSON>_Manfredi"}]}, {"year": "1418", "text": "Queen <PERSON><PERSON><PERSON>, Queen consort of Korea (d. 1483)", "html": "1418 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Queen consort of Korea (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Queen consort of Korea (d. 1483)", "links": [{"title": "Queen <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1424", "text": "<PERSON><PERSON><PERSON>, Belgian merchant, politician and diplomat (d. 1483)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian merchant, politician and diplomat (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian merchant, politician and diplomat (d. 1483)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sel<PERSON>_<PERSON>"}]}, {"year": "1538", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician (d. 1615)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Istv%C3%A1n<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikl%C3%B3s_Istv%C3%A1n<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 1615)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikl%C3%B3s_Istv%C3%A1nffy"}]}, {"year": "1542", "text": "<PERSON>, Queen of Scots, daughter of <PERSON> of Scotland and <PERSON> of Guise (d. 1587)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, daughter of <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"<PERSON> V of Scotland\"><PERSON> of Scotland</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Guise\" title=\"<PERSON> of Guise\"><PERSON> of Guise</a> (d. 1587)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, daughter of <a href=\"https://wikipedia.org/wiki/James_V_of_Scotland\" class=\"mw-redirect\" title=\"James V of Scotland\"><PERSON> of Scotland</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Guise\" title=\"<PERSON> of Guise\"><PERSON> of Guise</a> (d. 1587)", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/James_V_of_Scotland"}, {"title": "<PERSON> of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON>, Catholic cardinal (d. 1645)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Catholic cardinal (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Catholic cardinal (d. 1645)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_Rochefoucauld_(cardinal)"}]}, {"year": "1678", "text": "<PERSON>, colonial governor of Florida (d. 1762)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, colonial governor of Florida (d. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, colonial governor of Florida (d. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English politician and diplomat, British Ambassador to France (d. 1757)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_France\" class=\"mw-redirect\" title=\"British Ambassador to France\">British Ambassador to France</a> (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/British_Ambassador_to_France\" class=\"mw-redirect\" title=\"British Ambassador to France\">British Ambassador to France</a> (d. 1757)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>,_1st_Baron_<PERSON>"}, {"title": "British Ambassador to France", "link": "https://wikipedia.org/wiki/British_Ambassador_to_France"}]}, {"year": "1699", "text": "<PERSON> of Austria (d. 1757)", "html": "1699 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1757)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria"}]}, {"year": "1708", "text": "<PERSON>, Holy Roman Emperor (d. 1765)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1765)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1724", "text": "<PERSON>, French organist and composer (d. 1799)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, Dutch physician, physiologist, and botanist (d. 1799)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician, physiologist, and botanist (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician, physiologist, and botanist (d. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1731", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech pianist and composer (d. 1799)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Xaver_Du%C5%A1ek\" title=\"František Xaver <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Xaver_Du%C5%A1ek\" title=\"František Xaver Du<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech pianist and composer (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Xaver_Du%C5%A1ek"}]}, {"year": "1756", "text": "Archduke <PERSON> of Austria (d. 1801)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archduke <PERSON> of Austria</a> (d. 1801)", "links": [{"title": "Archduke <PERSON> of Austria", "link": "https://wikipedia.org/wiki/Archduke_<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1765", "text": "<PERSON>, American engineer, invented the cotton gin (d. 1825)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Cotton_gin\" title=\"Cotton gin\">cotton gin</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, invented the <a href=\"https://wikipedia.org/wiki/Cotton_gin\" title=\"Cotton gin\">cotton gin</a> (d. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cotton gin", "link": "https://wikipedia.org/wiki/Cotton_gin"}]}, {"year": "1795", "text": "<PERSON>, Danish astronomer and mathematician (d. 1874)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and mathematician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish astronomer and mathematician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, German pharmacist, botanist and phycologist (d. 1893)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCtzing\" title=\"<PERSON>\"><PERSON></a>, German pharmacist, botanist and <a href=\"https://wikipedia.org/wiki/Phycology\" title=\"Phycology\">phycologist</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCtzing\" title=\"<PERSON>\"><PERSON></a>, German pharmacist, botanist and <a href=\"https://wikipedia.org/wiki/Phycology\" title=\"Phycology\">phycologist</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_K%C3%BCtzing"}, {"title": "Phycology", "link": "https://wikipedia.org/wiki/Phycology"}]}, {"year": "1813", "text": "<PERSON> <PERSON>, Prussian-American financier and diplomat, 16th United States Ambassador to the Netherlands (d. 1890)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Prussian-American financier and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands\" class=\"mw-redirect\" title=\"United States Ambassador to the Netherlands\">United States Ambassador to the Netherlands</a> (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August Belmont\">August <PERSON></a>, Prussian-American financier and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands\" class=\"mw-redirect\" title=\"United States Ambassador to the Netherlands\">United States Ambassador to the Netherlands</a> (d. 1890)", "links": [{"title": "August Belmont", "link": "https://wikipedia.org/wiki/August_Belmont"}, {"title": "United States Ambassador to the Netherlands", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_Netherlands"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON>, German painter and illustrator (d. 1905)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and illustrator (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and illustrator (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, Danish lawyer and politician, 10th Prime Minister of Denmark (d. 1896)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>-<PERSON>\" title=\"Christian <PERSON>Vind-Frijs\"><PERSON></a>, Danish lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>d-Frijs\"><PERSON></a>, Danish lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a> (d. 1896)", "links": [{"title": "<PERSON>-Vin<PERSON>-Fr<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>-<PERSON>-<PERSON>s"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1818", "text": "<PERSON>, Prince of Monaco (d. 1889)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON>, Prince of Monaco</a> (d. 1889)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1822", "text": "<PERSON><PERSON><PERSON>, Hungarian-Serbian author (d. 1889)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gn<PERSON>tovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Serbian author (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Serbian author (d. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakov_Ignjatovi%C4%87"}]}, {"year": "1832", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian-French author and playwright, Nobel Prize laureate (d. 1910)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bjørnson\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-French author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Bjørnson\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-French author and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rnstjerne_Bj%C3%B8rnson"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1860", "text": "<PERSON>, Irish author and poet (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and poet (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author and poet (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American businessman, founded General Motors and Chevrolet (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> and <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> and <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}, {"title": "Chevrolet", "link": "https://wikipedia.org/wiki/Chevrolet"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, French sculptor and painter (d. 1944)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Aristide_Maillol\" title=\"Aristide Maillol\">Arist<PERSON> Maillol</a>, French sculptor and painter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aristide_Maillol\" title=\"Aristide Maillol\"><PERSON><PERSON><PERSON></a>, French sculptor and painter (d. 1944)", "links": [{"title": "Aristide <PERSON>", "link": "https://wikipedia.org/wiki/Aristide_Maillol"}]}, {"year": "1861", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 1938)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9li%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9li%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_M%C3%A9li%C3%A8s"}]}, {"year": "1862", "text": "<PERSON>, French playwright (d. 1921)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American zoologist (d. 1937)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American zoologist (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, French illustrator and sculptor (d. 1943)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator and sculptor (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French illustrator and sculptor (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON><PERSON>, German general (d. 1946)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German general (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German general (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, French mathematician and academic (d. 1963)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Finnish violinist and composer (d. 1957)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish violinist and composer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish violinist and composer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Austrian physician and pediatrician (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and pediatrician (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician and pediatrician (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Danish actor and screenwriter (d. 1925)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actor and screenwriter (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actor and screenwriter (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, French pianist, violinist, and composer (d. 1944)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, violinist, and composer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, violinist, and composer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Estonian linguist and philologist (d. 1973)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and philologist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian linguist and philologist (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Finnish politician (d. 1964)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, French painter (d. 1953)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English colonel and politician (d. 1965)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, English colonel and politician (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)\" title=\"<PERSON> (colonial administrator)\"><PERSON></a>, English colonel and politician (d. 1965)", "links": [{"title": "<PERSON> (colonial administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonial_administrator)"}]}, {"year": "1886", "text": "<PERSON>, Mexican painter and educator (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Rivera\"><PERSON></a>, Mexican painter and educator (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diego_Rivera\" title=\"Diego Rivera\"><PERSON></a>, Mexican painter and educator (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Rivera"}]}, {"year": "1887", "text": "<PERSON>, English poet (d. 1977) ", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1977) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (d. 1977) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Czech-American pianist and composer (d. 1959)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American pianist and composer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American pianist and composer (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Martin%C5%AF"}]}, {"year": "1892", "text": "<PERSON>, American historian, author, and academic (d. 1938)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON>, American cartoonist, created <PERSON><PERSON> (d. 1938)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/E._C._Segar\" title=\"E. C. Segar\"><PERSON><PERSON> <PERSON><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._C._Segar\" title=\"E. C. Segar\"><PERSON><PERSON> <PERSON><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> (d. 1938)", "links": [{"title": "E. C. Segar", "link": "https://wikipedia.org/wiki/E._C._Segar"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American humorist and cartoonist (d. 1961)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist and cartoonist (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American humorist and cartoonist (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, English-Welsh actor and playwright (d. 1970)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh actor and playwright (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh actor and playwright (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Canadian-American actor (d. 1987)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Chinese general and politician (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jen\" title=\"<PERSON> Li-jen\"><PERSON></a>, Chinese general and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jen\" title=\"<PERSON> Li-jen\"><PERSON></a>, Chinese general and politician (d. 1990)", "links": [{"title": "<PERSON>jen", "link": "https://wikipedia.org/wiki/<PERSON>_Li-jen"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Estonian-American author and academic (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Or<PERSON>\"><PERSON><PERSON></a>, Estonian-American author and academic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-American author and academic (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Oras"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Cuban-French painter (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-French painter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-French painter (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Black American opera singer (d. 1994)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Black American opera singer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Black American opera singer (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Spanish singer and actress (d. 1990)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer and actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer and actress (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American soldier and politician, 61st Governor of Massachusetts (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 61st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1911", "text": "<PERSON>, American actor (d. 1976)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Greek poet and songwriter (d. 1992)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and songwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and songwriter (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nik<PERSON>_<PERSON>os"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American poet and short story writer (d. 1966)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Schwartz\"><PERSON><PERSON></a>, American poet and short story writer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Schwartz\"><PERSON><PERSON></a>, American poet and short story writer (d. 1966)", "links": [{"title": "<PERSON><PERSON> Schwartz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American country music singer-songwriter and guitarist (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian cricketer (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American director, producer, and screenwriter (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American director, producer, and screenwriter (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian cricketer and administrator (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and administrator (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and administrator (d. 1998)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1919", "text": "<PERSON>, Samoan-American lawyer and politician, 43rd Governor of American Samoa (d. 1997)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_American_Samoa\" class=\"mw-redirect\" title=\"Governor of American Samoa\">Governor of American Samoa</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan-American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_American_Samoa\" class=\"mw-redirect\" title=\"Governor of American Samoa\">Governor of American Samoa</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of American Samoa", "link": "https://wikipedia.org/wiki/Governor_of_American_Samoa"}]}, {"year": "1919", "text": "<PERSON>, American mathematician and theorist (d. 1985)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Ukrainian computer scientist and academic (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON><PERSON> (scientist)\"><PERSON><PERSON><PERSON></a>, Ukrainian computer scientist and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON><PERSON> (scientist)\"><PERSON><PERSON><PERSON></a>, Ukrainian computer scientist and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)"}]}, {"year": "1920", "text": "<PERSON>, Trinidadian-English sprinter and rugby player (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-English sprinter and rugby player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-English sprinter and rugby player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, German-English painter and illustrator (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Lucian_Freud\" title=\"Lucian Freud\"><PERSON></a>, German-English painter and illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucian_Freud\" title=\"Lucian Freud\"><PERSON></a>, German-English painter and illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American singer-songwriter (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2018)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1923", "text": "<PERSON>, Chinese-American soldier and chemist (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American soldier and chemist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American soldier and chemist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian historian, author, and academic (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian, author, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian, author, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor, singer, and dancer (d. 1990)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor, singer, and dancer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor, singer, and dancer (d. 1990)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON><PERSON>, Pakistani Urdu poet (d. 1972)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani Urdu poet (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani Urdu poet (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Spanish author and poet (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Carmen_Mart%C3%ADn_Gaite\" title=\"<PERSON>\"><PERSON></a>, Spanish author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carmen_Mart%C3%ADn_Gaite\" title=\"<PERSON>\"><PERSON></a>, Spanish author and poet (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Mart%C3%ADn_Gaite"}]}, {"year": "1925", "text": "<PERSON>, American organist (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American organist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American organist (d. 2005)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1926", "text": "<PERSON>, American Army officer (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Army officer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Army officer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, German thinker and social theorist (d. 1998)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German thinker and social theorist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German thinker and social theorist (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Kazakhstani general, pilot, and astronaut (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani general, pilot, and astronaut (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani general, pilot, and astronaut (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian journalist and sportscaster (d. 1996)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian journalist and sportscaster (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian journalist and sportscaster (d. 1996)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, German-American psychologist, neuroscientist, and academic (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American psychologist, neuroscientist, and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American psychologist, neuroscientist, and academic (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, chronicler of the history of workers and trade union of Russia (d. 2011)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chronicler of the history of workers and trade union of Russia (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, chronicler of the history of workers and trade union of Russia (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English journalist and politician (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Austrian-Swiss actor, director, producer, and screenwriter (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss actor, director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Maximilian <PERSON>\"><PERSON></a>, Austrian-Swiss actor, director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American boxing promoter, founded Top Rank", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> A<PERSON>\"><PERSON></a>, American boxing promoter, founded <a href=\"https://wikipedia.org/wiki/Top_Rank\" title=\"Top Rank\">Top Rank</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bob A<PERSON>\"><PERSON></a>, American boxing promoter, founded <a href=\"https://wikipedia.org/wiki/Top_Rank\" title=\"Top Rank\">Top Rank</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Top Rank", "link": "https://wikipedia.org/wiki/Top_Rank"}]}, {"year": "1932", "text": "<PERSON>,  German automotive designer (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German automotive designer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German automotive designer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American basketball player (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2023)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actor and comedian (d. 1998)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor and comedian (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Flip_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor, producer, and politician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/D<PERSON>mendra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, producer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dharmendra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor, producer, and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>har<PERSON>dra"}]}, {"year": "1935", "text": "<PERSON>, Russian-Israeli chess player (d. 2017)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli chess player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Israeli chess player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, and producer (d. 2009)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American publisher (d. 2020)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English cricketer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Uruguayan football player and manager (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan football player and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan football player and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor (d. 2010)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, German-Norwegian mountaineer and businessman (d. 2004)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A6ss_Jr.\" title=\"<PERSON><PERSON>.\"><PERSON><PERSON>.</a>, German-Norwegian mountaineer and businessman (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A6ss_Jr.\" title=\"<PERSON><PERSON>.\"><PERSON><PERSON>.</a>, German-Norwegian mountaineer and businessman (d. 2004)", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/Arne_N%C3%A6ss_Jr."}]}, {"year": "1939", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Be<PERSON>son"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and producer (d. 2025)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Irish flute player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/James_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Irish flute player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_Galway\" title=\"James Galway\"><PERSON></a>, Irish flute player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Filipino lawyer and businessman", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Iranian director, producer, and screenwriter (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian director, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian director, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American drummer (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American baseball player (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Brant_Alyea\" title=\"Brant Alyea\"><PERSON><PERSON></a>, American baseball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brant_Alyea\" title=\"Brant Alyea\"><PERSON><PERSON></a>, American baseball player (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brant_Alyea"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and coach (d. 2008)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American football player (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)\" title=\"<PERSON> (offensive lineman)\"><PERSON></a>, American football player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)\" title=\"<PERSON> (offensive lineman)\"><PERSON></a>, American football player (d. 2023)", "links": [{"title": "<PERSON> (offensive lineman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(offensive_lineman)"}]}, {"year": "1941", "text": "<PERSON>, American commander and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English drummer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English footballer and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American basketball player (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Love\"><PERSON></a>, American basketball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American paleontologist and ornithologist (d. 2013)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and ornithologist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American paleontologist and ornithologist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and poet (d. 1971)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American poet (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American poet (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American poet (d. 2015)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1943", "text": "<PERSON><PERSON>, German runner", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Bodo_T%C3%BC<PERSON>ler\" title=\"<PERSON><PERSON> Tümmler\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bodo_T%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Tümmler\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bodo_T%C3%BCmmler"}]}, {"year": "1943", "text": "<PERSON>, American actress, director, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Dutch singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dutch_singer)\" title=\"<PERSON> (Dutch singer)\"><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dutch_singer)\" title=\"<PERSON> (Dutch singer)\"><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON> (Dutch singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dutch_singer)"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian ice hockey player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian educator and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Irish novelist and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, Irish novelist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, Irish novelist and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American tennis player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Polish-Israeli singer-songwriter and guitarist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor, director, and composer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American musician (d. 2017)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, French singer, guitarist, and actor (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, French singer, guitarist, and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French singer, guitarist, and actor (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish author and poet (d. 2011)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Finnish author and poet (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Finnish author and poet (d. 2011)", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American astrophysicist, astronomer, and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist, astronomer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astrophysicist, astronomer, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Argentinian-American mathematician and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English-Australian actor, singer-songwriter, and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Australian actor, singer-songwriter, and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Australian actor, singer-songwriter, and guitarist", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1949", "text": "<PERSON>, American author, critic, and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, critic, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, critic, and academic", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1949", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American psychologist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and makeup artist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(makeup_artist)\" class=\"mw-redirect\" title=\"<PERSON> (makeup artist)\"><PERSON></a>, American actor and makeup artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(makeup_artist)\" class=\"mw-redirect\" title=\"<PERSON> (makeup artist)\"><PERSON></a>, American actor and makeup artist", "links": [{"title": "<PERSON> (makeup artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(makeup_artist)"}]}, {"year": "1950", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and producer  (d. 1994)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American essayist, travel and science writer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist, travel and science writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist, travel and science writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English publisher and businessman, founded Northern & Shell", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher and businessman, founded <a href=\"https://wikipedia.org/wiki/Northern_%26_Shell\" title=\"Northern &amp; Shell\">Northern &amp; Shell</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher and businessman, founded <a href=\"https://wikipedia.org/wiki/Northern_%26_Shell\" title=\"Northern &amp; Shell\">Northern &amp; Shell</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Northern & Shell", "link": "https://wikipedia.org/wiki/Northern_%26_Shell"}]}, {"year": "1951", "text": "<PERSON>, Norwegian singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English-Hong Kong cricketer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English-Hong Kong cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English-Hong Kong cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Malayan-Singaporean politician, Singaporean Minister of Health", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on_Wan\" title=\"Khaw Boon Wan\"><PERSON><PERSON></a>, Malayan-Singaporean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Khaw Boon Wan\"><PERSON><PERSON></a>, Malayan-Singaporean politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)\" title=\"Ministry of Health (Singapore)\">Singaporean Minister of Health</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Health (Singapore)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Singapore)"}]}, {"year": "1953", "text": "<PERSON>, American actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American author, academic, and activist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, academic, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, academic, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American sportscaster and journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American comedian (d. 1992)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian-Polish pole vaulter and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-Polish pole vaulter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-Polish pole vaulter and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English footballer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1953)"}]}, {"year": "1954", "text": "<PERSON>, American lawyer, academic, and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, academic, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Frits_Pirard\" title=\"Frits Pirard\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Pirard\" title=\"Frits Pirard\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "Frits Pirard", "link": "https://wikipedia.org/wiki/Frits_Pirard"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Serbian actor, director, and screenwriter (d. 2008)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87anski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian actor, director, and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87anski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian actor, director, and screenwriter (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Zabla%C4%87anski"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Lithuanian academic and politician, 9th Prime Minister of Lithuania", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">9th Prime Minister of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">9th Prime Minister of Lithuania</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Lithuania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lithuania"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American wrestler and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Slick_(wrestling)\" title=\"Slick (wrestling)\"><PERSON><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slick_(wrestling)\" title=\"Slick (wrestling)\"><PERSON><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON><PERSON> (wrestling)", "link": "https://wikipedia.org/wiki/Slick_(wrestling)"}]}, {"year": "1957", "text": "<PERSON>, British men's rights advocate", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British men's rights advocate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British men's rights advocate", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1957", "text": "<PERSON>, American martial artist and educator (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American martial artist and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Collen"}]}, {"year": "1958", "text": "<PERSON>, American author and blogger", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Malayan-English journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Rob_Curling\" title=\"Rob Curling\"><PERSON></a>, Malayan-English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rob_Curling\" title=\"Rob Curling\"><PERSON></a>, Malayan-English journalist", "links": [{"title": "Rob <PERSON>", "link": "https://wikipedia.org/wiki/Rob_Curling"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Central African lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_Sombo-Dib%C3%A9l%C3%A9\" title=\"A<PERSON>tte Sombo-<PERSON>\"><PERSON><PERSON><PERSON></a>, Central African lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_Sombo-Dib%C3%A9l%C3%A9\" title=\"Arlette Sombo-Di<PERSON>\"><PERSON><PERSON><PERSON></a>, Central African lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>tte_Sombo-Dib%C3%A9l%C3%A9"}]}, {"year": "1958", "text": "<PERSON>, French race car driver (d. 2023)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michel_Fert%C3%A9"}]}, {"year": "1958", "text": "<PERSON>, American physiologist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fitness)\" title=\"<PERSON> (fitness)\"><PERSON></a>, American physiologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fitness)\" title=\"<PERSON> (fitness)\"><PERSON></a>, American physiologist and author", "links": [{"title": "<PERSON> (fitness)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fitness)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Miros%C5%82aw_Oko%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miros%C5%82aw_Oko%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miros%C5%82aw_Oko%C5%84ski"}]}, {"year": "1958", "text": "<PERSON>, American football player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1959", "text": "<PERSON>, South African cricketer and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian-American author and critic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American game designer and author (d. 2014)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Malaysian accountant and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Guan Eng\"><PERSON></a>, Malaysian accountant and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Guan Eng\"><PERSON></a>, Malaysian accountant and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Eng"}]}, {"year": "1961", "text": "<PERSON>, Australian rugby league player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American political commentator and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, São Toméan poet", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Concei%C3%A7%C3%A3o_Lima\" title=\"Conceição Lima\"><PERSON>cei<PERSON> Lima</a>, São Toméan poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concei%C3%A7%C3%A3o_Lima\" title=\"Conceição Lima\">Conceição Lima</a>, São Toméan poet", "links": [{"title": "Conceição Lima", "link": "https://wikipedia.org/wiki/Concei%C3%A7%C3%A3o_Lima"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Australian comedian and television host", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian comedian and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian comedian and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Robin<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian-American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American-Japanese guitarist, songwriter, and television host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese guitarist, songwriter, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Japanese guitarist, songwriter, and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Nikos_Karageorgiou\" title=\"Nik<PERSON>orgio<PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nik<PERSON>_Karageorgiou\" title=\"Nik<PERSON>orgio<PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikos_Karageorgiou"}]}, {"year": "1962", "text": "<PERSON>, Dutch footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian rugby league player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Chigus<PERSON>_Nagayo\" title=\"Chigus<PERSON> Nagayo\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chi<PERSON><PERSON>_<PERSON>gayo\" title=\"<PERSON><PERSON><PERSON> Nagayo\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chigusa_Nagayo"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Costa Rican footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Ram%C3%<PERSON><PERSON>_(footballer,_born_1964)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1964)\"><PERSON><PERSON><PERSON></a>, Costa Rican footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Ram%C3%<PERSON><PERSON>_(footballer,_born_1964)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1964)\"><PERSON><PERSON><PERSON></a>, Costa Rican footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1964)", "link": "https://wikipedia.org/wiki/%C3%93scar_Ram%C3%<PERSON><PERSON>_(footballer,_born_1964)"}]}, {"year": "1965", "text": "<PERSON>, English actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Dutch actor, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Weatherspoon"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Jamaican-American rapper (d. 2019)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bill\" title=\"Bushwick Bill\"><PERSON><PERSON></a>, Jamaican-American rapper (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>wick_Bill\" title=\"Bushwick Bill\"><PERSON><PERSON></a>, Jamaican-American rapper (d. 2019)", "links": [{"title": "<PERSON><PERSON> Bill", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bill"}]}, {"year": "1966", "text": "<PERSON>, English footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian wrestler and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Irish singer-songwriter (d. 2023)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish singer-songwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sin%C3%A9ad_O%27Connor"}]}, {"year": "1967", "text": "<PERSON>, American football player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German curler", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German curler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese voice actress and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American sportscaster and journalist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestling)\" title=\"<PERSON> (wrestling)\"><PERSON></a>, American sportscaster and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestling)\" title=\"<PERSON> (wrestling)\"><PERSON></a>, American sportscaster and journalist", "links": [{"title": "<PERSON> (wrestling)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestling)"}]}, {"year": "1968", "text": "<PERSON>, American baseball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Italian motorcycle racer (d. 2013)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American mathematician and cryptographer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and cryptographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mathematician and cryptographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Turkish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Estonian architect", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Indre<PERSON>_<PERSON>\" title=\"In<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indre<PERSON>_<PERSON>\" title=\"In<PERSON><PERSON>mann\"><PERSON><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indre<PERSON>_<PERSON>mann"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American powerlifter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American powerlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American powerlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Brazilian sprinter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/%C3%89dson_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89dson_Ribeiro"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter, musician, and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Mexican singer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Bahamian-American singer-songwriter and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian-American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian-American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ina"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Johnson\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Greek lawyer and politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German-British actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Polish tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Swiss runner and journalist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss runner and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss runner and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English-Welsh footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Piquionne"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Ethiopian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ethiopian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish lawyer and politician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician,_born_1979)\" title=\"<PERSON> (politician, born 1979)\"><PERSON></a>, Swedish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician,_born_1979)\" title=\"<PERSON> (politician, born 1979)\"><PERSON></a>, Swedish lawyer and politician", "links": [{"title": "<PERSON> (politician, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician,_born_1979)"}]}, {"year": "1979", "text": "<PERSON>, Chinese actor and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Ukrainian runner", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>v<PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>vsun\"><PERSON><PERSON><PERSON></a>, Ukrainian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sun"}]}, {"year": "1980", "text": "<PERSON>, American golfer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Mexican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Halil_Alt%C4%B1ntop\" title=\"<PERSON><PERSON>ıntop\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Halil_Alt%C4%B1ntop\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halil_Alt%C4%B1ntop"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Hamit_Alt%C4%B1ntop\" title=\"<PERSON><PERSON>ınto<PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hamit_Alt%C4%B1ntop\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hamit_Alt%C4%B1ntop"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Trinidadian-American rapper and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American rapper and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian-American rapper and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j"}]}, {"year": "1982", "text": "<PERSON>, Canadian singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ryder\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Swiss race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Cameroonian footballer (d. 2014)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroonian footballer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Val%C3%A9ry_M%C3%A9zague"}]}, {"year": "1984", "text": "<PERSON>, Swedish high jumper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tregaro\" class=\"mw-redirect\" title=\"<PERSON> Green Tregaro\"><PERSON></a>, Swedish high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tregaro\" class=\"mw-redirect\" title=\"<PERSON> Green Tregaro\"><PERSON></a>, Swedish high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>regaro"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Canadian figure skater", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>echerov\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>v\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American wrestler and rapper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler and rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1986", "text": "<PERSON>, English boxer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, English boxer", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1986", "text": "<PERSON>, New Zealand-Samoan rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ese\" title=\"Sam Tagataese\"><PERSON></a>, New Zealand-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Tagataese\" title=\"Sam Tagataese\"><PERSON></a>, New Zealand-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Tagataese"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English musician and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1989", "text": "<PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian television actress and YouTuber", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_P<PERSON>hit\" title=\"<PERSON><PERSON><PERSON><PERSON> P<PERSON>hit\"><PERSON><PERSON><PERSON><PERSON></a>, Indian television actress and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian television actress and YouTuber", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>hit"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mark"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese idol, model, and actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese idol, model, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_J%C3%B5esaar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5esaar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Janari_J%C3%B5esaar"}]}, {"year": "1993", "text": "<PERSON>, American model, Miss America 2018", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mu<PERSON>\" title=\"<PERSON> Mund\"><PERSON></a>, American model, <a href=\"https://wikipedia.org/wiki/Miss_America_2018\" title=\"Miss America 2018\">Miss America 2018</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mund\" title=\"Cara Mund\"><PERSON></a>, American model, <a href=\"https://wikipedia.org/wiki/Miss_America_2018\" title=\"Miss America 2018\">Miss America 2018</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cara_Mund"}, {"title": "Miss America 2018", "link": "https://wikipedia.org/wiki/Miss_America_2018"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jordan_Obita\" title=\"<PERSON> Obit<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Obita\" title=\"<PERSON> Obita\"><PERSON></a>, English footballer", "links": [{"title": "Jordan O<PERSON>a", "link": "https://wikipedia.org/wiki/Jordan_Obita"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Anna<PERSON><PERSON><PERSON>_Robb\" title=\"AnnaSop<PERSON> Robb\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AnnaSop<PERSON>_Robb\" title=\"AnnaSophia Robb\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/AnnaSophia_Robb"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Belgian-Nigerian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian-Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Kenyan runner", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"Con<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "Conseslus <PERSON>", "link": "https://wikipedia.org/wiki/Conseslus_Ki<PERSON>ruto"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Scottish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>iji"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American ice hockey player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teague\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Teague\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1999)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Tyrus_Wheat\" title=\"Tyrus Wheat\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyrus_Wheat\" title=\"Tyrus Wheat\"><PERSON><PERSON> Wheat</a>, American football player", "links": [{"title": "Tyrus Wheat", "link": "https://wikipedia.org/wiki/Tyrus_Wheat"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "DeMario <PERSON>", "link": "https://wikipedia.org/wiki/DeMario_Douglas"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Dominican American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ron_Matos"}]}, {"year": "2000", "text": "<PERSON>, Cuban baseball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2001", "text": "<PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer,_born_2002)\" title=\"<PERSON><PERSON><PERSON> (singer, born 2002)\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer,_born_2002)\" title=\"<PERSON><PERSON><PERSON> (singer, born 2002)\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON> (singer, born 2002)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer,_born_2002)"}]}, {"year": "2004", "text": "<PERSON>, American professional wrestler", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "855", "text": "<PERSON><PERSON> of Metz, illegitimate son of <PERSON><PERSON><PERSON><PERSON> (b. 801)", "html": "855 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, illegitimate son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"Charle<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, illegitimate son of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"Charle<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 801)", "links": [{"title": "<PERSON><PERSON> of Metz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}]}, {"year": "899", "text": "<PERSON><PERSON><PERSON> of Carinthia (b. 850)", "html": "899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Carinthia\" title=\"<PERSON><PERSON><PERSON> of Carinthia\"><PERSON><PERSON><PERSON> of Carinthia</a> (b. 850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Carinthia\" title=\"<PERSON><PERSON><PERSON> of Carinthia\"><PERSON><PERSON><PERSON> of Carinthia</a> (b. 850)", "links": [{"title": "<PERSON><PERSON><PERSON> of Carinthia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Carinthia"}]}, {"year": "964", "text": "<PERSON> the Elder, Chinese queen consort", "html": "964 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_the_Elder\" title=\"Queen <PERSON> the Elder\"><PERSON> the Elder</a>, Chinese queen consort", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON>_the_Elder\" title=\"Queen Zhou the Elder\"><PERSON> the Elder</a>, Chinese queen consort", "links": [{"title": "Queen <PERSON> the Elder", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_the_Elder"}]}, {"year": "1186", "text": "<PERSON><PERSON> <PERSON>, Duke of Zähringen (b.c 1125)", "html": "1186 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Z%C3%A4hr<PERSON>\" title=\"<PERSON><PERSON> IV, Duke of Zähringen\"><PERSON><PERSON>, Duke of Zähringen</a> (b.c <a href=\"https://wikipedia.org/wiki/1125\" title=\"1125\">1125</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV,_Duke_of_Z%C3%A4hr<PERSON>\" title=\"<PERSON><PERSON> IV, Duke of Zähringen\"><PERSON><PERSON>, Duke of Zähringen</a> (b.c <a href=\"https://wikipedia.org/wiki/1125\" title=\"1125\">1125</a>)", "links": [{"title": "<PERSON><PERSON>, Duke of Zähringen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Duke_of_Z%C3%A4hringen"}, {"title": "1125", "link": "https://wikipedia.org/wiki/1125"}]}, {"year": "1292", "text": "<PERSON>, Archbishop of Canterbury", "html": "1292 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1365", "text": "<PERSON>, Duke of Opava (b. 1288)", "html": "1365 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Opava\" title=\"<PERSON>, Duke of Opava\"><PERSON>, Duke of Opava</a> (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Opava\" title=\"<PERSON>, Duke of Opava\"><PERSON>, Duke of Opava</a> (b. 1288)", "links": [{"title": "<PERSON>, Duke of Opava", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Opava"}]}, {"year": "1431", "text": "<PERSON><PERSON><PERSON>, Polish and Lithuanian princess (b. 1408)", "html": "1431 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1408%E2%80%931431)\" title=\"<PERSON><PERSON><PERSON> (1408-1431)\">He<PERSON><PERSON></a>, Polish and Lithuanian princess (b. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1408%E2%80%931431)\" title=\"<PERSON><PERSON><PERSON> (1408-1431)\">He<PERSON><PERSON></a>, Polish and Lithuanian princess (b. 1408)", "links": [{"title": "<PERSON><PERSON><PERSON> (1408-1431)", "link": "https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_(1408%E2%80%931431)"}]}, {"year": "1550", "text": "<PERSON><PERSON>, Italian humanist, poet, dramatist and diplomat (b. 1478)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian humanist, poet, dramatist and diplomat (b. 1478)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian humanist, poet, dramatist and diplomat (b. 1478)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON> the Younger, Marrano writer and martyr (b. c. 1566/1567)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, <a href=\"https://wikipedia.org/wiki/Marrano\" title=\"Mar<PERSON>\"><PERSON><PERSON></a> writer and martyr (b. <abbr title=\"circa\">c.</abbr> 1566/1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, <a href=\"https://wikipedia.org/wiki/Marrano\" title=\"Mar<PERSON>\"><PERSON><PERSON></a> writer and martyr (b. <abbr title=\"circa\">c.</abbr> 1566/1567)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_the_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rano"}]}, {"year": "1626", "text": "<PERSON>, English poet, lawyer, and politician (b. 1569)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1569)\" title=\"<PERSON> (poet, born 1569)\"><PERSON></a>, English poet, lawyer, and politician (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet,_born_1569)\" title=\"<PERSON> (poet, born 1569)\"><PERSON></a>, English poet, lawyer, and politician (b. 1569)", "links": [{"title": "<PERSON> (poet, born 1569)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet,_born_1569)"}]}, {"year": "1632", "text": "<PERSON>, Dutch astronomer and mathematician (b. 1561)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch astronomer and mathematician (b. 1561)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON>, Croatian poet (b. 1589)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet (b. 1589)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet (b. 1589)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87"}]}, {"year": "1643", "text": "<PERSON>, English politician (b. 1583)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON><PERSON>, French missionary and saint (b. 1613)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Chabanel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French missionary and saint (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Chabanel\" title=\"<PERSON><PERSON> Cha<PERSON>\"><PERSON><PERSON></a>, French missionary and saint (b. 1613)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Chabanel"}]}, {"year": "1680", "text": "<PERSON>, 1st Marquess of Dorchester, English lawyer and politician (b. 1606)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Dorchester\" title=\"<PERSON>, 1st Marquess of Dorchester\"><PERSON>, 1st Marquess of Dorchester</a>, English lawyer and politician (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Dorchester\" title=\"<PERSON>, 1st Marquess of Dorchester\"><PERSON>, 1st Marquess of Dorchester</a>, English lawyer and politician (b. 1606)", "links": [{"title": "<PERSON>, 1st Marquess of Dorchester", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Dorchester"}]}, {"year": "1691", "text": "<PERSON>, English minister, poet, and hymn-writer (b. 1615)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, poet, and hymn-writer (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister, poet, and hymn-writer (b. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French orientalist and academic (b. 1625)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>Herb<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, French orientalist and academic (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>Herb<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, French orientalist and academic (b. 1625)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barth%C3%A9lemy_d%27Herbelot"}]}, {"year": "1709", "text": "<PERSON>, French playwright and philologist (b. 1625)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and philologist (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and philologist (b. 1625)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, Princess <PERSON><PERSON> (b. 1652)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Princess <PERSON><PERSON>\"><PERSON>, Princess <PERSON><PERSON></a> (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Princess <PERSON><PERSON>\"><PERSON>, Princess <PERSON></a> (b. 1652)", "links": [{"title": "<PERSON>, Princess <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Princess_<PERSON><PERSON>"}]}, {"year": "1734", "text": "<PERSON>, English prizefighter", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prizefighter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English prizefighter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON>, French mistress of <PERSON> of <PERSON> (b. 1717)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> of France</a> (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> of France</a> (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> XV of France", "link": "https://wikipedia.org/wiki/Louis_XV_of_France"}]}, {"year": "1745", "text": "<PERSON>, French orientalist and academic (b. 1683)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and academic (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orientalist and academic (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_Fourmont"}]}, {"year": "1746", "text": "<PERSON>, English courtier and soldier (b. 1693)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and soldier (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English courtier and soldier (b. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, 1st Earl of Harrington, English politician and diplomat, Lord Lieutenant of Ireland (b. 1690)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl of Harrington\"><PERSON>, 1st Earl <PERSON> Harrington</a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1690)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Earl <PERSON> Harrington\"><PERSON>, 1st Earl of Harrington</a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1690)", "links": [{"title": "<PERSON>, 1st Earl of Harrington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1768", "text": "<PERSON>, French painter and missionary (b. 1702)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and missionary (b. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and missionary (b. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, English physician (b. 1707)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician (b. 1707)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Methodist preacher and philanthropist (b. 1739)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher and philanthropist (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Methodism\" title=\"Methodism\">Methodist</a> preacher and philanthropist (b. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Methodism", "link": "https://wikipedia.org/wiki/Methodism"}]}, {"year": "1830", "text": "<PERSON>, Swiss-French philosopher and author (b. 1767)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French philosopher and author (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French philosopher and author (b. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, Irish social reformer and temperance movement leader (b. 1790)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(temperance_reformer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (temperance reformer)\"><PERSON><PERSON><PERSON></a>, Irish social reformer and temperance movement leader (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(temperance_reformer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (temperance reformer)\"><PERSON><PERSON><PERSON></a>, Irish social reformer and temperance movement leader (b. 1790)", "links": [{"title": "<PERSON><PERSON><PERSON> (temperance reformer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(temperance_reformer)"}]}, {"year": "1859", "text": "<PERSON>, English journalist and author (b. 1785)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English mathematician and philosopher (b. 1815)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and philosopher (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Ecuadorian saint (b. 1832)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs\" title=\"Narc<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian saint (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs\" title=\"Narc<PERSON>\"><PERSON><PERSON><PERSON></a>, Ecuadorian saint (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narc<PERSON>_<PERSON>_<PERSON>%C3%BAs"}]}, {"year": "1885", "text": "<PERSON>, American businessman and philanthropist (b. 1821)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American conchologist, geologist, and publisher (b. 1792)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conchologist, geologist, and publisher (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conchologist, geologist, and publisher (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian mathematician and theorist (b. 1821)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>f<PERSON><PERSON>_<PERSON>eb<PERSON>he<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>eb<PERSON>hev\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>f<PERSON><PERSON>_<PERSON>he<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>eb<PERSON>hev\"><PERSON><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (b. 1821)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>hev"}]}, {"year": "1903", "text": "<PERSON>, English biologist, anthropologist, sociologist, and philosopher (b. 1820)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anthropologist, sociologist, and philosopher (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anthropologist, sociologist, and philosopher (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "King <PERSON> of Sweden (b. 1829)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Oscar_II\" title=\"Oscar II\">King <PERSON> of Sweden</a> (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_II\" title=\"Oscar II\">King <PERSON> of Sweden</a> (b. 1829)", "links": [{"title": "Oscar II", "link": "https://wikipedia.org/wiki/Oscar_II"}]}, {"year": "1913", "text": "<PERSON>, Belgian race car driver (b. 1868)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Swiss mountain guide (b. 1828)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss mountain guide (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss mountain guide (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Danish-German admiral (b. 1861)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German admiral (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-German admiral (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Russian author (b. 1836)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cher_Sforim\" title=\"Men<PERSON>e Mocher Sforim\"><PERSON><PERSON><PERSON></a>, Russian author (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Sforim\" title=\"Mendele Mocher Sforim\"><PERSON><PERSON><PERSON></a>, Russian author (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>_<PERSON>rim"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Bosnian Catholic archbishop (b. 1843)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian Catholic archbishop (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian Catholic archbishop (b. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American painter (b. 1852)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter (b. 1852)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON> and three other prominent Irish Republican Army officers are executed during the Irish Civil War", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other prominent <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> officers are executed during the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and three other prominent <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> officers are executed during the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "Irish Civil War", "link": "https://wikipedia.org/wiki/Irish_Civil_War"}]}, {"year": "1929", "text": "<PERSON>, Colombian politician and 8th President of Colombia (b. 1867)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian politician and 8th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian politician and 8th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1932", "text": "<PERSON>, British horticulturist and writer (b. 1843)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British horticulturist and writer (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British horticulturist and writer (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Czech-Austrian botanist and academic (b. 1856)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian botanist and academic (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Austrian botanist and academic (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Swiss author (b. 1896)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English-Canadian bishop and theologian (b. 1861)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)\" class=\"mw-redirect\" title=\"<PERSON> (bishop of Saskatchewan)\"><PERSON></a>, English-Canadian bishop and theologian (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)\" class=\"mw-redirect\" title=\"<PERSON> (bishop of Saskatchewan)\"><PERSON></a>, English-Canadian bishop and theologian (b. 1861)", "links": [{"title": "<PERSON> (bishop of Saskatchewan)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Hungarian football player and coach (b. 1885)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCrschner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player and coach (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%<PERSON>rschner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian football player and coach (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zidor_K%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American architect, Fisher Building, Packard Automotive Plant, Ford River Rouge Complex (b. 1869)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect, <a href=\"https://wikipedia.org/wiki/Fisher_Building\" title=\"Fisher Building\">Fisher Building</a>, <a href=\"https://wikipedia.org/wiki/Packard_Automotive_Plant\" title=\"Packard Automotive Plant\">Packard Automotive Plant</a>, <a href=\"https://wikipedia.org/wiki/Ford_River_Rouge_Complex\" class=\"mw-redirect\" title=\"Ford River Rouge Complex\">Ford River Rouge Complex</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American architect, <a href=\"https://wikipedia.org/wiki/Fisher_Building\" title=\"Fisher Building\">Fisher Building</a>, <a href=\"https://wikipedia.org/wiki/Packard_Automotive_Plant\" title=\"Packard Automotive Plant\">Packard Automotive Plant</a>, <a href=\"https://wikipedia.org/wiki/Ford_River_Rouge_Complex\" class=\"mw-redirect\" title=\"Ford River Rouge Complex\">Ford River Rouge Complex</a> (b. 1869)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>(architect)"}, {"title": "Fisher Building", "link": "https://wikipedia.org/wiki/Fisher_Building"}, {"title": "Packard Automotive Plant", "link": "https://wikipedia.org/wiki/Packard_Automotive_Plant"}, {"title": "Ford River Rouge Complex", "link": "https://wikipedia.org/wiki/Ford_River_Rouge_Complex"}]}, {"year": "1952", "text": "<PERSON>, English sailor (b. 1874)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, French artist, photographer, and writer (b. 1894)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, photographer, and writer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French artist, photographer, and writer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress (b. 1904)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American lawyer and politician (b. 1888)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON> Speaker, American baseball player and manager (b. 1888)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Tri<PERSON>_Speaker\" title=\"Tris Speaker\"><PERSON><PERSON> Speaker</a>, American baseball player and manager (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tri<PERSON>_Speaker\" title=\"Tris Speaker\"><PERSON><PERSON> Speaker</a>, American baseball player and manager (b. 1888)", "links": [{"title": "Tris Speaker", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Speaker"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Thai field marshal and politician, 11th Prime Minister of Thailand (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai field marshal and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1966", "text": "<PERSON>, American playwright, author, and critic (b. 1899)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ward_Morehouse\" title=\"Ward Morehouse\"><PERSON></a>, American playwright, author, and critic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ward_Morehouse\" title=\"Ward Morehouse\"><PERSON></a>, American playwright, author, and critic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ward_Morehouse"}]}, {"year": "1971", "text": "<PERSON>, Russian geographer and explorer (b. 1903)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian geographer and explorer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian geographer and explorer (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Greek poet and critic (b. 1896)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and critic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek poet and critic (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eni_Ourani"}]}, {"year": "1975", "text": "<PERSON>, New Zealand bass player (b. 1948)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand bass player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand bass player (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Ukrainian-Israeli educator and politician, 4th Prime Minister of Israel (b. 1898)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1980", "text": "<PERSON>, English singer-songwriter and guitarist  (b. 1940)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Surinamese journalist and politician (b. 1951)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese journalist and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Surinamese journalist and politician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Surinamese footballer and manager (b. 1924)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Kamperveen\" title=\"<PERSON>\"><PERSON></a>, Surinamese footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Kamperveen\" title=\"<PERSON>\"><PERSON></a>, Surinamese footballer and manager (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>en"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter and race car driver (b. 1925)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and race car driver (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and race car driver (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Israel Defense Forces fifth Chief of Staff (b. 1919)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israel Defense Forces fifth Chief of Staff (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israel Defense Forces fifth Chief of Staff (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, New Zealand farmer and politician, 26th Prime Minister of New Zealand (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand farmer and politician, 26th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1983", "text": "<PERSON>, American actor (b. 1919)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pickens\"><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Slim Pickens\"><PERSON></a>, American actor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ens"}]}, {"year": "1984", "text": "<PERSON>, American actor (b. 1903)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American militant leader, founded The Order (b. 1953)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American militant leader, founded <a href=\"https://wikipedia.org/wiki/The_Order_(white_supremacist_group)\" title=\"The Order (white supremacist group)\">The Order</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American militant leader, founded <a href=\"https://wikipedia.org/wiki/The_Order_(white_supremacist_group)\" title=\"The Order (white supremacist group)\">The Order</a> (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Order (white supremacist group)", "link": "https://wikipedia.org/wiki/The_Order_(white_supremacist_group)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English drummer (b. 1960)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English drummer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, English drummer (b. 1960)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/Ra<PERSON>_(musician)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish general (b. 1911)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish general (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American trumpet player and composer (b. 1911)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American journalist (b. 1917)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Russian weightlifter (b. 1933)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian weightlifter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian weightlifter (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian singer-songwriter and pianist (b. 1927)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and pianist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and pianist (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor (b. 1950)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 47th <PERSON><PERSON><PERSON><PERSON> (b. 1938)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>yoshi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 47th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 47th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>yoshi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1997", "text": "<PERSON>, American clown and actor (b. 1922)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American clown and actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American clown and actor (b. 1922)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Hungarian poet and author (b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/P%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and author (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Bosnian basketball player and coach (b. 1954)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Mirza_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian basketball player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mirza_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian basketball player and coach (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mirza_Deliba%C5%A1i%C4%87"}]}, {"year": "2001", "text": "<PERSON>, American computer scientist and programmer (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Cuban pianist (b. 1919)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)\" title=\"<PERSON><PERSON><PERSON> (pianist)\"><PERSON><PERSON><PERSON></a>, Cuban pianist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)\" title=\"<PERSON><PERSON><PERSON> (pianist)\"><PERSON><PERSON><PERSON></a>, Cuban pianist (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON> (pianist)", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Gonz%C3%<PERSON><PERSON><PERSON>_(pianist)"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist (b. 1966)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Darrell\" title=\"<PERSON><PERSON><PERSON> Darrell\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Darrell\" title=\"<PERSON><PERSON><PERSON> Darrell\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, British barrister and judge (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British barrister and judge (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British barrister and judge (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_He<PERSON>bron"}]}, {"year": "2006", "text": "<PERSON>, American singer (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Dominican baseball player (b. 1959)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Uribe\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Uribe\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Uribe"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Mexican journalist (b. 1983)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Gerard<PERSON>_G<PERSON>c%C3%ADa_Pimentel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gerard<PERSON>_<PERSON>%C3%ADa_Pimentel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican journalist (b. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerardo_Garc%C3%ADa_Pimentel"}]}, {"year": "2008", "text": "<PERSON>, English voice actor, director, producer, and screenwriter (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>gate\"><PERSON></a>, English voice actor, director, producer, and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Postgate\"><PERSON></a>, English voice actor, director, producer, and screenwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_<PERSON>gate"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1930)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Dominican singer-songwriter and guitarist (b. 1952)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Dominican singer-songwriter and guitarist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_D%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Dominican singer-songwriter and guitarist (b. 1952)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/Luis_D%C3%<PERSON><PERSON>_(composer)"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1987)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" class=\"mw-redirect\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1987)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2012", "text": "<PERSON>, Scottish-English 16th General of The Salvation Army (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English 16th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English 16th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "2012", "text": "<PERSON>, American boxer (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian-English chemist and academic, Nobel Prize laureate (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Hungarian composer and academic (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Szokolay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian composer and academic (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Szokolay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian composer and academic (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Szokolay"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and diplomat (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and diplomat (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian lawyer and politician (b. 1951)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and coach (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Norwegian organist and composer (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t\" title=\"<PERSON><PERSON> Nys<PERSON>\"><PERSON><PERSON></a>, Norwegian organist and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"K<PERSON> Nys<PERSON>t\"><PERSON><PERSON></a>, Norwegian organist and composer (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Nystedt"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, American soprano and actress (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American soprano and actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American soprano and actress (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English footballer and coach (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman, co-founded The North Face and Esprit Holdings (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_North_Face\" title=\"The North Face\">The North Face</a> and <a href=\"https://wikipedia.org/wiki/Esprit_Holdings\" title=\"Esprit Holdings\">Esprit Holdings</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_North_Face\" title=\"The North Face\">The North Face</a> and <a href=\"https://wikipedia.org/wiki/Esprit_Holdings\" title=\"Esprit Holdings\">Esprit Holdings</a> (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The North Face", "link": "https://wikipedia.org/wiki/The_North_Face"}, {"title": "Esprit Holdings", "link": "https://wikipedia.org/wiki/Esprit_Holdings"}]}, {"year": "2015", "text": "<PERSON>, American author, poet, and actor (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and actor (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English-Hong Kong educator and politician (b. 1913)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Hong Kong educator and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Hong Kong educator and politician (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American astronaut and senator, first American to go into orbit (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and senator, first American to go into orbit (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut and senator, first American to go into orbit (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, English physician, geneticist, and academic (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, geneticist, and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, geneticist, and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Auber<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>jon<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American rapper, singer and songwriter (b. 1998)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Juice_Wrld\" title=\"Juice Wrld\"><PERSON><PERSON>rld</a>, American rapper, singer and songwriter (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ice_Wrld\" title=\"Juice Wrld\"><PERSON><PERSON>rld</a>, American rapper, singer and songwriter (b. 1998)", "links": [{"title": "Juice Wrld", "link": "https://wikipedia.org/wiki/Juice_Wrld"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American puppeteer and actor (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American puppeteer and actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American puppeteer and actor (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ney"}]}, {"year": "2021", "text": "<PERSON>, Jamaican bass guitarist and record producer (b. 1953)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican bass guitarist and record producer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shakespeare\"><PERSON></a>, Jamaican bass guitarist and record producer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American actor (b. 1941)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Neal\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ryan_O%27Neal"}]}, {"year": "2024", "text": "<PERSON>, American actress (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American businessman and politician (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}