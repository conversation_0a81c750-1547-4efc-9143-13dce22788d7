{"date": "September 17", "url": "https://wikipedia.org/wiki/September_17", "data": {"Events": [{"year": "1111", "text": "Highest Galician nobility led by <PERSON> and the bishop <PERSON> crown <PERSON> as \"King of Galicia\".", "html": "1111 - Highest <a href=\"https://wikipedia.org/wiki/Kingdom_of_Galicia\" title=\"Kingdom of Galicia\">Galician</a> nobility led by <a href=\"https://wikipedia.org/wiki/Pedro_Fr%C3%B3ila<PERSON>_de_Traba\" title=\"<PERSON>\"><PERSON></a> and the bishop <a href=\"https://wikipedia.org/wiki/Diego_Gelm%C3%ADrez\" title=\"<PERSON>el<PERSON>rez\"><PERSON></a> crown <a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> VII of León and Castile\"><PERSON> VII</a> as \"King of Galicia\".", "no_year_html": "Highest <a href=\"https://wikipedia.org/wiki/Kingdom_of_Galicia\" title=\"Kingdom of Galicia\">Galician</a> nobility led by <a href=\"https://wikipedia.org/wiki/Pedro_Fr%C3%B3ila<PERSON>_de_Traba\" title=\"<PERSON>\"><PERSON></a> and the bishop <a href=\"https://wikipedia.org/wiki/Diego_Gelm%C3%ADrez\" title=\"<PERSON> Gelmírez\"><PERSON></a> crown <a href=\"https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile\" title=\"<PERSON> of León and Castile\"><PERSON> VII</a> as \"King of Galicia\".", "links": [{"title": "Kingdom of Galicia", "link": "https://wikipedia.org/wiki/Kingdom_of_Galicia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Fr%C3%B3<PERSON><PERSON>_de_<PERSON>raba"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Gelm%C3%ADrez"}, {"title": "Alfonso VII of León and Castile", "link": "https://wikipedia.org/wiki/Alfonso_VII_of_Le%C3%B3n_and_Castile"}]}, {"year": "1176", "text": "The Battle of Myriokephalon is the last attempt by the Byzantine Empire to recover central Anatolia from the Seljuk Turks.", "html": "1176 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Myriokephalon\" title=\"Battle of Myriokephalon\">Battle of Myriokephalon</a> is the last attempt by the Byzantine Empire to recover central Anatolia from the Seljuk Turks.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Myriokephalon\" title=\"Battle of Myriokephalon\">Battle of Myriokephalon</a> is the last attempt by the Byzantine Empire to recover central Anatolia from the Seljuk Turks.", "links": [{"title": "Battle of Myriokephalon", "link": "https://wikipedia.org/wiki/Battle_of_Myriokephalon"}]}, {"year": "1382", "text": "<PERSON> the Great's daughter, <PERSON>, is crowned \"king\" of Hungary.", "html": "1382 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>'s daughter, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Hungary\" title=\"<PERSON>, Queen of Hungary\"><PERSON></a>, is crowned \"king\" of Hungary.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" class=\"mw-redirect\" title=\"<PERSON> the Great\"><PERSON> the Great</a>'s daughter, <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Hungary\" title=\"<PERSON>, Queen of Hungary\"><PERSON></a>, is crowned \"king\" of Hungary.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "<PERSON>, Queen of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Hungary"}]}, {"year": "1462", "text": "Thirteen Years' War: A Polish army under <PERSON><PERSON><PERSON> decisively defeats the Teutonic Order at the Battle of Świecino.", "html": "1462 - <a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)\" class=\"mw-redirect\" title=\"Thirteen Years' War (1454-66)\">Thirteen Years' War</a>: A Polish army under <PERSON><PERSON><PERSON> decisively defeats the Teutonic Order at the <a href=\"https://wikipedia.org/wiki/Battle_of_%C5%9Awiecino\" title=\"Battle of Świecino\">Battle of Świecino</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)\" class=\"mw-redirect\" title=\"Thirteen Years' War (1454-66)\">Thirteen Years' War</a>: A Polish army under <PERSON><PERSON><PERSON> decisively defeats the Teutonic Order at the <a href=\"https://wikipedia.org/wiki/Battle_of_%C5%9Awiecino\" title=\"Battle of Świecino\">Battle of Świecino</a>.", "links": [{"title": "Thirteen Years' War (1454-66)", "link": "https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)"}, {"title": "Battle of Świecino", "link": "https://wikipedia.org/wiki/Battle_of_%C5%9Awiecino"}]}, {"year": "1543", "text": "The first Finnish-language book, the Abckiria by <PERSON><PERSON><PERSON>, is published in Stockholm.", "html": "1543 - The first <a href=\"https://wikipedia.org/wiki/Finnish-language\" class=\"mw-redirect\" title=\"Finnish-language\">Finnish-language</a> book, the <i><a href=\"https://wikipedia.org/wiki/Abckiria\" title=\"Abcki<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/Mi<PERSON><PERSON>_Agricola\" title=\"Mika<PERSON> Agricola\"><PERSON><PERSON><PERSON></a>, is published in <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Finnish-language\" class=\"mw-redirect\" title=\"Finnish-language\">Finnish-language</a> book, the <i><a href=\"https://wikipedia.org/wiki/Abckiria\" title=\"Abckiria\"><PERSON><PERSON><PERSON><PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/Mika<PERSON>_<PERSON>gricola\" title=\"Mika<PERSON>gricola\"><PERSON><PERSON><PERSON></a>, is published in <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>.", "links": [{"title": "Finnish-language", "link": "https://wikipedia.org/wiki/Finnish-language"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ab<PERSON>ria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}]}, {"year": "1577", "text": "The Treaty of Bergerac is signed between King <PERSON> of France and the Huguenots.", "html": "1577 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Bergerac\" title=\"Treaty of Bergerac\">Treaty of Bergerac</a> is signed between King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> of France</a> and the Huguenots.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Bergerac\" title=\"Treaty of Bergerac\">Treaty of Bergerac</a> is signed between King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> of France</a> and the Huguenots.", "links": [{"title": "Treaty of Bergerac", "link": "https://wikipedia.org/wiki/Treaty_of_Bergerac"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1620", "text": "Polish-Ottoman War: The Ottoman Empire defeats the Polish-Lithuanian Commonwealth during the Battle of Cecora.", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Ottoman_War_(1620%E2%80%9321)\" class=\"mw-redirect\" title=\"Polish-Ottoman War (1620-21)\">Polish-Ottoman War</a>: The Ottoman Empire defeats the Polish-Lithuanian Commonwealth during the <a href=\"https://wikipedia.org/wiki/Battle_of_Cecora_(1620)\" title=\"Battle of Cecora (1620)\">Battle of Cecora</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Ottoman_War_(1620%E2%80%9321)\" class=\"mw-redirect\" title=\"Polish-Ottoman War (1620-21)\">Polish-Ottoman War</a>: The Ottoman Empire defeats the Polish-Lithuanian Commonwealth during the <a href=\"https://wikipedia.org/wiki/Battle_of_Cecora_(1620)\" title=\"Battle of Cecora (1620)\">Battle of Cecora</a>.", "links": [{"title": "Polish-Ottoman War (1620-21)", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Ottoman_War_(1620%E2%80%9321)"}, {"title": "Battle of Cecora (1620)", "link": "https://wikipedia.org/wiki/Battle_of_Cecora_(1620)"}]}, {"year": "1631", "text": "Sweden wins a major victory at the Battle of Breitenfeld against the Holy Roman Empire during the Thirty Years' War.", "html": "1631 - Sweden wins a major victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Breitenfeld_(1631)\" title=\"Battle of Breitenfeld (1631)\">Battle of Breitenfeld</a> against the Holy Roman Empire during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "Sweden wins a major victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Breitenfeld_(1631)\" title=\"Battle of Breitenfeld (1631)\">Battle of Breitenfeld</a> against the Holy Roman Empire during the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "Battle of Breitenfeld (1631)", "link": "https://wikipedia.org/wiki/Battle_of_Breitenfeld_(1631)"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1658", "text": "The Battle of Vilanova is fought between Portugal and Spain during the Portuguese Restoration War.", "html": "1658 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Vilanova\" title=\"Battle of Vilanova\">Battle of Vilanova</a> is fought between Portugal and Spain during the <a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Vilanova\" title=\"Battle of Vilanova\">Battle of Vilanova</a> is fought between Portugal and Spain during the <a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>.", "links": [{"title": "Battle of Vilanova", "link": "https://wikipedia.org/wiki/Battle_of_Vilanova"}, {"title": "Portuguese Restoration War", "link": "https://wikipedia.org/wiki/Portuguese_Restoration_War"}]}, {"year": "1683", "text": "<PERSON><PERSON> writes a letter to the Royal Society describing \"animalcules\", later known as protozoa.", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> writes a letter to the Royal Society describing \"<a href=\"https://wikipedia.org/wiki/Animalcule\" title=\"Animalcule\">animalcules</a>\", later known as <a href=\"https://wikipedia.org/wiki/Protozoa\" title=\"Protozoa\">protozoa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> writes a letter to the Royal Society describing \"<a href=\"https://wikipedia.org/wiki/Animalcule\" title=\"Animalcule\">animalcules</a>\", later known as <a href=\"https://wikipedia.org/wiki/Protozoa\" title=\"Protozoa\">protozoa</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Animalcule", "link": "https://wikipedia.org/wiki/Animalcule"}, {"title": "Protozoa", "link": "https://wikipedia.org/wiki/Protozoa"}]}, {"year": "1775", "text": "American Revolutionary War: The invasion of Quebec by the Continental Army begins with the Siege of Fort St. Jean.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Invasion_of_Quebec_(1775)\" title=\"Invasion of Quebec (1775)\">invasion of Quebec</a> by the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> begins with the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_St._Jean\" title=\"Siege of Fort St. Jean\">Siege of Fort St. Jean</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Invasion_of_Quebec_(1775)\" title=\"Invasion of Quebec (1775)\">invasion of Quebec</a> by the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> begins with the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_St._Jean\" title=\"Siege of Fort St. Jean\">Siege of Fort St. Jean</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Invasion of Quebec (1775)", "link": "https://wikipedia.org/wiki/Invasion_of_Quebec_(1775)"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "Siege of Fort St. Jean", "link": "https://wikipedia.org/wiki/Siege_of_Fort_St._Jean"}]}, {"year": "1776", "text": "The Presidio of San Francisco is founded in New Spain.", "html": "1776 - The <a href=\"https://wikipedia.org/wiki/Presidio_of_San_Francisco\" title=\"Presidio of San Francisco\">Presidio of San Francisco</a> is founded in New Spain.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Presidio_of_San_Francisco\" title=\"Presidio of San Francisco\">Presidio of San Francisco</a> is founded in New Spain.", "links": [{"title": "Presidio of San Francisco", "link": "https://wikipedia.org/wiki/Presidio_of_San_Francisco"}]}, {"year": "1778", "text": "The Treaty of Fort Pitt is signed. It is the first formal treaty between the United States and a Native American tribe.", "html": "1778 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Pitt\" title=\"Treaty of Fort Pitt\">Treaty of Fort Pitt</a> is signed. It is the first formal treaty between the United States and a Native American tribe.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fort_Pitt\" title=\"Treaty of Fort Pitt\">Treaty of Fort Pitt</a> is signed. It is the first formal treaty between the United States and a Native American tribe.", "links": [{"title": "Treaty of Fort Pitt", "link": "https://wikipedia.org/wiki/Treaty_of_Fort_Pitt"}]}, {"year": "1787", "text": "The United States Constitution is signed at Independence Hall in Philadelphia, bringing the Constitutional Convention to an end.", "html": "1787 - The <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">United States Constitution</a> is <a href=\"https://wikipedia.org/wiki/Signing_of_the_United_States_Constitution\" title=\"Signing of the United States Constitution\">signed</a> at <a href=\"https://wikipedia.org/wiki/Independence_Hall\" title=\"Independence Hall\">Independence Hall</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, bringing the <a href=\"https://wikipedia.org/wiki/Constitutional_Convention_(United_States)\" title=\"Constitutional Convention (United States)\">Constitutional Convention</a> to an end.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constitution_of_the_United_States\" title=\"Constitution of the United States\">United States Constitution</a> is <a href=\"https://wikipedia.org/wiki/Signing_of_the_United_States_Constitution\" title=\"Signing of the United States Constitution\">signed</a> at <a href=\"https://wikipedia.org/wiki/Independence_Hall\" title=\"Independence Hall\">Independence Hall</a> in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, bringing the <a href=\"https://wikipedia.org/wiki/Constitutional_Convention_(United_States)\" title=\"Constitutional Convention (United States)\">Constitutional Convention</a> to an end.", "links": [{"title": "Constitution of the United States", "link": "https://wikipedia.org/wiki/Constitution_of_the_United_States"}, {"title": "Signing of the United States Constitution", "link": "https://wikipedia.org/wiki/Signing_of_the_United_States_Constitution"}, {"title": "Independence Hall", "link": "https://wikipedia.org/wiki/Independence_Hall"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "Constitutional Convention (United States)", "link": "https://wikipedia.org/wiki/Constitutional_Convention_(United_States)"}]}, {"year": "1793", "text": "War of the Pyrenees: France defeats a Spanish force at the Battle of Peyrestortes.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pyrenees\" title=\"War of the Pyrenees\">War of the Pyrenees</a>: France defeats a Spanish force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Peyrestortes\" title=\"Battle of Peyrestortes\">Battle of Peyrestortes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pyrenees\" title=\"War of the Pyrenees\">War of the Pyrenees</a>: France defeats a Spanish force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Peyrestortes\" title=\"Battle of Peyrestortes\">Battle of Peyrestortes</a>.", "links": [{"title": "War of the Pyrenees", "link": "https://wikipedia.org/wiki/War_of_the_Pyrenees"}, {"title": "Battle of Peyrestortes", "link": "https://wikipedia.org/wiki/Battle_of_Peyrestortes"}]}, {"year": "1794", "text": "Flanders Campaign: France completes its conquest of the Austrian Netherlands at the Battle of Sprimont.", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a>: France completes its conquest of the Austrian Netherlands at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sprimont\" title=\"Battle of Sprimont\">Battle of Sprimont</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition\" title=\"Low Countries theatre of the War of the First Coalition\">Flanders Campaign</a>: France completes its conquest of the Austrian Netherlands at the <a href=\"https://wikipedia.org/wiki/Battle_of_Sprimont\" title=\"Battle of Sprimont\">Battle of Sprimont</a>.", "links": [{"title": "Low Countries theatre of the War of the First Coalition", "link": "https://wikipedia.org/wiki/Low_Countries_theatre_of_the_War_of_the_First_Coalition"}, {"title": "Battle of Sprimont", "link": "https://wikipedia.org/wiki/Battle_of_Sprimont"}]}, {"year": "1809", "text": "Peace between Sweden and Russia in the Finnish War; the territory that will become Finland is ceded to Russia by the Treaty of Fredrikshamn.", "html": "1809 - Peace between Sweden and Russia in the <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>; the territory that will become Finland is ceded to Russia by the <a href=\"https://wikipedia.org/wiki/Treaty_of_Fredrikshamn\" title=\"Treaty of Fredrikshamn\">Treaty of Fredrikshamn</a>.", "no_year_html": "Peace between Sweden and Russia in the <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>; the territory that will become Finland is ceded to Russia by the <a href=\"https://wikipedia.org/wiki/Treaty_of_Fredrikshamn\" title=\"Treaty of Fredrikshamn\">Treaty of Fredrikshamn</a>.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "Treaty of Fredrikshamn", "link": "https://wikipedia.org/wiki/Treaty_of_Fredrikshamn"}]}, {"year": "1849", "text": "American abolitionist <PERSON> escapes from slavery.", "html": "1849 - American abolitionist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from slavery.", "no_year_html": "American abolitionist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> escapes from slavery.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON> declares himself \"<PERSON>, Emperor of the United States.\"", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares himself \"<PERSON>, Emperor of the United States.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares himself \"<PERSON>, Emperor of the United States.\"", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "Argentine Civil Wars: The State of Buenos Aires defeats the Argentine Confederation at the Battle of Pavón.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Argentine_Civil_Wars\" title=\"Argentine Civil Wars\">Argentine Civil Wars</a>: The State of Buenos Aires defeats the Argentine Confederation at the <a href=\"https://wikipedia.org/wiki/Battle_of_Pav%C3%B3n\" title=\"Battle of Pavón\">Battle of Pavón</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Argentine_Civil_Wars\" title=\"Argentine Civil Wars\">Argentine Civil Wars</a>: The State of Buenos Aires defeats the Argentine Confederation at the <a href=\"https://wikipedia.org/wiki/Battle_of_Pav%C3%B3n\" title=\"Battle of Pavón\">Battle of Pavón</a>.", "links": [{"title": "Argentine Civil Wars", "link": "https://wikipedia.org/wiki/Argentine_Civil_Wars"}, {"title": "Battle of Pavón", "link": "https://wikipedia.org/wiki/Battle_of_Pav%C3%B3n"}]}, {"year": "1862", "text": "American Civil War: <PERSON>'s Army of the Potomac halts the first invasion of the North by <PERSON> and his Army of Northern Virginia in the single-day Battle of Antietam, the bloodiest day in American military history.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> halts the first <a href=\"https://wikipedia.org/wiki/Maryland_campaign\" title=\"Maryland campaign\">invasion</a> of the <a href=\"https://wikipedia.org/wiki/Northern_United_States#American_Civil_War\" title=\"Northern United States\">North</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> in the single-day <a href=\"https://wikipedia.org/wiki/Battle_of_Antietam\" title=\"Battle of Antietam\">Battle of Antietam</a>, the bloodiest day in American military history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> halts the first <a href=\"https://wikipedia.org/wiki/Maryland_campaign\" title=\"Maryland campaign\">invasion</a> of the <a href=\"https://wikipedia.org/wiki/Northern_United_States#American_Civil_War\" title=\"Northern United States\">North</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> in the single-day <a href=\"https://wikipedia.org/wiki/Battle_of_Antietam\" title=\"Battle of Antietam\">Battle of Antietam</a>, the bloodiest day in American military history.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "Maryland campaign", "link": "https://wikipedia.org/wiki/Maryland_campaign"}, {"title": "Northern United States", "link": "https://wikipedia.org/wiki/Northern_United_States#American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Battle of Antietam", "link": "https://wikipedia.org/wiki/Battle_of_Antietam"}]}, {"year": "1862", "text": "American Civil War: The Allegheny Arsenal explosion in Lawrenceville, Pennsylvania results in the single largest civilian disaster during the war.", "html": "1862 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Allegheny_Arsenal\" title=\"Allegheny Arsenal\">Allegheny Arsenal</a> explosion in <a href=\"https://wikipedia.org/wiki/Lawrenceville_(Pittsburgh)\" title=\"Lawrenceville (Pittsburgh)\">Lawrenceville, Pennsylvania</a> results in the single largest civilian disaster during the war.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Allegheny_Arsenal\" title=\"Allegheny Arsenal\">Allegheny Arsenal</a> explosion in <a href=\"https://wikipedia.org/wiki/Lawrenceville_(Pittsburgh)\" title=\"Lawrenceville (Pittsburgh)\">Lawrenceville, Pennsylvania</a> results in the single largest civilian disaster during the war.", "links": [{"title": "Allegheny Arsenal", "link": "https://wikipedia.org/wiki/Allegheny_Arsenal"}, {"title": "Lawrenceville (Pittsburgh)", "link": "https://wikipedia.org/wiki/Lawrenceville_(Pittsburgh)"}]}, {"year": "1894", "text": "Battle of the Yalu River, the largest naval engagement of the First Sino-Japanese War.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Yalu_River_(1894)\" title=\"Battle of the Yalu River (1894)\">Battle of the Yalu River</a>, the largest naval engagement of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Yalu_River_(1894)\" title=\"Battle of the Yalu River (1894)\">Battle of the Yalu River</a>, the largest naval engagement of the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>.", "links": [{"title": "Battle of the Yalu River (1894)", "link": "https://wikipedia.org/wiki/Battle_of_the_Yalu_River_(1894)"}, {"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}]}, {"year": "1900", "text": "Philippine-American War: Filipinos under <PERSON> defeat Americans under Colonel <PERSON>. at Mabitac.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>: Filipinos under <PERSON> defeat Americans under Colonel <PERSON>. at <a href=\"https://wikipedia.org/wiki/Battle_of_Mabitac\" title=\"Battle of Mabitac\">Mabitac</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>: Filipinos under <PERSON> defeat Americans under Colonel <PERSON>. at <a href=\"https://wikipedia.org/wiki/Battle_of_Mabitac\" title=\"Battle of Mabitac\">Mabitac</a>.", "links": [{"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}, {"title": "Battle of Mabitac", "link": "https://wikipedia.org/wiki/Battle_of_Mabitac"}]}, {"year": "1901", "text": "Second Boer War: A Boer column defeats a British force at the Battle of Blood River Poort.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: A Boer column defeats a British force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Blood_River_Poort\" title=\"Battle of Blood River Poort\">Battle of Blood River Poort</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: A Boer column defeats a British force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Blood_River_Poort\" title=\"Battle of Blood River Poort\">Battle of Blood River Poort</a>.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Battle of Blood River Poort", "link": "https://wikipedia.org/wiki/Battle_of_Blood_River_Poort"}]}, {"year": "1901", "text": "Second Boer War: Boers capture a squadron of the 17th Lancers at the Battle of Elands River.", "html": "1901 - Second Boer War: Boers capture a squadron of the 17th Lancers at the <a href=\"https://wikipedia.org/wiki/Battle_of_Elands_River_(1901)\" title=\"Battle of Elands River (1901)\">Battle of Elands River</a>.", "no_year_html": "Second Boer War: Boers capture a squadron of the 17th Lancers at the <a href=\"https://wikipedia.org/wiki/Battle_of_Elands_River_(1901)\" title=\"Battle of Elands River (1901)\">Battle of Elands River</a>.", "links": [{"title": "Battle of Elands River (1901)", "link": "https://wikipedia.org/wiki/Battle_of_Elands_River_(1901)"}]}, {"year": "1908", "text": "The Wright Flyer flown by <PERSON><PERSON>, with Lieutenant <PERSON> as passenger, crashes, killing <PERSON><PERSON>, who becomes the first airplane fatality.", "html": "1908 - The Wright Flyer flown by <PERSON><PERSON>, with Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as passenger, crashes, killing <PERSON><PERSON>, who becomes the first airplane fatality.", "no_year_html": "The Wright Flyer flown by <PERSON><PERSON>, with Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as passenger, crashes, killing <PERSON><PERSON>, who becomes the first airplane fatality.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON> becomes Prime Minister of Australia for the third time.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Prime Minister of Australia for the third time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Prime Minister of Australia for the third time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "World War I: The Race to the Sea begins.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Race_to_the_Sea\" title=\"Race to the Sea\">Race to the Sea</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Race_to_the_Sea\" title=\"Race to the Sea\">Race to the Sea</a> begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Race to the Sea", "link": "https://wikipedia.org/wiki/Race_to_the_Sea"}]}, {"year": "1916", "text": "World War I: <PERSON> (\"The <PERSON>\"), a flying ace of the German Luftstreitkräfte, wins his first aerial combat near Cambrai, France.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"The Red Baron\"), a flying ace of the German Luftstreitkräfte, wins his first aerial combat near Cambrai, France.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"The Red Baron\"), a flying ace of the German Luftstreitkräfte, wins his first aerial combat near Cambrai, France.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "The National Football League is organized as the American Professional Football Association in Canton, Ohio.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> is organized as the American Professional Football Association in Canton, Ohio.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> is organized as the American Professional Football Association in Canton, Ohio.", "links": [{"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}]}, {"year": "1924", "text": "The Border Protection Corps is established in the Second Polish Republic for the defence of the eastern border against armed Soviet raids and local bandits.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/Border_Protection_Corps\" title=\"Border Protection Corps\">Border Protection Corps</a> is established in the Second Polish Republic for the defence of the eastern border against armed Soviet raids and local bandits.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Border_Protection_Corps\" title=\"Border Protection Corps\">Border Protection Corps</a> is established in the Second Polish Republic for the defence of the eastern border against armed Soviet raids and local bandits.", "links": [{"title": "Border Protection Corps", "link": "https://wikipedia.org/wiki/Border_Protection_Corps"}]}, {"year": "1928", "text": "The Okeechobee hurricane strikes southeastern Florida, killing more than 2,500 people.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/1928_Okeechobee_hurricane\" title=\"1928 Okeechobee hurricane\">Okeechobee hurricane</a> strikes southeastern Florida, killing more than 2,500 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1928_Okeechobee_hurricane\" title=\"1928 Okeechobee hurricane\">Okeechobee hurricane</a> strikes southeastern Florida, killing more than 2,500 people.", "links": [{"title": "1928 Okeechobee hurricane", "link": "https://wikipedia.org/wiki/1928_Okeechobee_hurricane"}]}, {"year": "1930", "text": "The Kurdish Ararat rebellion is suppressed by the Turks.", "html": "1930 - The Kurdish <a href=\"https://wikipedia.org/wiki/Ararat_rebellion\" title=\"Ararat rebellion\">Ararat rebellion</a> is suppressed by the Turks.", "no_year_html": "The Kurdish <a href=\"https://wikipedia.org/wiki/Ararat_rebellion\" title=\"Ararat rebellion\">Ararat rebellion</a> is suppressed by the Turks.", "links": [{"title": "Ararat rebellion", "link": "https://wikipedia.org/wiki/Ararat_rebellion"}]}, {"year": "1932", "text": "A speech by <PERSON><PERSON><PERSON> leads to the escalation of the Leticia Incident.", "html": "1932 - A speech by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads to the escalation of the <a href=\"https://wikipedia.org/wiki/Leticia_Incident\" class=\"mw-redirect\" title=\"Leticia Incident\">Leticia Incident</a>.", "no_year_html": "A speech by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> leads to the escalation of the <a href=\"https://wikipedia.org/wiki/Leticia_Incident\" class=\"mw-redirect\" title=\"Leticia Incident\">Leticia Incident</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laureano_G%C3%B3mez"}, {"title": "Leticia Incident", "link": "https://wikipedia.org/wiki/Leticia_Incident"}]}, {"year": "1935", "text": "The Niagara Gorge Railroad ceases operations after a rockslide.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Niagara_Gorge_Railroad\" title=\"Niagara Gorge Railroad\">Niagara Gorge Railroad</a> ceases operations after a rockslide.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Niagara_Gorge_Railroad\" title=\"Niagara Gorge Railroad\">Niagara Gorge Railroad</a> ceases operations after a rockslide.", "links": [{"title": "Niagara Gorge Railroad", "link": "https://wikipedia.org/wiki/Niagara_Gorge_Railroad"}]}, {"year": "1939", "text": "World War II: The Soviet invasion of Poland begins.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_invasion_of_Poland\" title=\"Soviet invasion of Poland\">Soviet invasion of Poland</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_invasion_of_Poland\" title=\"Soviet invasion of Poland\">Soviet invasion of Poland</a> begins.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Soviet invasion of Poland", "link": "https://wikipedia.org/wiki/Soviet_invasion_of_Poland"}]}, {"year": "1939", "text": "World War II: German submarine U-29 sinks the British aircraft carrier HMS Courageous.", "html": "1939 - World War II: <a href=\"https://wikipedia.org/wiki/German_submarine_U-29_(1936)\" title=\"German submarine U-29 (1936)\">German submarine <i>U-29</i></a> sinks the British aircraft carrier <a href=\"https://wikipedia.org/wiki/HMS_Courageous_(50)\" title=\"HMS Courageous (50)\">HMS <i>Courageous</i></a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/German_submarine_U-29_(1936)\" title=\"German submarine U-29 (1936)\">German submarine <i>U-29</i></a> sinks the British aircraft carrier <a href=\"https://wikipedia.org/wiki/HMS_Courageous_(50)\" title=\"HMS Courageous (50)\">HMS <i>Courageous</i></a>.", "links": [{"title": "German submarine U-<PERSON> (1936)", "link": "https://wikipedia.org/wiki/German_submarine_U-29_(1936)"}, {"title": "HMS Courageous (50)", "link": "https://wikipedia.org/wiki/HMS_Courageous_(50)"}]}, {"year": "1940", "text": "World War II: Due to setbacks in the Battle of Britain and approaching autumn weather, Hitler postpones Operation Sea Lion.", "html": "1940 - World War II: Due to setbacks in the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a> and approaching autumn weather, <PERSON> postpones <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>.", "no_year_html": "World War II: Due to setbacks in the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a> and approaching autumn weather, <PERSON> postpones <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>.", "links": [{"title": "Battle of Britain", "link": "https://wikipedia.org/wiki/Battle_of_Britain"}, {"title": "Operation Sea Lion", "link": "https://wikipedia.org/wiki/Operation_Sea_Lion"}]}, {"year": "1941", "text": "World War II: A decree of the Soviet State Committee of Defense restores compulsory military training.", "html": "1941 - World War II: A decree of the Soviet State Committee of Defense restores <a href=\"https://wikipedia.org/wiki/Vsevobuch\" title=\"Vsevobuch\">compulsory military training</a>.", "no_year_html": "World War II: A decree of the Soviet State Committee of Defense restores <a href=\"https://wikipedia.org/wiki/Vsevobuch\" title=\"Vsevobuch\">compulsory military training</a>.", "links": [{"title": "Vsevobuch", "link": "https://wikipedia.org/wiki/Vsevobuch"}]}, {"year": "1941", "text": "World War II: Soviet forces enter Tehran during the Anglo-Soviet invasion of Iran.", "html": "1941 - World War II: Soviet forces enter Tehran during the <a href=\"https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran\" title=\"Anglo-Soviet invasion of Iran\">Anglo-Soviet invasion of Iran</a>.", "no_year_html": "World War II: Soviet forces enter Tehran during the <a href=\"https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran\" title=\"Anglo-Soviet invasion of Iran\">Anglo-Soviet invasion of Iran</a>.", "links": [{"title": "Anglo-Soviet invasion of Iran", "link": "https://wikipedia.org/wiki/Anglo-Soviet_invasion_of_Iran"}]}, {"year": "1944", "text": "World War II: Allied airborne troops parachute into the Netherlands as the \"Market\" half of Operation Market Garden and British XXX Corps advances into the Netherlands as the \"Garden\" half of the Operation.", "html": "1944 - World War II: Allied airborne troops parachute into the Netherlands as the \"Market\" half of <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a> and British <a href=\"https://wikipedia.org/wiki/XXX_Corps_(United_Kingdom)\" title=\"XXX Corps (United Kingdom)\">XXX Corps</a> advances into the Netherlands as the \"Garden\" half of the Operation.", "no_year_html": "World War II: Allied airborne troops parachute into the Netherlands as the \"Market\" half of <a href=\"https://wikipedia.org/wiki/Operation_Market_Garden\" title=\"Operation Market Garden\">Operation Market Garden</a> and British <a href=\"https://wikipedia.org/wiki/XXX_Corps_(United_Kingdom)\" title=\"XXX Corps (United Kingdom)\">XXX Corps</a> advances into the Netherlands as the \"Garden\" half of the Operation.", "links": [{"title": "Operation Market Garden", "link": "https://wikipedia.org/wiki/Operation_Market_Garden"}, {"title": "XXX Corps (United Kingdom)", "link": "https://wikipedia.org/wiki/XXX_Corps_(United_Kingdom)"}]}, {"year": "1944", "text": "World War II: Soviet troops launch the Tallinn Offensive against Germany and pro-independence Estonian units.", "html": "1944 - World War II: Soviet troops launch the <a href=\"https://wikipedia.org/wiki/Tallinn_Offensive\" class=\"mw-redirect\" title=\"Tallinn Offensive\">Tallinn Offensive</a> against Germany and <a href=\"https://wikipedia.org/wiki/Estonia_in_World_War_II\" title=\"Estonia in World War II\">pro-independence Estonian units</a>.", "no_year_html": "World War II: Soviet troops launch the <a href=\"https://wikipedia.org/wiki/Tallinn_Offensive\" class=\"mw-redirect\" title=\"Tallinn Offensive\">Tallinn Offensive</a> against Germany and <a href=\"https://wikipedia.org/wiki/Estonia_in_World_War_II\" title=\"Estonia in World War II\">pro-independence Estonian units</a>.", "links": [{"title": "Tallinn Offensive", "link": "https://wikipedia.org/wiki/Tallinn_Offensive"}, {"title": "Estonia in World War II", "link": "https://wikipedia.org/wiki/Estonia_in_World_War_II"}]}, {"year": "1944", "text": "World War II: German forces are attacked by the Allies in the Battle of San Marino.", "html": "1944 - World War II: German forces are attacked by the Allies in the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Marino\" title=\"Battle of San Marino\">Battle of San Marino</a>.", "no_year_html": "World War II: German forces are attacked by the Allies in the <a href=\"https://wikipedia.org/wiki/Battle_of_San_Marino\" title=\"Battle of San Marino\">Battle of San Marino</a>.", "links": [{"title": "Battle of San Marino", "link": "https://wikipedia.org/wiki/Battle_of_San_Marino"}]}, {"year": "1948", "text": "The Lehi (also known as the <PERSON> gang) assassinates Count <PERSON><PERSON>, who was appointed by the United Nations to mediate between the Arab nations and Israel.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Le<PERSON>_(group)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (group)\"><PERSON><PERSON></a> (also known as the Stern gang) assassinates Count <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tte\" title=\"Folke Bernadotte\"><PERSON><PERSON></a>, who was appointed by the United Nations to mediate between the Arab nations and Israel.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Le<PERSON>_(group)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (group)\"><PERSON><PERSON></a> (also known as the Stern gang) assassinates Count <a href=\"https://wikipedia.org/wiki/Folk<PERSON>_Bern<PERSON>tte\" title=\"Folke Bernadotte\"><PERSON><PERSON></a>, who was appointed by the United Nations to mediate between the Arab nations and Israel.", "links": [{"title": "Lehi (group)", "link": "https://wikipedia.org/wiki/Lehi_(group)"}, {"title": "Folke Bern<PERSON>tte", "link": "https://wikipedia.org/wiki/Folke_Bernadotte"}]}, {"year": "1948", "text": "The Nizam of Hyderabad surrenders his sovereignty over the Hyderabad State and joins the Indian Union.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/Nizam_of_Hyderabad\" title=\"Nizam of Hyderabad\"><PERSON>zam of Hyderabad</a> surrenders his sovereignty over the <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad State</a> and joins the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Indian Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nizam_of_Hyderabad\" title=\"Nizam of Hyderabad\"><PERSON>zam of Hyderabad</a> surrenders his sovereignty over the <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad State</a> and joins the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Indian Union</a>.", "links": [{"title": "<PERSON><PERSON> of Hyderabad", "link": "https://wikipedia.org/wiki/Nizam_of_Hyderabad"}, {"title": "Hyderabad State", "link": "https://wikipedia.org/wiki/Hyderabad_State"}, {"title": "Dominion of India", "link": "https://wikipedia.org/wiki/Dominion_of_India"}]}, {"year": "1949", "text": "The Canadian steamship SS Noronic burns in Toronto Harbour with the loss of over 118 lives.", "html": "1949 - The Canadian steamship <a href=\"https://wikipedia.org/wiki/SS_Noronic\" title=\"SS Noronic\">SS <i>Noronic</i></a> burns in <a href=\"https://wikipedia.org/wiki/Toronto_Harbour\" title=\"Toronto Harbour\">Toronto Harbour</a> with the loss of over 118 lives.", "no_year_html": "The Canadian steamship <a href=\"https://wikipedia.org/wiki/SS_Noronic\" title=\"SS Noronic\">SS <i>Noronic</i></a> burns in <a href=\"https://wikipedia.org/wiki/Toronto_Harbour\" title=\"Toronto Harbour\">Toronto Harbour</a> with the loss of over 118 lives.", "links": [{"title": "SS Noronic", "link": "https://wikipedia.org/wiki/SS_Noronic"}, {"title": "Toronto Harbour", "link": "https://wikipedia.org/wiki/Toronto_Harbour"}]}, {"year": "1961", "text": "The world's first retractable roof stadium, the Civic Arena, opens in Pittsburgh, Pennsylvania.", "html": "1961 - The world's first retractable roof stadium, the <a href=\"https://wikipedia.org/wiki/Civic_Arena_(Pittsburgh)\" title=\"Civic Arena (Pittsburgh)\">Civic Arena</a>, opens in Pittsburgh, Pennsylvania.", "no_year_html": "The world's first retractable roof stadium, the <a href=\"https://wikipedia.org/wiki/Civic_Arena_(Pittsburgh)\" title=\"Civic Arena (Pittsburgh)\">Civic Arena</a>, opens in Pittsburgh, Pennsylvania.", "links": [{"title": "Civic Arena (Pittsburgh)", "link": "https://wikipedia.org/wiki/Civic_Arena_(Pittsburgh)"}]}, {"year": "1961", "text": "Northwest Orient Airlines Flight 706 crashes during takeoff from O'Hare International Airport in Chicago, Illinois, killing all 37 people on board.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_706\" title=\"Northwest Orient Airlines Flight 706\">Northwest Orient Airlines Flight 706</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, killing all 37 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_706\" title=\"Northwest Orient Airlines Flight 706\">Northwest Orient Airlines Flight 706</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a> in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>, killing all 37 people on board.", "links": [{"title": "Northwest Orient Airlines Flight 706", "link": "https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_706"}, {"title": "O'Hare International Airport", "link": "https://wikipedia.org/wiki/O%27Hare_International_Airport"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}]}, {"year": "1965", "text": "The Battle of Chawinda is fought between Pakistan and India.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Chawinda\" title=\"Battle of Chawinda\">Battle of Chawinda</a> is fought between Pakistan and India.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Chawinda\" title=\"Battle of Chawinda\">Battle of Chawinda</a> is fought between Pakistan and India.", "links": [{"title": "Battle of Chawinda", "link": "https://wikipedia.org/wiki/Battle_of_Chawinda"}]}, {"year": "1974", "text": "Bangladesh, Grenada and Guinea-Bissau join the United Nations.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, <a href=\"https://wikipedia.org/wiki/Grenada\" title=\"Grenada\">Grenada</a> and <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> join the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a>, <a href=\"https://wikipedia.org/wiki/Grenada\" title=\"Grenada\">Grenada</a> and <a href=\"https://wikipedia.org/wiki/Guinea-Bissau\" title=\"Guinea-Bissau\">Guinea-Bissau</a> join the United Nations.", "links": [{"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Grenada", "link": "https://wikipedia.org/wiki/Grenada"}, {"title": "Guinea-Bissau", "link": "https://wikipedia.org/wiki/Guinea-Bissau"}]}, {"year": "1976", "text": "The Space Shuttle Enterprise is unveiled by NASA.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Enterprise\" title=\"Space Shuttle Enterprise\">Space Shuttle <i>Enterprise</i></a> is unveiled by NASA.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Enterprise\" title=\"Space Shuttle Enterprise\">Space Shuttle <i>Enterprise</i></a> is unveiled by NASA.", "links": [{"title": "Space Shuttle Enterprise", "link": "https://wikipedia.org/wiki/Space_Shuttle_Enterprise"}]}, {"year": "1978", "text": "The Camp David Accords are signed by Israel and Egypt.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/Camp_<PERSON>_Accords\" title=\"Camp David Accords\">Camp David Accords</a> are signed by Israel and Egypt.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Camp_<PERSON>_Accords\" title=\"Camp David Accords\">Camp David Accords</a> are signed by Israel and Egypt.", "links": [{"title": "Camp David Accords", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "After weeks of strikes at the Lenin Shipyard in Gdańsk, Poland, the nationwide independent trade union Solidarity is established.", "html": "1980 - After weeks of strikes at the <a href=\"https://wikipedia.org/wiki/Lenin_Shipyard\" class=\"mw-redirect\" title=\"Lenin Shipyard\">Lenin Shipyard</a> in Gdańsk, Poland, the nationwide independent trade union <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> is established.", "no_year_html": "After weeks of strikes at the <a href=\"https://wikipedia.org/wiki/Lenin_Shipyard\" class=\"mw-redirect\" title=\"Lenin Shipyard\">Lenin Shipyard</a> in Gdańsk, Poland, the nationwide independent trade union <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> is established.", "links": [{"title": "Lenin Shipyard", "link": "https://wikipedia.org/wiki/Lenin_Shipyard"}, {"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}]}, {"year": "1980", "text": "Former Nicaraguan President <PERSON><PERSON><PERSON> is killed in Asunción, Paraguay.", "html": "1980 - Former Nicaraguan President <a href=\"https://wikipedia.org/wiki/Anasta<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anastasio <PERSON>\"><PERSON><PERSON><PERSON></a> is killed in Asunción, Paraguay.", "no_year_html": "Former Nicaraguan President <a href=\"https://wikipedia.org/wiki/Ana<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anastasio <PERSON>\"><PERSON><PERSON><PERSON></a> is killed in Asunción, Paraguay.", "links": [{"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON> becomes the first black Miss America.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black Miss America.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first black Miss America.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "Estonia, North Korea, South Korea, Latvia, Lithuania, the Marshall Islands and Micronesia join the United Nations.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, the <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> and <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Micronesia</a> join the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a>, <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, the <a href=\"https://wikipedia.org/wiki/Marshall_Islands\" title=\"Marshall Islands\">Marshall Islands</a> and <a href=\"https://wikipedia.org/wiki/Federated_States_of_Micronesia\" title=\"Federated States of Micronesia\">Micronesia</a> join the United Nations.", "links": [{"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Marshall Islands", "link": "https://wikipedia.org/wiki/Marshall_Islands"}, {"title": "Federated States of Micronesia", "link": "https://wikipedia.org/wiki/Federated_States_of_Micronesia"}]}, {"year": "1991", "text": "The first version of the Linux kernel (0.01) is released to the Internet.", "html": "1991 - The first version of the <a href=\"https://wikipedia.org/wiki/Linux\" title=\"Linux\">Linux</a> kernel (0.01) is released to the Internet.", "no_year_html": "The first version of the <a href=\"https://wikipedia.org/wiki/Linux\" title=\"Linux\">Linux</a> kernel (0.01) is released to the Internet.", "links": [{"title": "Linux", "link": "https://wikipedia.org/wiki/Linux"}]}, {"year": "1992", "text": "An Iranian Kurdish leader and his two joiners are assassinated by political militants in Berlin.", "html": "1992 - An Iranian Kurdish leader and his two joiners are <a href=\"https://wikipedia.org/wiki/Mykonos_restaurant_assassinations\" title=\"Mykonos restaurant assassinations\">assassinated by political militants</a> in Berlin.", "no_year_html": "An Iranian Kurdish leader and his two joiners are <a href=\"https://wikipedia.org/wiki/Mykonos_restaurant_assassinations\" title=\"Mykonos restaurant assassinations\">assassinated by political militants</a> in Berlin.", "links": [{"title": "Mykonos restaurant assassinations", "link": "https://wikipedia.org/wiki/Mykonos_restaurant_assassinations"}]}, {"year": "2001", "text": "The New York Stock Exchange reopens for trading after the September 11 attacks, the longest closure since the Great Depression.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> reopens for trading after the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>, the longest closure since the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/New_York_Stock_Exchange\" title=\"New York Stock Exchange\">New York Stock Exchange</a> reopens for trading after the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>, the longest closure since the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>.", "links": [{"title": "New York Stock Exchange", "link": "https://wikipedia.org/wiki/New_York_Stock_Exchange"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}, {"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}]}, {"year": "2001", "text": "<PERSON>, president of the United States, delivers remarks at the Islamic Center of Washington praising Muslim Americans and condemning Islamophobia in the aftermath of the September 11 attacks.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a>, delivers <a href=\"https://wikipedia.org/wiki/Remarks_at_the_Islamic_Center_of_Washington\" title=\"Remarks at the Islamic Center of Washington\">remarks at the Islamic Center of Washington</a> praising Muslim Americans and condemning <a href=\"https://wikipedia.org/wiki/Islamophobia_in_the_United_States\" title=\"Islamophobia in the United States\">Islamophobia</a> in the <a href=\"https://wikipedia.org/wiki/Aftermath_of_the_September_11_attacks\" title=\"Aftermath of the September 11 attacks\">aftermath of the September 11 attacks</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">president of the United States</a>, delivers <a href=\"https://wikipedia.org/wiki/Remarks_at_the_Islamic_Center_of_Washington\" title=\"Remarks at the Islamic Center of Washington\">remarks at the Islamic Center of Washington</a> praising Muslim Americans and condemning <a href=\"https://wikipedia.org/wiki/Islamophobia_in_the_United_States\" title=\"Islamophobia in the United States\">Islamophobia</a> in the <a href=\"https://wikipedia.org/wiki/Aftermath_of_the_September_11_attacks\" title=\"Aftermath of the September 11 attacks\">aftermath of the September 11 attacks</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "Remarks at the Islamic Center of Washington", "link": "https://wikipedia.org/wiki/Remarks_at_the_Islamic_Center_of_Washington"}, {"title": "Islamophobia in the United States", "link": "https://wikipedia.org/wiki/Islamophobia_in_the_United_States"}, {"title": "Aftermath of the September 11 attacks", "link": "https://wikipedia.org/wiki/Aftermath_of_the_September_11_attacks"}]}, {"year": "2006", "text": "Fourpeaked Mountain in Alaska erupts, marking the first eruption for the volcano in at least 10,000 years.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Fourpeaked_Mountain\" title=\"Fourpeaked Mountain\">Fourpeaked Mountain</a> in Alaska erupts, marking the first eruption for the volcano in at least 10,000 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fourpeaked_Mountain\" title=\"Fourpeaked Mountain\">Fourpeaked Mountain</a> in Alaska erupts, marking the first eruption for the volcano in at least 10,000 years.", "links": [{"title": "Fourpeaked Mountain", "link": "https://wikipedia.org/wiki/Fourpeaked_Mountain"}]}, {"year": "2006", "text": "An audio tape of a private speech by Hungarian Prime Minister <PERSON><PERSON><PERSON> is leaked to the public, in which he confessed that his Hungarian Socialist Party had lied to win the 2006 election, sparking widespread protests across the country.", "html": "2006 - An audio tape of <a href=\"https://wikipedia.org/wiki/%C5%90sz%C3%B6d_speech\" title=\"Őszöd speech\">a private speech</a> by Hungarian Prime Minister <PERSON><PERSON><PERSON> is leaked to the public, in which he confessed that his Hungarian Socialist Party had lied to win the 2006 election, sparking <a href=\"https://wikipedia.org/wiki/2006_protests_in_Hungary\" title=\"2006 protests in Hungary\">widespread protests</a> across the country.", "no_year_html": "An audio tape of <a href=\"https://wikipedia.org/wiki/%C5%90sz%C3%B6d_speech\" title=\"Őszöd speech\">a private speech</a> by Hungarian Prime Minister <PERSON><PERSON><PERSON> is leaked to the public, in which he confessed that his Hungarian Socialist Party had lied to win the 2006 election, sparking <a href=\"https://wikipedia.org/wiki/2006_protests_in_Hungary\" title=\"2006 protests in Hungary\">widespread protests</a> across the country.", "links": [{"title": "Őszöd speech", "link": "https://wikipedia.org/wiki/%C5%90sz%C3%B6d_speech"}, {"title": "2006 protests in Hungary", "link": "https://wikipedia.org/wiki/2006_protests_in_Hungary"}]}, {"year": "2011", "text": "Occupy Wall Street movement begins in Zuccotti Park, New York City.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Occupy_Wall_Street\" title=\"Occupy Wall Street\">Occupy Wall Street</a> movement begins in Zuccotti Park, New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Occupy_Wall_Street\" title=\"Occupy Wall Street\">Occupy Wall Street</a> movement begins in Zuccotti Park, New York City.", "links": [{"title": "Occupy Wall Street", "link": "https://wikipedia.org/wiki/Occupy_Wall_Street"}]}, {"year": "2013", "text": "Grand Theft Auto V earns more than half a billion dollars on its first day of release.", "html": "2013 - <i><a href=\"https://wikipedia.org/wiki/Grand_Theft_Auto_V\" title=\"Grand Theft Auto V\">Grand Theft Auto V</a></i> earns more than half a billion dollars on its first day of release.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Grand_Theft_Auto_V\" title=\"Grand Theft Auto V\">Grand Theft Auto V</a></i> earns more than half a billion dollars on its first day of release.", "links": [{"title": "Grand Theft Auto V", "link": "https://wikipedia.org/wiki/Grand_Theft_Auto_V"}]}, {"year": "2016", "text": "Two bombs explode in Seaside Park, New Jersey, and Manhattan. Thirty-one people are injured in the Manhattan bombing.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings\" title=\"2016 New York and New Jersey bombings\">Two bombs explode</a> in Seaside Park, New Jersey, and Manhattan. Thirty-one people are injured in the Manhattan bombing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings\" title=\"2016 New York and New Jersey bombings\">Two bombs explode</a> in Seaside Park, New Jersey, and Manhattan. Thirty-one people are injured in the Manhattan bombing.", "links": [{"title": "2016 New York and New Jersey bombings", "link": "https://wikipedia.org/wiki/2016_New_York_and_New_Jersey_bombings"}]}, {"year": "2018", "text": "A Russian reconnaissance aircraft carrying 15 people on board is brought down by a Syrian surface-to-air missile over the Mediterranean Sea.", "html": "2018 - A Russian reconnaissance aircraft carrying 15 people on board is <a href=\"https://wikipedia.org/wiki/Syria_missile_strikes_(September_2018)\" title=\"Syria missile strikes (September 2018)\">brought down</a> by a Syrian surface-to-air missile over the Mediterranean Sea.", "no_year_html": "A Russian reconnaissance aircraft carrying 15 people on board is <a href=\"https://wikipedia.org/wiki/Syria_missile_strikes_(September_2018)\" title=\"Syria missile strikes (September 2018)\">brought down</a> by a Syrian surface-to-air missile over the Mediterranean Sea.", "links": [{"title": "Syria missile strikes (September 2018)", "link": "https://wikipedia.org/wiki/Syria_missile_strikes_(September_2018)"}]}], "Births": [{"year": "879", "text": "<PERSON> the <PERSON>, Frankish king (d. 929)", "html": "879 - <a href=\"https://wikipedia.org/wiki/Charles_the_Simple\" title=\"Charles the Simple\"><PERSON> the Simple</a>, Frankish king (d. 929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple\" title=\"Charles the Simple\"><PERSON></a>, Frankish king (d. 929)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple"}]}, {"year": "1433", "text": "<PERSON> of Portugal, Portuguese prince and cardinal (d. 1459)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/James_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, Portuguese prince and cardinal (d. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a>, Portuguese prince and cardinal (d. 1459)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/James_of_Portugal"}]}, {"year": "1479", "text": "<PERSON><PERSON>, Italian astronomer (d. 1541)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer (d. 1541)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer (d. 1541)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1550", "text": "<PERSON>, pope of the Catholic Church (d. 1621)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Pope_Paul_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a>, pope of the Catholic Church (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Paul <PERSON>\"><PERSON></a>, pope of the Catholic Church (d. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON>, German nobleman (d. 1600)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nobleman (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German nobleman (d. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1578", "text": "<PERSON>, English administrator and bishop (d. 1650)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English administrator and bishop (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English administrator and bishop (d. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON>, Italian composer (d. 1650)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_<PERSON>i"}]}, {"year": "1630", "text": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma (d. 1694)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON></a>, Duke of Parma (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON><PERSON></a>, Duke of Parma (d. 1694)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1639", "text": "<PERSON>, Swiss bishop (d. 1725)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss bishop (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss bishop (d. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON> of Savoy, queen consort of Spain (d. 1714)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Savoy\" class=\"mw-redirect\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, queen consort of Spain (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Savoy\" class=\"mw-redirect\" title=\"<PERSON> of Savoy\"><PERSON> of Savoy</a>, queen consort of Spain (d. 1714)", "links": [{"title": "<PERSON> of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Savoy"}]}, {"year": "1730", "text": "<PERSON>, Prussian-American general (d. 1794)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-American general (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian-American general (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON>, American judge and politician, 2nd Chief Justice of the United States (d. 1800)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 2nd <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1743", "text": "<PERSON>, French mathematician and political scientist (d. 1794)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/Marquis_<PERSON>_<PERSON>\" title=\"Marquis <PERSON>\"><PERSON></a>, French mathematician and political scientist (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marquis_<PERSON>_<PERSON>\" title=\"Marquis <PERSON>\"><PERSON></a>, French mathematician and political scientist (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, German jurist and author (d. 1816)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and author (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist and author (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_A<PERSON>"}]}, {"year": "1773", "text": "<PERSON>, American captain and farmer (d. 1849)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and farmer (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and farmer (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian soldier (d. 1866)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/Nadez<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian soldier (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian soldier (d. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1797", "text": "<PERSON>, German naturalist and zoologist (d. 1821)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German naturalist and zoologist (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German naturalist and zoologist (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, German geologist and paleontologist (d. 1902)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German geologist and paleontologist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, South African general and politician, 1st President of the South African Republic (d. 1901)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"President of the South African Republic\">President of the South African Republic</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_South_African_Republic\" class=\"mw-redirect\" title=\"President of the South African Republic\">President of the South African Republic</a> (d. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ius"}, {"title": "President of the South African Republic", "link": "https://wikipedia.org/wiki/President_of_the_South_African_Republic"}]}, {"year": "1820", "text": "<PERSON><PERSON>, French playwright (d. 1889)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Augier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French playwright (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Augier\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French playwright (d. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Augier"}]}, {"year": "1820", "text": "<PERSON>, Confederate general (d. 1863)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Confederate general (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Confederate general (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, French choreographer (d. 1870)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9on\" title=\"<PERSON>\"><PERSON></a>, French choreographer (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%A9on\" title=\"<PERSON>\"><PERSON></a>, French choreographer (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_Saint-L%C3%A9on"}]}, {"year": "1825", "text": "<PERSON>, American jurist and politician, 16th United States Secretary of the Interior (d. 1893)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tus_Cincinnatus_Lamar_II\" class=\"mw-redirect\" title=\"Lucius Quintus Cincinnatus Lamar II\"><PERSON>us <PERSON></a>, American jurist and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tus_Cincinnatus_Lamar_II\" class=\"mw-redirect\" title=\"<PERSON> Quintus Cincinnatus Lamar II\"><PERSON>us <PERSON></a>, American jurist and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_II"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1826", "text": "<PERSON>, German-Italian mathematician and academic (d. 1866)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian mathematician and academic (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Italian mathematician and academic (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, Portuguese journalist, lawyer, and politician (d. 1923)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Guerra_Junqueiro\" title=\"Guerra Junqueiro\"><PERSON><PERSON> Junqueiro</a>, Portuguese journalist, lawyer, and politician (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guerra_Junqueiro\" title=\"Guerra Junqueiro\"><PERSON><PERSON> Junqueiro</a>, Portuguese journalist, lawyer, and politician (d. 1923)", "links": [{"title": "Guerra Junqueiro", "link": "https://wikipedia.org/wiki/Guerra_Junqueiro"}]}, {"year": "1853", "text": "<PERSON>, British officer and Victoria Cross recipient (d. 1912)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British officer and <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British officer and <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1854", "text": "<PERSON>, Scottish-American businessman, founded Buick Motor Company (d. 1929)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>uick\" title=\"<PERSON> Buick\"><PERSON></a>, Scottish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Buick\" title=\"Buick\">Buick Motor Company</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buick\" title=\"<PERSON> Buick\"><PERSON></a>, Scottish-American businessman, founded <a href=\"https://wikipedia.org/wiki/Buick\" title=\"Buick\">Buick Motor Company</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ck"}, {"title": "Buick", "link": "https://wikipedia.org/wiki/Buick"}]}, {"year": "1857", "text": "<PERSON>, Russian scientist and engineer (d. 1935)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scientist and engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian scientist and engineer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, Canadian geologist and academic (d. 1942)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and academic (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian geologist and academic (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON> the Kid, American gunman (d. 1881)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Kid\" title=\"<PERSON> the Kid\"><PERSON> the Kid</a>, American gunman (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Kid\" title=\"<PERSON> the Kid\"><PERSON> the Kid</a>, American gunman (d. 1881)", "links": [{"title": "<PERSON> the Kid", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON> <PERSON><PERSON>, American politician, 18th Governor of Oregon (d. 1929)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American politician, 18th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1929)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Estonian journalist and politician (d. 1934)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician (d. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON>, Ukrainian writer (d. 1913)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian writer (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian writer (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English admiral (d. 1943)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Australian politician, 31st Premier of Victoria (d. 1932)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 31st <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1867", "text": "<PERSON>, Russian chemist (d. 1896)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chemist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chemist (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Canadian educator and politician, Canadian Minister of Militia and Defence (d. 1956)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)\" class=\"mw-redirect\" title=\"Minister of Militia and Defence (Canada)\">Canadian Minister of Militia and Defence</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)\" class=\"mw-redirect\" title=\"Minister of Militia and Defence (Canada)\">Canadian Minister of Militia and Defence</a> (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Militia and Defence (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Militia_and_Defence_(Canada)"}]}, {"year": "1869", "text": "<PERSON>, Norwegian political scientist, historian, and academic, Nobel Prize laureate (d. 1938)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian political scientist, historian, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian political scientist, historian, and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Norwegian explorer (d. 1895)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian explorer (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian explorer (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eivind_Astrup"}]}, {"year": "1874", "text": "<PERSON>, Australian author and academic (d. 1970)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and academic (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Italian composer (d. 1950)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1930)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON>, Indian businessman, social activist,  and politician (d. 1973)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Periyar_E._V._<PERSON>my\" class=\"mw-redirect\" title=\"Periyar <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Indian businessman, social activist, and politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Periyar_<PERSON>._<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Periya<PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Indian businessman, social activist, and politician (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Periyar_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English admiral, Victoria Cross recipient (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1883", "text": "<PERSON>, American poet, short story writer, and essayist (d. 1963)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, and essayist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, short story writer, and essayist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American pianist and composer (d. 1920)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Estonian captain (d. 1919)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I<PERSON>v\"><PERSON></a>, Estonian captain (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I<PERSON>v\"><PERSON></a>, Estonian captain (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Irv"}]}, {"year": "1897", "text": "<PERSON>, American baseball player and coach (d. 1965)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American baseball player (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, African-American physician, awarded the Presidential Medal of Freedom (d. 1986)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American physician, awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American physician, awarded the <a href=\"https://wikipedia.org/wiki/Presidential_Medal_of_Freedom\" title=\"Presidential Medal of Freedom\">Presidential Medal of Freedom</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Presidential Medal of Freedom", "link": "https://wikipedia.org/wiki/Presidential_Medal_of_Freedom"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American businessman, founded the Marriott Corporation (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Marriott_Corporation\" title=\"Marriott Corporation\">Marriott Corporation</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Marriott_Corporation\" title=\"Marriott Corporation\">Marriott Corporation</a> (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Marriott Corporation", "link": "https://wikipedia.org/wiki/Marriott_Corporation"}]}, {"year": "1900", "text": "<PERSON>, Canadian screenwriter and novelist (d. 1963)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian screenwriter and novelist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian screenwriter and novelist (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, New Zealand-born educator and political activist, founding member of the Communist Party of New Zealand (d. 1971)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-born educator and political activist, founding member of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_New_Zealand\" title=\"Communist Party of New Zealand\">Communist Party of New Zealand</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-born educator and political activist, founding member of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_New_Zealand\" title=\"Communist Party of New Zealand\">Communist Party of New Zealand</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Communist Party of New Zealand", "link": "https://wikipedia.org/wiki/Communist_Party_of_New_Zealand"}]}, {"year": "1901", "text": "<PERSON>, English pilot and sailor (d. 1972)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and sailor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pilot and sailor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Australian author (d. 1973)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian author (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Dutch boxer (d. 1984)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch boxer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch boxer (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Irish short story writer, novelist, and poet (d. 1966)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Irish short story writer, novelist, and poet (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Irish short story writer, novelist, and poet (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_O%27Connor"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 34th <PERSON><PERSON><PERSON><PERSON> (d. 1971)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Minanogawa_T%C5%8Dz%C5%8D\" title=\"Minanogawa Tōzō\"><PERSON><PERSON><PERSON> Tōzō</a>, Japanese sumo wrestler, the 34th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minanogawa_T%C5%8Dz%C5%8D\" title=\"Minanogawa Tōzō\"><PERSON><PERSON><PERSON>ō</a>, Japanese sumo wrestler, the 34th <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "links": [{"title": "Minanogawa Tōzō", "link": "https://wikipedia.org/wiki/Minanogawa_T%C5%8Dz%C5%8D"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, regent of the Bamangwato tribe (d. 1959)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Tshekedi_Khama\" title=\"Tshekedi Khama\"><PERSON><PERSON><PERSON><PERSON></a>, regent of the <a href=\"https://wikipedia.org/wiki/Bamangwato\" class=\"mw-redirect\" title=\"Bamangwato\">Bamangwato</a> tribe (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tshekedi_<PERSON>\" title=\"Tshekedi Khama\"><PERSON><PERSON><PERSON><PERSON></a>, regent of the <a href=\"https://wikipedia.org/wiki/Bamangwato\" class=\"mw-redirect\" title=\"Bamangwato\">Bamangwato</a> tribe (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsheked<PERSON>_<PERSON>hama"}, {"title": "Bamangwato", "link": "https://wikipedia.org/wiki/Bamangwato"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan lawyer and politician, 2nd President of Sri Lanka (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Sri_Lanka\" title=\"President of Sri Lanka\">President of Sri Lanka</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "President of Sri Lanka", "link": "https://wikipedia.org/wiki/President_of_Sri_Lanka"}]}, {"year": "1906", "text": "<PERSON>, American physician and environmentalist (d. 2010)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and environmentalist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and environmentalist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American lawyer and judge, 15th Chief Justice of the United States (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 15th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 15th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1908", "text": "<PERSON>, English author and politician (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and politician (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Armenian architect and educator, designed the Sardarapat Memorial and St. Vartan Cathedral (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Armenian architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Sardarapat_Memorial\" title=\"Sardarapat Memorial\">Sardarapat Memorial</a> and <a href=\"https://wikipedia.org/wiki/St._Vartan_Cathedral\" class=\"mw-redirect\" title=\"St. Vartan Cathedral\">St. Vartan Cathedral</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Armenian architect and educator, designed the <a href=\"https://wikipedia.org/wiki/Sardarapat_Memorial\" title=\"Sardarapat Memorial\">Sardarapat Memorial</a> and <a href=\"https://wikipedia.org/wiki/St._Vartan_Cathedral\" class=\"mw-redirect\" title=\"St. Vartan Cathedral\">St. Vartan Cathedral</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sardarapat Memorial", "link": "https://wikipedia.org/wiki/Sardarapat_Memorial"}, {"title": "St. Vartan Cathedral", "link": "https://wikipedia.org/wiki/St._Vartan_Cathedral"}]}, {"year": "1909", "text": "<PERSON>, American author and illustrator (d. 1968)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Polish actress (d. 2011)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actress (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Belarusian poet, journalist, and translator (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet, journalist, and translator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet, journalist, and translator (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Czech-Canadian businessman (d. 2008)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian businessman (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian businessman (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Swedish pentathlete (d. 2012)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pentathlete (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish pentathlete (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Japanese politician, Deputy Prime Minister of Japan (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Japan\" title=\"Deputy Prime Minister of Japan\">Deputy Prime Minister of Japan</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Japan\" title=\"Deputy Prime Minister of Japan\">Deputy Prime Minister of Japan</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>u"}, {"title": "Deputy Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Japan"}]}, {"year": "1915", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian painter and director (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian painter and director (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian painter and director (d. 2011)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON>, British author and poet (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, British author and poet (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, British author and poet (d. 2014)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Danish-American author and screenwriter (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American author and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American author and screenwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ib_<PERSON>chior"}]}, {"year": "1917", "text": "<PERSON><PERSON>, South Korean-German composer and educator (d. 1995)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-German composer and educator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-German composer and educator (d. 1995)", "links": [{"title": "Isang Yun", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Hungarian-Israeli fashion designer, founded the Gottex Company (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ie<PERSON>\" title=\"<PERSON> Gottlieb\"><PERSON></a>, Hungarian-Israeli fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Gottex\" title=\"Gottex\">Gottex Company</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Gottlieb\"><PERSON></a>, Hungarian-Israeli fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Gottex\" title=\"Gottex\">Gottex Company</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gottex", "link": "https://wikipedia.org/wiki/Gottex"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Irish-born Israeli general and politician, 6th President of Israel (d. 1997)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born Israeli general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born Israeli general and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1920", "text": "<PERSON><PERSON>, English actress (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, Angolan poet and politician, 1st President of Angola (d. 1979)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Agostinho_Neto\" title=\"Agostin<PERSON> Neto\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agostin<PERSON>_Neto\" title=\"Agostin<PERSON> Neto\"><PERSON><PERSON><PERSON><PERSON></a>, Angolan poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Angola\" title=\"President of Angola\">President of Angola</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agostinho_Neto"}, {"title": "President of Angola", "link": "https://wikipedia.org/wiki/President_of_Angola"}]}, {"year": "1923", "text": "<PERSON>, English-American pianist, composer, and conductor (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist, composer, and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist, composer, and conductor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1953)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actress and singer (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American murderer (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)\" class=\"mw-redirect\" title=\"<PERSON> (serial killer)\"><PERSON></a>, American murderer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)\" class=\"mw-redirect\" title=\"<PERSON> (serial killer)\"><PERSON></a>, American murderer (d. 2008)", "links": [{"title": "<PERSON> (serial killer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(serial_killer)"}]}, {"year": "1926", "text": "<PERSON>, American bass player and bandleader  (d. 1965)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and bandleader (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and bandleader (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor, director, and screenwriter (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American minister and pianist (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>vie_Lister\" title=\"Hovie Lister\"><PERSON><PERSON></a>, American minister and pianist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vie_Lister\" title=\"<PERSON>vie Lister\"><PERSON><PERSON></a>, American minister and pianist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lister"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, French cardinal (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American singer and organist (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and organist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and organist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American football player (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author and academic (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, English-American actor (d. 1998)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actor (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American saxophonist (d. 2001)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Sil_Austin\" title=\"Sil Austin\"><PERSON><PERSON></a>, American saxophonist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sil_Austin\" title=\"Sil Austin\"><PERSON><PERSON></a>, American saxophonist (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sil_Austin"}]}, {"year": "1929", "text": "<PERSON>, Baron <PERSON> of Radley, Northern Irish air marshal and politician", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Radley\" title=\"<PERSON>, Baron <PERSON> of Radley\"><PERSON>, Baron <PERSON> of Radley</a>, Northern Irish air marshal and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Radley\" title=\"<PERSON>, Baron <PERSON> of Radley\"><PERSON>, Baron <PERSON> of Radley</a>, Northern Irish air marshal and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Radley", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English racing driver and sportscaster (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Stirling_Moss\" title=\"Stirling Moss\"><PERSON></a>, English racing driver and sportscaster (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stirling_Moss\" title=\"Stirling Moss\"><PERSON></a>, English racing driver and sportscaster (d. 2020)", "links": [{"title": "Stirling Moss", "link": "https://wikipedia.org/wiki/Stirling_Moss"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Indian violinist and composer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Lalgu<PERSON>_<PERSON>n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian violinist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gu<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian violinist and composer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1930", "text": "<PERSON>, Dutch clarinet player and composer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch clarinet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch clarinet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American captain, pilot, and astronaut (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American philosopher and author (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1930", "text": "<PERSON>, American general, pilot, and astronaut (d. 2024) ", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, pilot, and astronaut (d. 2024) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, pilot, and astronaut (d. 2024) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress (d. 2005)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, French actor and screenwriter (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1932", "text": "<PERSON>, American author and academic (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Indian-English journalist", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Nigerian army officer and politician (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian army officer and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian army officer and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American wrestler (d. 1997)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Bulldog_Brower\" title=\"Bulldog Brower\"><PERSON><PERSON> Brower</a>, American wrestler (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulldog_Brower\" title=\"Bulldog Brower\"><PERSON><PERSON> Brower</a>, American wrestler (d. 1997)", "links": [{"title": "Bulldog Brower", "link": "https://wikipedia.org/wiki/Bulldog_Brower"}]}, {"year": "1933", "text": "<PERSON>, American lawyer and politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian-American ice hockey player (d. 1984)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American tennis player (d. 1969)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American novelist, essayist, and poet (d. 2001)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, essayist, and poet (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American physicist and academic (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American composer and educator (d. 1993)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English-Australian motorcycle racer (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian motorcycle racer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian motorcycle racer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Puerto Rican baseball player (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Orlando_Cepeda\" title=\"Orlando Cepeda\"><PERSON></a>, Puerto Rican baseball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Cepeda\" title=\"Orlando Cepeda\"><PERSON></a>, Puerto Rican baseball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Cepeda"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Indian poet and literary critic", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Sitakant_Ma<PERSON>patra\" title=\"<PERSON>aka<PERSON> Mahapatra\"><PERSON><PERSON><PERSON></a>, Indian poet and literary critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sitakant_Ma<PERSON>patra\" title=\"Sitaka<PERSON> Mahapatra\"><PERSON><PERSON><PERSON></a>, Indian poet and literary critic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitaka<PERSON>_<PERSON><PERSON>tra"}]}, {"year": "1938", "text": "<PERSON>, American actor (d. 2008)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American clarinet player and composer (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Robinson\"><PERSON></a>, American clarinet player and composer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American poet and educator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and voice actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Shelby_Flint\" title=\"Shelby Flint\"><PERSON></a>, American singer-songwriter and voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shelby_Flint\" title=\"Shelby Flint\"><PERSON></a>, American singer-songwriter and voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Flint"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and jurist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Swedish politician and diplomat, 4th Deputy Secretary-General of the United Nations", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations"}]}, {"year": "1940", "text": "<PERSON>, English cricketer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Italian motorcycle racer (d. 1972)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian motorcycle racer (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American lawyer and politician (d. 2005)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American author and illustrator", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Irish-English journalist and author", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_Lynam"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American actress (d. 2012)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ontiveros"}]}, {"year": "1944", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Italian mountaineer and explorer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer and explorer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American mathematician and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian economist and politician, 8th Minister of Foreign Affairs for Canada", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Minister of Foreign Affairs for Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)\" title=\"Minister of Foreign Affairs (Canada)\">Minister of Foreign Affairs for Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Canada)"}]}, {"year": "1945", "text": "<PERSON>, American basketball player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Indian religious leader (d. 2020)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Charu Swami\"><PERSON><PERSON><PERSON></a>, Indian religious leader (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Charu Swami\"><PERSON><PERSON><PERSON></a>, Indian religious leader (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English footballer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Estonian journalist and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian journalist and politician", "links": [{"title": "Heimar <PERSON>", "link": "https://wikipedia.org/wiki/Heimar_Lenk"}]}, {"year": "1947", "text": "<PERSON>, English social worker and politician, Minister for the Cabinet Office (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social worker and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English social worker and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office\" title=\"Minister for the Cabinet Office\">Minister for the Cabinet Office</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for the Cabinet Office", "link": "https://wikipedia.org/wiki/Minister_for_the_Cabinet_Office"}]}, {"year": "1947", "text": "<PERSON>, Mexican historian, critic, and publisher", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican historian, critic, and publisher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican historian, critic, and publisher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American cartoonist (d. 2000)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Bosnian singer-songwriter (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian singer-songwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor and producer (d. 2003)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian lawyer and politician (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Indian politician; Chief Minister of Gujarat and 14th Prime Minister of India", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician; Chief Minister of <a href=\"https://wikipedia.org/wiki/Gujarat\" title=\"Gujarat\">Gujarat</a> and 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician; Chief Minister of <a href=\"https://wikipedia.org/wiki/Gujarat\" title=\"Gujarat\">Gujarat</a> and 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Gujarat", "link": "https://wikipedia.org/wiki/Gujarat"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>bill\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Waybill\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>e_<PERSON>bill"}]}, {"year": "1951", "text": "<PERSON>, Scottish politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Scottish politician)\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON> (Scottish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_politician)"}]}, {"year": "1951", "text": "<PERSON>, American actress, television host, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, television host, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, television host, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Kermit_Washington\" title=\"Kermit Washington\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kermit_Washington\" title=\"Kermit Washington\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kermit_Washington"}]}, {"year": "1952", "text": "<PERSON>, American tennis player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Portuguese politician, former Minister of Foreign Affairs", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Amado\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, former Minister of Foreign Affairs", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lu%C3%ADs_Amado\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician, former Minister of Foreign Affairs", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lu%C3%ADs_Amado"}]}, {"year": "1953", "text": "<PERSON>, American basketball player and businessman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Junior_Bridgeman\" title=\"Junior Bridgeman\">Junior <PERSON></a>, American basketball player and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_Bridgeman\" title=\"Junior Bridgeman\">Junior <PERSON></a>, American basketball player and businessman", "links": [{"title": "Junior Bridgeman", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>man"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>-<PERSON>, English chef and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English chef and author", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n_Day-<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Pakistani-English soldier and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Pakistani_politician)\" title=\"<PERSON><PERSON> (Pakistani politician)\"><PERSON><PERSON></a>, Pakistani-English soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Pakistani_politician)\" title=\"<PERSON><PERSON> (Pakistani politician)\"><PERSON><PERSON></a>, Pakistani-English soldier and politician", "links": [{"title": "<PERSON><PERSON> (Pakistani politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(Pakistani_politician)"}]}, {"year": "1953", "text": "<PERSON>, American actress, comedian, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, French pianist and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Jo%C3%ABl-Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%ABl-Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%ABl-Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American wrestler", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1955", "text": "<PERSON>, American golfer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_(golfer)"}]}, {"year": "1955", "text": "<PERSON>,  American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American politician, 57th Governor of Missouri", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 57th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 57th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Missouri", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Missouri"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Kyrgyz politician, 4th President of Kyrgyzstan", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Almaz<PERSON>_<PERSON>ev\" title=\"<PERSON><PERSON><PERSON>am<PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyz politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almaz<PERSON>_<PERSON>ev\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyz politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Almaz<PERSON>_<PERSON>"}, {"title": "President of Kyrgyzstan", "link": "https://wikipedia.org/wiki/President_of_Kyrgyzstan"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Thad_<PERSON>\" title=\"Thad <PERSON>\">Thad <PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thad_<PERSON>\" title=\"Thad <PERSON>\">T<PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thad_<PERSON><PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian singer-songwriter and guitarist (d. 2013)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Mandawuy_Yunupingu\" title=\"Manda<PERSON>y Yunupingu\"><PERSON><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mandawuy_Yunupingu\" title=\"Manda<PERSON>y Yunupingu\"><PERSON><PERSON><PERSON><PERSON></a>, Australian singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manda<PERSON>y_Yunupingu"}]}, {"year": "1957", "text": "<PERSON>, English ballet dancer and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballet dancer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballet dancer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American businessman and politician (d. 2012)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Austrian politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Nurten_Y%C4%B1lmaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nurten_Y%C4%B1lmaz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nurten_Y%C4%B1lmaz"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Slovenian politician, 5th Prime Minister of Slovenia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jan%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jan%C5%A1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovenia\" title=\"Prime Minister of Slovenia\">Prime Minister of Slovenia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Janez_Jan%C5%A1a"}, {"title": "Prime Minister of Slovenia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovenia"}]}, {"year": "1958", "text": "<PERSON>, Scottish-American baseball player (d. 2019)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Scottish-American baseball player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, Scottish-American baseball player (d. 2019)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1960", "text": "<PERSON>, Canadian singer-songwriter (d. 2011)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American puppeteer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English racing driver and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American economist and academic (d. 2019)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American wrestling manager and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling manager and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestling manager and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Greek politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American rock singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ty Tabor\"><PERSON></a>, American rock singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ty Tabor\"><PERSON></a>, American rock singer-songwriter and guitarist", "links": [{"title": "Ty <PERSON>", "link": "https://wikipedia.org/wiki/Ty_Tabor"}]}, {"year": "1962", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Australian director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Vietnamese-American actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Vietnamese-American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Egyptian engineer and politician, 51st Prime Minister of Egypt", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Qandil\"><PERSON><PERSON></a>, Egyptian engineer and politician, 51st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Qandil\"><PERSON><PERSON></a>, Egyptian engineer and politician, 51st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a>", "links": [{"title": "<PERSON><PERSON> Qandil", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il"}, {"title": "Prime Minister of Egypt", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Egypt"}]}, {"year": "1962", "text": "<PERSON>, Australian golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Winans\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Win<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "Be<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Be<PERSON>e_<PERSON>ans"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American-Japanese wrestler and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese wrestler and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_Urbaniak"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese video game designer, created <PERSON> the Hedgehog", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese video game designer, created <i><a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog\" title=\"Sonic the Hedgehog\">Sonic the Hedgehog</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese video game designer, created <i><a href=\"https://wikipedia.org/wiki/Sonic_the_Hedgehog\" title=\"Sonic the Hedgehog\">Sonic the Hedgehog</a></i>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ka"}, {"title": "<PERSON> the Hedgehog", "link": "https://wikipedia.org/wiki/<PERSON>_the_Hedgehog"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Singer\" title=\"<PERSON> Singer\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Singer\" title=\"Bryan Singer\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Singer"}]}, {"year": "1966", "text": "<PERSON>, American rapper and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American boxer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Anastacia\" title=\"Anasta<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anasta<PERSON>\" title=\"Anasta<PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "Anastacia", "link": "https://wikipedia.org/wiki/Anastacia"}]}, {"year": "1968", "text": "<PERSON>, American author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Spanish footballer and manager (d. 2014)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Russian ice hockey player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Irish snooker player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English singer-songwriter (d. 2019)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American MMA fighter and wrestler", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MMA fighter and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American MMA fighter and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American interior designer and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American interior designer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American interior designer and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South African-English rugby player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)\" title=\"<PERSON> (footballer, born 1971)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1971)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1971)"}]}, {"year": "1971", "text": "<PERSON>, American actor and comedian", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Italian footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentine rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Greek footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American singer, model, and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, model, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, model, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Norwegian skier and explorer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Tor<PERSON><PERSON>_Granheim\" title=\"Tor<PERSON><PERSON> Gran<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier and explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>heim\" title=\"Tor<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier and explorer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tormod_Granheim"}]}, {"year": "1974", "text": "<PERSON>, Australian golfer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>she<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>she<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "Rasheed <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>she<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>_<PERSON>t"}]}, {"year": "1975", "text": "<PERSON>, American race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper (d. 2015)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (rapper)\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (rapper)\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(rapper)"}]}, {"year": "1977", "text": "<PERSON>, American screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Esmail"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian actor and singer (d. 2020)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and singer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Dutch football manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch football manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch football manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lot"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Danish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Steffen_Algreen\" title=\"Steffen Algreen\"><PERSON><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steffen_Algreen\" title=\"Steffen Algreen\"><PERSON><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>effen <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>green"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_Ayodele\" title=\"<PERSON>kin Ayodele\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_Ayodele\" title=\"<PERSON>kin Ayodele\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>yodele"}]}, {"year": "1979", "text": "<PERSON>, Canadian musician", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON>, Australian politician, 47th Premier of New South Wales", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 47th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, English lawyer and politician, Shadow Chief Secretary to the Treasury", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chief_Secretary_to_the_Treasury\" title=\"Shadow Chief Secretary to the Treasury\">Shadow Chief Secretary to the Treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chief_Secretary_to_the_Treasury\" title=\"Shadow Chief Secretary to the Treasury\">Shadow Chief Secretary to the Treasury</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Shadow Chief Secretary to the Treasury", "link": "https://wikipedia.org/wiki/Shadow_Chief_Secretary_to_the_Treasury"}]}, {"year": "1980", "text": "<PERSON>, Namibian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Namibian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kon%C3%A9\" title=\"<PERSON><PERSON> Koné\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kon%C3%A9\" title=\"<PERSON><PERSON> Koné\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bakari_Kon%C3%A9"}]}, {"year": "1981", "text": "<PERSON>, Solomon sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solomon sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Filipino singer, actor, director, and former chairman of the National Youth Commission of the Philippines (2016-18)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Se<PERSON>rra\" title=\"<PERSON> Seguerra\"><PERSON></a>, Filipino singer, actor, director, and former chairman of the <a href=\"https://wikipedia.org/wiki/National_Youth_Commission_(Philippines)\" title=\"National Youth Commission (Philippines)\">National Youth Commission of the Philippines</a> (2016-18)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Se<PERSON>rra\" title=\"<PERSON> Seguerra\"><PERSON></a>, Filipino singer, actor, director, and former chairman of the <a href=\"https://wikipedia.org/wiki/National_Youth_Commission_(Philippines)\" title=\"National Youth Commission (Philippines)\">National Youth Commission of the Philippines</a> (2016-18)", "links": [{"title": "<PERSON> Se<PERSON>rra", "link": "https://wikipedia.org/wiki/Ice_Se<PERSON>rra"}, {"title": "National Youth Commission (Philippines)", "link": "https://wikipedia.org/wiki/National_Youth_Commission_(Philippines)"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American swimmer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Dutch sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Czech tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Berdych\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Berdych\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Berdych"}]}, {"year": "1985", "text": "<PERSON>, Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Portuguese footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gon%C3%A7al<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gon%C3%A7<PERSON><PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Gon%C3%A7al<PERSON>_(footballer)"}]}, {"year": "1985", "text": "<PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Russian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>win"}]}, {"year": "1986", "text": "<PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Scottish music producer, disc jockey and singer (d. 2021)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish music producer, disc jockey and singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish music producer, disc jockey and singer (d. 2021)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Syrian footballer (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American soccer player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, English model and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English model and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Japanese golfer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Polish tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Just<PERSON>_Jegio%C5%82ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Just<PERSON>_Jegio%C5%82ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Justyna_Jegio%C5%82ka"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron King\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cameron King\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Egyptian-Canadian actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Canadian actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dominican baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%<PERSON><PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%<PERSON><PERSON>_(infielder)\" title=\"<PERSON> (infielder)\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON> (infielder)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%AD<PERSON>_(infielder)"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Moroccan footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al"}]}, {"year": "1993", "text": "<PERSON>, Scottish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1993", "text": "<PERSON>, Russian ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Na_In-woo\" title=\"Na In-woo\"><PERSON> <PERSON>-woo</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na_In-woo\" title=\"Na In-woo\"><PERSON> <PERSON>-woo</a>, South Korean actor", "links": [{"title": "<PERSON> <PERSON><PERSON>woo", "link": "https://wikipedia.org/wiki/Na_In-woo"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Salvadoran-American actress and singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Den<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran-American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran-American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Denyse_<PERSON>z"}]}, {"year": "1995", "text": "<PERSON>, Canadian ice hockey player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, South Korean singer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/YooA\" title=\"Yoo<PERSON>\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/YooA\" title=\"Yoo<PERSON>\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "YooA", "link": "https://wikipedia.org/wiki/YooA"}]}, {"year": "1996", "text": "<PERSON><PERSON>-<PERSON>, Croatian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Du<PERSON>_%C4%86aleta-Car\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du<PERSON>_%C4%86aleta-Car\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>-Car", "link": "https://wikipedia.org/wiki/Duje_%C4%86aleta-Car"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, French Formula One racing driver", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Esteban_Ocon\" title=\"Esteban Ocon\"><PERSON><PERSON><PERSON></a>, French Formula One racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Ocon\" title=\"Esteban Ocon\"><PERSON><PERSON><PERSON></a>, French Formula One racing driver", "links": [{"title": "Esteban Ocon", "link": "https://wikipedia.org/wiki/Esteban_Ocon"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American singer and songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Slayyyter\" title=\"Slayyyter\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slayyyter\" title=\"Slayyyter\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "Slayyyter", "link": "https://wikipedia.org/wiki/Slayyyter"}]}, {"year": "1996   -<PERSON>, South Korean singer and actor[64]", "text": null, "html": "1996   -<PERSON>, South Korean singer and actor[64] - 1996 -<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ae\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "1996 -<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>ae", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ae"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American ice hockey player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English actor", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Russian-Armenian  tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Armenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-Armenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "456", "text": "<PERSON><PERSON><PERSON>, Roman general", "html": "456 - <a href=\"https://wikipedia.org/wiki/<PERSON>mist<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Remistus"}]}, {"year": "936", "text": "<PERSON><PERSON>, archbishop of Hamburg-Bremen", "html": "936 - <a href=\"https://wikipedia.org/wiki/Un<PERSON>_(bishop)\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, archbishop of Hamburg-Bremen", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(bishop)\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, archbishop of Hamburg-Bremen", "links": [{"title": "<PERSON><PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(bishop)"}]}, {"year": "958", "text": "<PERSON>, Chinese prince (b. 920)", "html": "958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince (b. 920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese prince (b. 920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1025", "text": "<PERSON>, king of France (b. 1007)", "html": "1025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, king of France (b. 1007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, king of France (b. 1007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1148", "text": "<PERSON>, duke of Brittany (b. 1070)", "html": "1148 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1070)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1070)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1179", "text": "<PERSON><PERSON><PERSON> of Bingen, German abbess and polymath (b. 1098)", "html": "1179 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bingen\" title=\"<PERSON><PERSON><PERSON> of Bingen\"><PERSON><PERSON><PERSON> of Bingen</a>, German abbess and polymath (b. 1098)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Bingen\" title=\"<PERSON><PERSON><PERSON> of Bingen\"><PERSON><PERSON><PERSON> of Bingen</a>, German abbess and polymath (b. 1098)", "links": [{"title": "<PERSON><PERSON><PERSON> of Bingen", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bingen"}]}, {"year": "1322", "text": "<PERSON>, count of Flanders (b. 1249)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON></a>, count of Flanders (b. 1249)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders\" title=\"<PERSON>, Count of Flanders\"><PERSON></a>, count of Flanders (b. 1249)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Flanders"}]}, {"year": "1415", "text": "<PERSON>, 2nd Earl of Suffolk (b. 1367)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Suffolk\" title=\"<PERSON>, 2nd Earl of Suffolk\"><PERSON>, 2nd Earl of Suffolk</a> (b. 1367)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Suffolk\" title=\"<PERSON>, 2nd Earl of Suffolk\"><PERSON>, 2nd Earl of <PERSON></a> (b. 1367)", "links": [{"title": "<PERSON>, 2nd Earl of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Suffolk"}]}, {"year": "1422", "text": "<PERSON>, tsar of Bulgaria", "html": "1422 - <a href=\"https://wikipedia.org/wiki/Constantine_II_of_Bulgaria\" title=\"<PERSON> II of Bulgaria\"><PERSON> II</a>, tsar of Bulgaria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_II_of_Bulgaria\" title=\"Constantine II of Bulgaria\"><PERSON> II</a>, tsar of Bulgaria", "links": [{"title": "Constantine II of Bulgaria", "link": "https://wikipedia.org/wiki/Constantine_II_of_Bulgaria"}]}, {"year": "1482", "text": "<PERSON>, duke of Luxembourg (b. 1425)", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON></a>, duke of Luxembourg (b. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Thuringia\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON></a>, duke of Luxembourg (b. 1425)", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}]}, {"year": "1563", "text": "<PERSON>, 2nd Earl of Rutland, English soldier (b. 1526)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Rutland\" title=\"<PERSON>, 2nd Earl of Rutland\"><PERSON>, 2nd Earl of Rutland</a>, English soldier (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Rutland\" title=\"<PERSON>, 2nd Earl of Rutland\"><PERSON>, 2nd Earl of Rutland</a>, English soldier (b. 1526)", "links": [{"title": "<PERSON>, 2nd Earl of Rutland", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Rutland"}]}, {"year": "1574", "text": "<PERSON>, Spanish admiral and explorer, founded St. Augustine, Florida (b. 1519)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish admiral and explorer, founded <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a>, Spanish admiral and explorer, founded <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> (b. 1519)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s"}, {"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}]}, {"year": "1575", "text": "<PERSON>, Swiss theologian and reformer (b. 1504)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1504)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, Bohemian rabbi, mystic and philosopher (b. 1520)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON> ben <PERSON></a>, Bohemian rabbi, mystic and philosopher (b. 1520)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ben <PERSON>\"><PERSON></a>, Bohemian rabbi, mystic and philosopher (b. 1520)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, Italian cardinal and saint (b. 1542)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and saint (b. 1542)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, German cleric and politician, Archbishop-Elector of Mainz (b. 1553)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cleric and politician, Archbishop-Elector of Mainz (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cleric and politician, Archbishop<PERSON>Elector of Mainz (b. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1630", "text": "<PERSON>, English politician, English Secretary of State (b. 1567)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Lake\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_<PERSON>\" title=\"Thomas Lake\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">English Secretary of State</a> (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1637", "text": "<PERSON>, 2nd Baroness <PERSON>, English-Scottish peer", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baroness_<PERSON>\" title=\"<PERSON>, 2nd Baroness <PERSON>\"><PERSON>, 2nd Baroness <PERSON></a>, English-Scottish peer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baroness_<PERSON>\" title=\"<PERSON>, 2nd Baroness <PERSON>\"><PERSON>, 2nd Baroness <PERSON></a>, English-Scottish peer", "links": [{"title": "<PERSON>, 2nd Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baroness_<PERSON>"}]}, {"year": "1665", "text": "<PERSON>, king of Spain (b. 1605)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> IV of Spain\"><PERSON></a>, king of Spain (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> IV of Spain\"><PERSON> IV</a>, king of Spain (b. 1605)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1676", "text": "<PERSON><PERSON><PERSON>, Turkish rabbi and scholar (b. 1626)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/Sabbatai_Zevi\" title=\"Sabbatai Zevi\"><PERSON><PERSON><PERSON></a>, Turkish rabbi and scholar (b. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabbatai_Zevi\" title=\"Sabbatai Zevi\"><PERSON><PERSON><PERSON></a>, Turkish rabbi and scholar (b. 1626)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sabbatai_<PERSON>evi"}]}, {"year": "1679", "text": "<PERSON> of Austria the Younger, Spanish general and politician, Governor of the Habsburg Netherlands (b. 1629)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria_the_Younger\" class=\"mw-redirect\" title=\"<PERSON> of Austria the Younger\"><PERSON> of Austria the Younger</a>, Spanish general and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_the_Habsburg_Netherlands\" title=\"List of governors of the Habsburg Netherlands\">Governor of the Habsburg Netherlands</a> (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria_the_Younger\" class=\"mw-redirect\" title=\"<PERSON> of Austria the Younger\"><PERSON> of Austria the Younger</a>, Spanish general and politician, <a href=\"https://wikipedia.org/wiki/List_of_governors_of_the_Habsburg_Netherlands\" title=\"List of governors of the Habsburg Netherlands\">Governor of the Habsburg Netherlands</a> (b. 1629)", "links": [{"title": "<PERSON> of Austria the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria_the_Younger"}, {"title": "List of governors of the Habsburg Netherlands", "link": "https://wikipedia.org/wiki/List_of_governors_of_the_Habsburg_Netherlands"}]}, {"year": "1701", "text": "<PERSON><PERSON><PERSON>, Polish priest and saint (b. 1631)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish priest and saint (b. 1631)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>laus_Papczy%C5%84ski"}]}, {"year": "1721", "text": "<PERSON>, French princess (b. 1645)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, French princess (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, French princess (b. 1645)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans"}]}, {"year": "1762", "text": "<PERSON>, Italian violinist and composer (b. 1687)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (b. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, Scottish author and poet (b. 1721)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and poet (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Austrian composer and director (b. 1766)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Franz_Xaver_S%C3%BCssmayr\" title=\"Franz X<PERSON> Süssmayr\"><PERSON></a>, Austrian composer and director (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>aver_S%C3%BCssmayr\" title=\"Franz <PERSON> S<PERSON>mayr\"><PERSON></a>, Austrian composer and director (b. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Xaver_S%C3%BCssmayr"}]}, {"year": "1808", "text": "<PERSON>, American judge and politician (b. 1755)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician (b. 1755)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, French general (b. 1740)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Anselme\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Anselme\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27A<PERSON><PERSON><PERSON>"}]}, {"year": "1836", "text": "<PERSON>, French botanist and author (b. 1748)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and author (b. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French botanist and author (b. 1748)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Mexican businessman and politician. President (1841) (b. 1797)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician. President (1841) (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician. President (1841) (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADa"}]}, {"year": "1858", "text": "<PERSON><PERSON>, American slave (b. 1795)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American slave (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American slave (b. 1795)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American politician and Confederate general (b. 1820)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Lawrence_O%27Bryan_Branch\" title=\"<PERSON>'Bryan Branch\"><PERSON></a>, American politician and <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> general (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawrence_O%27Bryan_Branch\" title=\"<PERSON>'Bryan Branch\"><PERSON></a>, American politician and <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> general (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lawrence_O%27Bryan_Branch"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1862", "text": "<PERSON>, Confederate general (b. 1814)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> general (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> general (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1863", "text": "<PERSON>, English archaeologist and architect (b. 1788)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and architect (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and architect (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, French author, poet, and playwright (b. 1797)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English author and poet (b. 1775)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Native American warrior (b. circa 1823)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Roman_Nose\" class=\"mw-redirect\" title=\"Roman Nose\">Roman Nose</a>, Native American warrior (b. circa 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Nose\" class=\"mw-redirect\" title=\"Roman Nose\">Roman Nose</a>, Native American warrior (b. circa 1823)", "links": [{"title": "Roman Nose", "link": "https://wikipedia.org/wiki/Roman_Nose"}]}, {"year": "1877", "text": "<PERSON>, English photographer, developed the <PERSON><PERSON> (b. 1800)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer, developed the <a href=\"https://wikipedia.org/wiki/Calotype\" title=\"Calotype\">Calotype Process</a> (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer, developed the <a href=\"https://wikipedia.org/wiki/Calotype\" title=\"Calotype\">Calotype Process</a> (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Calotype", "link": "https://wikipedia.org/wiki/Calotype"}]}, {"year": "1878", "text": "Or<PERSON><PERSON><PERSON><PERSON>, French lawyer and adventurer (b. 1825)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and adventurer (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French lawyer and adventurer (b. 1825)", "links": [{"title": "Orélie<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Or%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, French architect and theorist (b. 1814)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and theorist (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect and theorist (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>-<PERSON>-<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German jurist (b. 1818)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, Chinese captain (b. 1849)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese captain (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese captain (b. 1849)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deng_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American businessman, co-founded the Pillsbury Company (b. 1842)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Pillsbury_Company\" class=\"mw-redirect\" title=\"Pillsbury Company\">Pillsbury Company</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Pillsbury_Company\" class=\"mw-redirect\" title=\"Pillsbury Company\">Pillsbury Company</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Pillsbury Company", "link": "https://wikipedia.org/wiki/Pillsbury_Company"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Czech-Austrian pianist and composer (b. 1846)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Ignaz_Br%C3%BCll\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Austrian pianist and composer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ignaz_Br%C3%BCll\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Austrian pianist and composer (b. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ignaz_Br%C3%BCll"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American sculptor (b. 1844)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sculptor (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sculptor (b. 1844)", "links": [{"title": "<PERSON><PERSON> Lewis", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lewis"}]}, {"year": "1908", "text": "<PERSON>, Canadian cartoonist (b. 1852)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian cartoonist (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American lieutenant and pilot (b. 1882)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Australian businessman and politician, 22nd Premier of Victoria (b. 1838)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 22nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Greek judge and politician, 92nd Prime Minister of Greece (b. 1842)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek judge and politician, 92nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek judge and politician, 92nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1842)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1925", "text": "<PERSON>, German-American painter and illustrator (b. 1862)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American painter and illustrator (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Maltese priest and missionary (b. 1877)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese priest and missionary (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese priest and missionary (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, New Zealand author and activist (b. 1877)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand author and activist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand author and activist (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, German logician and philosopher of science, Vienna circle member (b. 1895)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German logician and philosopher of science, Vienna circle member (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German logician and philosopher of science, Vienna circle member (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Polish poet and author (b. 1901)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish poet and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bruno_J<PERSON>e%C5%84ski"}]}, {"year": "1943", "text": "<PERSON>, German general (b. 1893)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American anthropologist and academic (b. 1887)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist and academic (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Swedish soldier and diplomat (b. 1895)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Folke_Bernadotte\" title=\"Folke Bernadotte\"><PERSON><PERSON></a>, Swedish soldier and diplomat (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Folke_Bernadotte\" title=\"Folke Bernadotte\"><PERSON><PERSON></a>, Swedish soldier and diplomat (b. 1895)", "links": [{"title": "Folke Bern<PERSON>tte", "link": "https://wikipedia.org/wiki/Folke_Bernadotte"}]}, {"year": "1951", "text": "<PERSON>, American pianist and composer (b. 1898)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American runner (b. 1884)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German general (Wehrmacht) (b. 1880)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (Wehrmacht) (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (Wehrmacht) (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Turkish lawyer and politician, 9th Prime Minister of Turkey (b. 1899)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Prime Ministers of Turkey\">Prime Minister of Turkey</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Turkey"}]}, {"year": "1965", "text": "<PERSON>, Spanish poet and playwright (b. 1903)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish poet and playwright (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German tenor and actor (b. 1930)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor and actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Brazilian captain (b. 1937)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian captain (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian captain (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actor (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American bandleader and composer (b. 1909)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and composer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Greek-American operatic bass (b. 1907)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American operatic bass (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American operatic bass (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Nicaraguan commander and politician, 73rd President of Nicaragua (b. 1925)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Anastasio_<PERSON>_<PERSON>\" title=\"Anastasio <PERSON>mo<PERSON>\">Ana<PERSON><PERSON></a>, Nicaraguan commander and politician, 73rd <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anastasio_<PERSON>_<PERSON>\" title=\"Anastasio Somo<PERSON>\">Anasta<PERSON></a>, Nicaraguan commander and politician, 73rd <a href=\"https://wikipedia.org/wiki/President_of_Nicaragua\" class=\"mw-redirect\" title=\"President of Nicaragua\">President of Nicaragua</a> (b. 1925)", "links": [{"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Nicaragua", "link": "https://wikipedia.org/wiki/President_of_Nicaragua"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Egyptian-Greek composer (b. 1937)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_Lo%C3%AFzos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lo%C3%AFzos\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek composer (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manos_Lo%C3%AFzos"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Portuguese-American cardinal (b. 1915)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros\" title=\"<PERSON><PERSON><PERSON> Sousa Medeiros\"><PERSON><PERSON><PERSON></a>, Portuguese-American cardinal (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros\" title=\"<PERSON><PERSON><PERSON> Sousa Medeiros\"><PERSON><PERSON><PERSON> Mede<PERSON></a>, Portuguese-American cardinal (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON> Medeiro<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sousa_Medeiros"}]}, {"year": "1984", "text": "<PERSON>, American actor and director (b. 1914)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Welsh fashion designer, founded Laura Ashley plc (b. 1925)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_plc\" class=\"mw-redirect\" title=\"Laura Ashley plc\">Laura Ashley plc</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_plc\" class=\"mw-redirect\" title=\"Laura Ashley plc\">Laura Ashley plc</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Laura Ashley plc", "link": "https://wikipedia.org/wiki/Laura_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English actor (b. 1913)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, French violinist and composer (b. 1902)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist and composer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French violinist and composer (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American conductor and educator (b. 1914)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American pool player and actor (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Pool_(cue_sports)\" title=\"Pool (cue sports)\">pool</a> player and actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Pool_(cue_sports)\" title=\"Pool (cue sports)\">pool</a> player and actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pool (cue sports)", "link": "https://wikipedia.org/wiki/Pool_(cue_sports)"}]}, {"year": "1993", "text": "<PERSON>, American director and producer (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nyby"}]}, {"year": "1994", "text": "<PERSON>, American accordion player (b. 1939)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accordion player (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American tennis player and coach (b. 1954)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gerulaitis\" title=\"<PERSON><PERSON> Gerulaitis\"><PERSON><PERSON></a>, American tennis player and coach (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>itis\" title=\"<PERSON><PERSON> Gerulaitis\"><PERSON><PERSON></a>, American tennis player and coach (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>ulaitis"}]}, {"year": "1994", "text": "<PERSON>, Austrian-English philosopher and academic (b. 1902)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English philosopher and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Estonian-American astronomer and academic (b. 1919)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American astronomer and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American astronomer and academic (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Belgian cyclist (b. 1931)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American soldier and politician, 39th Vice President of the United States (b. 1918)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gne<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier and politician, 39th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>gnew"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1997", "text": "<PERSON>, American actor and comedian (b. 1913)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Red_S<PERSON>ton\" title=\"Red Skelton\"><PERSON></a>, American actor and comedian (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Skelton\" title=\"<PERSON> Skelton\"><PERSON></a>, American actor and comedian (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Red_Skelton"}]}, {"year": "1998", "text": "<PERSON>, American poker player and businessman (b. 1943)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and businessman (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and businessman (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ted_Binion"}]}, {"year": "1998", "text": "<PERSON>, Australian historian and author (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English singer and actor (b. 1928)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Georgian-Ukrainian journalist and director (b. 1969)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Ukrainian journalist and director (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-Ukrainian journalist and director (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, German actor (b. 1951)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French journalist and critic (b. 1925)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French journalist and critic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French journalist and critic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "2005", "text": "<PERSON>, American composer and educator (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American stuntman and actor (b. 1937)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stuntman and actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stuntman and actor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Malaysian terrorist (b. 1968)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian terrorist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian terrorist (b. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian architect and author, designed the National Gallery of Australia (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and author, designed the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Australia\" title=\"National Gallery of Australia\">National Gallery of Australia</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and author, designed the <a href=\"https://wikipedia.org/wiki/National_Gallery_of_Australia\" title=\"National Gallery of Australia\">National Gallery of Australia</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Gallery of Australia", "link": "https://wikipedia.org/wiki/National_Gallery_of_Australia"}]}, {"year": "2012", "text": "<PERSON>, Canadian sculptor and architect (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and architect (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and architect (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English soldier and potter (b. 1908)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and potter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and potter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soldier and civil servant (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Russell <PERSON> Train\"><PERSON></a>, American soldier and civil servant (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Russell <PERSON>\"><PERSON></a>, American soldier and civil servant (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Swedish drummer and journalist  (b. 1983)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish drummer and journalist (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish drummer and journalist (b. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American-Canadian trumpet player and composer (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Canadian trumpet player and composer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American-Canadian trumpet player and composer (b. 1943)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2013", "text": "<PERSON>, Australian saxophonist and composer (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian saxophonist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Lithuanian-Norwegian singer-songwriter and producer (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Norwegian singer-songwriter and producer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Norwegian singer-songwriter and producer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Irish farmer and politician, 25th Irish Minister of Defence (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_(Fianna_F%C3%A1il_politician)\" title=\"<PERSON> (Fianna Fáil politician)\"><PERSON></a>, Irish farmer and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)\" title=\"Minister for Defence (Ireland)\">Irish Minister of Defence</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(Fianna_F%C3%A1il_politician)\" title=\"<PERSON> (Fianna Fáil politician)\"><PERSON></a>, Irish farmer and politician, 25th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)\" title=\"Minister for Defence (Ireland)\">Irish Minister of Defence</a> (b. 1935)", "links": [{"title": "<PERSON> (Fianna Fáil politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_(Fianna_F%C3%<PERSON>il_politician)"}, {"title": "Minister for Defence (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Japanese businessman (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese businessman (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oda"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American singer-songwriter and guitarist (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (b. 1972)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (b. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (b. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waka<PERSON><PERSON><PERSON>_<PERSON>mei"}]}, {"year": "2014", "text": "<PERSON>, Australian air marshal (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)\" title=\"<PERSON> (RAAF officer)\"><PERSON></a>, Australian air marshal (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)\" title=\"<PERSON> (RAAF officer)\"><PERSON></a>, Australian air marshal (b. 1918)", "links": [{"title": "<PERSON> (RAAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)"}]}, {"year": "2014", "text": "<PERSON>, Finnish historian, director, and screenwriter (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish historian, director, and screenwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish historian, director, and screenwriter (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Uruguayan actress (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/China_Zorrilla\" title=\"China Zorrilla\">China Zorrilla</a>, Uruguayan actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Zorrilla\" title=\"China Zorrilla\">China Zorrilla</a>, Uruguayan actress (b. 1922)", "links": [{"title": "China Zorrilla", "link": "https://wikipedia.org/wiki/China_Zorrilla"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian actress (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Ingr%C4%ABda_Andri%C5%86a\" title=\"Ingr<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian actress (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingr%C4%ABda_Andri%C5%86a\" title=\"Ingr<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian actress (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ingr%C4%ABda_Andri%C5%86a"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, German footballer and manager (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American sportscaster (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)\" title=\"<PERSON><PERSON><PERSON> (physicist)\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)\" title=\"<PERSON><PERSON><PERSON> (physicist)\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(physicist)"}]}, {"year": "2015", "text": "<PERSON>, English organist, composer, and conductor (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist, composer, and conductor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Iranian racing cyclist (b. 1968)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Golbarnezhad\" title=\"<PERSON><PERSON> Go<PERSON>bar<PERSON>\"><PERSON><PERSON></a>, Iranian racing cyclist (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Golbarnezhad\" title=\"<PERSON>hman Go<PERSON>bar<PERSON>\"><PERSON><PERSON></a>, Iranian racing cyclist (b. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bahman_Golbarnezhad"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Swedish footballer (b. 1936)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Sigg<PERSON>_Parling\" title=\"Sigge Parling\"><PERSON><PERSON><PERSON></a>, Swedish footballer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sigg<PERSON>_Parling\" title=\"Sigge Parling\"><PERSON><PERSON><PERSON></a>, Swedish footballer (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sigge_Parling"}]}, {"year": "2017", "text": "<PERSON>, American professional wrestling manager (b. 1944)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling manager (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling manager (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, American journalist and bestselling author (b. 1943)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and bestselling author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and bestselling author (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American engineer and businessman, co-inventor of Gore-Tex (b. 1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-inventor of <a href=\"https://wikipedia.org/wiki/Gore-Tex\" title=\"Gore-Tex\">Gore-Tex</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-inventor of <a href=\"https://wikipedia.org/wiki/Gore-Tex\" title=\"Gore-Tex\"><PERSON>-Tex</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Gore-Tex", "link": "https://wikipedia.org/wiki/Gore-Tex"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian politician, President of Algeria (b. 1937)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>flika\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian politician, President of Algeria (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>f<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian politician, President of Algeria (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Bouteflika"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Dutch astronomer (b. 1929)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lieutenant and author (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and author (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeMille"}]}, {"year": "2024", "text": "<PERSON>, American journalist and author (b. 1959)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American journalist and author (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American journalist and author (b. 1959)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2024", "text": "<PERSON><PERSON>, American singer, songwriter, and actor (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/JD_<PERSON>er\" title=\"J<PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, and actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/JD_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, and actor (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>_<PERSON>er"}]}]}}