{"date": "January 6", "url": "https://wikipedia.org/wiki/January_6", "data": {"Events": [{"year": "1066", "text": "Following the death of <PERSON> the Confessor on the previous day, the Witan meets to confirm <PERSON> as the new King of England; <PERSON> is crowned the same day, sparking a succession crisis that will eventually lead to the Norman conquest of England.", "html": "1066 - Following the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a> on the previous day, the <a href=\"https://wikipedia.org/wiki/Witan\" title=\"Witan\">Witan</a> meets to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the new King of England; <PERSON> is crowned the same day, sparking a succession crisis that will eventually lead to the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest of England</a>.", "no_year_html": "Following the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Confessor\" title=\"<PERSON> the Confessor\"><PERSON> the Confessor</a> on the previous day, the <a href=\"https://wikipedia.org/wiki/Witan\" title=\"Witan\">Witan</a> meets to confirm <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> as the new King of England; <PERSON> is crowned the same day, sparking a succession crisis that will eventually lead to the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest of England</a>.", "links": [{"title": "<PERSON> the Confessor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Confessor"}, {"title": "Witan", "link": "https://wikipedia.org/wiki/Witan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Norman conquest of England", "link": "https://wikipedia.org/wiki/Norman_conquest_of_England"}]}, {"year": "1205", "text": "<PERSON> of Swabia undergoes a second coronation as King of the Romans.", "html": "1205 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a> undergoes a second coronation as <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Swabia\" title=\"<PERSON> of Swabia\"><PERSON> of Swabia</a> undergoes a second coronation as <a href=\"https://wikipedia.org/wiki/King_of_the_Romans\" title=\"King of the Romans\">King of the Romans</a>.", "links": [{"title": "Philip of Swabia", "link": "https://wikipedia.org/wiki/Philip_of_Swabia"}, {"title": "King of the Romans", "link": "https://wikipedia.org/wiki/King_of_the_Romans"}]}, {"year": "1322", "text": "<PERSON> is crowned King of Serbia, having defeated his half-brother <PERSON> in battle. His son is crowned \"young king\" in the same ceremony.", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">King of Serbia</a>, having defeated his half-brother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in battle. His son is crowned \"young king\" in the same ceremony.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">King of Serbia</a>, having defeated his half-brother <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in battle. His son is crowned \"young king\" in the same ceremony.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Danski"}, {"title": "Kingdom of Serbia (medieval)", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1355", "text": "<PERSON> of Bohemia is crowned with the Iron Crown of Lombardy as King of Italy in Milan.", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> of Bohemia</a> is crowned with the <a href=\"https://wikipedia.org/wiki/Iron_Crown_of_Lombardy\" class=\"mw-redirect\" title=\"Iron Crown of Lombardy\">Iron Crown of Lombardy</a> as <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON> of Bohemia</a> is crowned with the <a href=\"https://wikipedia.org/wiki/Iron_Crown_of_Lombardy\" class=\"mw-redirect\" title=\"Iron Crown of Lombardy\">Iron Crown of Lombardy</a> as <a href=\"https://wikipedia.org/wiki/King_of_Italy\" title=\"King of Italy\">King of Italy</a> in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Iron Crown of Lombardy", "link": "https://wikipedia.org/wiki/Iron_Crown_of_Lombardy"}, {"title": "King of Italy", "link": "https://wikipedia.org/wiki/King_of_Italy"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1449", "text": "<PERSON> is crowned Byzantine Emperor at Mystras.", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XI_Palaiologos\" title=\"<PERSON> XI Palaiologos\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> at <a href=\"https://wikipedia.org/wiki/Mystras\" title=\"<PERSON><PERSON><PERSON>\">Mystra<PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_XI_Palaiologos\" title=\"Constantine XI Palaiologos\"><PERSON> XI</a> is crowned <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine Emperor</a> at <a href=\"https://wikipedia.org/wiki/Mystras\" title=\"<PERSON><PERSON><PERSON>\">My<PERSON><PERSON></a>.", "links": [{"title": "Constantine XI Palaiologos", "link": "https://wikipedia.org/wiki/Constantine_XI_Palaiologos"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mystras"}]}, {"year": "1492", "text": "The Catholic Monarchs <PERSON> and <PERSON> enter Granada at the conclusion of the Granada War.", "html": "1492 - The Catholic Monarchs <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and Isabella\"><PERSON> and <PERSON></a> enter Granada at the conclusion of the <a href=\"https://wikipedia.org/wiki/Granada_War\" title=\"Granada War\">Granada War</a>.", "no_year_html": "The Catholic Monarchs <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and Isabella\"><PERSON> and <PERSON></a> enter Granada at the conclusion of the <a href=\"https://wikipedia.org/wiki/Granada_War\" title=\"Granada War\">Granada War</a>.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}, {"title": "Granada War", "link": "https://wikipedia.org/wiki/Granada_War"}]}, {"year": "1536", "text": "The first European school of higher learning in the Americas, Colegio de Santa Cruz de Tlatelolco, is founded by Viceroy <PERSON> and Bishop <PERSON> in Mexico City.", "html": "1536 - The first European school of higher learning in the Americas, <a href=\"https://wikipedia.org/wiki/Colegio_de_Santa_Cruz_de_Tlatelolco\" title=\"Colegio de Santa Cruz de Tlatelolco\">Colegio de Santa Cruz de Tlatelolco</a>, is founded by <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Antonio_de_Mendoza\" title=\"Antonio de Mendoza\"><PERSON></a> and Bishop <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>um%C3%A1rraga\" title=\"<PERSON> Zumárraga\"><PERSON></a> in Mexico City.", "no_year_html": "The first European school of higher learning in the Americas, <a href=\"https://wikipedia.org/wiki/Colegio_de_Santa_Cruz_de_Tlatelolco\" title=\"Colegio de Santa Cruz de Tlatelolco\">Colegio de Santa Cruz de Tlatelolco</a>, is founded by <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Antonio_<PERSON>\" title=\"Antonio <PERSON>\"><PERSON></a> and <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>um%C3%A1rraga\" title=\"<PERSON>um<PERSON>\"><PERSON></a> in Mexico City.", "links": [{"title": "Colegio de Santa Cruz de Tlatelolco", "link": "https://wikipedia.org/wiki/Colegio_de_Santa_Cruz_de_Tlatelolco"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rraga"}]}, {"year": "1540", "text": "King <PERSON> of England marries <PERSON> of Cleves.", "html": "1540 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON> Cleves</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cleves\"><PERSON>leves</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "<PERSON> Cleves", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "The Union of Arras unites the southern Netherlands under the <PERSON> of Parma (<PERSON><PERSON><PERSON>), governor in the name of King <PERSON> of Spain.", "html": "1579 - The <a href=\"https://wikipedia.org/wiki/Union_of_Arras\" title=\"Union of Arras\">Union of Arras</a> unites the southern Netherlands under the <a href=\"https://wikipedia.org/wiki/Duke_of_Parma\" title=\"Duke of Parma\">Duke of Parma</a> (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON><PERSON><PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON></a>), governor in the name of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\">King <PERSON> of Spain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Union_of_Arras\" title=\"Union of Arras\">Union of Arras</a> unites the southern Netherlands under the <a href=\"https://wikipedia.org/wiki/Duke_of_Parma\" title=\"Duke of Parma\">Duke of Parma</a> (<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON><PERSON><PERSON>, Duke of Parma\"><PERSON><PERSON><PERSON></a>), governor in the name of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\">King <PERSON> of Spain</a>.", "links": [{"title": "Union of Arras", "link": "https://wikipedia.org/wiki/Union_of_Arras"}, {"title": "Duke of Parma", "link": "https://wikipedia.org/wiki/Duke_of_Parma"}, {"title": "<PERSON><PERSON><PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Parma"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1641", "text": "Arauco War: The first Parliament of Quillín is celebrated, putting a temporary hold on hostilities between Mapuches and Spanish in Chile.", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: The first <a href=\"https://wikipedia.org/wiki/Parliament_of_Quill%C3%ADn_(1641)\" class=\"mw-redirect\" title=\"Parliament of Quillín (1641)\">Parliament of Quillín</a> is celebrated, putting a temporary hold on hostilities between <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> and Spanish in Chile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arauco_War\" title=\"Arauco War\">Arauco War</a>: The first <a href=\"https://wikipedia.org/wiki/Parliament_of_Quill%C3%ADn_(1641)\" class=\"mw-redirect\" title=\"Parliament of Quillín (1641)\">Parliament of Quillín</a> is celebrated, putting a temporary hold on hostilities between <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> and Spanish in Chile.", "links": [{"title": "Arauco War", "link": "https://wikipedia.org/wiki/Arauco_War"}, {"title": "Parliament of Quillín (1641)", "link": "https://wikipedia.org/wiki/Parliament_of_Quill%C3%ADn_(1641)"}, {"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}]}, {"year": "1661", "text": "English Restoration: The Fifth Monarchists unsuccessfully attempt to seize control of London, England. The revolt is suppressed after a few days.", "html": "1661 - <a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: The <a href=\"https://wikipedia.org/wiki/Fifth_Monarchists\" title=\"Fifth Monarchists\">Fifth Monarchists</a> unsuccessfully attempt to seize control of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England. The revolt is suppressed after a few days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: The <a href=\"https://wikipedia.org/wiki/Fifth_Monarchists\" title=\"Fifth Monarchists\">Fifth Monarchists</a> unsuccessfully attempt to seize control of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, England. The revolt is suppressed after a few days.", "links": [{"title": "Restoration (England)", "link": "https://wikipedia.org/wiki/Restoration_(England)"}, {"title": "Fifth Monarchists", "link": "https://wikipedia.org/wiki/Fifth_Monarchists"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1721", "text": "The Committee of Inquiry on the South Sea Bubble publishes its findings, revealing details of fraud among company directors and corrupt politicians.", "html": "1721 - The Committee of Inquiry on the <a href=\"https://wikipedia.org/wiki/South_Sea_Company\" title=\"South Sea Company\">South Sea Bubble</a> publishes its findings, revealing details of fraud among company directors and corrupt politicians.", "no_year_html": "The Committee of Inquiry on the <a href=\"https://wikipedia.org/wiki/South_Sea_Company\" title=\"South Sea Company\">South Sea Bubble</a> publishes its findings, revealing details of fraud among company directors and corrupt politicians.", "links": [{"title": "South Sea Company", "link": "https://wikipedia.org/wiki/South_Sea_Company"}]}, {"year": "1724", "text": "<PERSON><PERSON> werden aus Saba alle kommen, BWV 65, a Bach cantata, for <PERSON><PERSON><PERSON><PERSON>, is performed the first time.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_werden_aus_Saba_alle_kommen,_BWV_65\" title=\"Sie werden aus Saba alle kommen, BWV 65\"><i>Sie werden aus Saba alle kommen</i>, BWV 65</a>, a <a href=\"https://wikipedia.org/wiki/Bach_cantata\" title=\"Bach cantata\">Bach cantata</a>, for <a href=\"https://wikipedia.org/wiki/Epiphany_(holiday)\" title=\"Epiphany (holiday)\">Epiphany</a>, is performed the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_werden_aus_Saba_alle_kommen,_BWV_65\" title=\"Sie werden aus Saba alle kommen, BWV 65\"><i>Sie werden aus Saba alle kommen</i>, BWV 65</a>, a <a href=\"https://wikipedia.org/wiki/Bach_cantata\" title=\"Bach cantata\">Bach cantata</a>, for <a href=\"https://wikipedia.org/wiki/Epiphany_(holiday)\" title=\"Epiphany (holiday)\">Epiphany</a>, is performed the first time.", "links": [{"title": "<PERSON><PERSON> werden aus Saba alle kommen, BWV 65", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_we<PERSON>_aus_<PERSON><PERSON>_alle_kommen,_BWV_65"}, {"title": "Bach cantata", "link": "https://wikipedia.org/wiki/Bach_cantata"}, {"title": "Epiphany (holiday)", "link": "https://wikipedia.org/wiki/Epiphany_(holiday)"}]}, {"year": "1725", "text": "<PERSON><PERSON> <PERSON><PERSON> leads the first performance of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> der Frommen, BWV 123, a chorale cantata for <PERSON><PERSON><PERSON><PERSON>.", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>,_Herzo<PERSON>_der_Frommen,_BWV_123\" title=\"<PERSON><PERSON><PERSON> Immanuel, Herzo<PERSON> der Frommen, BWV 123\"><i><PERSON><PERSON><PERSON> Immanuel, Herzog der Frommen</i>, BWV 123</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> for <a href=\"https://wikipedia.org/wiki/Epiphany_(holiday)\" title=\"Epiphany (holiday)\">Epiphany</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>,_Herzo<PERSON>_der_Frommen,_BWV_123\" title=\"<PERSON><PERSON><PERSON>, Herzo<PERSON> der Frommen, BWV 123\"><i><PERSON><PERSON><PERSON> Immanuel, Herzog der Frommen</i>, BWV 123</a>, a <a href=\"https://wikipedia.org/wiki/Chorale_cantata_cycle\" title=\"Chorale cantata cycle\">chorale cantata</a> for <a href=\"https://wikipedia.org/wiki/Epiphany_(holiday)\" title=\"Epiphany (holiday)\">Epiphany</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> der Frommen, BWV 123", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>,_Herzo<PERSON>_der_From<PERSON>,_BWV_123"}, {"title": "Chorale cantata cycle", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_cantata_cycle"}, {"title": "Epiphany (holiday)", "link": "https://wikipedia.org/wiki/Epiphany_(holiday)"}]}, {"year": "1781", "text": "In the Battle of Jersey, the British defeat the last attempt by France to invade Jersey in the Channel Islands.", "html": "1781 - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Jersey\" title=\"Battle of Jersey\">Battle of Jersey</a>, the British defeat the last attempt by France to invade Jersey in the <a href=\"https://wikipedia.org/wiki/Channel_Islands\" title=\"Channel Islands\">Channel Islands</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Battle_of_Jersey\" title=\"Battle of Jersey\">Battle of Jersey</a>, the British defeat the last attempt by France to invade Jersey in the <a href=\"https://wikipedia.org/wiki/Channel_Islands\" title=\"Channel Islands\">Channel Islands</a>.", "links": [{"title": "Battle of Jersey", "link": "https://wikipedia.org/wiki/Battle_of_Jersey"}, {"title": "Channel Islands", "link": "https://wikipedia.org/wiki/Channel_Islands"}]}, {"year": "1809", "text": "Combined British, Portuguese and colonial Brazilian forces begin the Invasion of Cayenne during the Napoleonic Wars.", "html": "1809 - Combined British, Portuguese and colonial Brazilian forces begin the <a href=\"https://wikipedia.org/wiki/Invasion_of_Cayenne_(1809)\" class=\"mw-redirect\" title=\"Invasion of Cayenne (1809)\">Invasion of Cayenne</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "no_year_html": "Combined British, Portuguese and colonial Brazilian forces begin the <a href=\"https://wikipedia.org/wiki/Invasion_of_Cayenne_(1809)\" class=\"mw-redirect\" title=\"Invasion of Cayenne (1809)\">Invasion of Cayenne</a> during the <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>.", "links": [{"title": "Invasion of Cayenne (1809)", "link": "https://wikipedia.org/wiki/Invasion_of_Cayenne_(1809)"}, {"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}]}, {"year": "1838", "text": "<PERSON> and colleagues demonstrate a telegraph system using dots and dashes (this is the forerunner of Morse code).", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and colleagues demonstrate a <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> system using dots and dashes (this is the forerunner of <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and colleagues demonstrate a <a href=\"https://wikipedia.org/wiki/Telegraphy\" title=\"Telegraphy\">telegraph</a> system using dots and dashes (this is the forerunner of <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ail"}, {"title": "Telegraphy", "link": "https://wikipedia.org/wiki/Telegraphy"}, {"title": "Morse code", "link": "https://wikipedia.org/wiki/Morse_code"}]}, {"year": "1839", "text": "The Night of the Big Wind, the most damaging storm in 300 years, sweeps across Ireland, damaging or destroying more than 20% of the houses in Dublin.", "html": "1839 - The <a href=\"https://wikipedia.org/wiki/Night_of_the_Big_Wind\" title=\"Night of the Big Wind\">Night of the Big Wind</a>, the most damaging storm in 300 years, sweeps across Ireland, damaging or destroying more than 20% of the houses in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Night_of_the_Big_Wind\" title=\"Night of the Big Wind\">Night of the Big Wind</a>, the most damaging storm in 300 years, sweeps across Ireland, damaging or destroying more than 20% of the houses in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "links": [{"title": "Night of the Big Wind", "link": "https://wikipedia.org/wiki/Night_of_the_Big_Wind"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "1847", "text": "<PERSON> obtains his first contract for the sale of revolver pistols to the United States government.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Samuel Colt\"><PERSON></a> obtains his first contract for the sale of <a href=\"https://wikipedia.org/wiki/Revolver\" title=\"Revolver\">revolver</a> pistols to the United States government.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Samuel Colt\"><PERSON></a> obtains his first contract for the sale of <a href=\"https://wikipedia.org/wiki/Revolver\" title=\"Revolver\">revolver</a> pistols to the United States government.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Revolver", "link": "https://wikipedia.org/wiki/Revolver"}]}, {"year": "1870", "text": "The inauguration of the Musikverein in Vienna, Austria.", "html": "1870 - The inauguration of the <a href=\"https://wikipedia.org/wiki/Musikverein\" title=\"Musikverein\">Musikverein</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna, Austria</a>.", "no_year_html": "The inauguration of the <a href=\"https://wikipedia.org/wiki/Musikverein\" title=\"Musikverein\">Musikverein</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna, Austria</a>.", "links": [{"title": "Musikverein", "link": "https://wikipedia.org/wiki/Musikverein"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1893", "text": "The Washington National Cathedral is chartered by Congress. The charter is signed by President <PERSON>.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/Washington_National_Cathedral\" title=\"Washington National Cathedral\">Washington National Cathedral</a> is chartered by <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>. The charter is signed by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Washington_National_Cathedral\" title=\"Washington National Cathedral\">Washington National Cathedral</a> is chartered by <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>. The charter is signed by <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Washington National Cathedral", "link": "https://wikipedia.org/wiki/Washington_National_Cathedral"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "Second Boer War: Having already besieged the fortress at Ladysmith, Boer forces attack it, but are driven back by British defenders.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: Having already <a href=\"https://wikipedia.org/wiki/Siege_of_Ladysmith\" title=\"Siege of Ladysmith\">besieged</a> the fortress at <a href=\"https://wikipedia.org/wiki/Ladysmith,_KwaZulu-Natal\" title=\"Ladysmith, KwaZulu-Natal\">Ladysmith</a>, <a href=\"https://wikipedia.org/wiki/Boer\" class=\"mw-redirect\" title=\"Boer\">Boer</a> forces attack it, but are driven back by <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> defenders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Boer_War\" title=\"Second Boer War\">Second Boer War</a>: Having already <a href=\"https://wikipedia.org/wiki/Siege_of_Ladysmith\" title=\"Siege of Ladysmith\">besieged</a> the fortress at <a href=\"https://wikipedia.org/wiki/Ladysmith,_KwaZulu-Natal\" title=\"Ladysmith, KwaZulu-Natal\">Ladysmith</a>, <a href=\"https://wikipedia.org/wiki/Boer\" class=\"mw-redirect\" title=\"Boer\">Boer</a> forces attack it, but are driven back by <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a> defenders.", "links": [{"title": "Second Boer War", "link": "https://wikipedia.org/wiki/Second_Boer_War"}, {"title": "Siege of Ladysmith", "link": "https://wikipedia.org/wiki/Siege_of_Ladysmith"}, {"title": "Ladysmith, KwaZulu-Natal", "link": "https://wikipedia.org/wiki/Ladysmith,_KwaZulu-Natal"}, {"title": "Boer", "link": "https://wikipedia.org/wiki/Boer"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1907", "text": "<PERSON> opens her first school and daycare center for working class children in Rome, Italy.", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens her first school and daycare center for <a href=\"https://wikipedia.org/wiki/Working_class\" title=\"Working class\">working class</a> children in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens her first school and daycare center for <a href=\"https://wikipedia.org/wiki/Working_class\" title=\"Working class\">working class</a> children in <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>, Italy.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Montessori"}, {"title": "Working class", "link": "https://wikipedia.org/wiki/Working_class"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}]}, {"year": "1912", "text": "New Mexico is admitted to the Union as the 47th U.S. state.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a> is admitted to the Union as the 47th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a> is admitted to the Union as the 47th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "New Mexico", "link": "https://wikipedia.org/wiki/New_Mexico"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1912", "text": "German geophysicist <PERSON> first presents his theory of continental drift.", "html": "1912 - German <a href=\"https://wikipedia.org/wiki/Geophysics\" title=\"Geophysics\">geophysicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> first presents his theory of <a href=\"https://wikipedia.org/wiki/Continental_drift\" title=\"Continental drift\">continental drift</a>.", "no_year_html": "German <a href=\"https://wikipedia.org/wiki/Geophysics\" title=\"Geophysics\">geophysicist</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> first presents his theory of <a href=\"https://wikipedia.org/wiki/Continental_drift\" title=\"Continental drift\">continental drift</a>.", "links": [{"title": "Geophysics", "link": "https://wikipedia.org/wiki/Geophysics"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Continental drift", "link": "https://wikipedia.org/wiki/Continental_drift"}]}, {"year": "1929", "text": "King <PERSON> of the Serbs, Croats and Slovenes suspends his country's constitution, starting the January 6th Dictatorship.", "html": "1929 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of the Serbs, Croats and Slovenes</a> suspends <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">his country</a>'s constitution, starting the <a href=\"https://wikipedia.org/wiki/January_6th_Dictatorship\" class=\"mw-redirect\" title=\"January 6th Dictatorship\">January 6th Dictatorship</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of the Serbs, Croats and Slovenes</a> suspends <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">his country</a>'s constitution, starting the <a href=\"https://wikipedia.org/wiki/January_6th_Dictatorship\" class=\"mw-redirect\" title=\"January 6th Dictatorship\">January 6th Dictatorship</a>.", "links": [{"title": "<PERSON> of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia"}, {"title": "Kingdom of Yugoslavia", "link": "https://wikipedia.org/wiki/Kingdom_of_Yugoslavia"}, {"title": "January 6th Dictatorship", "link": "https://wikipedia.org/wiki/January_6th_Dictatorship"}]}, {"year": "1929", "text": "<PERSON> arrives by sea in Calcutta, India, to begin her work among India's poorest and sick people.", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives by sea in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, India, to begin her work among India's poorest and sick people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives by sea in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, India, to begin her work among India's poorest and sick people.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON> arrives at the National Automobile Show in New York City, having driven a car powered by one of his diesel engines from Indianapolis.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Cum<PERSON>\"><PERSON><PERSON><PERSON></a> arrives at the National Automobile Show in New York City, having driven a car powered by one of his diesel engines from Indianapolis.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Cummins\"><PERSON><PERSON><PERSON></a> arrives at the National Automobile Show in New York City, having driven a car powered by one of his diesel engines from Indianapolis.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "United States President <PERSON> delivers his Four Freedoms speech in the State of the Union address.", "html": "1941 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Four_Freedoms\" title=\"Four Freedoms\">Four Freedoms</a> speech in the <a href=\"https://wikipedia.org/wiki/State_of_the_Union_address\" class=\"mw-redirect\" title=\"State of the Union address\">State of the Union address</a>.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers his <a href=\"https://wikipedia.org/wiki/Four_Freedoms\" title=\"Four Freedoms\">Four Freedoms</a> speech in the <a href=\"https://wikipedia.org/wiki/State_of_the_Union_address\" class=\"mw-redirect\" title=\"State of the Union address\">State of the Union address</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Four Freedoms", "link": "https://wikipedia.org/wiki/Four_Freedoms"}, {"title": "State of the Union address", "link": "https://wikipedia.org/wiki/State_of_the_Union_address"}]}, {"year": "1946", "text": "The first general election ever in Vietnam is held.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/1946_North_Vietnamese_legislative_election\" title=\"1946 North Vietnamese legislative election\">first general election</a> ever in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> is held.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1946_North_Vietnamese_legislative_election\" title=\"1946 North Vietnamese legislative election\">first general election</a> ever in <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> is held.", "links": [{"title": "1946 North Vietnamese legislative election", "link": "https://wikipedia.org/wiki/1946_North_Vietnamese_legislative_election"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1947", "text": "Pan American Airlines becomes the first commercial airline to offer a round-the-world ticket.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American Airlines</a> becomes the first commercial airline to offer a <a href=\"https://wikipedia.org/wiki/Round-the-world_ticket\" title=\"Round-the-world ticket\">round-the-world ticket</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_American_World_Airways\" class=\"mw-redirect\" title=\"Pan American World Airways\">Pan American Airlines</a> becomes the first commercial airline to offer a <a href=\"https://wikipedia.org/wiki/Round-the-world_ticket\" title=\"Round-the-world ticket\">round-the-world ticket</a>.", "links": [{"title": "Pan American World Airways", "link": "https://wikipedia.org/wiki/Pan_American_World_Airways"}, {"title": "Round-the-world ticket", "link": "https://wikipedia.org/wiki/Round-the-world_ticket"}]}, {"year": "1950", "text": "The United Kingdom recognizes the People's Republic of China. The Republic of China severs diplomatic relations with the UK in response.", "html": "1950 - The United Kingdom recognizes the <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a>. The <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Republic of China</a> severs diplomatic relations with the UK in response.", "no_year_html": "The United Kingdom recognizes the <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">People's Republic of China</a>. The <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Republic of China</a> severs diplomatic relations with the UK in response.", "links": [{"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1951", "text": "Korean War: Beginning of the Ganghwa massacre, in the course of which an estimated 200-1,300 South Korean communist sympathizers are slaughtered.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Beginning of the <a href=\"https://wikipedia.org/wiki/Ganghwa_massacre\" title=\"Ganghwa massacre\">Ganghwa massacre</a>, in the course of which an estimated 200-1,300 South Korean communist sympathizers are slaughtered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Beginning of the <a href=\"https://wikipedia.org/wiki/Ganghwa_massacre\" title=\"Ganghwa massacre\">Ganghwa massacre</a>, in the course of which an estimated 200-1,300 South Korean communist sympathizers are slaughtered.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Ganghwa massacre", "link": "https://wikipedia.org/wiki/Ganghwa_massacre"}]}, {"year": "1960", "text": "National Airlines Flight 2511 is destroyed in mid-air by a bomb, while en route from New York City to Miami.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_2511\" title=\"National Airlines Flight 2511\">National Airlines Flight 2511</a> is destroyed in mid-air by a bomb, while en route from New York City to <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Airlines_Flight_2511\" title=\"National Airlines Flight 2511\">National Airlines Flight 2511</a> is destroyed in mid-air by a bomb, while en route from New York City to <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>.", "links": [{"title": "National Airlines Flight 2511", "link": "https://wikipedia.org/wiki/National_Airlines_Flight_2511"}, {"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}]}, {"year": "1960", "text": "The Associations Law comes into force in Iraq, allowing registration of political parties.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Associations_Law\" title=\"Associations Law\">Associations Law</a> comes into force in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, allowing registration of political parties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Associations_Law\" title=\"Associations Law\">Associations Law</a> comes into force in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, allowing registration of political parties.", "links": [{"title": "Associations Law", "link": "https://wikipedia.org/wiki/Associations_Law"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "1967", "text": "Vietnam War: United States Marine Corps and ARVN troops launch \"Operation Deckhouse Five\" in the Mekong River delta.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> and <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">ARVN</a> troops launch \"<a href=\"https://wikipedia.org/wiki/Operation_Deckhouse_Five\" title=\"Operation Deckhouse Five\">Operation Deckhouse Five</a>\" in the <a href=\"https://wikipedia.org/wiki/Mekong\" title=\"Mekong\">Mekong</a> River delta.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">United States Marine Corps</a> and <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">ARVN</a> troops launch \"<a href=\"https://wikipedia.org/wiki/Operation_Deckhouse_Five\" title=\"Operation Deckhouse Five\">Operation Deckhouse Five</a>\" in the <a href=\"https://wikipedia.org/wiki/Mekong\" title=\"Mekong\">Mekong</a> River delta.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "Operation Deckhouse Five", "link": "https://wikipedia.org/wiki/Operation_Deckhouse_Five"}, {"title": "Mekong", "link": "https://wikipedia.org/wiki/Mekong"}]}, {"year": "1968", "text": "Aeroflot Flight 1668 crashes near Olyokminsk, killing 45.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1668\" title=\"Aeroflot Flight 1668\">Aeroflot Flight 1668</a> crashes near <a href=\"https://wikipedia.org/wiki/Oly<PERSON>minsk\" title=\"Olyokminsk\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, killing 45.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_1668\" title=\"Aeroflot Flight 1668\">Aeroflot Flight 1668</a> crashes near <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>k\" title=\"Olyokminsk\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, killing 45.", "links": [{"title": "Aeroflot Flight 1668", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_1668"}, {"title": "Olyokminsk", "link": "https://wikipedia.org/wiki/<PERSON>lyokminsk"}]}, {"year": "1969", "text": "Allegheny Airlines Flight 737 crashes in Lafayette Township, McKean County, Pennsylvania, United States, killing 11.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Allegheny_Airlines_Flight_737\" title=\"Allegheny Airlines Flight 737\">Allegheny Airlines Flight 737</a> crashes in <a href=\"https://wikipedia.org/wiki/Lafayette_Township,_McKean_County,_Pennsylvania\" title=\"Lafayette Township, McKean County, Pennsylvania\">Lafayette Township, McKean County, Pennsylvania</a>, United States, killing 11.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Allegheny_Airlines_Flight_737\" title=\"Allegheny Airlines Flight 737\">Allegheny Airlines Flight 737</a> crashes in <a href=\"https://wikipedia.org/wiki/Lafayette_Township,_McKean_County,_Pennsylvania\" title=\"Lafayette Township, McKean County, Pennsylvania\">Lafayette Township, McKean County, Pennsylvania</a>, United States, killing 11.", "links": [{"title": "Allegheny Airlines Flight 737", "link": "https://wikipedia.org/wiki/Allegheny_Airlines_Flight_737"}, {"title": "Lafayette Township, McKean County, Pennsylvania", "link": "https://wikipedia.org/wiki/Lafayette_Township,_McKean_County,_Pennsylvania"}]}, {"year": "1974", "text": "In response to the 1973 oil crisis, daylight saving time commences nearly four months early in the United States.", "html": "1974 - In response to the <a href=\"https://wikipedia.org/wiki/1973_oil_crisis\" title=\"1973 oil crisis\">1973 oil crisis</a>, <a href=\"https://wikipedia.org/wiki/Daylight_saving_time\" title=\"Daylight saving time\">daylight saving time</a> commences nearly four months early in the United States.", "no_year_html": "In response to the <a href=\"https://wikipedia.org/wiki/1973_oil_crisis\" title=\"1973 oil crisis\">1973 oil crisis</a>, <a href=\"https://wikipedia.org/wiki/Daylight_saving_time\" title=\"Daylight saving time\">daylight saving time</a> commences nearly four months early in the United States.", "links": [{"title": "1973 oil crisis", "link": "https://wikipedia.org/wiki/1973_oil_crisis"}, {"title": "Daylight saving time", "link": "https://wikipedia.org/wiki/Daylight_saving_time"}]}, {"year": "1974", "text": "Aeroflot Flight H-75 crashes near Mukachevo, killing 24.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_H-75\" title=\"Aeroflot Flight H-75\">Aeroflot Flight H-75</a> crashes near <a href=\"https://wikipedia.org/wiki/Mu<PERSON><PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, killing 24.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_H-75\" title=\"Aeroflot Flight H-75\">Aeroflot Flight H-75</a> crashes near <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, killing 24.", "links": [{"title": "Aeroflot Flight H-75", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_H-75"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>o"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> are sentenced to death for conspiracy in the assassination of Prime Minister <PERSON><PERSON>; the two men are executed the same day.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are sentenced to death for conspiracy in the <a href=\"https://wikipedia.org/wiki/Assassination_of_In<PERSON>_Gandhi\" title=\"Assassination of <PERSON><PERSON> Gandhi\">assassination</a> of Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gandhi\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>; the two men are executed the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> are sentenced to death for conspiracy in the <a href=\"https://wikipedia.org/wiki/Assassination_of_Indira_Gandhi\" title=\"Assassination of <PERSON><PERSON> Gandhi\">assassination</a> of Prime Minister <a href=\"https://wikipedia.org/wiki/In<PERSON>_Gandhi\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>; the two men are executed the same day.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON><PERSON> Gandhi", "link": "https://wikipedia.org/wiki/Assassination_of_Indira_Gandhi"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}]}, {"year": "1992", "text": "President of Georgia <PERSON><PERSON><PERSON> flees the country as a result of the military coup.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> <a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"Zviad Gamsakhurdia\"><PERSON><PERSON><PERSON></a> flees the country as a result of the military <a href=\"https://wikipedia.org/wiki/1991%E2%80%931992_Georgian_coup_d%27%C3%A9tat\" title=\"1991-1992 Georgian coup d'état\">coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President of Georgia</a> <a href=\"https://wikipedia.org/wiki/Zviad_Gamsakhurdia\" title=\"<PERSON>viad Gamsakhurdia\"><PERSON><PERSON><PERSON></a> flees the country as a result of the military <a href=\"https://wikipedia.org/wiki/1991%E2%80%931992_Georgian_coup_d%27%C3%A9tat\" title=\"1991-1992 Georgian coup d'état\">coup</a>.", "links": [{"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zviad_Gamsakhurdia"}, {"title": "1991-1992 Georgian coup d'état", "link": "https://wikipedia.org/wiki/1991%E2%80%931992_Georgian_coup_d%27%C3%A9tat"}]}, {"year": "1993", "text": "Indian Border Security Force units kill 55 Kashmiri civilians in Sopore, Jammu and Kashmir, in revenge after militants ambushed a BSF patrol.", "html": "1993 - Indian <a href=\"https://wikipedia.org/wiki/Border_Security_Force\" title=\"Border Security Force\">Border Security Force</a> units <a href=\"https://wikipedia.org/wiki/1993_Sopore_massacre\" title=\"1993 Sopore massacre\">kill 55 Kashmiri civilians</a> in <a href=\"https://wikipedia.org/wiki/Sopore\" title=\"Sopore\">Sopore</a>, <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir</a>, in revenge after militants ambushed a BSF patrol.", "no_year_html": "Indian <a href=\"https://wikipedia.org/wiki/Border_Security_Force\" title=\"Border Security Force\">Border Security Force</a> units <a href=\"https://wikipedia.org/wiki/1993_Sopore_massacre\" title=\"1993 Sopore massacre\">kill 55 Kashmiri civilians</a> in <a href=\"https://wikipedia.org/wiki/Sopore\" title=\"Sopore\">Sopore</a>, <a href=\"https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)\" title=\"Jammu and Kashmir (state)\">Jammu and Kashmir</a>, in revenge after militants ambushed a BSF patrol.", "links": [{"title": "Border Security Force", "link": "https://wikipedia.org/wiki/Border_Security_Force"}, {"title": "1993 Sopore massacre", "link": "https://wikipedia.org/wiki/1993_Sopore_massacre"}, {"title": "Sopore", "link": "https://wikipedia.org/wiki/Sopore"}, {"title": "Jammu and Kashmir (state)", "link": "https://wikipedia.org/wiki/Jammu_and_Kashmir_(state)"}]}, {"year": "1993", "text": "Four people are killed when Lufthansa CityLine Flight 5634 crashes on approach to Charles de Gaulle Airport in Roissy-en-France, France.", "html": "1993 - Four people are killed when <a href=\"https://wikipedia.org/wiki/Lufthansa_CityLine_Flight_5634\" title=\"Lufthansa CityLine Flight 5634\">Lufthansa CityLine Flight 5634</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Charles_de_Gaulle_Airport\" title=\"Charles de Gaulle Airport\">Charles de Gaulle Airport</a> in <a href=\"https://wikipedia.org/wiki/Roissy-en-France\" title=\"Roissy-en-France\">Roissy-en-France</a>, France.", "no_year_html": "Four people are killed when <a href=\"https://wikipedia.org/wiki/Lufthansa_CityLine_Flight_5634\" title=\"Lufthansa CityLine Flight 5634\">Lufthansa CityLine Flight 5634</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Charles_de_Gaulle_Airport\" title=\"Charles de Gaulle Airport\">Charles de Gaulle Airport</a> in <a href=\"https://wikipedia.org/wiki/Roissy-en-France\" title=\"Roissy-en-France\">Roissy-en-France</a>, France.", "links": [{"title": "Lufthansa CityLine Flight 5634", "link": "https://wikipedia.org/wiki/Lufthansa_CityLine_Flight_5634"}, {"title": "Charles <PERSON> Airport", "link": "https://wikipedia.org/wiki/Charles_de_<PERSON>_Airport"}, {"title": "Roissy-en-France", "link": "https://wikipedia.org/wiki/Roissy-en-France"}]}, {"year": "1994", "text": "U.S. figure skater <PERSON> is attacked and injured by an assailant hired by her rival <PERSON><PERSON>'s ex-husband during the U.S. Figure Skating Championships.", "html": "1994 - U.S. figure skater <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1994_Cobo_Arena_attack\" class=\"mw-redirect\" title=\"1994 Cobo Arena attack\">attacked</a> and injured by an assailant hired by her rival <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s ex-husband during the <a href=\"https://wikipedia.org/wiki/1994_United_States_Figure_Skating_Championships\" class=\"mw-redirect\" title=\"1994 United States Figure Skating Championships\">U.S. Figure Skating Championships</a>.", "no_year_html": "U.S. figure skater <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/1994_Cobo_Arena_attack\" class=\"mw-redirect\" title=\"1994 Cobo Arena attack\">attacked</a> and injured by an assailant hired by her rival <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s ex-husband during the <a href=\"https://wikipedia.org/wiki/1994_United_States_Figure_Skating_Championships\" class=\"mw-redirect\" title=\"1994 United States Figure Skating Championships\">U.S. Figure Skating Championships</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1994 Cobo Arena attack", "link": "https://wikipedia.org/wiki/1994_Cobo_Arena_attack"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1994 United States Figure Skating Championships", "link": "https://wikipedia.org/wiki/1994_United_States_Figure_Skating_Championships"}]}, {"year": "1995", "text": "A chemical fire in an apartment complex in Manila, Philippines, leads to the discovery of plans for Project Bojinka, a mass-terrorist attack.", "html": "1995 - A chemical fire in an apartment complex in <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila, Philippines</a>, leads to the discovery of plans for <a href=\"https://wikipedia.org/wiki/Bojinka_plot\" title=\"Bojinka plot\">Project Bojinka</a>, a mass-terrorist attack.", "no_year_html": "A chemical fire in an apartment complex in <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila, Philippines</a>, leads to the discovery of plans for <a href=\"https://wikipedia.org/wiki/Bojinka_plot\" title=\"Bojinka plot\">Project Bojinka</a>, a mass-terrorist attack.", "links": [{"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}, {"title": "Bojinka plot", "link": "https://wikipedia.org/wiki/Bojinka_plot"}]}, {"year": "2000", "text": "The last natural Pyrenean ibex, <PERSON>, is killed by a falling tree, thus making the species extinct.", "html": "2000 - The last natural <a href=\"https://wikipedia.org/wiki/Pyrenean_ibex\" title=\"Pyrenean ibex\">Pyrenean ibex</a>, <PERSON>, is killed by a falling tree, thus making the species extinct.", "no_year_html": "The last natural <a href=\"https://wikipedia.org/wiki/Pyrenean_ibex\" title=\"Pyrenean ibex\">Pyrenean ibex</a>, <PERSON>, is killed by a falling tree, thus making the species extinct.", "links": [{"title": "Pyrenean ibex", "link": "https://wikipedia.org/wiki/Pyrenean_ibex"}]}, {"year": "2005", "text": "<PERSON> is indicted for the 1964 murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> during the American Civil Rights Movement.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted for the 1964 <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>\" title=\"Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>\">murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/American_Civil_Rights_Movement\" class=\"mw-redirect\" title=\"American Civil Rights Movement\">American Civil Rights Movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted for the 1964 <a href=\"https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>\" title=\"Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>\">murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/American_Civil_Rights_Movement\" class=\"mw-redirect\" title=\"American Civil Rights Movement\">American Civil Rights Movement</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Murders of <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murders_of_<PERSON><PERSON>,_<PERSON>,_and_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "American Civil Rights Movement", "link": "https://wikipedia.org/wiki/American_Civil_Rights_Movement"}]}, {"year": "2005", "text": "A train collision in Graniteville, South Carolina, United States, releases about 60 tons of chlorine gas.", "html": "2005 - A <a href=\"https://wikipedia.org/wiki/Graniteville_train_crash\" title=\"Graniteville train crash\">train collision</a> in <a href=\"https://wikipedia.org/wiki/Graniteville,_South_Carolina\" title=\"Graniteville, South Carolina\">Graniteville</a>, South Carolina, United States, releases about 60 tons of <a href=\"https://wikipedia.org/wiki/Chlorine\" title=\"Chlorine\">chlorine</a> gas.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Graniteville_train_crash\" title=\"Graniteville train crash\">train collision</a> in <a href=\"https://wikipedia.org/wiki/Graniteville,_South_Carolina\" title=\"Graniteville, South Carolina\">Graniteville</a>, South Carolina, United States, releases about 60 tons of <a href=\"https://wikipedia.org/wiki/Chlorine\" title=\"Chlorine\">chlorine</a> gas.", "links": [{"title": "Graniteville train crash", "link": "https://wikipedia.org/wiki/Graniteville_train_crash"}, {"title": "Graniteville, South Carolina", "link": "https://wikipedia.org/wiki/Graniteville,_South_Carolina"}, {"title": "Chlorine", "link": "https://wikipedia.org/wiki/Chlorine"}]}, {"year": "2012", "text": "Twenty-six people are killed and 63 wounded when a suicide bomber blows himself up at a police station in Damascus.", "html": "2012 - Twenty-six people are killed and 63 wounded when a suicide bomber <a href=\"https://wikipedia.org/wiki/January_2012_al-Midan_bombing\" title=\"January 2012 al-Midan bombing\">blows himself up</a> at a police station in <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>.", "no_year_html": "Twenty-six people are killed and 63 wounded when a suicide bomber <a href=\"https://wikipedia.org/wiki/January_2012_al-Midan_bombing\" title=\"January 2012 al-Midan bombing\">blows himself up</a> at a police station in <a href=\"https://wikipedia.org/wiki/Damascus\" title=\"Damascus\">Damascus</a>.", "links": [{"title": "January 2012 al-Midan bombing", "link": "https://wikipedia.org/wiki/January_2012_al-Midan_bombing"}, {"title": "Damascus", "link": "https://wikipedia.org/wiki/Damascus"}]}, {"year": "2017", "text": "Five people are killed and six others injured in a mass shooting at Fort Lauderdale-Hollywood International Airport in Broward County, Florida.", "html": "2017 - Five people are killed and six others injured in a <a href=\"https://wikipedia.org/wiki/Fort_Lauderdale_airport_shooting\" title=\"Fort Lauderdale airport shooting\">mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Fort_Lauderdale%E2%80%93Hollywood_International_Airport\" title=\"Fort Lauderdale-Hollywood International Airport\">Fort Lauderdale-Hollywood International Airport</a> in <a href=\"https://wikipedia.org/wiki/Broward_County,_Florida\" title=\"Broward County, Florida\">Broward County</a>, Florida.", "no_year_html": "Five people are killed and six others injured in a <a href=\"https://wikipedia.org/wiki/Fort_Lauderdale_airport_shooting\" title=\"Fort Lauderdale airport shooting\">mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Fort_Lauderdale%E2%80%93Hollywood_International_Airport\" title=\"Fort Lauderdale-Hollywood International Airport\">Fort Lauderdale-Hollywood International Airport</a> in <a href=\"https://wikipedia.org/wiki/Broward_County,_Florida\" title=\"Broward County, Florida\">Broward County</a>, Florida.", "links": [{"title": "Fort Lauderdale airport shooting", "link": "https://wikipedia.org/wiki/Fort_Lauderdale_airport_shooting"}, {"title": "Fort Lauderdale-Hollywood International Airport", "link": "https://wikipedia.org/wiki/Fort_Lauderdale%E2%80%93Hollywood_International_Airport"}, {"title": "Broward County, Florida", "link": "https://wikipedia.org/wiki/Broward_County,_Florida"}]}, {"year": "2019", "text": "Forty people are killed in a gold mine collapse in Badakhshan province, in northern Afghanistan.", "html": "2019 - Forty people are killed in a <a href=\"https://wikipedia.org/wiki/Mining_in_Afghanistan\" title=\"Mining in Afghanistan\">gold mine</a> collapse in <a href=\"https://wikipedia.org/wiki/Badakhshan_province\" class=\"mw-redirect\" title=\"Badakhshan province\">Badakhshan province</a>, in northern Afghanistan.", "no_year_html": "Forty people are killed in a <a href=\"https://wikipedia.org/wiki/Mining_in_Afghanistan\" title=\"Mining in Afghanistan\">gold mine</a> collapse in <a href=\"https://wikipedia.org/wiki/Badakhshan_province\" class=\"mw-redirect\" title=\"Badakhshan province\">Badakhshan province</a>, in northern Afghanistan.", "links": [{"title": "Mining in Afghanistan", "link": "https://wikipedia.org/wiki/Mining_in_Afghanistan"}, {"title": "Badakhshan province", "link": "https://wikipedia.org/wiki/Badakhshan_province"}]}, {"year": "2019", "text": "<PERSON> of Kelantan resigns as the <PERSON><PERSON> of Malaysia, becoming the first monarch to do so.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Kelantan\" title=\"Muhammad V of Kelantan\"><PERSON> of Kelantan</a> resigns as the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of Malaysia, becoming the first monarch to do so.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_V_of_Kelantan\" title=\"Muhammad V of Kelantan\"><PERSON> of Kelantan</a> resigns as the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> of Malaysia, becoming the first monarch to do so.", "links": [{"title": "<PERSON> of Kelantan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Kelant<PERSON>"}, {"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "Americans storm the United States Capitol Building to disrupt certification of the 2020 presidential election, resulting in four deaths and evacuation of the U.S. Congress.", "html": "2021 - Americans <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">storm the United States Capitol Building</a> to <a href=\"https://wikipedia.org/wiki/Attempts_to_overturn_the_2020_United_States_presidential_election\" title=\"Attempts to overturn the 2020 United States presidential election\">disrupt</a> <a href=\"https://wikipedia.org/wiki/2021_United_States_Electoral_College_vote_count\" title=\"2021 United States Electoral College vote count\">certification</a> of the <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 presidential election</a>, resulting in four deaths and evacuation of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "no_year_html": "Americans <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">storm the United States Capitol Building</a> to <a href=\"https://wikipedia.org/wiki/Attempts_to_overturn_the_2020_United_States_presidential_election\" title=\"Attempts to overturn the 2020 United States presidential election\">disrupt</a> <a href=\"https://wikipedia.org/wiki/2021_United_States_Electoral_College_vote_count\" title=\"2021 United States Electoral College vote count\">certification</a> of the <a href=\"https://wikipedia.org/wiki/2020_United_States_presidential_election\" title=\"2020 United States presidential election\">2020 presidential election</a>, resulting in four deaths and evacuation of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>.", "links": [{"title": "January 6 United States Capitol attack", "link": "https://wikipedia.org/wiki/January_6_United_States_Capitol_attack"}, {"title": "Attempts to overturn the 2020 United States presidential election", "link": "https://wikipedia.org/wiki/Attempts_to_overturn_the_2020_United_States_presidential_election"}, {"title": "2021 United States Electoral College vote count", "link": "https://wikipedia.org/wiki/2021_United_States_Electoral_College_vote_count"}, {"title": "2020 United States presidential election", "link": "https://wikipedia.org/wiki/2020_United_States_presidential_election"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "2025", "text": "<PERSON> announces his resignation as leader of the Liberal Party of Canada and Prime Minister of Canada after 9 years in office.", "html": "2025 - <a href=\"https://wikipedia.org/wiki/2025\" title=\"2025\">2025</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his <a href=\"https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Resignation of <PERSON>\">resignation</a> as leader of the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party of Canada</a> and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> after 9 years in office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2025\" title=\"2025\">2025</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> announces his <a href=\"https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Resignation of <PERSON>\">resignation</a> as leader of the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Canada\" title=\"Liberal Party of Canada\">Liberal Party of Canada</a> and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> after 9 years in office.", "links": [{"title": "2025", "link": "https://wikipedia.org/wiki/2025"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Resignation of <PERSON>", "link": "https://wikipedia.org/wiki/Resignation_of_<PERSON>_<PERSON>"}, {"title": "Liberal Party of Canada", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Canada"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}], "Births": [{"year": "1256", "text": "<PERSON> the <PERSON>, German mystic (d. 1302)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (d. 1302)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (d. 1302)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "German mystic", "link": "https://wikipedia.org/wiki/German_mystic"}]}, {"year": "1367", "text": "<PERSON> of England (d. 1400)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> (d. 1400)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1384", "text": "<PERSON>, 4th Earl of Kent (d. 1408)", "html": "1384 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Kent\" title=\"<PERSON>, 4th Earl <PERSON> Kent\"><PERSON>, 4th Earl of Kent</a> (d. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_<PERSON>_Kent\" title=\"<PERSON>, 4th Earl <PERSON> Kent\"><PERSON>, 4th Earl of Kent</a> (d. 1408)", "links": [{"title": "<PERSON>, 4th Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Kent"}]}, {"year": "1412", "text": "<PERSON> Arc, French martyr and saint (d. 1431)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Arc\"><PERSON> Arc</a>, French martyr and saint (d. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Joan of Arc\"><PERSON> Arc</a>, French martyr and saint (d. 1431)", "links": [{"title": "<PERSON> of Arc", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1486", "text": "<PERSON>, German composer and theorist (d. 1556)", "html": "1486 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and theorist (d. 1556)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1488", "text": "<PERSON><PERSON><PERSON>, German poet (d. 1540)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German poet (d. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">He<PERSON><PERSON></a>, German poet (d. 1540)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON>, Swedish clergyman (d. 1552)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish clergyman (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish clergyman (d. 1552)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1500", "text": "<PERSON> of Ávila, Spanish mystic and saint (d. 1569)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_%C3%81vila\" title=\"<PERSON> of Ávila\"><PERSON> of Ávila</a>, Spanish mystic and saint (d. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%81vila\" title=\"<PERSON> of Ávila\"><PERSON> of Ávila</a>, Spanish mystic and saint (d. 1569)", "links": [{"title": "<PERSON> of Ávila", "link": "https://wikipedia.org/wiki/<PERSON>_of_%C3%81vila"}]}, {"year": "1525", "text": "<PERSON><PERSON><PERSON>, German physician and scholar (d. 1602)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and scholar (d. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and scholar (d. 1602)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aspar_<PERSON>cer"}]}, {"year": "1538", "text": "<PERSON>, Duchess of Feria (d. 1612)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duchess_of_Feria\" class=\"mw-redirect\" title=\"Duchess of Feria\">Duchess of Feria</a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Duchess_of_Feria\" class=\"mw-redirect\" title=\"Duchess of Feria\">Duchess of Feria</a> (d. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Duchess of Feria", "link": "https://wikipedia.org/wiki/<PERSON>_of_Feria"}]}, {"year": "1561", "text": "<PERSON>, Danish mathematician and physicist (d. 1656)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and physicist (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish mathematician and physicist (d. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1587", "text": "<PERSON><PERSON>, Count-Duke of Olivares (d. 1645)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>m%C3%<PERSON>n,_Count-Duke_of_Olivares\" title=\"<PERSON><PERSON>, Count-Duke of Olivares\"><PERSON><PERSON>, Count-Duke of Olivares</a> (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>,_Count-Duke_of_Olivares\" title=\"<PERSON><PERSON>, Count-Duke of Olivares\"><PERSON><PERSON>, Count-Duke of Olivares</a> (d. 1645)", "links": [{"title": "<PERSON><PERSON>, Count-Duke of Olivares", "link": "https://wikipedia.org/wiki/Gaspar_de_Guzm%C3%A1n,_Count-Duke_<PERSON>_<PERSON>"}]}, {"year": "1595", "text": "<PERSON>, French educator and courtier (d. 1650)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and courtier (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator and courtier (d. 1650)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON>, Danish politician (d. 1673)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish politician (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish politician (d. 1673)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1632", "text": "<PERSON>, 3rd Duchess of Hamilton, Scottish peeress (d. 1716)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Duchess_of_Hamilton\" title=\"<PERSON>, 3rd Duchess of Hamilton\"><PERSON>, 3rd Duchess of Hamilton</a>, Scottish peeress (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duchess_of_Hamilton\" title=\"<PERSON>, 3rd Duchess of Hamilton\"><PERSON>, 3rd Duchess of Hamilton</a>, Scottish peeress (d. 1716)", "links": [{"title": "<PERSON>, 3rd Duchess of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duchess_<PERSON>_<PERSON>"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON> of Neuburg (d. 1720)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Neuburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Neuburg\"><PERSON><PERSON><PERSON> of Neuburg</a> (d. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Neuburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Neuburg\"><PERSON><PERSON><PERSON> of Neuburg</a> (d. 1720)", "links": [{"title": "<PERSON><PERSON><PERSON> of Neuburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_of_Neuburg"}]}, {"year": "1673", "text": "<PERSON>, 1st Duke of Chandos, English academic and politician, Lord Lieutenant of Radnorshire (d. 1744)", "html": "1673 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos\" title=\"<PERSON>, 1st Duke of Chandos\"><PERSON>, 1st Duke of Chandos</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire\" title=\"Lord Lieutenant of Radnorshire\">Lord Lieutenant of Radnorshire</a> (d. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos\" title=\"<PERSON>, 1st Duke of Chandos\"><PERSON>, 1st Duke of Chandos</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire\" title=\"Lord Lieutenant of Radnorshire\">Lord Lieutenant of Radnorshire</a> (d. 1744)", "links": [{"title": "<PERSON>, 1st Duke of Chandos", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Chandos"}, {"title": "Lord Lieutenant of Radnorshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Radnorshire"}]}, {"year": "1695", "text": "<PERSON>, Italian oboe player and composer (d. 1750)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer (d. 1750)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oboe", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1702", "text": "<PERSON>, Spanish composer (d. 1768)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Nebra\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_de_Nebra\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_de_Nebra"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON><PERSON>, English surgeon (d. 1788)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English surgeon (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>tt\"><PERSON><PERSON><PERSON><PERSON></a>, English surgeon (d. 1788)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Percival<PERSON>_<PERSON>tt"}]}, {"year": "1745", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French co-inventor of the hot air balloon (d. 1799)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French co-inventor of the hot air balloon (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French co-inventor of the hot air balloon (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, Paraguayan lawyer and politician, first dictator of Paraguay (d. 1840)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gaspar_Rodr%C3%ADguez_de_Francia\" title=\"<PERSON>ancia\"><PERSON></a>, Paraguayan lawyer and politician, first dictator of Paraguay (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Gaspar_Rodr%C3%ADguez_de_Francia\" title=\"<PERSON> Francia\"><PERSON></a>, Paraguayan lawyer and politician, first dictator of Paraguay (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Gaspar_Rodr%C3%ADguez_de_Francia"}]}, {"year": "1785", "text": "<PERSON>, Greek historian and philologist (d. 1860)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek historian and philologist (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek historian and philologist (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, American lawyer and politician, 18th United States Secretary of War (d. 1862)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON>, French chemist and academic (d. 1871)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/Anselme_Payen\" title=\"Anselme Payen\"><PERSON><PERSON><PERSON></a>, French chemist and academic (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anselme_<PERSON>en\" title=\"Anselme Payen\"><PERSON><PERSON><PERSON></a>, French chemist and academic (d. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ansel<PERSON>_<PERSON>en"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, American hunter, explorer, and author (d. 1831)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hunter, explorer, and author (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American hunter, explorer, and author (d. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, Austrian pianist and composer (d. 1888)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, German-Hungarian mathematician and physicist (d. 1891)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Hungarian mathematician and physicist (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Hungarian mathematician and physicist (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American conchologist and paleontologist (d. 1864)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conchologist and paleontologist (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conchologist and paleontologist (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, American lawyer and politician (d. 1874)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, German archaeologist and businessman (d. 1890)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and businessman (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German archaeologist and businessman (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, French painter and sculptor (d. 1883)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and sculptor (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and sculptor (d. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Dor%C3%A9"}]}, {"year": "1838", "text": "<PERSON>, German composer and conductor (d. 1920)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>ch\"><PERSON></a>, German composer and conductor (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, American geologist, mountaineer, and critic (d. 1901)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist, mountaineer, and critic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clarence <PERSON>\"><PERSON></a>, American geologist, mountaineer, and critic (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Italian pianist, composer, and conductor (d. 1909)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian pianist, composer, and conductor (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Irish-Australian publisher and politician, 10th Australian Minister for Foreign Affairs (d. 1931)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian publisher and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian publisher and politician, 10th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1857", "text": "<PERSON>, American lawyer and politician, 37th Governor of Massachusetts (d. 1896)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" class=\"mw-redirect\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1896)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>(governor)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1859", "text": "<PERSON>, Australian-English philosopher and academic (d. 1938)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English philosopher and academic (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English philosopher and academic (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, British actor (d. 1939)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Belgian architect, designed <PERSON><PERSON><PERSON> (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian architect, designed <a href=\"https://wikipedia.org/wiki/H%C3%B4tel_van_E<PERSON>velde\" title=\"<PERSON><PERSON><PERSON> van Eetvelde\"><PERSON><PERSON><PERSON> van Eetvelde</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian architect, designed <a href=\"https://wikipedia.org/wiki/H%C3%B4tel_van_E<PERSON>velde\" title=\"<PERSON><PERSON><PERSON> van Eetvelde\"><PERSON><PERSON><PERSON> van Eetvelde</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> van <PERSON>", "link": "https://wikipedia.org/wiki/H%C3%B4tel_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English-Canadian bishop and theologian (d. 1940)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)\" class=\"mw-redirect\" title=\"<PERSON> (bishop of Saskatchewan)\"><PERSON></a>, English-Canadian bishop and theologian (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)\" class=\"mw-redirect\" title=\"<PERSON> (bishop of Saskatchewan)\"><PERSON></a>, English-Canadian bishop and theologian (d. 1940)", "links": [{"title": "<PERSON> (bishop of Saskatchewan)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Saskatchewan)"}]}, {"year": "1870", "text": "<PERSON>, German journalist and politician, 11th Chancellor of Germany (d. 1944)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1872", "text": "<PERSON>, Russian pianist and composer (d. 1915)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and composer (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American actor, director, and producer (d. 1948)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Danish-born British ballerina (d. 1970)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Adeline_Gen%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-born British ballerina (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adeline_Gen%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-born British ballerina (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adeline_Gen%C3%A9e"}]}, {"year": "1878", "text": "<PERSON>, American poet and historian (d. 1967)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carl <PERSON>\"><PERSON></a>, American poet and historian (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Carl <PERSON>\"><PERSON></a>, American poet and historian (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American cowboy and actor (d. 1940)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Tom_Mix\" title=\"Tom Mix\"><PERSON></a>, American cowboy and actor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom_Mix\" title=\"Tom Mix\"><PERSON></a>, American cowboy and actor (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Romanian author, poet, and critic (d. 1944)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian author, poet, and critic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian author, poet, and critic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON> <PERSON>, Albanian-American bishop and politician, 13th Prime Minister of Albania (d. 1965)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, Albanian-American bishop and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, Albanian-American bishop and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Albania\" title=\"Prime Minister of Albania\">Prime Minister of Albania</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Albania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Albania"}]}, {"year": "1882", "text": "<PERSON>, American lawyer and politician, 48th Speaker of the United States House of Representatives (d. 1961)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, Lebanese-American poet, painter, and philosopher (d. 1931)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American poet, painter, and philosopher (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese-American poet, painter, and philosopher (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Australian cricketer (d. 1937)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Irish soldier and pilot (d. 1965)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)\" class=\"mw-redirect\" title=\"<PERSON> (pilot)\"><PERSON></a>, Irish soldier and pilot (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(pilot)\" class=\"mw-redirect\" title=\"<PERSON> (pilot)\"><PERSON></a>, Irish soldier and pilot (d. 1965)", "links": [{"title": "<PERSON> (pilot)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pilot)"}]}, {"year": "1899", "text": "<PERSON>, German engineer (d. 1968)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON> of Yugoslavia, Queen of Yugoslavia (d. 1961)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Maria_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a>, Queen of Yugoslavia (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a>, Queen of Yugoslavia (d. 1961)", "links": [{"title": "Maria of Yugoslavia", "link": "https://wikipedia.org/wiki/Maria_of_Yugoslavia"}]}, {"year": "1903", "text": "<PERSON>, Greek-American pianist and conductor (d. 1993)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American pianist and conductor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American pianist and conductor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Cuban boxer (d. 1988)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Kid_Chocolate\" title=\"Kid Chocolate\"><PERSON> Chocolate</a>, Cuban boxer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kid_Chocolate\" title=\"Kid Chocolate\"><PERSON> Chocolate</a>, Cuban boxer (d. 1988)", "links": [{"title": "Kid Chocolate", "link": "https://wikipedia.org/wiki/Kid_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American author and photographer (d. 1998)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, American author and photographer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wright Morris\"><PERSON></a>, American author and photographer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Greek composer and educator (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek composer and educator (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French philosopher and critic (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and critic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and critic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American actor, comedian, producer, and humanitarian (d. 1991)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer, and humanitarian (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, producer, and humanitarian (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Polish lawyer and politician (d. 2001)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish lawyer and politician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish lawyer and politician (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American actress (d. 2000)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Young\"><PERSON><PERSON></a>, American actress (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Young"}]}, {"year": "1914", "text": "<PERSON>, Austrian-American physician and academic (d. 1989)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physician and academic (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physician and academic (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American soldier, lawyer, and politician (d. 2015)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American psychoanalyst, physician, and philosopher (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst, physician, and philosopher (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychoanalyst, physician, and philosopher (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English-American philosopher and author (d. 1973)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher and author (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, influential Korean poet and academic (d. 1978)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wol\" class=\"mw-redirect\" title=\"<PERSON>wol\"><PERSON></a>, influential Korean poet and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wol\" class=\"mw-redirect\" title=\"<PERSON>wol\"><PERSON>-<PERSON></a>, influential Korean poet and academic (d. 1978)", "links": [{"title": "Park Mo<PERSON>-wol", "link": "https://wikipedia.org/wiki/Park_Mok-wol"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Taiwanese businessman and diplomat (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu\" title=\"<PERSON><PERSON>-fu\"><PERSON><PERSON></a>, Taiwanese businessman and diplomat (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu\" title=\"<PERSON><PERSON>-fu\"><PERSON><PERSON></a>, Taiwanese businessman and diplomat (d. 2005)", "links": [{"title": "<PERSON><PERSON>u", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu"}]}, {"year": "1920", "text": "<PERSON>, Canadian-born American actor (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-born American actor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-born American actor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English biologist and geneticist (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and geneticist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and geneticist (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Korean religious leader; founder of the Unification Church (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Sun_Myung_Moon\" title=\"Sun Myung Moon\"><PERSON> Myung <PERSON></a>, Korean religious leader; founder of the <a href=\"https://wikipedia.org/wiki/Unification_Church\" title=\"Unification Church\">Unification Church</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Myung_Moon\" title=\"Sun Myung Moon\"><PERSON> Myung <PERSON></a>, Korean religious leader; founder of the <a href=\"https://wikipedia.org/wiki/Unification_Church\" title=\"Unification Church\">Unification Church</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Unification Church", "link": "https://wikipedia.org/wiki/Unification_Church"}]}, {"year": "1920", "text": "<PERSON>, American baseball player, coach, and sportscaster (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Early_Wynn\" title=\"Early Wynn\"><PERSON> <PERSON><PERSON></a>, American baseball player, coach, and sportscaster (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Early_Wynn\" title=\"Early Wynn\">Early <PERSON>ynn</a>, American baseball player, coach, and sportscaster (d. 1999)", "links": [{"title": "Early Wynn", "link": "https://wikipedia.org/wiki/Early_Wynn"}]}, {"year": "1921", "text": "<PERSON>-<PERSON><PERSON>, Russian-French biochemist and academic (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French biochemist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French biochemist and academic (d. 2013)", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American golfer and sportscaster (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cary_Middlecoff"}]}, {"year": "1923", "text": "<PERSON>, Russian runner (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Russian runner (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Russian runner (d. 2007)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1923", "text": "<PERSON>, New Zealand engineer and politician, 29th Prime Minister of New Zealand (d. 1974)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand engineer and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand engineer and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Argentinian journalist and author (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian journalist and author (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rman"}]}, {"year": "1924", "text": "<PERSON>, South Korean soldier and politician, 8th President of South Korea, Nobel Prize laureate (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1924", "text": "<PERSON>, American banjo player (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banjo player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American engineer and businessman, founded the DeLorean Motor Company (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/DeLorean_Motor_Company\" title=\"DeLorean Motor Company\">DeLorean Motor Company</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/DeLorean_Motor_Company\" title=\"DeLorean Motor Company\">DeLorean Motor Company</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "DeLorean Motor Company", "link": "https://wikipedia.org/wiki/DeLorean_Motor_Company"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American race car driver (d. 2002)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 2002)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1926", "text": "<PERSON>, Hungarian-American actor and bodybuilder (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and bodybuilder (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor and bodybuilder (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American physician and academic, 11th Surgeon General of the United States (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 11th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, 11th <a href=\"https://wikipedia.org/wiki/Surgeon_General_of_the_United_States\" title=\"Surgeon General of the United States\">Surgeon General of the United States</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Surgeon General of the United States", "link": "https://wikipedia.org/wiki/Surgeon_General_of_the_United_States"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, French actress and model (d. 1990)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and model (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and model (d. 1990)", "links": [{"title": "Capucine", "link": "https://wikipedia.org/wiki/Capucine"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 1990)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON> <PERSON><PERSON>, American novelist, playwright, and short story writer (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist, playwright, and short story writer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist, playwright, and short story writer (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English actor (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American chemist and academic (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English actor and author (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Russian engineer and astronaut (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, New Zealand-Australian talent agent and publicist (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian talent agent and publicist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian talent agent and publicist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actress (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, English actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sym<PERSON>\"><PERSON></a>, English actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Syms"}]}, {"year": "1935", "text": "<PERSON>, Australian cricketer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American musician, singer, and actor", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician, singer, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>no_<PERSON>mpo"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American tennis player (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hard\"><PERSON><PERSON></a>, American tennis player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hard\"><PERSON><PERSON></a>, American tennis player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Uruguayan journalist, lawyer, and politician, 29th President of Uruguay", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist, lawyer, and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julio_<PERSON>%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan journalist, lawyer, and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Uruguay\" title=\"President of Uruguay\">President of Uruguay</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_Mar%C3%<PERSON><PERSON>_<PERSON>"}, {"title": "President of Uruguay", "link": "https://wikipedia.org/wiki/President_of_Uruguay"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech discus thrower (d. 1998)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk\" title=\"Ludvík Daněk\"><PERSON>d<PERSON><PERSON></a>, Czech discus thrower (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk\" title=\"Ludvík Daněk\"><PERSON>d<PERSON><PERSON></a>, Czech discus thrower (d. 1998)", "links": [{"title": "Ludvík Daněk", "link": "https://wikipedia.org/wiki/Ludv%C3%ADk_Dan%C4%9Bk"}]}, {"year": "1937", "text": "<PERSON>, American football player, coach, and sportscaster", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter (d. 2004)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Italian singer-songwriter, actor, and director", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter, actor, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer-songwriter, actor, and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Australian botanist and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian botanist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian botanist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Soviet film director, screenwriter, and actress (d. 1979)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet film director, screenwriter, and actress (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet film director, screenwriter, and actress (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer and manager (d. 2002)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_Lo<PERSON>i\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>y_<PERSON>i\" title=\"<PERSON><PERSON>y <PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer and manager (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>y_<PERSON>i"}]}, {"year": "1939", "text": "<PERSON>, English-Australian swimmer and sportscaster (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rose\"><PERSON></a>, English-Australian swimmer and sportscaster (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rose\"><PERSON></a>, English-Australian swimmer and sportscaster (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter and producer (d. 1979)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer and manager (d. 2023)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>enables"}]}, {"year": "1944", "text": "<PERSON>, American actress and singer (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French singer-songwriter and harp player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and harp player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and harp player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Swiss immunologist and academic, Nobel Prize laureate", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss immunologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss immunologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1945", "text": "<PERSON>, Welsh rugby player (d. 2024)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist (d. 2006)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English folk-rock singer-songwriter (d. 1978)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English folk-rock singer-songwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English folk-rock singer-songwriter (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American colonel and astronaut", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American colonel and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American colonel and astronaut", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, New Zealand cricketer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Kenyan runner and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American poet and academic (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American lawyer and jurist, 10th Director of the Federal Bureau of Investigation", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 10th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, 10th <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">Director of the Federal Bureau of Investigation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and coach (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and harmonica player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and harmonica player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish-Australian singer-songwriter, guitarist, and producer (d. 2017)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter, guitarist, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter, guitarist, and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English director and screenwriter (d. 2008)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Mother of <PERSON><PERSON><PERSON> (d. 2024)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mother of Em<PERSON>m (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mother of <PERSON><PERSON><PERSON> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American novelist and short story writer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rout\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_Strout"}]}, {"year": "1956", "text": "<PERSON>, English archbishop", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English rugby player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, British-American astrophysicist and astronaut", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American astrophysicist and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American astrophysicist and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American golfer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Israeli tennis player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shlomo_<PERSON>stein"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American golfer and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Finnish ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, English chef and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English chef and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American football player and sports commentator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sports commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and sports commentator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Belgian motocross racer (d. 2012)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Belgian motocross racer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Belgian motocross racer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Job%C3%A9"}]}, {"year": "1961", "text": "<PERSON>, English rugby player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, British politician, author, journalist, and broadcaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British politician, author, journalist, and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, British politician, author, journalist, and broadcaster", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Charlton"}]}, {"year": "1963", "text": "<PERSON>, Kenyan runner (d. 1995)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American football player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Finnish journalist and politician (d. 2021)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish journalist and politician (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish author and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Danish author and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B8rn_<PERSON>mborg"}]}, {"year": "1966", "text": "<PERSON>, Filipino singer and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/At<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian composer, singer-songwriter, music producer, musician, and philanthropist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian composer, singer-songwriter, music producer, musician, and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian composer, singer-songwriter, music producer, musician, and philanthropist", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American director, producer, and screenwriter (d. 2019)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor and model", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actor and podcaster (d. 2019)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and podcaster (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and podcaster (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American television journalist, presenter, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American television journalist, presenter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American television journalist, presenter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Czech footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Radoslav_L%C3%A1tal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radoslav_L%C3%A1tal\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radoslav_L%C3%A1tal"}]}, {"year": "1970", "text": "<PERSON>, American volleyball player, sportscaster, and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volleyball player, sportscaster, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American volleyball player, sportscaster, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American-Australian singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Greek beach volleyball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Vasso_<PERSON>u\" title=\"<PERSON>ass<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek beach volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasso_<PERSON>\" title=\"<PERSON>ass<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek beach volleyball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vasso_Kara<PERSON>siou"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American baseball player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Argentinian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Slovak ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_Z<PERSON>n%C3%ADk"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fossum\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fossum\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>um"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON>_<PERSON>chi"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Samuel\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Samuel\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gilbert Arenas\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gilbert_Arenas\" title=\"Gilbert Arenas\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_Arenas"}]}, {"year": "1982", "text": "<PERSON>, New Zealand rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American television personality", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pollard\"><PERSON></a>, American television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Pollard"}]}, {"year": "1982", "text": "<PERSON>, English actor and model", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Chinese basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player and analyst", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Hawk\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player and analyst", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. J<PERSON> Hawk\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player and analyst", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress and comedian", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American businessman", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Irish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Norwegian skier", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ug\" title=\"<PERSON><PERSON> Northug\"><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ug\" title=\"<PERSON><PERSON> Northug\"><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ug"}]}, {"year": "1986", "text": "<PERSON>, English singer, songwriter, and musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American YouTuber", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American YouTuber", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, South African footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>alo\" title=\"<PERSON><PERSON><PERSON>alo\"><PERSON><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>alo\" title=\"<PERSON><PERSON><PERSON>alo\"><PERSON><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bon<PERSON><PERSON>_<PERSON>alo"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ndamukong_Suh\" title=\"Ndamukong Suh\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ndamukong_Su<PERSON>\" title=\"Ndamukong Suh\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "Ndamukong Suh", "link": "https://wikipedia.org/wiki/Ndamukong_Suh"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Le%C3%B3n"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Brazilian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Portuguese politician", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Canadian professional golfer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian professional golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Filipino-Australian model, singer and beauty queen, Miss Universe 2018", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino-Australian model, singer and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_2018\" title=\"Miss Universe 2018\">Miss Universe 2018</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino-Australian model, singer and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_2018\" title=\"Miss Universe 2018\">Miss Universe 2018</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>riona_Gray"}, {"title": "Miss Universe 2018", "link": "https://wikipedia.org/wiki/Miss_Universe_2018"}]}, {"year": "1994", "text": "<PERSON>, Spanish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_Su%C3%A1rez"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, South Korean singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American rapper", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Polo_G\" title=\"Polo G\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polo_G\" title=\"Polo G\"><PERSON></a>, American rapper", "links": [{"title": "Polo G", "link": "https://wikipedia.org/wiki/Polo_G"}]}, {"year": "1999", "text": "<PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, South Korean singer and actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bin\" title=\"<PERSON><PERSON>-bin\"><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bin\" title=\"<PERSON><PERSON>-bin\"><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-bin"}]}, {"year": "2000", "text": "<PERSON>, Canadian ice hockey player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Taiwanese singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Shuhua\" title=\"Shu<PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shuhua\" title=\"Shu<PERSON>\"><PERSON><PERSON></a>, Taiwanese singer", "links": [{"title": "Shuhua", "link": "https://wikipedia.org/wiki/Shuhua"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_Lavia\" title=\"Roméo La<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rom%C3%A9o_Lavia\" title=\"Roméo La<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "Roméo <PERSON>", "link": "https://wikipedia.org/wiki/Rom%C3%A9o_Lavia"}]}], "Deaths": [{"year": "786", "text": "<PERSON><PERSON> <PERSON> Tiflis, Iraqi martyr and saint (b. 756)", "html": "786 - <a href=\"https://wikipedia.org/wiki/Abo_of_Tiflis\" title=\"<PERSON><PERSON> of Tiflis\"><PERSON><PERSON> of Tiflis</a>, Iraqi martyr and saint (b. 756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_of_Tiflis\" title=\"<PERSON><PERSON> of Tiflis\"><PERSON><PERSON> of Tiflis</a>, Iraqi martyr and saint (b. 756)", "links": [{"title": "Abo of Tiflis", "link": "https://wikipedia.org/wiki/Abo_of_Tiflis"}]}, {"year": "1088", "text": "<PERSON><PERSON><PERSON> <PERSON> Tours, French scholar and theologian (b. 999)", "html": "1088 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tours\" title=\"<PERSON><PERSON><PERSON> of Tours\"><PERSON><PERSON><PERSON> of Tours</a>, French scholar and theologian (b. 999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tours\" title=\"<PERSON><PERSON><PERSON> of Tours\"><PERSON><PERSON><PERSON> <PERSON> Tours</a>, French scholar and theologian (b. 999)", "links": [{"title": "<PERSON><PERSON><PERSON> of Tours", "link": "https://wikipedia.org/wiki/Berengar_of_Tours"}]}, {"year": "1148", "text": "<PERSON>, 1st Earl of Pembroke (b. 1100)", "html": "1148 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Pembroke\" title=\"<PERSON>, 1st Earl of Pembroke\"><PERSON>, 1st Earl of Pembroke</a> (b. 1100)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Pembroke\" title=\"<PERSON>, 1st Earl of Pembroke\"><PERSON>, 1st Earl of Pembroke</a> (b. 1100)", "links": [{"title": "<PERSON>, 1st Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Pembroke"}]}, {"year": "1233", "text": "<PERSON> of Chester, Countess of Huntingdon, Anglo-Norman noblewoman (b. 1171)", "html": "1233 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Chester,_Countess_of_Huntingdon\" title=\"<PERSON> of Chester, Countess of Huntingdon\"><PERSON> of Chester, Countess of Huntingdon</a>, Anglo-Norman noblewoman (b. 1171)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Chester,_Countess_of_Huntingdon\" title=\"<PERSON> of Chester, Countess of Huntingdon\"><PERSON> of Chester, Countess of Huntingdon</a>, Anglo-Norman noblewoman (b. 1171)", "links": [{"title": "<PERSON> of Chester, Countess of Huntingdon", "link": "https://wikipedia.org/wiki/<PERSON>_of_Chester,_Countess_of_Huntingdon"}]}, {"year": "1275", "text": "<PERSON> of Penyafort, Catalan archbishop and saint (b. 1175)", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Penyafort\" title=\"<PERSON> of Penyafort\"><PERSON> of Penyafort</a>, Catalan archbishop and saint (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pen<PERSON>\" title=\"<PERSON> of Penyafort\"><PERSON> of Penyafort</a>, Catalan archbishop and saint (b. 1175)", "links": [{"title": "<PERSON> of Penyafort", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1350", "text": "<PERSON>, second doge of the Republic of Genoa", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Giovanni I di Murta\"><PERSON> Murta</a>, second doge of the Republic of Genoa", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Giovanni I di Murta\"><PERSON> Murta</a>, second doge of the Republic of Genoa", "links": [{"title": "Giovanni I di Murta", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1358", "text": "<PERSON>, <PERSON><PERSON><PERSON> mystic", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> mystic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> mystic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1406", "text": "<PERSON>, English bishop", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1448", "text": "<PERSON> of Bavaria, King of Denmark, Norway and Sweden (b. 1418)", "html": "1448 - <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, <a href=\"https://wikipedia.org/wiki/King_of_Denmark\" class=\"mw-redirect\" title=\"King of Denmark\">King of Denmark</a>, Norway and Sweden (b. 1418)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria\" title=\"<PERSON> of Bavaria\"><PERSON> of Bavaria</a>, <a href=\"https://wikipedia.org/wiki/King_of_Denmark\" class=\"mw-redirect\" title=\"King of Denmark\">King of Denmark</a>, Norway and Sweden (b. 1418)", "links": [{"title": "<PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria"}, {"title": "King of Denmark", "link": "https://wikipedia.org/wiki/King_of_Denmark"}]}, {"year": "1477", "text": "<PERSON>, Count of Vendôme (b. 1425)", "html": "1477 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Vend%C3%B4me\" class=\"mw-redirect\" title=\"<PERSON>, Count of Vendôme\"><PERSON>, Count of Vendôme</a> (b. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Vend%C3%B4me\" class=\"mw-redirect\" title=\"<PERSON>, Count of Vendôme\"><PERSON>, Count of Vendôme</a> (b. 1425)", "links": [{"title": "<PERSON>, Count of Vendôme", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Vend%C3%B4me"}]}, {"year": "1478", "text": "<PERSON><PERSON><PERSON>, 9th Shahans<PERSON> of the Turkoman A<PERSON> dynasty (b. 1423)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 9th Shahansha<PERSON> of the Turkoman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dynasty (b. 1423)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 9th Shahansha<PERSON> of the Turkoman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> dynasty (b. 1423)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>lu"}]}, {"year": "1481", "text": "<PERSON> bin <PERSON>, Mongolian ruler", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_bin_K%C3%BCch%C3%BCk\" title=\"<PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Mongolian ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_bin_K%C3%BCch%C3%BCk\" title=\"<PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Mongolian ruler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_bin_K%C3%BCch%C3%BCk"}]}, {"year": "1537", "text": "<PERSON>, Duke of Florence (b. 1510)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Florence\" title=\"<PERSON>, Duke of Florence\"><PERSON>, Duke of Florence</a> (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Florence\" title=\"<PERSON>, Duke of Florence\"><PERSON>, Duke of Florence</a> (b. 1510)", "links": [{"title": "<PERSON>, Duke of Florence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Florence"}]}, {"year": "1537", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian architect and painter, designed the Palazzo Massimo alle Colonne (b. 1481)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/Baldassa<PERSON>_<PERSON>zzi\" title=\"Baldassa<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian architect and painter, designed the <a href=\"https://wikipedia.org/wiki/Palazzo_Massimo_alle_Colonne\" title=\"Palazzo Massimo alle Colonne\">Palazzo Massimo alle Colonne</a> (b. 1481)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_<PERSON>\" title=\"Balda<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian architect and painter, designed the <a href=\"https://wikipedia.org/wiki/Palazzo_Massimo_alle_Colonne\" title=\"Palazzo Massimo alle Colonne\">Palazzo Massimo alle Colonne</a> (b. 1481)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lda<PERSON><PERSON>_<PERSON>"}, {"title": "Palazzo Massimo alle Colonne", "link": "https://wikipedia.org/wiki/Palazzo_Massimo_alle_Colonne"}]}, {"year": "1616", "text": "<PERSON>, English impresario (b. 1550)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English impresario (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English impresario (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, German architect, designed the Augsburg Town Hall (b. 1573)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Augsburg_Town_Hall\" title=\"Augsburg Town Hall\">Augsburg Town Hall</a> (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/Augsburg_Town_Hall\" title=\"Augsburg Town Hall\">Augsburg Town Hall</a> (b. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Augsburg Town Hall", "link": "https://wikipedia.org/wiki/Augsburg_Town_Hall"}]}, {"year": "1689", "text": "<PERSON>, English bishop, mathematician, and astronomer (b. 1617)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Salisbury)\" title=\"<PERSON> (bishop of Salisbury)\"><PERSON></a>, English bishop, mathematician, and astronomer (b. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Salisbury)\" title=\"<PERSON> (bishop of Salisbury)\"><PERSON></a>, English bishop, mathematician, and astronomer (b. 1617)", "links": [{"title": "<PERSON> (bishop of Salisbury)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_Salisbury)"}]}, {"year": "1693", "text": "<PERSON><PERSON><PERSON>, Ottoman sultan (b. 1642)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"Mehmed IV\"><PERSON><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV\" title=\"Mehmed IV\"><PERSON><PERSON><PERSON> IV</a>, Ottoman sultan (b. 1642)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV"}]}, {"year": "1711", "text": "<PERSON>, Dutch admiral (b. 1646)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch admiral (b. 1646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch admiral (b. 1646)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, Italian lawyer and jurist (b. 1664)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and jurist (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and jurist (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese actor and playwright (b. 1653)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Monzaemon\" title=\"<PERSON><PERSON><PERSON><PERSON> Monzaemon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and playwright (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Monzaemon\" title=\"<PERSON><PERSON><PERSON><PERSON> Monzaemon\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor and playwright (b. 1653)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>zaemon"}]}, {"year": "1731", "text": "<PERSON>, French physician and chemist (b. 1672)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>an%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and chemist (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>an%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and chemist (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>an%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, English playwright and critic (b. 1657)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English playwright and critic (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dramatist)\" title=\"<PERSON> (dramatist)\"><PERSON></a>, English playwright and critic (b. 1657)", "links": [{"title": "<PERSON> (dramatist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(dramatist)"}]}, {"year": "1813", "text": "<PERSON>, French general (b. 1764)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_d%27Hilliers\" title=\"<PERSON>'Hilliers\"><PERSON></a>, French general (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_d%27Hilliers\" title=\"<PERSON> d'Hilliers\"><PERSON></a>, French general (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Baraguey_d%27Hilliers"}]}, {"year": "1829", "text": "<PERSON>, Czech philologist and historian (b. 1753)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech philologist and historian (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech philologist and historian (b. 1753)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BD"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, French violinist, composer, and conductor (b. 1766)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist, composer, and conductor (b. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist, composer, and conductor (b. 1766)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, English author and playwright (b. 1752)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, French educator, invented <PERSON><PERSON><PERSON> (b. 1809)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator, invented <a href=\"https://wikipedia.org/wiki/Braille\" title=\"Brail<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French educator, invented <a href=\"https://wikipedia.org/wiki/Braille\" title=\"Brail<PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}, {"title": "Braille", "link": "https://wikipedia.org/wiki/Braille"}]}, {"year": "1855", "text": "<PERSON>, Italian jurist, explorer, and author (b. 1779)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian jurist, explorer, and author (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian jurist, explorer, and author (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American lawyer and politician (b. 1815)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (b. 1815)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1884", "text": "<PERSON>, Czech geneticist and botanist (b. 1822)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech geneticist and botanist (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech geneticist and botanist (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author, poet, and playwright (b. 1850)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and playwright (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Norwegian painter (b. 1830)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Dutch economist and historian (b. 1834)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\">He<PERSON><PERSON></a>, Dutch economist and historian (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">He<PERSON><PERSON></a>, Dutch economist and historian (b. 1834)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, German mathematician and philosopher (b. 1845)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and philosopher (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American colonel and politician, 26th President of the United States (b. 1858)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1921", "text": "<PERSON>, American guerrilla leader (b. 1839)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Devil <PERSON>\">Devil <PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare\" title=\"Guerrilla warfare\">guerrilla</a> leader (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Devil <PERSON>\">Devil <PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Guerrilla_warfare\" title=\"Guerrilla warfare\">guerrilla</a> leader (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Guerrilla warfare", "link": "https://wikipedia.org/wiki/Guerrilla_warfare"}]}, {"year": "1922", "text": "<PERSON>, Ukrainian-German mathematician and chess player (b. 1842)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German mathematician and chess player (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German mathematician and chess player (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American hurdler and long jumper (b. 1876)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and long jumper (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and long jumper (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Finnish geologist and professor (b. 1865)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish geologist and professor (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish geologist and professor (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Ukrainian-German pianist (b. 1848)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German pianist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German pianist (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager (b. 1878)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian saint (b. 1845)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saint (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian saint (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Latvian journalist and politician, 2nd President of Latvia (b. 1871)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a> (b. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and coach (b. 1882)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary"}]}, {"year": "1942", "text": "<PERSON>, French soprano and actress (b. 1858)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Emma_Calv%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soprano and actress (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emma_Calv%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French soprano and actress (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emma_Calv%C3%A9"}]}, {"year": "1942", "text": "<PERSON>, Belgian businessman, 3rd President of the International Olympic Committee (b. 1876)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "1944", "text": "<PERSON>, Estonian-German architect (b. 1878)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German architect (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American journalist, reformer, and educator (b. 1857)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ell\" title=\"<PERSON>\"><PERSON></a>, American journalist, reformer, and educator (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, reformer, and educator (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_<PERSON>ell"}]}, {"year": "1945", "text": "<PERSON>, Russian mineralogist and chemist (b. 1863)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Vladimir_V<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mineralogist and chemist (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vladimir_V<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mineralogist and chemist (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Vernadsky"}]}, {"year": "1949", "text": "<PERSON>, American director, producer, and cinematographer (b. 1883)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French painter (b. 1892)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7at\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7at\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7at"}]}, {"year": "1972", "text": "<PERSON>, Chinese general and politician, 2nd Foreign Minister of the People's Republic of China (b. 1901)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Foreign Minister of the People's Republic of China\">Foreign Minister of the People's Republic of China</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Foreign Minister of the People's Republic of China\">Foreign Minister of the People's Republic of China</a> (b. 1901)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}, {"title": "Foreign Minister of the People's Republic of China", "link": "https://wikipedia.org/wiki/Foreign_Minister_of_the_People%27s_Republic_of_China"}]}, {"year": "1974", "text": "<PERSON>, Mexican painter (b. 1896)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican painter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, New Zealand motorcycle racer (b. 1899)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand motorcycle racer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand motorcycle racer (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish physician and author (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish physician and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish physician and author (b. 1896)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>in"}]}, {"year": "1984", "text": "<PERSON>, Hungarian-American cinematographer (b. 1898)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American cinematographer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Scottish-English actor (b. 1949)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Russian physicist and academic, Nobel Prize laureate (b. 1904)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1991", "text": "<PERSON>, American baseball player (b. 1958)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, New Zealand vocalist and songwriter (b. 1949)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand vocalist and songwriter (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand vocalist and songwriter (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American singer-songwriter and trumpet player (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and trumpet player (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and trumpet player (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Russian-French dancer and choreographer (b. 1938)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French dancer and choreographer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French dancer and choreographer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Lithuanian-South African lawyer and politician (b. 1926)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-South African lawyer and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-South African lawyer and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, French-American pianist (b. 1962)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American pianist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American pianist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Dominican educator and politician, 5th Prime Minister of Dominica (b. 1954)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)\" title=\"<PERSON> (Dominican politician)\"><PERSON></a>, Dominican educator and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)\" title=\"<PERSON> (Dominican politician)\"><PERSON></a>, Dominican educator and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Dominica\" title=\"Prime Minister of Dominica\">Prime Minister of Dominica</a> (b. 1954)", "links": [{"title": "<PERSON> (Dominican politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Dominican_politician)"}, {"title": "Prime Minister of Dominica", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Dominica"}]}, {"year": "2005", "text": "<PERSON>, Irish civil servant and politician, 12th Irish Minister for Health (b. 1932)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish civil servant and politician, 12th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish civil servant and politician, 12th <a href=\"https://wikipedia.org/wiki/Minister_for_Health_(Ireland)\" title=\"Minister for Health (Ireland)\">Irish Minister for Health</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Health (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Health_(Ireland)"}]}, {"year": "2005", "text": "<PERSON>, Canadian academic and politician, 15th Lieutenant Governor of Alberta (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and politician, 15th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta\" title=\"Lieutenant Governor of Alberta\">Lieutenant Governor of Alberta</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Lois <PERSON>\"><PERSON></a>, Canadian academic and politician, 15th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta\" title=\"Lieutenant Governor of Alberta\">Lieutenant Governor of Alberta</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Alberta", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Alberta"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian motorcycle racer (b. 1933)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Tarquinio_Provini\" title=\"Tarquin<PERSON> Provini\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarquin<PERSON>_<PERSON>vini\" title=\"Tarquin<PERSON> Provini\"><PERSON><PERSON><PERSON><PERSON></a>, Italian motorcycle racer (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tarquinio_Provini"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American political scientist, historian, and academic (b. 1912)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, historian, and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist, historian, and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Rabbi of Mir Yeshiva (Brooklyn) (b. 1920)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Rabbi of <a href=\"https://wikipedia.org/wiki/Mir_Yeshiva_(Brooklyn)\" title=\"Mir Yeshiva (Brooklyn)\">Mir Yeshiva (Brooklyn)</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Rabbi of <a href=\"https://wikipedia.org/wiki/Mir_Yeshiva_(Brooklyn)\" title=\"Mir Yeshiva (Brooklyn)\">Mir Yeshiva (Brooklyn)</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mir Yeshiva (Brooklyn)", "link": "https://wikipedia.org/wiki/Mir_Yeshiva_(Brooklyn)"}]}, {"year": "2009", "text": "<PERSON>, American guitarist, songwriter, and actor (probable; b. 1948)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and actor (probable; b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and actor (probable; b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Nigerian footballer, coach, and sportscaster (b. 1967)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>afor\" title=\"<PERSON><PERSON> Okafor\"><PERSON><PERSON></a>, Nigerian footballer, coach, and sportscaster (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>afor\" title=\"<PERSON><PERSON> Okafor\"><PERSON><PERSON></a>, Nigerian footballer, coach, and sportscaster (b. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>afor"}]}, {"year": "2012", "text": "<PERSON>, South African-English radio and television host (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English radio and television host (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English radio and television host (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian footballer and soldier (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pol<PERSON>\"><PERSON></a>, Australian footballer and soldier (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Pol<PERSON>\"><PERSON></a>, Australian footballer and soldier (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Pola"}]}, {"year": "2013", "text": "<PERSON>, American art collector, founded the Amon Carter Museum of American Art (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Museum_of_American_Art\" title=\"Amon <PERSON> Museum of American Art\">Amon Carter Museum of American Art</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art collector, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Museum_of_American_Art\" title=\"Amon <PERSON> Museum of American Art\">Amon Carter Museum of American Art</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> Carter Museum of American Art", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Museum_of_American_Art"}]}, {"year": "2014", "text": "<PERSON>, French Resistance soldier and photographer (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Marina_Ginest%C3%A0\" title=\"Marina Ginestà\"><PERSON></a>, French Resistance soldier and photographer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Ginest%C3%A0\" title=\"Marina Ginestà\"><PERSON></a>, French Resistance soldier and photographer (b. 1919)", "links": [{"title": "Marina G<PERSON>t<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Ginest%C3%A0"}]}, {"year": "2014", "text": "<PERSON>, Brazilian singer-songwriter (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nelson Ned\"><PERSON></a>, Brazilian singer-songwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ned\" title=\"Nelson Ned\"><PERSON></a>, Brazilian singer-songwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ned"}]}, {"year": "2014", "text": "<PERSON>, American psychologist and academic (b. 1916)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American lieutenant and target shooter (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, American lieutenant and target shooter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)\" title=\"<PERSON> (sport shooter)\"><PERSON></a>, American lieutenant and target shooter (b. 1918)", "links": [{"title": "<PERSON> (sport shooter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sport_shooter)"}]}, {"year": "2015", "text": "<PERSON>, English meteorologist and academic (b. 1923)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English meteorologist and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English meteorologist and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Jr., American actor and screenwriter (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American actor and screenwriter (b. 1929)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "2016", "text": "<PERSON>, American journalist and author (b. 1936)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Florence_King\" title=\"Florence King\"><PERSON></a>, American journalist and author (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_King\" title=\"Florence King\"><PERSON></a>, American journalist and author (b. 1936)", "links": [{"title": "Florence King", "link": "https://wikipedia.org/wiki/Florence_King"}]}, {"year": "2016", "text": "<PERSON>, Irish golfer and architect (b. 1948)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, Irish golfer and architect (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, Irish golfer and architect (b. 1948)", "links": [{"title": "<PERSON> Jnr", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Connor_Jnr"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Italian model, actress, and director, Miss Italy 1946 (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model, actress, and director, <a href=\"https://wikipedia.org/wiki/Miss_Italia\" title=\"Miss Italia\">Miss Italy 1946</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian model, actress, and director, <a href=\"https://wikipedia.org/wiki/Miss_Italia\" title=\"Miss Italia\">Miss Italy 1946</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silvana_Pampanini"}, {"title": "Miss Italia", "link": "https://wikipedia.org/wiki/Miss_Italia"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Venezuelan politician, President of Venezuela (b. 1923)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"Octavi<PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavio_<PERSON>\" title=\"<PERSON>avi<PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Octavio_Lepage"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Indian actor (b. 1950)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/O<PERSON>_P<PERSON>\" title=\"O<PERSON> Puri\"><PERSON><PERSON></a>, Indian actor (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON>_P<PERSON>\" title=\"<PERSON><PERSON> Puri\"><PERSON><PERSON></a>, Indian actor (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Om_Puri"}]}, {"year": "2019", "text": "<PERSON>, Cuban revolution leader (b. 1923)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Fern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Cuban revolution leader (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Fern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Cuban revolution leader (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ram%C3%B3n_Fern%C3%A1ndez"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Gambian-born American professor (b. 1942)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian-born American professor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian-born American professor (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "2019", "text": "<PERSON><PERSON>, British actor (b. 1932)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, British actor (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Austrian-born British economics professor (b. 1917)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British economics professor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British economics professor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, South African businessman (b. 1920)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African businessman (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African businessman (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian ice hockey administrator and businessman (b. 1935)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>_<PERSON>\" title=\"Gord <PERSON>\"><PERSON></a>, Canadian ice hockey administrator and businessman (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON>_<PERSON>\" title=\"Gord <PERSON>\"><PERSON></a>, Canadian ice hockey administrator and businessman (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>_<PERSON>wick"}]}, {"year": "2021", "text": "<PERSON>, British diplomat kidnapped during the 1970 October crisis in Québec (b. 1921)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Cross\"><PERSON></a>, British diplomat kidnapped during the 1970 <a href=\"https://wikipedia.org/wiki/October_crisis\" class=\"mw-redirect\" title=\"October crisis\">October crisis</a> in Québec (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"James Cross\"><PERSON></a>, British diplomat kidnapped during the 1970 <a href=\"https://wikipedia.org/wiki/October_crisis\" class=\"mw-redirect\" title=\"October crisis\">October crisis</a> in Québec (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "October crisis", "link": "https://wikipedia.org/wiki/October_crisis"}]}, {"year": "2022", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1939)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Bahamian-American actor, director, and diplomat (b. 1927)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-American actor, director, and diplomat (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian-American actor, director, and diplomat (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Philippine novelist (b. 1924)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/F._<PERSON><PERSON>l_Jos%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON></a>, Philippine novelist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F._Si<PERSON>l_Jos%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON></a>, Philippine novelist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/F._Sionil_Jos%C3%A9"}]}, {"year": "2023", "text": "<PERSON>, American Roman Catholic nun, peace activist, and writer", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic nun, peace activist, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic nun, peace activist, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}]}}