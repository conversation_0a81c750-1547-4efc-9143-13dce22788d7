{"date": "February 4", "url": "https://wikipedia.org/wiki/February_4", "data": {"Events": [{"year": "211", "text": "Following the death of the Roman Emperor <PERSON><PERSON><PERSON> at Eboracum (modern York, England) while preparing to lead a campaign against the Caledonians, the empire is left in the control of his two quarrelling sons, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, whom he had instructed to make peace.", "html": "211 - Following the death of the Roman Emperor <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Septimius <PERSON></a> at Eboracum (modern York, England) while preparing to lead a campaign against the <a href=\"https://wikipedia.org/wiki/Caledonians\" title=\"Caledonians\">Caledonians</a>, the empire is left in the control of his two quarrelling sons, <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracalla\">Caracalla</a> and <a href=\"https://wikipedia.org/wiki/Get<PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, whom he had instructed to make peace.", "no_year_html": "Following the death of the Roman Emperor <a href=\"https://wikipedia.org/wiki/Septimius_Severus\" title=\"Septimius Severus\">Septimius <PERSON></a> at Eboracum (modern York, England) while preparing to lead a campaign against the <a href=\"https://wikipedia.org/wiki/Caledonians\" title=\"Caledonians\">Caledonians</a>, the empire is left in the control of his two quarrelling sons, <a href=\"https://wikipedia.org/wiki/Caracalla\" title=\"Caracalla\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Get<PERSON>_(emperor)\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, whom he had instructed to make peace.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septimius_Severus"}, {"title": "Caledonians", "link": "https://wikipedia.org/wiki/Caledonians"}, {"title": "Caracal<PERSON>", "link": "https://wikipedia.org/wiki/Caracalla"}, {"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "960", "text": "<PERSON> declares himself Emperor <PERSON><PERSON> of Song, ending the Later Zhou and beginning the Song dynasty.", "html": "960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares himself <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a>, ending the <a href=\"https://wikipedia.org/wiki/Later_Zhou\" title=\"<PERSON> Zhou\">Later Zhou</a> and beginning the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> declares himself <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a>, ending the <a href=\"https://wikipedia.org/wiki/Later_Zhou\" title=\"Later Zhou\">Later Zhou</a> and beginning the <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}, {"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}, {"title": "Later <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}]}, {"year": "1169", "text": "A strong earthquake strikes the Ionian coast of Sicily, causing tens of thousands of injuries and deaths, especially in Catania.", "html": "1169 - A <a href=\"https://wikipedia.org/wiki/1169_Sicily_earthquake\" title=\"1169 Sicily earthquake\">strong earthquake</a> strikes the Ionian coast of Sicily, causing tens of thousands of injuries and deaths, especially in <a href=\"https://wikipedia.org/wiki/Catania\" title=\"Catania\">Catania</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1169_Sicily_earthquake\" title=\"1169 Sicily earthquake\">strong earthquake</a> strikes the Ionian coast of Sicily, causing tens of thousands of injuries and deaths, especially in <a href=\"https://wikipedia.org/wiki/Catania\" title=\"Catania\">Catania</a>.", "links": [{"title": "1169 Sicily earthquake", "link": "https://wikipedia.org/wiki/1169_Sicily_earthquake"}, {"title": "Catania", "link": "https://wikipedia.org/wiki/Catania"}]}, {"year": "1454", "text": "Thirteen Years' War: The Secret Council of the Prussian Confederation sends a formal act of disobedience to the Grand Master of the Teutonic Knights, sparking the Thirteen Years' War.", "html": "1454 - <a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)\" class=\"mw-redirect\" title=\"Thirteen Years' War (1454-66)\">Thirteen Years' War</a>: The Secret Council of the <a href=\"https://wikipedia.org/wiki/Prussian_Confederation\" title=\"Prussian Confederation\">Prussian Confederation</a> sends a formal act of disobedience to the <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Grand Masters of the Teutonic Knights\">Grand Master</a> of the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Knights</a>, sparking the Thirteen Years' War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)\" class=\"mw-redirect\" title=\"Thirteen Years' War (1454-66)\">Thirteen Years' War</a>: The Secret Council of the <a href=\"https://wikipedia.org/wiki/Prussian_Confederation\" title=\"Prussian Confederation\">Prussian Confederation</a> sends a formal act of disobedience to the <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Teutonic_Knights\" class=\"mw-redirect\" title=\"Grand Masters of the Teutonic Knights\">Grand Master</a> of the <a href=\"https://wikipedia.org/wiki/Teutonic_Order\" title=\"Teutonic Order\">Teutonic Knights</a>, sparking the Thirteen Years' War.", "links": [{"title": "Thirteen Years' War (1454-66)", "link": "https://wikipedia.org/wiki/Thirteen_Years%27_War_(1454%E2%80%9366)"}, {"title": "Prussian Confederation", "link": "https://wikipedia.org/wiki/Prussian_Confederation"}, {"title": "Grand Masters of the Teutonic Knights", "link": "https://wikipedia.org/wiki/Grand_Masters_of_the_Teutonic_Knights"}, {"title": "Teutonic Order", "link": "https://wikipedia.org/wiki/Teutonic_Order"}]}, {"year": "1555", "text": "<PERSON> is burned at the stake, becoming the first English Protestant martyr under <PERSON> of England.", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_editor_and_martyr)\" title=\"<PERSON> (Bible editor and martyr)\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Burned_at_the_stake\" class=\"mw-redirect\" title=\"Burned at the stake\">burned at the stake</a>, becoming the first English <a href=\"https://wikipedia.org/wiki/Protestant\" class=\"mw-redirect\" title=\"Protestant\">Protestant</a> <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Mary I of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Bible_editor_and_martyr)\" title=\"<PERSON> (Bible editor and martyr)\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Burned_at_the_stake\" class=\"mw-redirect\" title=\"Burned at the stake\">burned at the stake</a>, becoming the first English <a href=\"https://wikipedia.org/wiki/Protestant\" class=\"mw-redirect\" title=\"Protestant\">Protestant</a> <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON> (Bible editor and martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_editor_and_martyr)"}, {"title": "Burned at the stake", "link": "https://wikipedia.org/wiki/Burned_at_the_stake"}, {"title": "Protestant", "link": "https://wikipedia.org/wiki/Protestant"}, {"title": "Martyr", "link": "https://wikipedia.org/wiki/Martyr"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1703", "text": "In Edo (now Tokyo), all but one of the Forty-seven <PERSON><PERSON> commit seppuku (ritual suicide) as recompense for avenging their master's death.", "html": "1703 - In <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> (now Tokyo), all but one of the <a href=\"https://wikipedia.org/wiki/Forty-seven_Ronin\" class=\"mw-redirect\" title=\"Forty-seven Ronin\">Forty-seven Ronin</a> commit <a href=\"https://wikipedia.org/wiki/Seppuku\" title=\"Seppuku\">seppuku</a> (ritual suicide) as recompense for avenging their master's death.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> (now Tokyo), all but one of the <a href=\"https://wikipedia.org/wiki/Forty-seven_Ronin\" class=\"mw-redirect\" title=\"Forty-seven Ronin\">Forty-seven Ronin</a> commit <a href=\"https://wikipedia.org/wiki/Seppuku\" title=\"Seppuku\">seppuku</a> (ritual suicide) as recompense for avenging their master's death.", "links": [{"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}, {"title": "Forty-seven <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Forty-seven_<PERSON>in"}, {"title": "Seppuku", "link": "https://wikipedia.org/wiki/Seppuku"}]}, {"year": "1758", "text": "The city of Macapá in Brazil is founded by Sebastião Veiga Cabral.", "html": "1758 - The city of <a href=\"https://wikipedia.org/wiki/Macap%C3%A1\" title=\"Macapá\">Macapá</a> in Brazil is founded by <PERSON><PERSON><PERSON>ão Veiga Cabral.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Macap%C3%A1\" title=\"Macapá\">Macapá</a> in Brazil is founded by <PERSON><PERSON><PERSON><PERSON> Veiga Cabral.", "links": [{"title": "Macapá", "link": "https://wikipedia.org/wiki/Macap%C3%A1"}]}, {"year": "1789", "text": "<PERSON> is unanimously elected as the first President of the United States by the U.S. Electoral College.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> is unanimously elected as the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> by the <a href=\"https://wikipedia.org/wiki/U.S._Electoral_College\" class=\"mw-redirect\" title=\"U.S. Electoral College\">U.S. Electoral College</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\"><PERSON></a> is unanimously elected as the first <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> by the <a href=\"https://wikipedia.org/wiki/U.S._Electoral_College\" class=\"mw-redirect\" title=\"U.S. Electoral College\">U.S. Electoral College</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "U.S. Electoral College", "link": "https://wikipedia.org/wiki/U.S._Electoral_College"}]}, {"year": "1794", "text": "The French legislature abolishes slavery throughout all territories of the French First Republic. It would be reestablished in the French West Indies in 1802.", "html": "1794 - The French legislature abolishes <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> throughout all territories of the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French First Republic</a>. It would be reestablished in the <a href=\"https://wikipedia.org/wiki/French_West_Indies\" title=\"French West Indies\">French West Indies</a> in 1802.", "no_year_html": "The French legislature abolishes <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> throughout all territories of the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French First Republic</a>. It would be reestablished in the <a href=\"https://wikipedia.org/wiki/French_West_Indies\" title=\"French West Indies\">French West Indies</a> in 1802.", "links": [{"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}, {"title": "French West Indies", "link": "https://wikipedia.org/wiki/French_West_Indies"}]}, {"year": "1797", "text": "The Riobamba earthquake strikes Ecuador, causing up to 40,000 casualties.", "html": "1797 - The <a href=\"https://wikipedia.org/wiki/1797_Riobamba_earthquake\" title=\"1797 Riobamba earthquake\">Riobamba earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, causing up to 40,000 casualties.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1797_Riobamba_earthquake\" title=\"1797 Riobamba earthquake\">Riobamba earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, causing up to 40,000 casualties.", "links": [{"title": "1797 Riobamba earthquake", "link": "https://wikipedia.org/wiki/1797_Riobamba_earthquake"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}]}, {"year": "1801", "text": "<PERSON> is sworn in as Chief Justice of the United States.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1810", "text": "Napoleonic Wars: Britain seizes Guadeloupe.", "html": "1810 - Napoleonic Wars: Britain <a href=\"https://wikipedia.org/wiki/Invasion_of_Guadeloupe_(1810)\" title=\"Invasion of Guadeloupe (1810)\">seizes Guadeloupe</a>.", "no_year_html": "Napoleonic Wars: Britain <a href=\"https://wikipedia.org/wiki/Invasion_of_Guadeloupe_(1810)\" title=\"Invasion of Guadeloupe (1810)\">seizes Guadeloupe</a>.", "links": [{"title": "Invasion of Guadeloupe (1810)", "link": "https://wikipedia.org/wiki/Invasion_of_Guadeloupe_(1810)"}]}, {"year": "1820", "text": "The Chilean Navy under the command of Lord <PERSON> completes the two-day long Capture of Valdivia with just 300 men and two ships.", "html": "1820 - The <a href=\"https://wikipedia.org/wiki/Chilean_Navy\" title=\"Chilean Navy\">Chilean Navy</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_<PERSON>_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\">Lord <PERSON></a> completes the two-day long <a href=\"https://wikipedia.org/wiki/Capture_of_Valdivia\" title=\"Capture of Valdivia\">Capture of Valdivia</a> with just 300 men and two ships.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chilean_Navy\" title=\"Chilean Navy\">Chilean Navy</a> under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_<PERSON>_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\">Lord <PERSON></a> completes the two-day long <a href=\"https://wikipedia.org/wiki/Capture_of_Valdivia\" title=\"Capture of Valdivia\">Capture of Valdivia</a> with just 300 men and two ships.", "links": [{"title": "Chilean Navy", "link": "https://wikipedia.org/wiki/Chilean_Navy"}, {"title": "<PERSON>, 10th Earl of Dundonald", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald"}, {"title": "Capture of Valdivia", "link": "https://wikipedia.org/wiki/Capture_of_Valdivia"}]}, {"year": "1825", "text": "The Ohio Legislature authorizes the construction of the Ohio and Erie Canal and the Miami and Erie Canal.", "html": "1825 - The <a href=\"https://wikipedia.org/wiki/Ohio_Legislature\" class=\"mw-redirect\" title=\"Ohio Legislature\">Ohio Legislature</a> authorizes the construction of the <a href=\"https://wikipedia.org/wiki/Ohio_and_Erie_Canal\" title=\"Ohio and Erie Canal\">Ohio and Erie Canal</a> and the <a href=\"https://wikipedia.org/wiki/Miami_and_Erie_Canal\" class=\"mw-redirect\" title=\"Miami and Erie Canal\">Miami and Erie Canal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ohio_Legislature\" class=\"mw-redirect\" title=\"Ohio Legislature\">Ohio Legislature</a> authorizes the construction of the <a href=\"https://wikipedia.org/wiki/Ohio_and_Erie_Canal\" title=\"Ohio and Erie Canal\">Ohio and Erie Canal</a> and the <a href=\"https://wikipedia.org/wiki/Miami_and_Erie_Canal\" class=\"mw-redirect\" title=\"Miami and Erie Canal\">Miami and Erie Canal</a>.", "links": [{"title": "Ohio Legislature", "link": "https://wikipedia.org/wiki/Ohio_Legislature"}, {"title": "Ohio and Erie Canal", "link": "https://wikipedia.org/wiki/Ohio_and_Erie_Canal"}, {"title": "Miami and Erie Canal", "link": "https://wikipedia.org/wiki/Miami_and_Erie_Canal"}]}, {"year": "1846", "text": "The first Mormon pioneers make their exodus from Nauvoo, Illinois, westward towards Salt Lake Valley.", "html": "1846 - The first <a href=\"https://wikipedia.org/wiki/Mormon_pioneers\" title=\"Mormon pioneers\">Mormon pioneers</a> make their exodus from <a href=\"https://wikipedia.org/wiki/Nauvoo,_Illinois\" title=\"Nauvoo, Illinois\">Nauvoo, Illinois</a>, westward towards <a href=\"https://wikipedia.org/wiki/Salt_Lake_Valley\" title=\"Salt Lake Valley\">Salt Lake Valley</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Mormon_pioneers\" title=\"Mormon pioneers\">Mormon pioneers</a> make their exodus from <a href=\"https://wikipedia.org/wiki/Nauvoo,_Illinois\" title=\"Nauvoo, Illinois\">Nauvoo, Illinois</a>, westward towards <a href=\"https://wikipedia.org/wiki/Salt_Lake_Valley\" title=\"Salt Lake Valley\">Salt Lake Valley</a>.", "links": [{"title": "Mormon pioneers", "link": "https://wikipedia.org/wiki/Mormon_pioneers"}, {"title": "Nauvoo, Illinois", "link": "https://wikipedia.org/wiki/Nauvoo,_Illinois"}, {"title": "Salt Lake Valley", "link": "https://wikipedia.org/wiki/Salt_Lake_Valley"}]}, {"year": "1859", "text": "The Codex Sinaiticus is discovered in Egypt.", "html": "1859 - The <a href=\"https://wikipedia.org/wiki/Codex_Sinaiticus\" title=\"Codex Sinaiticus\">Codex Sinaiticus</a> is discovered in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Codex_Sinaiticus\" title=\"Codex Sinaiticus\">Codex Sinaiticus</a> is discovered in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Codex Sinaiticus", "link": "https://wikipedia.org/wiki/Codex_Sinaiticus"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1861", "text": "American Civil War: In Montgomery, Alabama, delegates from six breakaway U.S. states meet and initiate the process that would form the Confederate States of America on February 8.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, delegates from six breakaway <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a> meet and initiate the process that would form the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a> on <a href=\"https://wikipedia.org/wiki/February_8\" title=\"February 8\">February 8</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, delegates from six breakaway <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. states</a> meet and initiate the process that would form the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate States of America</a> on <a href=\"https://wikipedia.org/wiki/February_8\" title=\"February 8\">February 8</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Montgomery, Alabama", "link": "https://wikipedia.org/wiki/Montgomery,_Alabama"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "February 8", "link": "https://wikipedia.org/wiki/February_8"}]}, {"year": "1899", "text": "The Philippine-American War begins when four Filipino soldiers enter the \"American Zone\" in Manila, igniting the Battle of Manila.", "html": "1899 - The <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a> begins when four Filipino soldiers enter the \"American Zone\" in Manila, igniting the <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1899)\" title=\"Battle of Manila (1899)\">Battle of Manila</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a> begins when four Filipino soldiers enter the \"American Zone\" in Manila, igniting the <a href=\"https://wikipedia.org/wiki/Battle_of_Manila_(1899)\" title=\"Battle of Manila (1899)\">Battle of Manila</a>.", "links": [{"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}, {"title": "Battle of Manila (1899)", "link": "https://wikipedia.org/wiki/Battle_of_Manila_(1899)"}]}, {"year": "1932", "text": "Second Sino-Japanese War: Harbin, Manchuria, falls to Japan.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Harbin\" title=\"Harbin\">Harbin</a>, <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>, <a href=\"https://wikipedia.org/wiki/Defense_of_Harbin\" title=\"Defense of Harbin\">falls to</a> <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Harbin\" title=\"Harbin\">Harbin</a>, <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a>, <a href=\"https://wikipedia.org/wiki/Defense_of_Harbin\" title=\"Defense of Harbin\">falls to</a> <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japan</a>.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harbin"}, {"title": "Manchuria", "link": "https://wikipedia.org/wiki/Manchuria"}, {"title": "Defense of Harbin", "link": "https://wikipedia.org/wiki/Defense_of_Harbin"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1938", "text": "<PERSON> appoints himself as head of the Armed Forces High Command.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> appoints himself as head of the <a href=\"https://wikipedia.org/wiki/Oberkommando_der_Wehrmacht\" title=\"Oberkommando der Wehrmacht\">Armed Forces High Command</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> appoints himself as head of the <a href=\"https://wikipedia.org/wiki/Oberkommando_der_Wehrmacht\" title=\"Oberkommando der Wehrmacht\">Armed Forces High Command</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oberkommando der Wehrmacht", "link": "https://wikipedia.org/wiki/Oberkommando_der_Wehrmacht"}]}, {"year": "1941", "text": "The United Service Organization (USO) is created to entertain American troops.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/United_Service_Organization\" class=\"mw-redirect\" title=\"United Service Organization\">United Service Organization</a> (USO) is created to entertain American troops.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Service_Organization\" class=\"mw-redirect\" title=\"United Service Organization\">United Service Organization</a> (USO) is created to entertain American troops.", "links": [{"title": "United Service Organization", "link": "https://wikipedia.org/wiki/United_Service_Organization"}]}, {"year": "1945", "text": "World War II: Santo Tomas Internment Camp is liberated from Japanese authority.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Santo_Tomas_Internment_Camp\" title=\"Santo Tomas Internment Camp\">Santo Tomas Internment Camp</a> is liberated from Japanese authority.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Santo_Tomas_Internment_Camp\" title=\"Santo Tomas Internment Camp\">Santo Tomas Internment Camp</a> is liberated from Japanese authority.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Santo Tomas Internment Camp", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Internment_Camp"}]}, {"year": "1945", "text": "World War II: The Yalta Conference between the \"Big Three\" (<PERSON>, <PERSON>, and <PERSON>) opens at the Livadia Palace in the Crimea.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Yalta_Conference\" title=\"Yalta Conference\">Yalta Conference</a> between the \"Big Three\" (<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>) opens at the <a href=\"https://wikipedia.org/wiki/Livadia_Palace\" title=\"Livadia Palace\">Livadia Palace</a> in the <a href=\"https://wikipedia.org/wiki/Crimea#Soviet_Union:_1922.E2.80.931991\" title=\"Crimea\">Crimea</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Yalta_Conference\" title=\"Yalta Conference\">Yalta Conference</a> between the \"Big Three\" (<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>) opens at the <a href=\"https://wikipedia.org/wiki/Livadia_Palace\" title=\"Livadia Palace\">Livadia Palace</a> in the <a href=\"https://wikipedia.org/wiki/Crimea#Soviet_Union:_1922.E2.80.931991\" title=\"Crimea\">Crimea</a>.", "links": [{"title": "Yalta Conference", "link": "https://wikipedia.org/wiki/Yalta_Conference"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Livadia Palace", "link": "https://wikipedia.org/wiki/Livadia_Palace"}, {"title": "Crimea", "link": "https://wikipedia.org/wiki/Crimea#Soviet_Union:_1922.E2.80.931991"}]}, {"year": "1945", "text": "World War II: The British Indian Army and Imperial Japanese Army begin a series of battles known as the Battle of Pokoku and Irrawaddy River operations.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/British_Indian_Army\" title=\"British Indian Army\">British Indian Army</a> and <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> begin a series of battles known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Pokoku_and_Irrawaddy_River_operations\" title=\"Battle of Pokoku and Irrawaddy River operations\">Battle of Pokoku and Irrawaddy River operations</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/British_Indian_Army\" title=\"British Indian Army\">British Indian Army</a> and <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> begin a series of battles known as the <a href=\"https://wikipedia.org/wiki/Battle_of_Pokoku_and_Irrawaddy_River_operations\" title=\"Battle of Pokoku and Irrawaddy River operations\">Battle of Pokoku and Irrawaddy River operations</a>.", "links": [{"title": "British Indian Army", "link": "https://wikipedia.org/wiki/British_Indian_Army"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Battle of Pokoku and Irrawaddy River operations", "link": "https://wikipedia.org/wiki/Battle_of_Pokoku_and_Irrawaddy_River_operations"}]}, {"year": "1948", "text": "Ceylon (later renamed Sri Lanka) becomes independent within the British Commonwealth.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Dominion_of_Ceylon\" title=\"Dominion of Ceylon\">Ceylon</a> (later renamed <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>) becomes independent within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">British Commonwealth</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominion_of_Ceylon\" title=\"Dominion of Ceylon\">Ceylon</a> (later renamed <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>) becomes independent within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">British Commonwealth</a>.", "links": [{"title": "Dominion of Ceylon", "link": "https://wikipedia.org/wiki/Dominion_of_Ceylon"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1961", "text": "The Angolan War of Independence and the greater Portuguese Colonial War begin.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Angolan_War_of_Independence\" title=\"Angolan War of Independence\">Angolan War of Independence</a> and the greater <a href=\"https://wikipedia.org/wiki/Portuguese_Colonial_War\" title=\"Portuguese Colonial War\">Portuguese Colonial War</a> begin.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Angolan_War_of_Independence\" title=\"Angolan War of Independence\">Angolan War of Independence</a> and the greater <a href=\"https://wikipedia.org/wiki/Portuguese_Colonial_War\" title=\"Portuguese Colonial War\">Portuguese Colonial War</a> begin.", "links": [{"title": "Angolan War of Independence", "link": "https://wikipedia.org/wiki/Angolan_War_of_Independence"}, {"title": "Portuguese Colonial War", "link": "https://wikipedia.org/wiki/Portuguese_Colonial_War"}]}, {"year": "1966", "text": "All Nippon Airways Flight 60 plunges into Tokyo Bay, killing 133.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_60\" title=\"All Nippon Airways Flight 60\">All Nippon Airways Flight 60</a> plunges into <a href=\"https://wikipedia.org/wiki/Tokyo_Bay\" title=\"Tokyo Bay\">Tokyo Bay</a>, killing 133.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/All_Nippon_Airways_Flight_60\" title=\"All Nippon Airways Flight 60\">All Nippon Airways Flight 60</a> plunges into <a href=\"https://wikipedia.org/wiki/Tokyo_Bay\" title=\"Tokyo Bay\">Tokyo Bay</a>, killing 133.", "links": [{"title": "All Nippon Airways Flight 60", "link": "https://wikipedia.org/wiki/All_Nippon_Airways_Flight_60"}, {"title": "Tokyo Bay", "link": "https://wikipedia.org/wiki/Tokyo_Bay"}]}, {"year": "1967", "text": "Lunar Orbiter program: Lunar Orbiter 3 lifts off from Cape Canaveral's Launch Complex 13 on its mission to identify possible landing sites for the Surveyor and Apollo spacecraft.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Lunar_Orbiter_program\" title=\"Lunar Orbiter program\">Lunar Orbiter program</a>: <a href=\"https://wikipedia.org/wiki/Lunar_Orbiter_3\" title=\"Lunar Orbiter 3\">Lunar Orbiter 3</a> lifts off from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral's</a> Launch Complex 13 on its mission to identify possible landing sites for the <a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor</a> and <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> spacecraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lunar_Orbiter_program\" title=\"Lunar Orbiter program\">Lunar Orbiter program</a>: <a href=\"https://wikipedia.org/wiki/Lunar_Orbiter_3\" title=\"Lunar Orbiter 3\">Lunar Orbiter 3</a> lifts off from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station\" class=\"mw-redirect\" title=\"Cape Canaveral Air Force Station\">Cape Canaveral's</a> Launch Complex 13 on its mission to identify possible landing sites for the <a href=\"https://wikipedia.org/wiki/Surveyor_program\" title=\"Surveyor program\">Surveyor</a> and <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo</a> spacecraft.", "links": [{"title": "Lunar Orbiter program", "link": "https://wikipedia.org/wiki/Lunar_Orbiter_program"}, {"title": "Lunar Orbiter 3", "link": "https://wikipedia.org/wiki/Lunar_Orbiter_3"}, {"title": "Cape Canaveral Air Force Station", "link": "https://wikipedia.org/wiki/Cape_Canaveral_Air_Force_Station"}, {"title": "Surveyor program", "link": "https://wikipedia.org/wiki/Surveyor_program"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}]}, {"year": "1974", "text": "The Symbionese Liberation Army kidnaps <PERSON> in Berkeley, California.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Symbionese_Liberation_Army\" title=\"Symbionese Liberation Army\">Symbionese Liberation Army</a> kidnaps <a href=\"https://wikipedia.org/wiki/<PERSON>_Hearst\" title=\"<PERSON> Hearst\"><PERSON></a> in <a href=\"https://wikipedia.org/wiki/Berkeley,_California\" title=\"Berkeley, California\">Berkeley, California</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Symbionese_Liberation_Army\" title=\"Symbionese Liberation Army\">Symbionese Liberation Army</a> kidnaps <a href=\"https://wikipedia.org/wiki/<PERSON>_Hearst\" title=\"<PERSON> Hearst\"><PERSON>st</a> in <a href=\"https://wikipedia.org/wiki/Berkeley,_California\" title=\"Berkeley, California\">Berkeley, California</a>.", "links": [{"title": "Symbionese Liberation Army", "link": "https://wikipedia.org/wiki/Symbionese_Liberation_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>st"}, {"title": "Berkeley, California", "link": "https://wikipedia.org/wiki/Berkeley,_California"}]}, {"year": "1974", "text": "M62 coach bombing: The Provisional Irish Republican Army (IRA) explodes a bomb on a bus carrying off-duty British Armed Forces personnel in Yorkshire, England. Nine soldiers and three civilians are killed.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/M62_coach_bombing\" title=\"M62 coach bombing\">M62 coach bombing</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> (IRA) explodes a bomb on a bus carrying off-duty <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British Armed Forces</a> personnel in <a href=\"https://wikipedia.org/wiki/Yorkshire\" title=\"Yorkshire\">Yorkshire</a>, England. Nine soldiers and three civilians are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M62_coach_bombing\" title=\"M62 coach bombing\">M62 coach bombing</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> (IRA) explodes a bomb on a bus carrying off-duty <a href=\"https://wikipedia.org/wiki/British_Armed_Forces\" title=\"British Armed Forces\">British Armed Forces</a> personnel in <a href=\"https://wikipedia.org/wiki/Yorkshire\" title=\"Yorkshire\">Yorkshire</a>, England. Nine soldiers and three civilians are killed.", "links": [{"title": "M62 coach bombing", "link": "https://wikipedia.org/wiki/M62_coach_bombing"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "British Armed Forces", "link": "https://wikipedia.org/wiki/British_Armed_Forces"}, {"title": "Yorkshire", "link": "https://wikipedia.org/wiki/Yorkshire"}]}, {"year": "1975", "text": "Haicheng earthquake (magnitude 7.3 on the Richter scale) occurs in Haicheng, Liaoning, China.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/1975_Haicheng_earthquake\" title=\"1975 Haicheng earthquake\">Haicheng earthquake</a> (magnitude 7.3 on the <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">Richter scale</a>) occurs in <a href=\"https://wikipedia.org/wiki/Haicheng,_Liaoning\" title=\"Haicheng, Liaoning\">Haicheng, Liaoning</a>, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1975_Haicheng_earthquake\" title=\"1975 Haicheng earthquake\">Haicheng earthquake</a> (magnitude 7.3 on the <a href=\"https://wikipedia.org/wiki/Richter_magnitude_scale\" class=\"mw-redirect\" title=\"Richter magnitude scale\">Richter scale</a>) occurs in <a href=\"https://wikipedia.org/wiki/Haicheng,_Liaoning\" title=\"Haicheng, Liaoning\">Haicheng, Liaoning</a>, China.", "links": [{"title": "1975 Haicheng earthquake", "link": "https://wikipedia.org/wiki/1975_Haicheng_earthquake"}, {"title": "Richter magnitude scale", "link": "https://wikipedia.org/wiki/Richter_magnitude_scale"}, {"title": "Haicheng, Liaoning", "link": "https://wikipedia.org/wiki/Haicheng,_Liaoning"}]}, {"year": "1976", "text": "In Guatemala and Honduras an earthquake kills more than 22,000.", "html": "1976 - In <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> and <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> an <a href=\"https://wikipedia.org/wiki/1976_Guatemala_earthquake\" title=\"1976 Guatemala earthquake\">earthquake</a> kills more than 22,000.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemala</a> and <a href=\"https://wikipedia.org/wiki/Honduras\" title=\"Honduras\">Honduras</a> an <a href=\"https://wikipedia.org/wiki/1976_Guatemala_earthquake\" title=\"1976 Guatemala earthquake\">earthquake</a> kills more than 22,000.", "links": [{"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "Honduras", "link": "https://wikipedia.org/wiki/Honduras"}, {"title": "1976 Guatemala earthquake", "link": "https://wikipedia.org/wiki/1976_Guatemala_earthquake"}]}, {"year": "1977", "text": "A Chicago Transit Authority elevated train rear-ends another and derails, killing 11 and injuring 180, the worst accident in the agency's history.", "html": "1977 - A <a href=\"https://wikipedia.org/wiki/Chicago_Transit_Authority\" title=\"Chicago Transit Authority\">Chicago Transit Authority</a> <a href=\"https://wikipedia.org/wiki/Elevated_train\" class=\"mw-redirect\" title=\"Elevated train\">elevated train</a> <a href=\"https://wikipedia.org/wiki/1977_Chicago_Loop_derailment\" title=\"1977 Chicago Loop derailment\">rear-ends another and derails</a>, killing 11 and injuring 180, the worst accident in the agency's history.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Chicago_Transit_Authority\" title=\"Chicago Transit Authority\">Chicago Transit Authority</a> <a href=\"https://wikipedia.org/wiki/Elevated_train\" class=\"mw-redirect\" title=\"Elevated train\">elevated train</a> <a href=\"https://wikipedia.org/wiki/1977_Chicago_Loop_derailment\" title=\"1977 Chicago Loop derailment\">rear-ends another and derails</a>, killing 11 and injuring 180, the worst accident in the agency's history.", "links": [{"title": "Chicago Transit Authority", "link": "https://wikipedia.org/wiki/Chicago_Transit_Authority"}, {"title": "Elevated train", "link": "https://wikipedia.org/wiki/Elevated_train"}, {"title": "1977 Chicago Loop derailment", "link": "https://wikipedia.org/wiki/1977_Chicago_Loop_derailment"}]}, {"year": "1992", "text": "A coup d'état is led by <PERSON> against Venezuelan President <PERSON>.", "html": "1992 - A <a href=\"https://wikipedia.org/wiki/1992_Venezuelan_coup_d%27%C3%A9tat_attempts\" class=\"mw-redirect\" title=\"1992 Venezuelan coup d'état attempts\">coup d'état</a> is led by <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> against <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1992_Venezuelan_coup_d%27%C3%A9tat_attempts\" class=\"mw-redirect\" title=\"1992 Venezuelan coup d'état attempts\">coup d'état</a> is led by <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> against <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> President <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "1992 Venezuelan coup d'état attempts", "link": "https://wikipedia.org/wiki/1992_Venezuelan_coup_d%27%C3%A9tat_attempts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Andr%C3%A9s_P%C3%A9rez"}]}, {"year": "1997", "text": "En route to Lebanon, two Israeli Sikorsky CH-53 troop-transport helicopters collide in mid-air over northern Galilee, Israel, killing 73.", "html": "1997 - En route to <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, two Israeli <a href=\"https://wikipedia.org/wiki/Sikorsky_CH-53_Sea_Stallion\" title=\"Sikorsky CH-53 Sea Stallion\">Sikorsky CH-53</a> troop-transport helicopters <a href=\"https://wikipedia.org/wiki/1997_Israeli_helicopter_disaster\" title=\"1997 Israeli helicopter disaster\">collide in mid-air</a> over northern Galilee, Israel, killing 73.", "no_year_html": "En route to <a href=\"https://wikipedia.org/wiki/Lebanon\" title=\"Lebanon\">Lebanon</a>, two Israeli <a href=\"https://wikipedia.org/wiki/Sikorsky_CH-53_Sea_Stallion\" title=\"Sikorsky CH-53 Sea Stallion\">Sikorsky CH-53</a> troop-transport helicopters <a href=\"https://wikipedia.org/wiki/1997_Israeli_helicopter_disaster\" title=\"1997 Israeli helicopter disaster\">collide in mid-air</a> over northern Galilee, Israel, killing 73.", "links": [{"title": "Lebanon", "link": "https://wikipedia.org/wiki/Lebanon"}, {"title": "Sikorsky CH-53 Sea Stallion", "link": "https://wikipedia.org/wiki/Sikorsky_CH-53_Sea_Stallion"}, {"title": "1997 Israeli helicopter disaster", "link": "https://wikipedia.org/wiki/1997_Israeli_helicopter_disaster"}]}, {"year": "1997", "text": "The Bojnurd earthquake measuring Mw  6.5 strikes Iran. With a Mercalli intensity of VIII, it kills at least 88 and damages 173 villages.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/1997_Bojnurd_earthquake\" title=\"1997 Bojnurd earthquake\">Bojnurd earthquake</a> measuring M<sub>w</sub>  6.5 strikes Iran. With a Mercalli intensity of VIII, it kills at least 88 and damages 173 villages.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1997_Bojnurd_earthquake\" title=\"1997 Bojnurd earthquake\">Bojnurd earthquake</a> measuring M<sub>w</sub>  6.5 strikes Iran. With a Mercalli intensity of VIII, it kills at least 88 and damages 173 villages.", "links": [{"title": "1997 Bojnurd earthquake", "link": "https://wikipedia.org/wiki/1997_Bojnurd_earthquake"}]}, {"year": "1998", "text": "The 5.9 Mw  Afghanistan earthquake shakes the Takhar Province with a maximum Mercalli intensity of VII (Very strong). With 2,323 killed, and 818 injured, damage is considered extreme.", "html": "1998 - The 5.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/February_1998_Afghanistan_earthquake\" title=\"February 1998 Afghanistan earthquake\">Afghanistan earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Takhar_Province\" title=\"Takhar Province\">Takhar Province</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VII (<i>Very strong</i>). With 2,323 killed, and 818 injured, damage is considered extreme.", "no_year_html": "The 5.9 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/February_1998_Afghanistan_earthquake\" title=\"February 1998 Afghanistan earthquake\">Afghanistan earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Takhar_Province\" title=\"Takhar Province\">Takhar Province</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of VII (<i>Very strong</i>). With 2,323 killed, and 818 injured, damage is considered extreme.", "links": [{"title": "February 1998 Afghanistan earthquake", "link": "https://wikipedia.org/wiki/February_1998_Afghanistan_earthquake"}, {"title": "Takhar Province", "link": "https://wikipedia.org/wiki/Takhar_Province"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1999", "text": "Unarmed West African immigrant <PERSON><PERSON><PERSON> is shot 41 times by four plainclothes New York City police officers on an unrelated stake-out, inflaming race relations in the city.", "html": "1999 - Unarmed West African immigrant <a href=\"https://wikipedia.org/wiki/Shooting_of_<PERSON>ado<PERSON>_<PERSON>allo\" class=\"mw-redirect\" title=\"Shooting of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> is shot 41 times</a> by four plainclothes New York City police officers on an unrelated stake-out, inflaming race relations in the city.", "no_year_html": "Unarmed West African immigrant <a href=\"https://wikipedia.org/wiki/Shooting_of_<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Shooting of <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> is shot 41 times</a> by four plainclothes New York City police officers on an unrelated stake-out, inflaming race relations in the city.", "links": [{"title": "Shooting of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shooting_of_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "The World Summit Against Cancer for the New Millennium, Charter of Paris is signed by the President of France, <PERSON> and the Director General of UNESCO, <PERSON><PERSON><PERSON>, initiating World Cancer Day which is held on February 4 every year.", "html": "2000 - The World Summit Against Cancer for the New Millennium, Charter of Paris is signed by the President of France, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the Director General of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, initiating <a href=\"https://wikipedia.org/wiki/World_Cancer_Day\" title=\"World Cancer Day\">World Cancer Day</a> which is held on February 4 every year.", "no_year_html": "The World Summit Against Cancer for the New Millennium, Charter of Paris is signed by the President of France, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the Director General of <a href=\"https://wikipedia.org/wiki/UNESCO\" title=\"UNESCO\">UNESCO</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, initiating <a href=\"https://wikipedia.org/wiki/World_Cancer_Day\" title=\"World Cancer Day\">World Cancer Day</a> which is held on February 4 every year.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "UNESCO", "link": "https://wikipedia.org/wiki/UNESCO"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "World Cancer Day", "link": "https://wikipedia.org/wiki/World_Cancer_Day"}]}, {"year": "2003", "text": "The Federal Republic of Yugoslavia adopts a new constitution, becoming a loose confederacy between Montenegro and Serbia.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a> adopts a new constitution, becoming a loose confederacy between Montenegro and Serbia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Federal Republic of Yugoslavia</a> adopts a new constitution, becoming a loose confederacy between Montenegro and Serbia.", "links": [{"title": "Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia"}]}, {"year": "2004", "text": "Facebook, a mainstream online social networking site, is founded by <PERSON> and <PERSON>.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>, a mainstream online <a href=\"https://wikipedia.org/wiki/Social_networking_site\" class=\"mw-redirect\" title=\"Social networking site\">social networking site</a>, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>, a mainstream online <a href=\"https://wikipedia.org/wiki/Social_networking_site\" class=\"mw-redirect\" title=\"Social networking site\">social networking site</a>, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Facebook", "link": "https://wikipedia.org/wiki/Facebook"}, {"title": "Social networking site", "link": "https://wikipedia.org/wiki/Social_networking_site"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "Civic mobilizations in Colombia against FARC, under the name A million voices against the FARC.", "html": "2008 - Civic mobilizations in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> against <a href=\"https://wikipedia.org/wiki/Revolutionary_Armed_Forces_of_Colombia\" title=\"Revolutionary Armed Forces of Colombia\">FARC</a>, under the name <a href=\"https://wikipedia.org/wiki/One_million_voices_against_FARC\" title=\"One million voices against FARC\"><i>A million voices against the FARC</i></a>.", "no_year_html": "Civic mobilizations in <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> against <a href=\"https://wikipedia.org/wiki/Revolutionary_Armed_Forces_of_Colombia\" title=\"Revolutionary Armed Forces of Colombia\">FARC</a>, under the name <a href=\"https://wikipedia.org/wiki/One_million_voices_against_FARC\" title=\"One million voices against FARC\"><i>A million voices against the FARC</i></a>.", "links": [{"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Revolutionary Armed Forces of Colombia", "link": "https://wikipedia.org/wiki/Revolutionary_Armed_Forces_of_Colombia"}, {"title": "One million voices against FARC", "link": "https://wikipedia.org/wiki/One_million_voices_against_FARC"}]}, {"year": "2015", "text": "TransAsia Airways Flight 235, with 58 people on board, en route from the Taiwanese capital Taipei to Kinmen, crashes into the Keelung River just after takeoff, killing 43 people.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/TransAsia_Airways_Flight_235\" title=\"TransAsia Airways Flight 235\">TransAsia Airways Flight 235</a>, with 58 people on board, en route from the Taiwanese capital <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a> to <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Keelung_River\" title=\"Keelung River\">Keelung River</a> just after takeoff, killing 43 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TransAsia_Airways_Flight_235\" title=\"TransAsia Airways Flight 235\">TransAsia Airways Flight 235</a>, with 58 people on board, en route from the Taiwanese capital <a href=\"https://wikipedia.org/wiki/Taipei\" title=\"Taipei\">Taipei</a> to <a href=\"https://wikipedia.org/wiki/Kinmen\" title=\"Kinmen\">Kinmen</a>, crashes into the <a href=\"https://wikipedia.org/wiki/Keelung_River\" title=\"Keelung River\">Keelung River</a> just after takeoff, killing 43 people.", "links": [{"title": "TransAsia Airways Flight 235", "link": "https://wikipedia.org/wiki/TransAsia_Airways_Flight_235"}, {"title": "Taipei", "link": "https://wikipedia.org/wiki/Taipei"}, {"title": "Kinmen", "link": "https://wikipedia.org/wiki/Kinmen"}, {"title": "Keelung River", "link": "https://wikipedia.org/wiki/Keelung_River"}]}, {"year": "2020", "text": "The COVID-19 pandemic causes all casinos in Macau to be closed down for 15 days.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> causes all casinos in <a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a> to be closed down for 15 days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a> causes all casinos in <a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a> to be closed down for 15 days.", "links": [{"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}, {"title": "Macau", "link": "https://wikipedia.org/wiki/Macau"}]}, {"year": "2025", "text": "Ten people are killed in a mass shooting at an adult education centre in Örebro, Sweden.", "html": "2025 - Ten people are killed in <a href=\"https://wikipedia.org/wiki/2025_Risbergska_school_shooting\" class=\"mw-redirect\" title=\"2025 Risbergska school shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Campus_Risbergska\" title=\"Campus Risbergska\">an adult education centre</a> in <a href=\"https://wikipedia.org/wiki/%C3%96rebro\" title=\"Örebro\">Örebro</a>, Sweden.", "no_year_html": "Ten people are killed in <a href=\"https://wikipedia.org/wiki/2025_Risbergska_school_shooting\" class=\"mw-redirect\" title=\"2025 Risbergska school shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Campus_Risbergska\" title=\"Campus Risbergska\">an adult education centre</a> in <a href=\"https://wikipedia.org/wiki/%C3%96rebro\" title=\"Örebro\">Örebro</a>, Sweden.", "links": [{"title": "2025 Risbergska school shooting", "link": "https://wikipedia.org/wiki/2025_Risbergska_school_shooting"}, {"title": "Campus Risbergska", "link": "https://wikipedia.org/wiki/Campus_Risbergska"}, {"title": "Örebro", "link": "https://wikipedia.org/wiki/%C3%96rebro"}]}], "Births": [{"year": "1447", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian poet (d. 1500)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (d. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian poet (d. 1500)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1495", "text": "<PERSON>, Duke of Milan (d. 1535)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_Sforza\" title=\"Francesco II Sforza\"><PERSON>rz<PERSON></a>, Duke of Milan (d. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_Sforza\" title=\"Francesco II Sforza\"><PERSON>rz<PERSON></a>, Duke of Milan (d. 1535)", "links": [{"title": "Francesco II Sforza", "link": "https://wikipedia.org/wiki/Francesco_II_S<PERSON>rza"}]}, {"year": "1495", "text": "<PERSON>, Grand Master of the Knights Hospitaller (d. 1568)", "html": "1495 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Hospitaller (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Grand Master of the Knights Hospitaller (d. 1568)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1505", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish poet and author (d. 1580)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/Miko%C5%82aj_Rej\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>%C5%82aj_Rej\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish poet and author (d. 1580)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miko%C5%82aj_Rej"}]}, {"year": "1575", "text": "<PERSON>, French cardinal and theologian, founded the French school of spirituality (d. 1629)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9ru<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and theologian, founded the <a href=\"https://wikipedia.org/wiki/French_school_of_spirituality\" title=\"French school of spirituality\">French school of spirituality</a> (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rulle\" title=\"<PERSON>\"><PERSON></a>, French cardinal and theologian, founded the <a href=\"https://wikipedia.org/wiki/French_school_of_spirituality\" title=\"French school of spirituality\">French school of spirituality</a> (d. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rulle"}, {"title": "French school of spirituality", "link": "https://wikipedia.org/wiki/French_school_of_spirituality"}]}, {"year": "1646", "text": "<PERSON>, German poet and politician (d. 1699)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/Hans_<PERSON>_A%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German poet and politician (d. 1699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%9Fmann\" title=\"<PERSON>\"><PERSON></a>, German poet and politician (d. 1699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hans_Erasmus_A%C3%9Fmann"}]}, {"year": "1677", "text": "<PERSON>, German violinist and composer (d. 1731)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German violinist and composer (d. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, French author and playwright (d. 1763)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (d. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1725", "text": "<PERSON><PERSON>, English entomologist and author (d. 1804)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/Dru_<PERSON><PERSON>\" title=\"Dru <PERSON><PERSON>\"><PERSON><PERSON></a>, English entomologist and author (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dru_<PERSON><PERSON>\" title=\"Dru <PERSON><PERSON>\"><PERSON><PERSON></a>, English entomologist and author (d. 1804)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dru_<PERSON><PERSON>"}]}, {"year": "1740", "text": "<PERSON>, Swedish poet and composer (d. 1795)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and composer (d. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish poet and composer (d. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1778", "text": "<PERSON><PERSON>, Swiss botanist, mycologist, and academic (d. 1841)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/Augustin_<PERSON><PERSON><PERSON>_<PERSON>_Candolle\" title=\"Augustin P<PERSON><PERSON> Candolle\">Augustin <PERSON></a>, Swiss botanist, mycologist, and academic (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augustin_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>lle\" title=\"Augustin <PERSON>lle\">Augustin <PERSON><PERSON></a>, Swiss botanist, mycologist, and academic (d. 1841)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Augustin_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, Portuguese journalist and author (d. 1854)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Almei<PERSON>_Garrett\" title=\"Almei<PERSON> Garrett\"><PERSON><PERSON><PERSON></a>, Portuguese journalist and author (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Almei<PERSON>_Garrett\" title=\"Almei<PERSON> Garrett\"><PERSON><PERSON><PERSON></a>, Portuguese journalist and author (d. 1854)", "links": [{"title": "Almeida Garrett", "link": "https://wikipedia.org/wiki/Almeida_Garrett"}]}, {"year": "1818", "text": "<PERSON>, San Francisco eccentric and visionary (d. 1880)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Norton\">Emperor <PERSON></a>, San Francisco eccentric and visionary (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Norton\">Emperor <PERSON></a>, San Francisco eccentric and visionary (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, American financier and politician, 35th Governor of Massachusetts (d. 1895)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American financier and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American financier and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1895)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1848", "text": "<PERSON>, French poet, author, and playwright (d. 1921)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, author, and playwright (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, author, and playwright (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, French poet, author, and playwright (d. 1926)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, author, and playwright (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet, author, and playwright (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON>, French novelist (d. 1942)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_Estauni%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French novelist (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89do<PERSON>_<PERSON>uni%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French novelist (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_E<PERSON>uni%C3%A9"}]}, {"year": "1865", "text": "<PERSON>, Japanese minister and politician (d. 1949)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese minister and politician (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese minister and politician (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Irish revolutionary and first woman elected to the UK House of Commons (d. 1927)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish revolutionary and first woman elected to the UK <a href=\"https://wikipedia.org/wiki/House_of_Commons\" title=\"House of Commons\">House of Commons</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish revolutionary and first woman elected to the UK <a href=\"https://wikipedia.org/wiki/House_of_Commons\" title=\"House of Commons\">House of Commons</a> (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "House of Commons", "link": "https://wikipedia.org/wiki/House_of_Commons"}]}, {"year": "1869", "text": "<PERSON>, American labor organizer (d. 1928)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor organizer (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, German lawyer and politician, first President of Germany (d. 1925)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)\" title=\"President of Germany (1919-1945)\">President of Germany</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)\" title=\"President of Germany (1919-1945)\">President of Germany</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Germany (1919-1945)", "link": "https://wikipedia.org/wiki/President_of_Germany_(1919%E2%80%931945)"}]}, {"year": "1872", "text": "<PERSON><PERSON>, Bulgarian and Macedonian revolutionary activist (d. 1903)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian and Macedonian revolutionary activist (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian and Macedonian revolutionary activist (d. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Canadian shot putter and discus thrower (d. 1905)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian shot putter and discus thrower (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian shot putter and discus thrower (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, German physicist and engineer (d. 1953)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American football player and coach (d. 1953)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Mexican general and politician, President of Mexico (d. 1939)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>lio_G<PERSON>%C3%A9rrez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9rrez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican general and politician, <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eulalio_Guti%C3%A9rrez"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, French painter and sculptor (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Fernand_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fernand_L%C3%A9ger\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and sculptor (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fernand_L%C3%A9ger"}]}, {"year": "1881", "text": " <PERSON><PERSON><PERSON>, Soviet politician and Marshal of the Soviet Union, People's Commissar for Defence (d. 1969)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician and Marshal of the Soviet Union, People's Commissar for Defence (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet politician and Marshal of the Soviet Union, People's Commissar for Defence (d. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, German-American inventor and a pioneer of electron microscopy (d. 1961)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American inventor and a pioneer of <a href=\"https://wikipedia.org/wiki/Electron_microscopy\" class=\"mw-redirect\" title=\"Electron microscopy\">electron microscopy</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American inventor and a pioneer of <a href=\"https://wikipedia.org/wiki/Electron_microscopy\" class=\"mw-redirect\" title=\"Electron microscopy\">electron microscopy</a> (d. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Electron microscopy", "link": "https://wikipedia.org/wiki/Electron_microscopy"}]}, {"year": "1891", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, second Speaker of the Lok Sabha (d. 1978)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, second <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, second <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 1978)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M<PERSON>_<PERSON><PERSON>_<PERSON>yyangar"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1892", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian poet and academic (d. 1964)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and academic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian poet and academic (d. 1964)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Australian paleoanthropologist (d. 1988)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian paleoanthropologist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian paleoanthropologist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English actor (d. 1953)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Austrian-Swiss author (d. 1938)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss author (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Swiss author (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, German physicist and academic (d. 1997)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German soldier and politician, second Chancellor of West Germany (d. 1977)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, second <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of West Germany</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, second <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic of Germany)\">Chancellor of West Germany</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic of Germany)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic_of_Germany)"}]}, {"year": "1899", "text": "Virginia <PERSON><PERSON>, American physician and founder of the Aspiranto Health Home (d. 1949)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Virginia_<PERSON><PERSON>_<PERSON>\" title=\"Virginia M<PERSON>\"><PERSON> <PERSON><PERSON></a>, American physician and founder of the Aspiranto Health Home (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_M<PERSON>_<PERSON>\" title=\"Virginia M<PERSON>\"><PERSON> <PERSON><PERSON></a>, American physician and founder of the Aspiranto Health Home (d. 1949)", "links": [{"title": "Virginia M<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_M<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, French poet and screenwriter (d. 1977)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vert\" title=\"<PERSON>\"><PERSON></a>, French poet and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vert\" title=\"<PERSON>\"><PERSON></a>, French poet and screenwriter (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_Pr%C3%A9vert"}]}, {"year": "1902", "text": "<PERSON>, American pilot and explorer (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and explorer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and explorer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, <PERSON>, German-English lawyer and politician, Attorney General for England and Wales (d. 2003)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, German-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, German-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (d. 2003)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1903", "text": "<PERSON>, Polish-American chemist, parapsychologist, and academic (d. 2014)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American chemist, parapsychologist, and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American chemist, parapsychologist, and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and screenwriter (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ntor\" title=\"<PERSON><PERSON><PERSON><PERSON> Kantor\"><PERSON><PERSON><PERSON><PERSON></a>, American author and screenwriter (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ntor\" title=\"<PERSON><PERSON><PERSON><PERSON> Kantor\"><PERSON><PERSON><PERSON><PERSON></a>, American author and screenwriter (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>r"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Chinese politician, Chairwoman of the Chinese People's Political Consultative Conference (d. 1968)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Deng_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, Chairwoman of the Chinese People's Political Consultative Conference (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician, Chairwoman of the Chinese People's Political Consultative Conference (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deng_<PERSON>o"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, English comedian, actress and music hall performer (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actress and music hall performer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English comedian, actress and music hall performer (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German pastor and theologian (d. 1945)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pastor and theologian (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish librarian (d. 1994)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish librarian (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>-<PERSON>\" title=\"Let<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish librarian (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Letiti<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American astronomer and academic, discovered Pluto (d. 1997)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic, discovered <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic, discovered <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}]}, {"year": "1908", "text": "<PERSON>, English poet and academic (d. 1937)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Norwegian banker and politician, Norwegian Minister of Industry (d. 1999)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Ola_Skj%C3%A5k_Br%C3%A6k\" title=\"<PERSON>la Skjåk Bræk\"><PERSON><PERSON></a>, Norwegian banker and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Trade_and_Industry_(Norway)\" title=\"Ministry of Trade and Industry (Norway)\">Norwegian Minister of Industry</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ola_Skj%C3%A5k_Br%C3%A6k\" title=\"<PERSON><PERSON> Skjåk Bræk\"><PERSON><PERSON>r<PERSON></a>, Norwegian banker and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Trade_and_Industry_(Norway)\" title=\"Ministry of Trade and Industry (Norway)\">Norwegian Minister of Industry</a> (d. 1999)", "links": [{"title": "<PERSON><PERSON>j<PERSON>k Bræk", "link": "https://wikipedia.org/wiki/Ola_Skj%C3%A5k_Br%C3%A6k"}, {"title": "Ministry of Trade and Industry (Norway)", "link": "https://wikipedia.org/wiki/Ministry_of_Trade_and_Industry_(Norway)"}]}, {"year": "1912", "text": "<PERSON>, Austrian-American conductor (d. 1993)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American golfer and sportscaster (d. 2006)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American civil rights activist (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Rosa_Parks\" title=\"Rosa Parks\"><PERSON></a>, American civil rights activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosa_Parks\" title=\"Rosa Parks\"><PERSON></a>, American civil rights activist (d. 2005)", "links": [{"title": "Rosa <PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Parks"}]}, {"year": "1914", "text": "<PERSON>, German-Swiss author and publisher (d. 1980)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author and publisher (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss author and publisher (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor and screenwriter (d. 1968)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 1968)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1915", "text": "<PERSON>, English comedian, actor and singer-songwriter (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wisdom\" title=\"Norman Wisdom\"><PERSON></a>, English comedian, actor and singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wisdom\" title=\"Norman Wisdom\"><PERSON></a>, English comedian, actor and singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Pakistan general and politician, third President of Pakistan (d. 1980)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistan general and politician, third <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistan general and politician, third <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1918", "text": "<PERSON>, English-American actress and director (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and director (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and director (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ida_Lupino"}]}, {"year": "1918", "text": "<PERSON>, Italian philosopher and author (d. 1991)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actress and voice artist (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and voice artist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American author and feminist (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and feminist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and feminist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Iranian-American mathematician and computer scientist and founder of fuzzy logic (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American mathematician and computer scientist and founder of <a href=\"https://wikipedia.org/wiki/Fuzzy_logic\" title=\"Fuzzy logic\">fuzzy logic</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian-American mathematician and computer scientist and founder of <a href=\"https://wikipedia.org/wiki/Fuzzy_logic\" title=\"Fuzzy logic\">fuzzy logic</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Fuzzy logic", "link": "https://wikipedia.org/wiki/Fuzzy_logic"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Indian vocalist of the Hindustani classical music tradition (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Josh<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian vocalist of the Hindustani classical music tradition (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Josh<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian vocalist of the Hindustani classical music tradition (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1923", "text": "<PERSON>, Canadian-American actor (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American author and illustrator (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American journalist and historian (d. 2013)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and historian (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English mathematician and academic (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>la_G<PERSON>\" title=\"G<PERSON>la Grosics\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G<PERSON>\" title=\"<PERSON><PERSON>la Grosics\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>la_G<PERSON>ics"}]}, {"year": "1927", "text": "<PERSON>, German-American physicist and academic (d. 1999)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Argentinian racing driver (d. 1967)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>abal%C3%A9n"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Finnish journalist, academic, and politician (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Osmo Antero Wiio\"><PERSON><PERSON></a>, Finnish journalist, academic, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Osmo Antero Wiio\"><PERSON><PERSON></a>, Finnish journalist, academic, and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1929", "text": "<PERSON>, American actor, director, and producer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American musician (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American basketball player (d. 1978)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Hungarian volleyball player and diplomat, Hungarian Ambassador to the United Kingdom (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Tibor_<PERSON>talp%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian volleyball player and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_Hungary_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"List of Ambassadors of Hungary to the United Kingdom\">Hungarian Ambassador to the United Kingdom</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tib<PERSON>_<PERSON>talp%C3%A9ter\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian volleyball player and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_Hungary_to_the_United_Kingdom\" class=\"mw-redirect\" title=\"List of Ambassadors of Hungary to the United Kingdom\">Hungarian Ambassador to the United Kingdom</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tibor_Antalp%C3%A9ter"}, {"title": "List of Ambassadors of Hungary to the United Kingdom", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_Hungary_to_the_United_Kingdom"}]}, {"year": "1930", "text": "<PERSON>, American businessman and politician (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American basketball player and coach (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Argentinian dancer and politician, 41st President of Argentina", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian dancer and politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian dancer and politician, 41st <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isabel_Per%C3%B3n"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1932", "text": "<PERSON>, American novelist (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Pakistani cricketer (d. 1994)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Finnish opera singer (d. 1989)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish opera singer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish opera singer (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actress (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress (d. 2009)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_(actress)"}]}, {"year": "1936", "text": "<PERSON>, American comedian, actor, and author (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Swiss businessman, founded the Montreux Jazz Festival (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded the <a href=\"https://wikipedia.org/wiki/Montreux_Jazz_Festival\" title=\"Montreux Jazz Festival\">Montreux Jazz Festival</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss businessman, founded the <a href=\"https://wikipedia.org/wiki/Montreux_Jazz_Festival\" title=\"Montreux Jazz Festival\">Montreux Jazz Festival</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montreux Jazz Festival", "link": "https://wikipedia.org/wiki/Montreux_Jazz_Festival"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Indian dancer, composer, singer and exponent of the Lucknow \"Kalka-Bindadin\" Gharana of Kathak dance (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>haraj\" title=\"<PERSON><PERSON><PERSON> Maharaj\"><PERSON><PERSON><PERSON></a>, Indian dancer, composer, singer and exponent of the <a href=\"https://wikipedia.org/wiki/Lucknow_gharana\" title=\"Lucknow gharana\">Lucknow \"Kalka-Bindadin\" Gharana</a> of <a href=\"https://wikipedia.org/wiki/Kathak\" title=\"Kathak\"><PERSON><PERSON></a> dance (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j\" title=\"<PERSON><PERSON><PERSON>j\"><PERSON><PERSON><PERSON></a>, Indian dancer, composer, singer and exponent of the <a href=\"https://wikipedia.org/wiki/Lucknow_gharana\" title=\"Lucknow gharana\">Lucknow \"Kalka-Bindadin\" Gharana</a> of <a href=\"https://wikipedia.org/wiki/Kathak\" title=\"Kathak\"><PERSON><PERSON></a> dance (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>j"}, {"title": "Lucknow gharana", "link": "https://wikipedia.org/wiki/Lucknow_gharana"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hak"}]}, {"year": "1937", "text": "<PERSON>, American director and screenwriter (d. 2003)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American director and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(screenwriter)\" title=\"<PERSON> (screenwriter)\"><PERSON></a>, American director and screenwriter (d. 2003)", "links": [{"title": "<PERSON> (screenwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(screenwriter)"}]}, {"year": "1938", "text": "<PERSON>, American businessman and politician, president of the New Jersey Senate (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, president of the <a href=\"https://wikipedia.org/wiki/New_Jersey_Senate\" title=\"New Jersey Senate\">New Jersey Senate</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, president of the <a href=\"https://wikipedia.org/wiki/New_Jersey_Senate\" title=\"New Jersey Senate\">New Jersey Senate</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New Jersey Senate", "link": "https://wikipedia.org/wiki/New_Jersey_Senate"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician, Lieutenant Governor of New York", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York\" title=\"Lieutenant Governor of New York\">Lieutenant Governor of New York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York\" title=\"Lieutenant Governor of New York\">Lieutenant Governor of New York</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of New York", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_New_York"}]}, {"year": "1940", "text": "<PERSON>, American director and producer (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian politician, 33rd Premier of Queensland", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 33rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1941", "text": "<PERSON>, New Zealand rugby player (d. 1988)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Czech skier and coach (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ra%C5%A1ka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech skier and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ra%C5%A1ka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech skier and coach (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ra%C5%A1ka"}]}, {"year": "1941", "text": "<PERSON>, English musician and songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, English musician and songwriter", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1943", "text": "<PERSON>, Portuguese journalist and politician, second President of the Regional Government of Madeira", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3o_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese journalist and politician, second <a href=\"https://wikipedia.org/wiki/Presidents_of_the_Regional_Government_of_Madeira\" class=\"mw-redirect\" title=\"Presidents of the Regional Government of Madeira\">President of the Regional Government of Madeira</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A3<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese journalist and politician, second <a href=\"https://wikipedia.org/wiki/Presidents_of_the_Regional_Government_of_Madeira\" class=\"mw-redirect\" title=\"Presidents of the Regional Government of Madeira\">President of the Regional Government of Madeira</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_Jo%C3%A3o_<PERSON><PERSON>im"}, {"title": "Presidents of the Regional Government of Madeira", "link": "https://wikipedia.org/wiki/Presidents_of_the_Regional_Government_of_Madeira"}]}, {"year": "1943", "text": "<PERSON>, Lithuanian-Polish mountaineer (d. 1992)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Polish mountaineer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-Polish mountaineer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American computer scientist and programmer, co-developed the B programming language", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-developed the <a href=\"https://wikipedia.org/wiki/B_(programming_language)\" title=\"B (programming language)\">B programming language</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and programmer, co-developed the <a href=\"https://wikipedia.org/wiki/B_(programming_language)\" title=\"B (programming language)\">B programming language</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "B (programming language)", "link": "https://wikipedia.org/wiki/B_(programming_language)"}]}, {"year": "1944", "text": "<PERSON>, American singer and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Florence_LaRue\" title=\"Florence LaRue\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_LaRue\" title=\"Florence LaRue\"><PERSON></a>, American singer and actress", "links": [{"title": "Florence LaRue", "link": "https://wikipedia.org/wiki/Florence_LaRue"}]}, {"year": "1944", "text": "<PERSON>, American artist and ship captain (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and ship captain (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and ship captain (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American admiral and politician, third Director of National Intelligence", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, third <a href=\"https://wikipedia.org/wiki/Director_of_National_Intelligence\" title=\"Director of National Intelligence\">Director of National Intelligence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician, third <a href=\"https://wikipedia.org/wiki/Director_of_National_Intelligence\" title=\"Director of National Intelligence\">Director of National Intelligence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Director of National Intelligence", "link": "https://wikipedia.org/wiki/Director_of_National_Intelligence"}]}, {"year": "1947", "text": "<PERSON>, American sergeant, lawyer, and politician, 44th Vice President of the United States", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, lawyer, and politician, 44th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yoshi\" title=\"<PERSON><PERSON><PERSON>yoshi\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>yoshi\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Bosnian general (d. 2010)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Rasim_Deli%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian general (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rasim_Deli%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian general (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rasim_Deli%C4%87"}]}, {"year": "1951", "text": "<PERSON>, Irish actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress, writer, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, writer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, New Zealand politician, Prime Minister of New Zealand", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1952", "text": "<PERSON>, American criminal and prisoner (d. 2019)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal and prisoner (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal and prisoner (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Slovak politician, Prime Minister of Slovakia", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Mikul%C3%A1%C5%A1_Dzurinda\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovakia\" title=\"Prime Minister of Slovakia\">Prime Minister of Slovakia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mikul%C3%A1%C5%A1_Dzurinda\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Slovak politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Slovakia\" title=\"Prime Minister of Slovakia\">Prime Minister of Slovakia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mikul%C3%A1%C5%A1_Dzurinda"}, {"title": "Prime Minister of Slovakia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Slovakia"}]}, {"year": "1957", "text": "<PERSON>, British zoologist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British zoologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British zoologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Polish journalist and author (d. 2005)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pacy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Pacy%C5%84ski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish journalist and author (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tomasz_Pacy%C5%84ski"}]}, {"year": "1959", "text": "<PERSON>, German footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American football player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, English author and activist (d. 2007)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author and activist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "1960", "text": "<PERSON>, American lyricist, composer, and playwright (d. 1996)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist, composer, and playwright (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lyricist, composer, and playwright (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American basketball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fleming\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "Noodles, American musician and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Noodles_(musician)\" title=\"Noodles (musician)\"><PERSON>odles</a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noodles_(musician)\" title=\"Noodles (musician)\">Noodles</a>, American musician and songwriter", "links": [{"title": "Noodles (musician)", "link": "https://wikipedia.org/wiki/Noodles_(musician)"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Swiss skier", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German Paralympic equestrian", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Paralympic equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German Paralympic equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player (d. 1992)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Russian cyclist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Russian figure skater (d. 1995)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English-American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American attorney and lobbyist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hunter Biden\"><PERSON></a>, American attorney and lobbyist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hunter Biden\"><PERSON></a>, American attorney and lobbyist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>y\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rob_<PERSON>rddry"}]}, {"year": "1971", "text": "<PERSON>, American actor, director, and writer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Brazilian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)\" title=\"<PERSON> (footballer, born 1972)\"><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1972)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1972)"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Irish comedian and television host", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Dara_%C3%93_Bria<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish comedian and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dara_%C3%93_Bria<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish comedian and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dara_%C3%93_<PERSON><PERSON>in"}]}, {"year": "1973", "text": "<PERSON>, American boxer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian singer-songwriter and actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Natalie_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper and actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Cam%27ron\" title=\"<PERSON>'ron\"><PERSON>'ron</a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam%27ron\" title=\"<PERSON>'ron\"><PERSON><PERSON>ron</a>, American rapper and actor", "links": [{"title": "<PERSON>'ron", "link": "https://wikipedia.org/wiki/Cam%27ron"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Italian racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Vaiku<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Vaiku<PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Belgian cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>rmanis"}]}, {"year": "1983", "text": "<PERSON>, American comedian and actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>s\" title=\"<PERSON> B<PERSON>s\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>s\" title=\"<PERSON> Buress\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hannibal_Buress"}]}, {"year": "1983", "text": "<PERSON>, Australian politician", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Chilean footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, German racing driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Maximilian_G%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maximilian_G%C3%B6tz\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maximilian_G%C3%B6tz"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Bangladeshi cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Riyad\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Riyad\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>iya<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Riyad\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dea\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Darren_O%27Dea"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Lucie_%C5%A0af%C3%A1%C5%99ov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucie_%C5%A0af%C3%A1%C5%99ov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucie_%C5%A0af%C3%A1%C5%99ov%C3%A1"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1988", "text": "<PERSON>, American gymnast and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Egyptian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Austrian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Maximilian_W%C3%B6ber\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maximilian_W%C3%B6ber\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maximilian_W%C3%B6ber"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Danish footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%B8<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rasmus_H%C3%B8jlund"}]}], "Deaths": [{"year": "211", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 145)", "html": "211 - <a href=\"https://wikipedia.org/wiki/Septimius_Se<PERSON>us\" title=\"Septimius Severus\">Sept<PERSON><PERSON></a>, Roman emperor (b. 145)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Septimius_Se<PERSON>us\" title=\"Septimius Severus\">Sept<PERSON>ius <PERSON></a>, Roman emperor (b. 145)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septimius_Severus"}]}, {"year": "708", "text": "<PERSON> (b. 650)", "html": "708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>\">Pope <PERSON><PERSON><PERSON></a> (b. 650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>\">Pope <PERSON><PERSON><PERSON></a> (b. 650)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "856", "text": "<PERSON><PERSON><PERSON>, Frankish archbishop and theologian (b. 780)", "html": "856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Maurus\" title=\"<PERSON><PERSON><PERSON> Maurus\"><PERSON><PERSON><PERSON></a>, Frankish archbishop and theologian (b. 780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ma<PERSON>\" title=\"<PERSON><PERSON><PERSON> Maurus\"><PERSON><PERSON><PERSON></a>, Frankish archbishop and theologian (b. 780)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "870", "text": "<PERSON><PERSON><PERSON><PERSON>, archbishop of Canterbury", "html": "870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>not<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>not<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Canterbury\" title=\"Diocese of Canterbury\">Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>noth"}, {"title": "Diocese of Canterbury", "link": "https://wikipedia.org/wiki/Diocese_of_Canterbury"}]}, {"year": "1169", "text": "<PERSON> of Ajello, Bishop of Catania", "html": "1169 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ajello\" title=\"<PERSON> of Ajello\"><PERSON> of Ajello</a>, Bishop of Catania", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_A<PERSON><PERSON>\" title=\"<PERSON> of Ajello\"><PERSON> of Ajello</a>, Bishop of Catania", "links": [{"title": "<PERSON> of Ajello", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1498", "text": "<PERSON>, Italian artist (b. 1429/1433)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist (b. 1429/1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian artist (b. 1429/1433)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1505", "text": "<PERSON>, daughter of <PERSON> of <PERSON> (b. 1464)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Berry\" title=\"<PERSON> of <PERSON>, Duchess of Berry\"><PERSON></a>, daughter of <PERSON> of France (b. 1464)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Berry\" title=\"<PERSON> of France, Duchess of Berry\"><PERSON></a>, daughter of <PERSON> of <PERSON> (b. 1464)", "links": [{"title": "<PERSON> of France, Duchess of Berry", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_<PERSON>_Berry"}]}, {"year": "1508", "text": "<PERSON>, German poet and scholar (b. 1459)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and scholar (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and scholar (b. 1459)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, English clergyman and translator (b. 1505)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_editor_and_martyr)\" title=\"<PERSON> (Bible editor and martyr)\"><PERSON></a>, English clergyman and translator (b. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_editor_and_martyr)\" title=\"<PERSON> (Bible editor and martyr)\"><PERSON></a>, English clergyman and translator (b. 1505)", "links": [{"title": "<PERSON> (Bible editor and martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Bible_editor_and_martyr)"}]}, {"year": "1590", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and theorist (b. 1517)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer and theorist (b. 1517)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>ef<PERSON>_<PERSON>"}]}, {"year": "1615", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian playwright and scholar (b. 1535)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> della Porta\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and scholar (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>a\"><PERSON><PERSON><PERSON><PERSON></a>, Italian playwright and scholar (b. 1535)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON><PERSON> Elzevir, Dutch publisher, co-founded the House of Elzevir (b. 1546)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Lodewijk_Elzevir\" title=\"Lodewijk Elzevir\">Lodewijk Elzevir</a>, Dutch publisher, co-founded the <a href=\"https://wikipedia.org/wiki/House_of_Elzevir\" title=\"House of Elzevir\">House of Elzevir</a> (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lodewijk_Elzevir\" title=\"Lodewijk Elzevir\">Lodewijk Elzevir</a>, Dutch publisher, co-founded the <a href=\"https://wikipedia.org/wiki/House_of_Elzevir\" title=\"House of Elzevir\">House of Elzevir</a> (b. 1546)", "links": [{"title": "Lodewijk Elzevir", "link": "https://wikipedia.org/wiki/Lodewijk_Elzevir"}, {"title": "House of Elzevir", "link": "https://wikipedia.org/wiki/House_of_Elzevir"}]}, {"year": "1713", "text": "<PERSON>, 3rd Earl of Shaftesbury, English philosopher and politician (b. 1671)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Shaftesbury\" title=\"<PERSON>, 3rd Earl of Shaftesbury\"><PERSON>, 3rd Earl of Shaftesbury</a>, English philosopher and politician (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Shaftesbury\" title=\"<PERSON>, 3rd Earl of Shaftesbury\"><PERSON>, 3rd Earl of Shaftesbury</a>, English philosopher and politician (b. 1671)", "links": [{"title": "<PERSON>, 3rd Earl of Shaftesbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Shaftesbury"}]}, {"year": "1774", "text": "<PERSON>, French mathematician and geographer (b. 1701)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and geographer (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and geographer (b. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, Czech composer (b. 1737)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech composer (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech composer (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_Myslive%C4%8Dek"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, French architect and educator (b. 1728)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect and educator (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect and educator (b. 1728)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>-<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1843", "text": "<PERSON><PERSON>, Greek general (b. 1770)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1770)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Roman Catholic archbishop and Mexican politician who served as regent during the Second Mexican Empire (b. 1816)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Pela<PERSON>_Antonio_de_Labastida_y_D%C3%A1valos\" title=\"Pelagio Antonio de Labastida y Dávalos\">Pela<PERSON> de Labastida y Dávalos</a>, Roman Catholic <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> and Mexican politician who served as <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> during the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pela<PERSON>_<PERSON>_<PERSON>_Labastida_y_D%C3%A1valos\" title=\"Pelagio Antonio de Labastida y Dávalos\">Pela<PERSON> de Labastida y Dávalos</a>, Roman Catholic <a href=\"https://wikipedia.org/wiki/Archbishop\" title=\"Archbishop\">archbishop</a> and Mexican politician who served as <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> during the <a href=\"https://wikipedia.org/wiki/Second_Mexican_Empire\" title=\"Second Mexican Empire\">Second Mexican Empire</a> (b. 1816)", "links": [{"title": "Pelagio Antonio de Labastida y Dávalos", "link": "https://wikipedia.org/wiki/Pelagio_Antonio_de_Labastida_y_D%C3%A1valos"}, {"title": "Archbishop", "link": "https://wikipedia.org/wiki/Archbishop"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "Second Mexican Empire", "link": "https://wikipedia.org/wiki/Second_Mexican_Empire"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, French sculptor and academic (b. 1841)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and academic (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor and academic (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, French tailor and inventor (b. 1878)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tailor and inventor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tailor and inventor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish author and scholar (b. 1875)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/%C4%B0skilipli_%C3%82t%C4%B1f_Hodja\" class=\"mw-redirect\" title=\"İskilipli Âtıf <PERSON>\">İski<PERSON><PERSON></a>, Turkish author and scholar (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0skilipli_%C3%82t%C4%B1f_Hodja\" class=\"mw-redirect\" title=\"İskilipli Âtıf <PERSON>\">İskilip<PERSON></a>, Turkish author and scholar (b. 1875)", "links": [{"title": "İskilipli Âtı<PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0skilipli_%C3%82t%C4%B1f_Hodja"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Dutch physicist and academic, Nobel Prize laureate (b. 1853)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1933", "text": "<PERSON>, English linguist and educator (b. 1846)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and educator (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and educator (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, German-Swiss soldier, founded Swiss NSDAP/AO (b. 1895)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss soldier, founded <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/NSDAP/AO\" class=\"mw-redirect\" title=\"NSDAP/AO\">NSDAP/AO</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss soldier, founded <a href=\"https://wikipedia.org/wiki/Switzerland\" title=\"Switzerland\">Swiss</a> <a href=\"https://wikipedia.org/wiki/NSDAP/AO\" class=\"mw-redirect\" title=\"NSDAP/AO\">NSDAP/AO</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Switzerland", "link": "https://wikipedia.org/wiki/Switzerland"}, {"title": "NSDAP/AO", "link": "https://wikipedia.org/wiki/NSDAP/AO"}]}, {"year": "1940", "text": "<PERSON>, Russian police officer and politician (b. 1895)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian police officer and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian police officer and politician (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English-Canadian ice hockey player and journalist (b. 1877)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ice hockey player and journalist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian ice hockey player and journalist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Russian author and translator (b. 1872)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and translator (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and translator (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Russian-French chess player, journalist, and author (b. 1887)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Savielly_Tartakower\" title=\"Savielly Tartakower\"><PERSON><PERSON><PERSON></a>, Russian-French chess player, journalist, and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Savielly_Tartakower\" title=\"Savielly Tartakower\"><PERSON><PERSON><PERSON></a>, Russian-French chess player, journalist, and author (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vie<PERSON>_<PERSON>wer"}]}, {"year": "1958", "text": "<PERSON>, American author and screenwriter (b. 1915)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Irish-American actress (b. 1880)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Irish-American actress (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Irish-American actress (b. 1880)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(actress)"}]}, {"year": "1968", "text": "<PERSON>, American novelist and poet (b. 1926)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and poet (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American poet and critic (b. 1897)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Indian physicist, mathematician, and academic (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicist, mathematician, and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicist, mathematician, and academic (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and saxophonist (b. 1908)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Jordan\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Jordan\"><PERSON></a>, American singer-songwriter and saxophonist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Jordan"}]}, {"year": "1982", "text": "<PERSON>, Scottish singer-songwriter and guitarist (b. 1935)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish singer-songwriter and guitarist (b. 1935)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1982", "text": "<PERSON>, German lawyer and judge (b. 1909)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and judge (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer (b. 1950)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist, (b. 1919)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Liberace\" title=\"Liberace\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist, (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberace\" title=\"Liberace\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist, (b. 1919)", "links": [{"title": "Liberace", "link": "https://wikipedia.org/wiki/Liberace"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Afghan activist, founded the Revolutionary Association of the Women of Afghanistan (b. 1956)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan activist, founded the <a href=\"https://wikipedia.org/wiki/Revolutionary_Association_of_the_Women_of_Afghanistan\" title=\"Revolutionary Association of the Women of Afghanistan\">Revolutionary Association of the Women of Afghanistan</a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan activist, founded the <a href=\"https://wikipedia.org/wiki/Revolutionary_Association_of_the_Women_of_Afghanistan\" title=\"Revolutionary Association of the Women of Afghanistan\">Revolutionary Association of the Women of Afghanistan</a> (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Revolutionary Association of the Women of Afghanistan", "link": "https://wikipedia.org/wiki/Revolutionary_Association_of_the_Women_of_Afghanistan"}]}, {"year": "1987", "text": "<PERSON>, American psychologist and academic (b. 1902)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Canadian-American wrestler and trainer (b. 1915)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>\" title=\"Whipper <PERSON>\">Whipper <PERSON></a>, Canadian-American wrestler and trainer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>\" title=\"Whipper <PERSON>\">Whipper <PERSON></a>, Canadian-American wrestler and trainer (b. 1915)", "links": [{"title": "Whip<PERSON>", "link": "https://wikipedia.org/wiki/Whipper_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American novelist and short story writer (b. 1921)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>smith"}]}, {"year": "2000", "text": "<PERSON>, American lawyer and politician, 54th Speaker of the United States House of Representatives (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "2002", "text": "Count <PERSON><PERSON><PERSON> of Wisborg (b. 1907)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>_<PERSON>_of_Wisborg\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON> of Wisborg\">Count <PERSON><PERSON><PERSON> of Wisborg</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>_<PERSON>_of_Wisborg\" class=\"mw-redirect\" title=\"Count <PERSON><PERSON><PERSON> of Wisborg\">Count <PERSON><PERSON><PERSON> of Wisborg</a> (b. 1907)", "links": [{"title": "Count <PERSON><PERSON><PERSON> of Wisborg", "link": "https://wikipedia.org/wiki/Count_<PERSON><PERSON><PERSON>_<PERSON>_of_Wisborg"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian pharmacist and politician (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>youce<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian pharmacist and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>youce<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian pharmacist and politician (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yo<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Brazilian poet, novelist, and playwright (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, novelist, and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, novelist, and playwright (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American actor, director, and playwright (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and playwright (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and playwright (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American author and activist (b. 1921)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Brazilian footballer and manager (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Russian-English poet and translator (b. 1959)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English poet and translator (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English poet and translator (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "2007", "text": "<PERSON>, American singer and actress (b. 1934)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Ukrainian-American painter and sculptor (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and sculptor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American painter and sculptor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Austrian journalist, author, and academic (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, author, and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist, author, and academic (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actress (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Augusta_Dabney\" title=\"<PERSON>ney\"><PERSON></a>, American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Dabney\" title=\"<PERSON>ney\"><PERSON></a>, American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Da<PERSON>ney"}]}, {"year": "2008", "text": "<PERSON>, Polish academic and politician, Minister of Foreign Affairs of Poland (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Poland\">Minister of Foreign Affairs of Poland</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs of Poland\">Minister of Foreign Affairs of Poland</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs of Poland", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_of_Poland"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Greek-French philosopher and author (b. 1924)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek-French philosopher and author (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Estonian-American composer (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American composer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-American composer (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Haitian lawyer and politician, first Prime Minister of Haiti (b. 1913)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Martial_C%C3%A9<PERSON>tin\" title=\"<PERSON>\"><PERSON></a>, Haitian lawyer and politician, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martial_C%C3%A9<PERSON>tin\" title=\"<PERSON>\"><PERSON></a>, Haitian lawyer and politician, first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Haiti\" title=\"Prime Minister of Haiti\">Prime Minister of Haiti</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martial_C%C3%A9lestin"}, {"title": "Prime Minister of Haiti", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Haiti"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Hungarian journalist and politician (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Csurka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Csurka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Csurka"}]}, {"year": "2012", "text": "<PERSON>, English soldier (b. 1901)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Florence_Green\" title=\"Florence Green\"><PERSON></a>, English soldier (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Green\" title=\"Florence Green\"><PERSON> Green</a>, English soldier (b. 1901)", "links": [{"title": "Florence Green", "link": "https://wikipedia.org/wiki/Florence_Green"}]}, {"year": "2012", "text": "<PERSON>, American farmer, soldier, and politician (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, soldier, and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, soldier, and politician (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director, producer, and cinematographer (b. 1951)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and cinematographer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Hungarian-born watercolorist and illustrator, active in the United States (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-born watercolorist and illustrator, active in the United States (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-born watercolorist and illustrator, active in the United States (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2013", "text": "<PERSON>, American trumpet player (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English singer-songwriter (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Presley\"><PERSON></a>, English singer-songwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American ice hockey player, coach, and manager (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player, coach, and manager (b. 1923)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Italian soldier, author, and playwright (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soldier, author, and playwright (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soldier, author, and playwright (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2014", "text": "<PERSON>, Zambian footballer (b. 1973)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian footballer (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>, American colonel and pilot (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>tzhugh_<PERSON><PERSON>_<PERSON>\" title=\"Fitzhugh <PERSON><PERSON>\">Fitzhu<PERSON> <PERSON><PERSON></a>, American colonel and pilot (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tzhu<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Fitzhugh <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American colonel and pilot (b. 1925)", "links": [{"title": "Fitzhugh L. Fulton", "link": "https://wikipedia.org/wiki/<PERSON>tzhu<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American captain, pilot, and astronaut (b. 1930)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Canadian bass player (b. 1949)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bass player (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Pakistani writer (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Bano_Qudsia\" title=\"Bano Qudsia\"><PERSON><PERSON></a>, Pakistani writer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bano_Qudsia\" title=\"Bano Qudsia\"><PERSON><PERSON></a>, Pakistani writer (b. 1928)", "links": [{"title": "Bano Qudsia", "link": "https://wikipedia.org/wiki/Bano_Qudsia"}]}, {"year": "2018", "text": "<PERSON>, English-American actor, voice artist, and comedian (b. 1940)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, voice artist, and comedian (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor, voice artist, and comedian (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Finnish Olympic-winning ski jumper and singer (b. 1963)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish Olympic-winning ski jumper and singer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish Olympic-winning ski jumper and singer (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matti_Nyk%C3%A4nen"}]}, {"year": "2020", "text": "<PERSON>, Former  President of Kenya (b. 1924)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former <a href=\"https://wikipedia.org/wiki/President_of_Kenya\" title=\"President of Kenya\">President of Kenya</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Kenya", "link": "https://wikipedia.org/wiki/President_of_Kenya"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>, American astronaut, molecular biologist and NASA payload specialist (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut, molecular biologist and <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> payload specialist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronaut, molecular biologist and <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> payload specialist (b. 1945)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}]}, {"year": "2022", "text": "<PERSON>, South Korean volleyball player (b. 1995)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yeok\" title=\"<PERSON>\"><PERSON></a>, South Korean volleyball player (b. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ye<PERSON>\" title=\"<PERSON>ye<PERSON>\"><PERSON></a>, South Korean volleyball player (b. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yeok"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Indian playback singer (b. 1945)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, 53rd Prime Minister of Egypt (b. 1955)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 53rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, 53rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Egypt", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Egypt"}]}, {"year": "2024", "text": "<PERSON>, Welsh rugby player (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON>, 49th Imam of the Nizari Isma'ili community (b. 1936)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>ga_Khan_IV\" title=\"<PERSON>ga Khan IV\"><PERSON><PERSON> Khan IV</a>, 49th Imam of the Nizari Isma'ili community (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khan_IV\" title=\"<PERSON>ga Khan IV\"><PERSON><PERSON> IV</a>, 49th Imam of the Nizari Isma'ili community (b. 1936)", "links": [{"title": "<PERSON><PERSON> Khan IV", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_IV"}]}]}}