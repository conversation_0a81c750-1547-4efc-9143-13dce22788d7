{"date": "April 2", "url": "https://wikipedia.org/wiki/April_2", "data": {"Events": [{"year": "1513", "text": "Having spotted land on March 27, Spanish explorer <PERSON> comes ashore on what is now the U.S. state of Florida, landing somewhere between the modern city of St. Augustine and the mouth of the St. Johns River.", "html": "1513 - Having spotted land on March 27, Spanish explorer <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>_<PERSON>_Le%C3%B3n\" title=\"Juan <PERSON>\"><PERSON></a> comes ashore on what is now the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> of <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, landing somewhere between the modern city of <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> and the mouth of the <a href=\"https://wikipedia.org/wiki/St._Johns_River\" title=\"St. Johns River\">St. Johns River</a>.", "no_year_html": "Having spotted land on March 27, Spanish explorer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Le%C3%B3n\" title=\"<PERSON>\"><PERSON></a> comes ashore on what is now the <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a> of <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, landing somewhere between the modern city of <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine</a> and the mouth of the <a href=\"https://wikipedia.org/wiki/St._Johns_River\" title=\"St. Johns River\">St. Johns River</a>.", "links": [{"title": "Juan <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B3n"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}, {"title": "St. Johns River", "link": "https://wikipedia.org/wiki/St._Johns_River"}]}, {"year": "1755", "text": "Commodore <PERSON> captures the Maratha fortress of Suvarnadurg on the west coast of India.", "html": "1755 - <a href=\"https://wikipedia.org/wiki/Commodore_(Royal_Navy)\" title=\"Commodore (Royal Navy)\">Commodore</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(naval_commander)\" class=\"mw-redirect\" title=\"<PERSON> (naval commander)\"><PERSON></a> captures the <a href=\"https://wikipedia.org/wiki/Maratha\" class=\"mw-redirect\" title=\"Maratha\">Maratha</a> fortress of <a href=\"https://wikipedia.org/wiki/Suvarnadurg\" title=\"Suvarnadurg\">Suvarnadurg</a> on the west coast of India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Commodore_(Royal_Navy)\" title=\"Commodore (Royal Navy)\">Commodore</a> <a href=\"https://wikipedia.org/wiki/<PERSON>(naval_commander)\" class=\"mw-redirect\" title=\"<PERSON> (naval commander)\"><PERSON></a> captures the <a href=\"https://wikipedia.org/wiki/Maratha\" class=\"mw-redirect\" title=\"Maratha\">Maratha</a> fortress of <a href=\"https://wikipedia.org/wiki/Suvarnadurg\" title=\"Suvarnadurg\">Suvarnadurg</a> on the west coast of India.", "links": [{"title": "Commodore (Royal Navy)", "link": "https://wikipedia.org/wiki/Commodore_(Royal_Navy)"}, {"title": "<PERSON> (naval commander)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(naval_commander)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maratha"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suvarnadurg"}]}, {"year": "1792", "text": "The Coinage Act is passed by Congress, establishing the United States Mint.", "html": "1792 - The <a href=\"https://wikipedia.org/wiki/Coinage_Act_of_1792\" title=\"Coinage Act of 1792\">Coinage Act</a> is passed by Congress, establishing the <a href=\"https://wikipedia.org/wiki/United_States_Mint\" title=\"United States Mint\">United States Mint</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coinage_Act_of_1792\" title=\"Coinage Act of 1792\">Coinage Act</a> is passed by Congress, establishing the <a href=\"https://wikipedia.org/wiki/United_States_Mint\" title=\"United States Mint\">United States Mint</a>.", "links": [{"title": "Coinage Act of 1792", "link": "https://wikipedia.org/wiki/Coinage_Act_of_1792"}, {"title": "United States Mint", "link": "https://wikipedia.org/wiki/United_States_Mint"}]}, {"year": "1800", "text": "<PERSON> leads the premiere of his First Symphony in Vienna.", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON> van <PERSON></a> leads the premiere of his <a href=\"https://wikipedia.org/wiki/Symphony_No._1_(Beethoven)\" title=\"Symphony No. 1 (Beethoven)\">First Symphony</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> van Beethoven\"><PERSON> van <PERSON></a> leads the premiere of his <a href=\"https://wikipedia.org/wiki/Symphony_No._1_(<PERSON>)\" title=\"Symphony No. 1 (Beethoven)\">First Symphony</a> in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Symphony No. 1 (<PERSON>)", "link": "https://wikipedia.org/wiki/Symphony_No._1_(<PERSON>)"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1801", "text": "French Revolutionary Wars: In the Battle of Copenhagen a British Royal Navy squadron defeats a hastily assembled, smaller, mostly-volunteer Dano-Norwegian Navy at high cost, forcing Denmark out of the Second League of Armed Neutrality.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Copenhagen_(1801)\" title=\"Battle of Copenhagen (1801)\">Battle of Copenhagen</a> a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> squadron defeats a hastily assembled, smaller, mostly-volunteer <a href=\"https://wikipedia.org/wiki/Royal_Danish_Navy\" title=\"Royal Danish Navy\">Dano-Norwegian Navy</a> at high cost, forcing <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> out of the <a href=\"https://wikipedia.org/wiki/Second_League_of_Armed_Neutrality\" title=\"Second League of Armed Neutrality\">Second League of Armed Neutrality</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Copenhagen_(1801)\" title=\"Battle of Copenhagen (1801)\">Battle of Copenhagen</a> a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> squadron defeats a hastily assembled, smaller, mostly-volunteer <a href=\"https://wikipedia.org/wiki/Royal_Danish_Navy\" title=\"Royal Danish Navy\">Dano-Norwegian Navy</a> at high cost, forcing <a href=\"https://wikipedia.org/wiki/Denmark\" title=\"Denmark\">Denmark</a> out of the <a href=\"https://wikipedia.org/wiki/Second_League_of_Armed_Neutrality\" title=\"Second League of Armed Neutrality\">Second League of Armed Neutrality</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of Copenhagen (1801)", "link": "https://wikipedia.org/wiki/Battle_of_Copenhagen_(1801)"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Royal Danish Navy", "link": "https://wikipedia.org/wiki/Royal_Danish_Navy"}, {"title": "Denmark", "link": "https://wikipedia.org/wiki/Denmark"}, {"title": "Second League of Armed Neutrality", "link": "https://wikipedia.org/wiki/Second_League_of_Armed_Neutrality"}]}, {"year": "1863", "text": "American Civil War: The largest in a series of Southern bread riots occurs in Richmond, Virginia.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The largest in a series of <a href=\"https://wikipedia.org/wiki/Southern_bread_riots\" title=\"Southern bread riots\">Southern bread riots</a> occurs in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The largest in a series of <a href=\"https://wikipedia.org/wiki/Southern_bread_riots\" title=\"Southern bread riots\">Southern bread riots</a> occurs in <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Southern bread riots", "link": "https://wikipedia.org/wiki/Southern_bread_riots"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1865", "text": "American Civil War: Defeat at the Third Battle of Petersburg forces the Army of Northern Virginia and the Confederate government to abandon Richmond, Virginia.", "html": "1865 - American Civil War: Defeat at the <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Petersburg\" title=\"Third Battle of Petersburg\">Third Battle of Petersburg</a> forces the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> and the Confederate government to abandon <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "no_year_html": "American Civil War: Defeat at the <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Petersburg\" title=\"Third Battle of Petersburg\">Third Battle of Petersburg</a> forces the <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> and the Confederate government to abandon <a href=\"https://wikipedia.org/wiki/Richmond,_Virginia\" title=\"Richmond, Virginia\">Richmond, Virginia</a>.", "links": [{"title": "Third Battle of Petersburg", "link": "https://wikipedia.org/wiki/Third_Battle_of_Petersburg"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "Richmond, Virginia", "link": "https://wikipedia.org/wiki/Richmond,_Virginia"}]}, {"year": "1885", "text": "Canadian Cree warriors attack the village of Frog Lake, killing nine.", "html": "1885 - Canadian <a href=\"https://wikipedia.org/wiki/Cree\" title=\"Cree\">Cree</a> warriors <a href=\"https://wikipedia.org/wiki/Frog_Lake_Massacre\" title=\"Frog Lake Massacre\">attack</a> the village of <a href=\"https://wikipedia.org/wiki/Frog_Lake,_Alberta\" title=\"Frog Lake, Alberta\">Frog Lake</a>, killing nine.", "no_year_html": "Canadian <a href=\"https://wikipedia.org/wiki/Cree\" title=\"Cree\">Cree</a> warriors <a href=\"https://wikipedia.org/wiki/Frog_Lake_Massacre\" title=\"Frog Lake Massacre\">attack</a> the village of <a href=\"https://wikipedia.org/wiki/Frog_Lake,_Alberta\" title=\"Frog Lake, Alberta\">Frog Lake</a>, killing nine.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cree"}, {"title": "Frog Lake Massacre", "link": "https://wikipedia.org/wiki/Frog_Lake_Massacre"}, {"title": "Frog Lake, Alberta", "link": "https://wikipedia.org/wiki/Frog_Lake,_Alberta"}]}, {"year": "1902", "text": "<PERSON>, Minister of Interior of the Russian Empire, is assassinated in the Mariinsky Palace, Saint Petersburg.", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)\" title=\"Ministry of Internal Affairs (Russia)\">Minister of Interior</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>, is assassinated in the <a href=\"https://wikipedia.org/wiki/Mariinsky_Palace\" title=\"Mariinsky Palace\">Mariinsky Palace</a>, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)\" title=\"Ministry of Internal Affairs (Russia)\">Minister of Interior</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>, is assassinated in the <a href=\"https://wikipedia.org/wiki/Mariinsky_Palace\" title=\"Mariinsky Palace\">Mariinsky Palace</a>, <a href=\"https://wikipedia.org/wiki/Saint_Petersburg\" title=\"Saint Petersburg\">Saint Petersburg</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Internal Affairs (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Internal_Affairs_(Russia)"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Mariinsky Palace", "link": "https://wikipedia.org/wiki/Mariinsky_Palace"}, {"title": "Saint Petersburg", "link": "https://wikipedia.org/wiki/Saint_Petersburg"}]}, {"year": "1902", "text": "\"Electric Theatre\", the first full-time movie theater in the United States, opens in Los Angeles.", "html": "1902 - \"Electric Theatre\", the first full-time <a href=\"https://wikipedia.org/wiki/Movie_theater\" title=\"Movie theater\">movie theater</a> in the United States, opens in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "no_year_html": "\"Electric Theatre\", the first full-time <a href=\"https://wikipedia.org/wiki/Movie_theater\" title=\"Movie theater\">movie theater</a> in the United States, opens in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "links": [{"title": "Movie theater", "link": "https://wikipedia.org/wiki/Movie_theater"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1911", "text": "The Australian Bureau of Statistics conducts the country's first national census.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/Australian_Bureau_of_Statistics\" title=\"Australian Bureau of Statistics\">Australian Bureau of Statistics</a> conducts the country's first <a href=\"https://wikipedia.org/wiki/Census_in_Australia\" title=\"Census in Australia\">national census</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Australian_Bureau_of_Statistics\" title=\"Australian Bureau of Statistics\">Australian Bureau of Statistics</a> conducts the country's first <a href=\"https://wikipedia.org/wiki/Census_in_Australia\" title=\"Census in Australia\">national census</a>.", "links": [{"title": "Australian Bureau of Statistics", "link": "https://wikipedia.org/wiki/Australian_Bureau_of_Statistics"}, {"title": "Census in Australia", "link": "https://wikipedia.org/wiki/Census_in_Australia"}]}, {"year": "1912", "text": "The ill-fated RMS Titanic begins sea trials.", "html": "1912 - The ill-fated <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a> begins <a href=\"https://wikipedia.org/wiki/Sea_trial\" title=\"Sea trial\">sea trials</a>.", "no_year_html": "The ill-fated <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a> begins <a href=\"https://wikipedia.org/wiki/Sea_trial\" title=\"Sea trial\">sea trials</a>.", "links": [{"title": "Titanic", "link": "https://wikipedia.org/wiki/Titanic"}, {"title": "Sea trial", "link": "https://wikipedia.org/wiki/Sea_trial"}]}, {"year": "1917", "text": "American entry into World War I: President <PERSON> asks the U.S. Congress for a declaration of war on Germany.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/American_entry_into_World_War_I\" title=\"American entry into World War I\">American entry into World War I</a>: President <PERSON> <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)#President_<PERSON>'s_speech_to_<PERSON>\" title=\"United States declaration of war on Germany (1917)\">asks the U.S. Congress</a> for a declaration of war on Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_entry_into_World_War_I\" title=\"American entry into World War I\">American entry into World War I</a>: President <PERSON> <a href=\"https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)#President<PERSON><PERSON>'s_speech_to_<PERSON>\" title=\"United States declaration of war on Germany (1917)\">asks the U.S. Congress</a> for a declaration of war on Germany.", "links": [{"title": "American entry into World War I", "link": "https://wikipedia.org/wiki/American_entry_into_World_War_I"}, {"title": "United States declaration of war on Germany (1917)", "link": "https://wikipedia.org/wiki/United_States_declaration_of_war_on_Germany_(1917)#President_<PERSON>'s_speech_to_<PERSON>"}]}, {"year": "1921", "text": "The Autonomous Government of Khorasan, a military government encompassing the modern state of Iran, is established.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/Autonomous_Government_of_Khorasan\" title=\"Autonomous Government of Khorasan\">Autonomous Government of Khorasan</a>, a military government encompassing the modern state of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Autonomous_Government_of_Khorasan\" title=\"Autonomous Government of Khorasan\">Autonomous Government of Khorasan</a>, a military government encompassing the modern state of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>, is established.", "links": [{"title": "Autonomous Government of Khorasan", "link": "https://wikipedia.org/wiki/Autonomous_Government_of_Khorasan"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "1930", "text": "After the mysterious death of Empress <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> is proclaimed emperor of Ethiopia.", "html": "1930 - After the mysterious death of Empress <a href=\"https://wikipedia.org/wiki/Zewditu\" title=\"Zewditu\">Zewditu</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON><PERSON> Se<PERSON>ie\"><PERSON><PERSON></a> is proclaimed emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "no_year_html": "After the mysterious death of Empress <a href=\"https://wikipedia.org/wiki/Zewditu\" title=\"Zewditu\">Zewditu</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON><PERSON> Se<PERSON>\"><PERSON><PERSON></a> is proclaimed emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>.", "links": [{"title": "Zewditu", "link": "https://wikipedia.org/wiki/Zewditu"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}]}, {"year": "1954", "text": "A 19-month-old infant is swept up in the ocean tides at Hermosa Beach, California. Local photographer <PERSON> photographs the incident; 1955 Pulitzer winner \"Tragedy by the Sea\".", "html": "1954 - A 19-month-old infant is swept up in the ocean tides at <a href=\"https://wikipedia.org/wiki/Hermosa_Beach,_California\" title=\"Hermosa Beach, California\">Hermosa Beach, California</a>. Local photographer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> photographs the incident; 1955 Pulitzer winner \"<a href=\"https://wikipedia.org/wiki/Tragedy_by_the_Sea\" title=\"Tragedy by the Sea\">Tragedy by the Sea</a>\".", "no_year_html": "A 19-month-old infant is swept up in the ocean tides at <a href=\"https://wikipedia.org/wiki/Hermosa_Beach,_California\" title=\"Hermosa Beach, California\">Hermosa Beach, California</a>. Local photographer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> photographs the incident; 1955 Pulitzer winner \"<a href=\"https://wikipedia.org/wiki/Tragedy_by_the_Sea\" title=\"Tragedy by the Sea\">Tragedy by the Sea</a>\".", "links": [{"title": "Hermosa Beach, California", "link": "https://wikipedia.org/wiki/Hermosa_Beach,_California"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Tragedy by the Sea", "link": "https://wikipedia.org/wiki/Tragedy_by_the_Sea"}]}, {"year": "1956", "text": "As the World Turns and The Edge of Night premiere on CBS. The two soaps become the first daytime dramas to debut in the 30-minute format.", "html": "1956 - <i><a href=\"https://wikipedia.org/wiki/As_the_World_Turns\" title=\"As the World Turns\">As the World Turns</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Edge_of_Night\" title=\"The Edge of Night\">The Edge of Night</a></i> premiere on <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a>. The two soaps become the first daytime dramas to debut in the 30-minute format.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/As_the_World_Turns\" title=\"As the World Turns\">As the World Turns</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Edge_of_Night\" title=\"The Edge of Night\">The Edge of Night</a></i> premiere on <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a>. The two soaps become the first daytime dramas to debut in the 30-minute format.", "links": [{"title": "As the World Turns", "link": "https://wikipedia.org/wiki/As_the_World_Turns"}, {"title": "The Edge of Night", "link": "https://wikipedia.org/wiki/The_Edge_of_Night"}, {"title": "CBS", "link": "https://wikipedia.org/wiki/CBS"}]}, {"year": "1964", "text": "The Soviet Union launches Zond 1.", "html": "1964 - The Soviet Union launches <a href=\"https://wikipedia.org/wiki/Zond_1\" title=\"Zond 1\">Zond 1</a>.", "no_year_html": "The Soviet Union launches <a href=\"https://wikipedia.org/wiki/Zond_1\" title=\"Zond 1\">Zond 1</a>.", "links": [{"title": "Zond 1", "link": "https://wikipedia.org/wiki/Zond_1"}]}, {"year": "1969", "text": "LOT Polish Airlines Flight 165 crashes into the Polica mountain near Zawoja, Poland, killing 53.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_165\" title=\"LOT Polish Airlines Flight 165\">LOT Polish Airlines Flight 165</a> crashes into the <a href=\"https://wikipedia.org/wiki/Polica_(mountain)\" title=\"Polica (mountain)\">Polica mountain</a> near <a href=\"https://wikipedia.org/wiki/Zawoja\" title=\"Zawoja\">Zawoja</a>, Poland, killing 53.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_165\" title=\"LOT Polish Airlines Flight 165\">LOT Polish Airlines Flight 165</a> crashes into the <a href=\"https://wikipedia.org/wiki/Polica_(mountain)\" title=\"Polica (mountain)\">Polica mountain</a> near <a href=\"https://wikipedia.org/wiki/Zawoja\" title=\"Zawoja\">Zawoja</a>, Poland, killing 53.", "links": [{"title": "LOT Polish Airlines Flight 165", "link": "https://wikipedia.org/wiki/LOT_Polish_Airlines_Flight_165"}, {"title": "Polica (mountain)", "link": "https://wikipedia.org/wiki/Polica_(mountain)"}, {"title": "Zaw<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aw<PERSON>"}]}, {"year": "1972", "text": "Actor <PERSON> returns to the United States for the first time since being labeled a communist during the Red Scare in the early 1950s.", "html": "1972 - Actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to the United States for the first time since being labeled a <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> during the <a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\"><PERSON></a> in the early 1950s.", "no_year_html": "Actor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to the United States for the first time since being labeled a <a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> during the <a href=\"https://wikipedia.org/wiki/Second_Red_Scare\" class=\"mw-redirect\" title=\"Second Red Scare\"><PERSON></a> in the early 1950s.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "Second Red Scare", "link": "https://wikipedia.org/wiki/Second_Red_Scare"}]}, {"year": "1973", "text": "Launch of the LexisNexis computerized legal research service.", "html": "1973 - Launch of the <a href=\"https://wikipedia.org/wiki/LexisNexis\" title=\"LexisNexis\">LexisNexis</a> computerized legal research service.", "no_year_html": "Launch of the <a href=\"https://wikipedia.org/wiki/LexisNexis\" title=\"LexisNexis\">LexisNexis</a> computerized legal research service.", "links": [{"title": "LexisNexis", "link": "https://wikipedia.org/wiki/LexisNexis"}]}, {"year": "1975", "text": "Vietnam War: Thousands of civilian refugees flee from Quảng Ngãi Province in front of advancing North Vietnamese troops.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Thousands of civilian <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">refugees</a> flee from <a href=\"https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Ng%C3%A3i_Province\" class=\"mw-redirect\" title=\"Quảng Ngãi Province\">Quảng Ngãi Province</a> in front of advancing <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Thousands of civilian <a href=\"https://wikipedia.org/wiki/Refugee\" title=\"Refugee\">refugees</a> flee from <a href=\"https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Ng%C3%A3i_Province\" class=\"mw-redirect\" title=\"Quảng Ngãi Province\">Quảng Ngãi Province</a> in front of advancing <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> troops.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Refugee", "link": "https://wikipedia.org/wiki/Refugee"}, {"title": "Quảng Ngãi Province", "link": "https://wikipedia.org/wiki/Qu%E1%BA%A3ng_Ng%C3%A3i_Province"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1976", "text": "Prince <PERSON><PERSON><PERSON> resigns as leader of Cambodia and is placed under house arrest.", "html": "1976 - Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns as leader of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a> and is placed under <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house arrest</a>.", "no_year_html": "Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> resigns as leader of <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a> and is placed under <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house arrest</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norodom_<PERSON>"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}, {"title": "House arrest", "link": "https://wikipedia.org/wiki/House_arrest"}]}, {"year": "1979", "text": "A Soviet bio-warfare laboratory at Sverdlovsk accidentally releases airborne anthrax spores, killing 66 plus an unknown amount of livestock.", "html": "1979 - A Soviet bio-warfare laboratory at Sverdlovsk <a href=\"https://wikipedia.org/wiki/Sverdlovsk_anthrax_leak\" title=\"Sverdlovsk anthrax leak\">accidentally releases airborne anthrax spores</a>, killing 66 plus an unknown amount of livestock.", "no_year_html": "A Soviet bio-warfare laboratory at Sverdlovsk <a href=\"https://wikipedia.org/wiki/Sverdlovsk_anthrax_leak\" title=\"Sverdlovsk anthrax leak\">accidentally releases airborne anthrax spores</a>, killing 66 plus an unknown amount of livestock.", "links": [{"title": "Sverdlovsk anthrax leak", "link": "https://wikipedia.org/wiki/Sverdlovsk_anthrax_leak"}]}, {"year": "1980", "text": "United States President <PERSON> signs the Crude Oil Windfall Profits Tax Act.", "html": "1980 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Crude_Oil\" class=\"mw-redirect\" title=\"Crude Oil\">Crude Oil</a> <a href=\"https://wikipedia.org/wiki/Windfall_Profits_Tax\" class=\"mw-redirect\" title=\"Windfall Profits Tax\">Windfall Profits Tax</a> Act.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Crude_Oil\" class=\"mw-redirect\" title=\"Crude Oil\">Crude Oil</a> <a href=\"https://wikipedia.org/wiki/Windfall_Profits_Tax\" class=\"mw-redirect\" title=\"Windfall Profits Tax\">Windfall Profits Tax</a> Act.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Crude Oil", "link": "https://wikipedia.org/wiki/<PERSON>rude_<PERSON>"}, {"title": "Windfall Profits Tax", "link": "https://wikipedia.org/wiki/Windfall_Profits_Tax"}]}, {"year": "1982", "text": "Falklands War: Argentina invades the Falkland Islands.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/1982_invasion_of_the_Falkland_Islands\" title=\"1982 invasion of the Falkland Islands\">Argentina invades the Falkland Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/1982_invasion_of_the_Falkland_Islands\" title=\"1982 invasion of the Falkland Islands\">Argentina invades the Falkland Islands</a>.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "1982 invasion of the Falkland Islands", "link": "https://wikipedia.org/wiki/1982_invasion_of_the_Falkland_Islands"}]}, {"year": "1986", "text": "Alabama governor <PERSON>, a former segregationist, best known for the \"Stand in the Schoolhouse Door\", announces that he will not seek a fifth four-year term and will retire from public life upon the end of his term in January 1987.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former <a href=\"https://wikipedia.org/wiki/Segregationist\" class=\"mw-redirect\" title=\"Segregationist\">segregationist</a>, best known for the \"<a href=\"https://wikipedia.org/wiki/Stand_in_the_Schoolhouse_Door\" title=\"Stand in the Schoolhouse Door\">Stand in the Schoolhouse Door</a>\", announces that he will not seek a fifth four-year term and will retire from public life upon the end of his term in January 1987.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alabama\" title=\"Alabama\">Alabama</a> governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former <a href=\"https://wikipedia.org/wiki/Segregationist\" class=\"mw-redirect\" title=\"Segregationist\">segregationist</a>, best known for the \"<a href=\"https://wikipedia.org/wiki/Stand_in_the_Schoolhouse_Door\" title=\"Stand in the Schoolhouse Door\">Stand in the Schoolhouse Door</a>\", announces that he will not seek a fifth four-year term and will retire from public life upon the end of his term in January 1987.", "links": [{"title": "Alabama", "link": "https://wikipedia.org/wiki/Alabama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Segregationist", "link": "https://wikipedia.org/wiki/Segregationist"}, {"title": "Stand in the Schoolhouse Door", "link": "https://wikipedia.org/wiki/Stand_in_the_Schoolhouse_Door"}]}, {"year": "1989", "text": "Soviet leader <PERSON> arrives in Havana, Cuba, to meet with <PERSON><PERSON> in an attempt to mend strained relations.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, Cuba, to meet with <a href=\"https://wikipedia.org/wiki/Fidel_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a> in an attempt to mend strained relations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, Cuba, to meet with <a href=\"https://wikipedia.org/wiki/Fidel_<PERSON>\" title=\"Fidel Castro\"><PERSON><PERSON></a> in an attempt to mend strained relations.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON> becomes the first female Premier of a Canadian province when she succeeds <PERSON> (who had resigned) as Premier of British Columbia.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Premier_(Canada)\" title=\"Premier (Canada)\">Premier</a> of a <a href=\"https://wikipedia.org/wiki/Province_(Canada)\" class=\"mw-redirect\" title=\"Province (Canada)\">Canadian province</a> when she succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (who had resigned) as <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Premier_(Canada)\" title=\"Premier (Canada)\">Premier</a> of a <a href=\"https://wikipedia.org/wiki/Province_(Canada)\" class=\"mw-redirect\" title=\"Province (Canada)\">Canadian province</a> when she succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (who had resigned) as <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier (Canada)", "link": "https://wikipedia.org/wiki/Premier_(Canada)"}, {"title": "Province (Canada)", "link": "https://wikipedia.org/wiki/Province_(Canada)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1992", "text": "In New York, Mafia boss <PERSON> is convicted of murder and racketeering and is later sentenced to life in prison.", "html": "1992 - In <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, <a href=\"https://wikipedia.org/wiki/American_Mafia\" title=\"American Mafia\">Mafia</a> boss <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of murder and <a href=\"https://wikipedia.org/wiki/Racketeering\" title=\"Racketeering\">racketeering</a> and is later <a href=\"https://wikipedia.org/wiki/Life_imprisonment\" title=\"Life imprisonment\">sentenced to life</a> in prison.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/New_York_(state)\" title=\"New York (state)\">New York</a>, <a href=\"https://wikipedia.org/wiki/American_Mafia\" title=\"American Mafia\">Mafia</a> boss <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is convicted of murder and <a href=\"https://wikipedia.org/wiki/Racketeering\" title=\"Racketeering\">racketeering</a> and is later <a href=\"https://wikipedia.org/wiki/Life_imprisonment\" title=\"Life imprisonment\">sentenced to life</a> in prison.", "links": [{"title": "New York (state)", "link": "https://wikipedia.org/wiki/New_York_(state)"}, {"title": "American Mafia", "link": "https://wikipedia.org/wiki/American_Mafia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Racketeering", "link": "https://wikipedia.org/wiki/Racketeering"}, {"title": "Life imprisonment", "link": "https://wikipedia.org/wiki/Life_imprisonment"}]}, {"year": "1992", "text": "Forty-two civilians are massacred in the town of Bijeljina in Bosnia and Herzegovina.", "html": "1992 - Forty-two civilians <a href=\"https://wikipedia.org/wiki/Bijeljina_massacre\" title=\"Bijeljina massacre\">are massacred</a> in the town of <a href=\"https://wikipedia.org/wiki/Bijeljina\" title=\"Bijeljina\">Bijeljina</a> in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "no_year_html": "Forty-two civilians <a href=\"https://wikipedia.org/wiki/Bijeljina_massacre\" title=\"Bijeljina massacre\">are massacred</a> in the town of <a href=\"https://wikipedia.org/wiki/Bijeljina\" title=\"Bijeljina\">Bijeljina</a> in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>.", "links": [{"title": "Bijeljina massacre", "link": "https://wikipedia.org/wiki/Bijeljina_massacre"}, {"title": "Bijeljina", "link": "https://wikipedia.org/wiki/Bijeljina"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "2002", "text": "Israeli forces surround the Church of the Nativity in Bethlehem, into which armed Palestinians had retreated.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces <a href=\"https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem\" class=\"mw-redirect\" title=\"Siege of the Church of the Nativity in Bethlehem\">surround the Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a>, into which armed <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinians</a> had retreated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> forces <a href=\"https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem\" class=\"mw-redirect\" title=\"Siege of the Church of the Nativity in Bethlehem\">surround the Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a>, into which armed <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinians</a> had retreated.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Siege of the Church of the Nativity in Bethlehem", "link": "https://wikipedia.org/wiki/Siege_of_the_Church_of_the_Nativity_in_Bethlehem"}, {"title": "Bethlehem", "link": "https://wikipedia.org/wiki/Bethlehem"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}]}, {"year": "2004", "text": "Islamist terrorists involved in the 11 March 2004 Madrid attacks attempt to bomb the Spanish high-speed train AVE near Madrid; the attack is thwarted.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorists</a> involved in the <a href=\"https://wikipedia.org/wiki/11_March_2004_Madrid_attacks\" class=\"mw-redirect\" title=\"11 March 2004 Madrid attacks\">11 March 2004 Madrid attacks</a> attempt to bomb the Spanish high-speed train <a href=\"https://wikipedia.org/wiki/AVE\" title=\"AVE\">AVE</a> near <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>; the attack is thwarted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islamism\" title=\"Islamism\">Islamist</a> <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorists</a> involved in the <a href=\"https://wikipedia.org/wiki/11_March_2004_Madrid_attacks\" class=\"mw-redirect\" title=\"11 March 2004 Madrid attacks\">11 March 2004 Madrid attacks</a> attempt to bomb the Spanish high-speed train <a href=\"https://wikipedia.org/wiki/AVE\" title=\"AVE\">AVE</a> near <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>; the attack is thwarted.", "links": [{"title": "Islamism", "link": "https://wikipedia.org/wiki/Islamism"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "11 March 2004 Madrid attacks", "link": "https://wikipedia.org/wiki/11_March_2004_Madrid_attacks"}, {"title": "AVE", "link": "https://wikipedia.org/wiki/AVE"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}]}, {"year": "2006", "text": "Over 60 tornadoes break out in the United States; Tennessee is hardest hit with 29 people killed.", "html": "2006 - Over 60 <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornadoes</a> <a href=\"https://wikipedia.org/wiki/April_2,_2006_tornado_outbreak\" class=\"mw-redirect\" title=\"April 2, 2006 tornado outbreak\">break out</a> in the United States; <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> is hardest hit with 29 people killed.", "no_year_html": "Over 60 <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornadoes</a> <a href=\"https://wikipedia.org/wiki/April_2,_2006_tornado_outbreak\" class=\"mw-redirect\" title=\"April 2, 2006 tornado outbreak\">break out</a> in the United States; <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> is hardest hit with 29 people killed.", "links": [{"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "April 2, 2006 tornado outbreak", "link": "https://wikipedia.org/wiki/April_2,_2006_tornado_outbreak"}, {"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "2011", "text": "India wins the Cricket World Cup for the second time in history under the captaincy of <PERSON>.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/India_national_cricket_team\" title=\"India national cricket team\">India</a> wins the <a href=\"https://wikipedia.org/wiki/2011_Cricket_World_Cup\" title=\"2011 Cricket World Cup\">Cricket World Cup</a> for the second time in history under the captaincy of <a href=\"https://wikipedia.org/wiki/MS_Dhoni\" title=\"MS Dhoni\">MS Dhoni</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India_national_cricket_team\" title=\"India national cricket team\">India</a> wins the <a href=\"https://wikipedia.org/wiki/2011_Cricket_World_Cup\" title=\"2011 Cricket World Cup\">Cricket World Cup</a> for the second time in history under the captaincy of <a href=\"https://wikipedia.org/wiki/MS_Dhoni\" title=\"MS Dhoni\">MS Dhoni</a>.", "links": [{"title": "India national cricket team", "link": "https://wikipedia.org/wiki/India_national_cricket_team"}, {"title": "2011 Cricket World Cup", "link": "https://wikipedia.org/wiki/2011_Cricket_World_Cup"}, {"title": "MS Dhoni", "link": "https://wikipedia.org/wiki/MS_<PERSON><PERSON>i"}]}, {"year": "2012", "text": "A mass shooting at Oikos University in California leaves seven people dead and three injured.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012_Oikos_University_shooting\" title=\"2012 Oikos University shooting\">mass shooting at Oikos University in California</a> leaves seven people dead and three injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012_Oikos_University_shooting\" title=\"2012 Oikos University shooting\">mass shooting at Oikos University in California</a> leaves seven people dead and three injured.", "links": [{"title": "2012 Oikos University shooting", "link": "https://wikipedia.org/wiki/2012_Oikos_University_shooting"}]}, {"year": "2012", "text": "UTair Flight 120 crashes after takeoff from Roshchino International Airport in Tyumen, Russia, killing 33 and injuring 10.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/UTair_Flight_120\" title=\"UTair Flight 120\">UTair Flight 120</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Roshchino_International_Airport\" title=\"Roshchino International Airport\">Roshchino International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tyumen\" title=\"Tyumen\">Tyumen</a>, Russia, killing 33 and injuring 10.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/UTair_Flight_120\" title=\"UTair Flight 120\">UTair Flight 120</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Roshchino_International_Airport\" title=\"Roshchino International Airport\">Roshchino International Airport</a> in <a href=\"https://wikipedia.org/wiki/Tyumen\" title=\"Tyumen\">Tyumen</a>, Russia, killing 33 and injuring 10.", "links": [{"title": "UTair Flight 120", "link": "https://wikipedia.org/wiki/UTair_Flight_120"}, {"title": "Roshchino International Airport", "link": "https://wikipedia.org/wiki/Roshchino_International_Airport"}, {"title": "Tyumen", "link": "https://wikipedia.org/wiki/Tyumen"}]}, {"year": "2014", "text": "A spree shooting occurs at the Fort Hood army base in Texas, with four dead, including the gunman, and 16 others injured.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Fort_Hood_shootings\" title=\"2014 Fort Hood shootings\">spree shooting</a> occurs at the <a href=\"https://wikipedia.org/wiki/Fort_Hood\" class=\"mw-redirect\" title=\"Fort Hood\">Fort Hood</a> army base in Texas, with four dead, including the gunman, and 16 others injured.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Fort_Hood_shootings\" title=\"2014 Fort Hood shootings\">spree shooting</a> occurs at the <a href=\"https://wikipedia.org/wiki/Fort_Hood\" class=\"mw-redirect\" title=\"Fort Hood\">Fort Hood</a> army base in Texas, with four dead, including the gunman, and 16 others injured.", "links": [{"title": "2014 Fort Hood shootings", "link": "https://wikipedia.org/wiki/2014_Fort_Hood_shootings"}, {"title": "Fort Hood", "link": "https://wikipedia.org/wiki/Fort_Hood"}]}, {"year": "2015", "text": "Gunmen attack Garissa University College in Kenya, killing at least 148 people and wounding 79 others.", "html": "2015 - Gunmen <a href=\"https://wikipedia.org/wiki/Garissa_University_College_attack\" title=\"Garissa University College attack\">attack</a> <a href=\"https://wikipedia.org/wiki/Garissa_University_College\" title=\"Garissa University College\">Garissa University College</a> in <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a>, killing at least 148 people and wounding 79 others.", "no_year_html": "Gunmen <a href=\"https://wikipedia.org/wiki/Garissa_University_College_attack\" title=\"Garissa University College attack\">attack</a> <a href=\"https://wikipedia.org/wiki/Garissa_University_College\" title=\"Garissa University College\">Garissa University College</a> in <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a>, killing at least 148 people and wounding 79 others.", "links": [{"title": "<PERSON><PERSON>sa University College attack", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sa_University_College_attack"}, {"title": "Garissa University College", "link": "https://wikipedia.org/wiki/<PERSON>arissa_University_College"}, {"title": "Kenya", "link": "https://wikipedia.org/wiki/Kenya"}]}, {"year": "2015", "text": "Four men steal items worth up to £200 million from an underground safe deposit facility in London's Hatton Garden area in what has been called the \"largest burglary in English legal history.\"", "html": "2015 - Four men <a href=\"https://wikipedia.org/wiki/Hatton_Garden_safe_deposit_burglary\" title=\"Hatton Garden safe deposit burglary\">steal</a> items worth up to £200 million from an underground <a href=\"https://wikipedia.org/wiki/Safe_deposit\" class=\"mw-redirect\" title=\"Safe deposit\">safe deposit</a> facility in London's <a href=\"https://wikipedia.org/wiki/Hatton_Garden\" title=\"Hatton Garden\">Hatton Garden</a> area in what has been called the \"largest burglary in English legal history.\"", "no_year_html": "Four men <a href=\"https://wikipedia.org/wiki/Hatton_Garden_safe_deposit_burglary\" title=\"Hatton Garden safe deposit burglary\">steal</a> items worth up to £200 million from an underground <a href=\"https://wikipedia.org/wiki/Safe_deposit\" class=\"mw-redirect\" title=\"Safe deposit\">safe deposit</a> facility in London's <a href=\"https://wikipedia.org/wiki/Hatton_Garden\" title=\"Hatton Garden\">Hatton Garden</a> area in what has been called the \"largest burglary in English legal history.\"", "links": [{"title": "Hatton Garden safe deposit burglary", "link": "https://wikipedia.org/wiki/Hatton_Garden_safe_deposit_burglary"}, {"title": "Safe deposit", "link": "https://wikipedia.org/wiki/Safe_deposit"}, {"title": "Hatton Garden", "link": "https://wikipedia.org/wiki/Hatton_Garden"}]}, {"year": "2020", "text": "COVID-19 pandemic: The total number of confirmed cases reach one million.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The total number of confirmed cases reach one million.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The total number of confirmed cases reach one million.", "links": [{"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}]}, {"year": "2021", "text": "At least 49 people are killed in a train derailment in Taiwan after a truck accidentally rolls onto the track.", "html": "2021 - At least 49 people are killed in a <a href=\"https://wikipedia.org/wiki/2021_Hualien_train_derailment\" title=\"2021 Hualien train derailment\">train derailment</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> after a truck accidentally rolls onto the track.", "no_year_html": "At least 49 people are killed in a <a href=\"https://wikipedia.org/wiki/2021_Hualien_train_derailment\" title=\"2021 Hualien train derailment\">train derailment</a> in <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a> after a truck accidentally rolls onto the track.", "links": [{"title": "2021 Hualien train derailment", "link": "https://wikipedia.org/wiki/2021_<PERSON><PERSON><PERSON>_train_derailment"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "2021", "text": "A Capitol Police officer is killed and another injured when an attacker rams his car into a barricade outside the United States Capitol.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/United_States_Capitol_Police\" title=\"United States Capitol Police\">Capitol Police</a> officer is killed and another injured <a href=\"https://wikipedia.org/wiki/2021_United_States_Capitol_car_attack\" title=\"2021 United States Capitol car attack\">when an attacker rams his car into a barricade</a> outside the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Capitol_Police\" title=\"United States Capitol Police\">Capitol Police</a> officer is killed and another injured <a href=\"https://wikipedia.org/wiki/2021_United_States_Capitol_car_attack\" title=\"2021 United States Capitol car attack\">when an attacker rams his car into a barricade</a> outside the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a>.", "links": [{"title": "United States Capitol Police", "link": "https://wikipedia.org/wiki/United_States_Capitol_Police"}, {"title": "2021 United States Capitol car attack", "link": "https://wikipedia.org/wiki/2021_United_States_Capitol_car_attack"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}]}, {"year": "2024", "text": "Viertola school shooting: A 12-year-old pupil is killed and two others injured by a shooter of the same age in Vantaa, Finland.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Viertola_school_shooting\" title=\"Viertola school shooting\">Viertola school shooting</a>: A 12-year-old pupil is killed and two others injured by a shooter of the same age in <a href=\"https://wikipedia.org/wiki/Vantaa\" title=\"Vantaa\">Vantaa, Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viertola_school_shooting\" title=\"Viertola school shooting\">Viertola school shooting</a>: A 12-year-old pupil is killed and two others injured by a shooter of the same age in <a href=\"https://wikipedia.org/wiki/Vantaa\" title=\"Vantaa\">Vantaa, Finland</a>.", "links": [{"title": "Viertola school shooting", "link": "https://wikipedia.org/wiki/Viertola_school_shooting"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Van<PERSON>a"}]}], "Births": [{"year": "181", "text": "Emperor <PERSON><PERSON> of Han, Chinese emperor (d. 234)", "html": "181 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a>, Chinese emperor (d. 234)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a>, Chinese emperor (d. 234)", "links": [{"title": "Emperor <PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han"}]}, {"year": "747", "text": "<PERSON><PERSON><PERSON><PERSON>, Frankish king (d. 814)", "html": "747 - <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish king (d. 814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Frankish king (d. 814)", "links": [{"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}]}, {"year": "1473", "text": "<PERSON>, Hungarian noble (d. 1504)", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian noble (d. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian noble (d. 1504)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON> Valois (d. 1568)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a> (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a> (d. 1568)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, Dutch explorer (d. 1599)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch explorer (d. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch explorer (d. 1599)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1586", "text": "<PERSON>, Italian traveler (d. 1652)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian traveler (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian traveler (d. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON> of <PERSON> of Ágreda, Franciscan abbess (d. 1665)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_of_%C3%81greda\" title=\"<PERSON> of Jesus of Ágreda\"><PERSON> of Jesus of Ágreda</a>, Franciscan abbess (d. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>_of_%C3%81greda\" title=\"<PERSON> of Jesus of Ágreda\"><PERSON> of Jesus of Ágreda</a>, Franciscan abbess (d. 1665)", "links": [{"title": "<PERSON> of Jesus of Ágreda", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_%C3%81greda"}]}, {"year": "1618", "text": "<PERSON>, Italian mathematician and physicist (d. 1663)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and physicist (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and physicist (d. 1663)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON>, German-Dutch botanist and illustrator (d. 1717)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch botanist and illustrator (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch botanist and illustrator (d. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON> of Denmark (d. 1708)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Denmark\" title=\"Prince <PERSON> of Denmark\">Prince <PERSON> of Denmark</a> (d. 1708)", "links": [{"title": "Prince <PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1696", "text": "<PERSON>, Italian operatic soprano (d. 1778)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian operatic soprano (d. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian operatic soprano (d. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, German poet (d. 1803)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, Italian explorer and author (d. 1798)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian explorer and author (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian explorer and author (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, French lawyer and politician (d. 1826)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician (d. 1826)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, Filipino poet and author (d. 1862)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco Balagtas\"><PERSON></a>, Filipino poet and author (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>\" title=\"Francisco Balagtas\"><PERSON></a>, Filipino poet and author (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>lagtas"}]}, {"year": "1788", "text": "<PERSON><PERSON>, German balloonist (d. 1848)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German balloonist (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German balloonist (d. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON>, Argentinian general and politician (d. 1871)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician (d. 1871)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Colombian general and politician, 4th President of the Republic of the New Granada (d. 1840)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>nder\"><PERSON></a>, Colombian general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada\" class=\"mw-redirect\" title=\"President of the Republic of the New Granada\">President of the Republic of the New Granada</a> (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco de <PERSON>nder\"><PERSON></a>, Colombian general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada\" class=\"mw-redirect\" title=\"President of the Republic of the New Granada\">President of the Republic of the New Granada</a> (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Republic of the New Granada", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada"}]}, {"year": "1798", "text": "<PERSON> <PERSON>, German poet and academic (d. 1874)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and academic (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German poet and academic (d. 1874)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Danish novelist, short story writer, and poet (d. 1875)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish novelist, short story writer, and poet (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish novelist, short story writer, and poet (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, American general and judge (d. 1875)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and judge (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and judge (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON><PERSON>, American inventor (d. 1879)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>elow\" title=\"<PERSON><PERSON><PERSON>elow\"><PERSON><PERSON><PERSON></a>, American inventor (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bigelow\" title=\"<PERSON><PERSON><PERSON>elow\"><PERSON><PERSON><PERSON></a>, American inventor (d. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Bigelow"}]}, {"year": "1827", "text": "<PERSON>, English soldier and painter (d. 1910)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, American engineer (d. 1907)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jacob <PERSON>\"><PERSON></a>, American engineer (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1838", "text": "<PERSON>, French lawyer and politician, 45th Prime Minister of France (d. 1882)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 45th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1840", "text": "<PERSON><PERSON>, French novelist, playwright, journalist (d. 1902)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>ola\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, playwright, journalist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist, playwright, journalist (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Zola"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON>, French engineer, designed the Ader Avion III (d. 1926)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Ader\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer, designed the <a href=\"https://wikipedia.org/wiki/Ader_Avion_III\" title=\"Ader Avion III\">Ader Avion III</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Ader\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French engineer, designed the <a href=\"https://wikipedia.org/wiki/Ader_Avion_III\" title=\"Ader Avion III\">Ader Avion III</a> (d. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Ader"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Italian Catholic saint, adolescent student of Saint <PERSON> (d. 1857)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Catholic saint, adolescent student of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON>\">Saint <PERSON></a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Catholic saint, adolescent student of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON>\">Saint <PERSON></a> (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Slovenian priest and author (d. 1935)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Persa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian priest and author (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Persa\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian priest and author (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Persa"}]}, {"year": "1862", "text": "<PERSON>, American philosopher and academic, Nobel Prize laureate (d. 1947)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1869", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1928)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Irish-Australian politician, 29th Premier of Tasmania (d. 1945)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1945)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1875", "text": "<PERSON>, American businessman, founded Chrysler (d. 1940)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walter_Chrysler\" title=\"Walter Chrysler\">Walter Chrysler</a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> (d. 1940)", "links": [{"title": "Walter <PERSON>", "link": "https://wikipedia.org/wiki/Walter_Chrysler"}, {"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}]}, {"year": "1875", "text": "<PERSON>, English cricketer and captain (d. 1942)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and captain (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and captain (d. 1942)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1884", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet, author, and historian (d. 1958)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._Squire\" title=\"J. C. Squire\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet, author, and historian (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._C._Squire\" title=\"J. C. Squire\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet, author, and historian (d. 1958)", "links": [{"title": "J. C. Squire", "link": "https://wikipedia.org/wiki/J._C._Squire"}]}, {"year": "1888", "text": "<PERSON>,  English cricket and music writer (d. 1975)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricket and music writer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricket and music writer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Scottish entertainer (d. 1957)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish entertainer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish entertainer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German painter, sculptor, and poet (d. 1976)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter, sculptor, and poet (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter, sculptor, and poet (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "Tristão de Bragança Cunha, Indian nationalist and anti-colonial activist from Goa (d. 1958)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/Trist%C3%A3o_de_Bragan%C3%A7a_Cunha\" class=\"mw-redirect\" title=\"Tristão de Bragança Cunha\">Tristão de Bragança Cunha</a>, Indian nationalist and anti-colonial activist from <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trist%C3%A3o_de_Bragan%C3%A7a_Cunha\" class=\"mw-redirect\" title=\"Tristão de Bragança Cunha\">Tristão de Bragança Cunha</a>, Indian nationalist and anti-colonial activist from <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a> (d. 1958)", "links": [{"title": "Tristão de Bragança Cunha", "link": "https://wikipedia.org/wiki/Trist%C3%A3o_de_Bragan%C3%A7a_Cunha"}, {"title": "Goa", "link": "https://wikipedia.org/wiki/Goa"}]}, {"year": "1896", "text": "<PERSON>, American golfer (d. 1936)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Johnny Golden\"><PERSON></a>, American golfer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Johnny_Golden\" title=\"Johnny Golden\"><PERSON></a>, American golfer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, Indian poet, actor and politician (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hyay\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, actor and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, actor and politician (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Chinese mathematician (d. 1940)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Chiungtze_<PERSON>._Tsen\" title=\"Chiungtze C. Tsen\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Chinese mathematician (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiungtze_<PERSON>._Tsen\" title=\"Chiungtze C. Tsen\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Chinese mathematician (d. 1940)", "links": [{"title": "Chiungtze C. Tsen", "link": "https://wikipedia.org/wiki/<PERSON>ungtze_<PERSON><PERSON>_Tsen"}]}, {"year": "1900", "text": "<PERSON>, Argentinian journalist, author, and playwright (d. 1942)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist, author, and playwright (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian journalist, author, and playwright (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Cypriot-American pianist, composer, and conductor (d. 1970)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot-American pianist, composer, and conductor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot-American pianist, composer, and conductor (d. 1970)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English footballer (d. 1978)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Strange\"><PERSON></a>, English footballer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German-Swiss graphic designer and typographer (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss graphic designer and typographer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss graphic designer and typographer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, the seventh <PERSON><PERSON><PERSON><PERSON><PERSON> (d. 1994)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>hneerson\" title=\"<PERSON>ache<PERSON> Mendel Schneerson\"><PERSON><PERSON><PERSON></a>, the seventh <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Re<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ache<PERSON> Mendel Schneerson\"><PERSON><PERSON><PERSON></a>, the seventh <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chabad", "link": "https://wikipedia.org/wiki/Chabad"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Canadian lawyer and politician, 27th Canadian Minister of Justice (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian priest and educator (d. 1970)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest and educator (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian priest and educator (d. 1970)", "links": [{"title": "<PERSON>phon<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American-Swedish discus thrower (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swedish discus thrower (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swedish discus thrower (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American baseball player and manager (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Appling\" title=\"<PERSON> Appling\"><PERSON></a>, American baseball player and manager (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ling\" title=\"<PERSON> Appling\"><PERSON></a>, American baseball player and manager (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Appling"}]}, {"year": "1908", "text": "<PERSON>, American actor and dancer (d. 2003)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian general, Victoria Cross recipient (d. 1980)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian general, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1910", "text": "<PERSON>, Brazilian spiritual medium (d. 2002)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian spiritual medium (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Xavier\" title=\"Chico Xavier\"><PERSON></a>, Brazilian spiritual medium (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chico_Xavier"}]}, {"year": "1914", "text": "<PERSON>, English actor (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Argentinian runner and soldier (d. 1981)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian runner and soldier (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>fo_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian runner and soldier (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Delfo_Cabrera"}]}, {"year": "1920", "text": "<PERSON>, Canadian lieutenant and civil servant (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and civil servant (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lieutenant and civil servant (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English animator and director (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English animator and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English animator and director (d. 2013)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1920", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1982)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American banker and politician, 9th United States Deputy Secretary of State (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State\" title=\"United States Deputy Secretary of State\">United States Deputy Secretary of State</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Deputy Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Deputy_Secretary_of_State"}]}, {"year": "1923", "text": "<PERSON>,  actress (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Scottish footballer, coach, and manager (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, coach, and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, coach, and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, English mathematician, psychologist, and author (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English mathematician, psychologist, and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English mathematician, psychologist, and author (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Mexican baseball player (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81vila\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81vila\" title=\"<PERSON>\"><PERSON></a>, Mexican baseball player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81vila"}]}, {"year": "1925", "text": "<PERSON>, Scottish author and screenwriter (d. 2008)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish author and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German radio and television host (d. 1987)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German radio and television host (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German radio and television host (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian race car driver (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Sri Lankan police officer and diplomat (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan police officer and diplomat (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan police officer and diplomat (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American boxer and soldier (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Basilio"}]}, {"year": "1927", "text": "<PERSON>, American soldier and politician, 11th United States Secretary of the Army (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Army\" title=\"United States Secretary of the Army\">United States Secretary of the Army</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Army", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Army"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American baseball player and sportscaster (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English author and critic (d. 1980)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American cardinal (d. 1996)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, French singer-songwriter, actor, and director (d. 1991)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, actor, and director (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter, actor, and director (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English-American radio host (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, English-American radio host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)\" title=\"<PERSON> (commentator)\"><PERSON></a>, English-American radio host (d. 2021)", "links": [{"title": "<PERSON> (commentator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(commentator)"}]}, {"year": "1928", "text": "<PERSON>, Northern Irish horticulturist and academic (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horticulturist)\" title=\"<PERSON> (horticulturist)\"><PERSON></a>, Northern Irish horticulturist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(horticulturist)\" title=\"<PERSON> (horticulturist)\"><PERSON></a>, Northern Irish horticulturist and academic (d. 2004)", "links": [{"title": "<PERSON> (horticulturist)", "link": "https://wikipedia.org/wiki/<PERSON>_(horticulturist)"}]}, {"year": "1929", "text": "<PERSON>, American poet and educator (d. 1999)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and educator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English actor", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON><PERSON>\"><PERSON><PERSON>-<PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON><PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American historian (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Russian javelin thrower (d. 1986)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Russian javelin thrower (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Russian javelin thrower (d. 1986)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1932", "text": "<PERSON>, American cardinal (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian sociologist and author (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Konr%C3%A1d\" title=\"György Konrád\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian sociologist and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Konr%C3%A1d\" title=\"György Konrád\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian sociologist and author (d. 2019)", "links": [{"title": "György <PERSON>", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Konr%C3%A1d"}]}, {"year": "1934", "text": "<PERSON>, American mathematician and theorist (d. 2007)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and theorist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English wrestler and actor (d. 1997)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler and actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler and actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American journalist and game show host (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and game show host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and game show host (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American sound engineer (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sound engineer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sound engineer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Austrian-born Israeli rabbi", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-born Israeli rabbi", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-born Israeli rabbi", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Serbian-Israeli race walker and engineer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Israeli race walker and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-Israeli race walker and engineer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>y"}]}, {"year": "1937", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Swedish 17th General of The Salvation Army (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish 17th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish 17th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1938", "text": "<PERSON>, American trumpet player and composer (d. 1961)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter (d. 1984)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American academic and diplomat, 18th United States National Security Advisor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anthony Lake\"><PERSON></a>, American academic and diplomat, 18th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anthony Lake\"><PERSON></a>, American academic and diplomat, 18th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States National Security Advisor", "link": "https://wikipedia.org/wiki/United_States_National_Security_Advisor"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Canadian journalist and politician, 27th Lieutenant Governor of Quebec", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist and politician, 27th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1940", "text": "<PERSON>, Canadian figure skater and coach", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_(figure_skater)"}]}, {"year": "1940", "text": "<PERSON>, English motorcycle racer (d. 1981)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American radio host", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON><PERSON>\">Dr. <PERSON></a>, American radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON>\" title=\"Dr. <PERSON>\">Dr. <PERSON></a>, American radio host", "links": [{"title": "Dr. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American country singer-songwriter", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ton\"><PERSON></a>, American country singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and pianist (d. 2016)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Indian-English actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-English actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Baron <PERSON>, South African-English admiral and politician, Lord Warden of the Cinque Ports (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, South African-English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, South African-English admiral and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (d. 2022)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Italian singer (d. 2007)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian singer (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>o"}]}, {"year": "1943", "text": "<PERSON>, American jazz guitarist (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz guitarist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz guitarist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Sr., Italian actor (d. 2021)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0to,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Italian actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0to,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, Italian actor (d. 2021)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A0to,_<PERSON>."}]}, {"year": "1944", "text": "<PERSON>, American football player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, French race car driver", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Guy_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>in"}]}, {"year": "1945", "text": "<PERSON>, American actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and coach", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and sportscaster (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American poet", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, New Zealand cricketer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English author and playwright (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Canadian guitarist and songwriter (d. 1997)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian guitarist and songwriter (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Mexican singer, songwriter and actress (d. 2025)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Pa<PERSON><PERSON>_la_del_Barrio\" title=\"Pa<PERSON><PERSON> la del Barrio\"><PERSON><PERSON><PERSON> del Barrio</a>, Mexican singer, songwriter and actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_la_del_Barrio\" title=\"Pa<PERSON><PERSON> la del Barrio\"><PERSON><PERSON><PERSON> del Barrio</a>, Mexican singer, songwriter and actress (d. 2025)", "links": [{"title": "Paquita la del Barrio", "link": "https://wikipedia.org/wiki/Paquita_la_del_Barrio"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Finnish writer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Tua_Forsstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tua_Forsstr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tua_Forsstr%C3%B6m"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American author and critic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Danish author and illustrator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>oa<PERSON>_<PERSON>\" title=\"<PERSON>oa<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oa<PERSON>_<PERSON>\" title=\"<PERSON>oa<PERSON>\"><PERSON><PERSON><PERSON></a>, Danish author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roald_Als"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Greek singer (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mi<PERSON>pan<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American journalist and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American-English radio and television host", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English radio and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English radio and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German footballer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Bernd_M%C3%<PERSON><PERSON>_(footballer,_born_1949)\" title=\"<PERSON><PERSON> (footballer, born 1949)\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernd_M%C3%<PERSON><PERSON>_(footballer,_born_1949)\" title=\"<PERSON><PERSON> (footballer, born 1949)\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1949)", "link": "https://wikipedia.org/wiki/Bernd_M%C3%BCller_(footballer,_born_1949)"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American drummer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_(drummer)"}]}, {"year": "1950", "text": "<PERSON>, American politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Lynn_Westmoreland\" title=\"Lynn Westmoreland\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynn_Westmoreland\" title=\"Lynn Westmoreland\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lynn_Westmoreland"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Japanese golfer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Swedish cyclist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Len<PERSON><PERSON> Fagerlund\"><PERSON><PERSON><PERSON></a>, Swedish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lennart Fagerlund\"><PERSON><PERSON><PERSON></a>, Swedish cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_F<PERSON>lund"}]}, {"year": "1952", "text": "<PERSON>, English race car driver (d. 2002)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American bass player and songwriter  (d. 2001)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Northern Irish lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, 20th and 21st-century U.S. Navy aviator (d. 2019)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 20th and 21st-century U.S. Navy aviator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 20th and 21st-century U.S. Navy aviator (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Moroccan Berber writer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan Berber writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan Berber writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2005)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American author and playwright (d. 2017)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)\" title=\"<PERSON> (comics)\"><PERSON></a>, American author and playwright (d. 2017)", "links": [{"title": "<PERSON> (comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comics)"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Northern Irish loyalist paramilitary", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a>, Northern Irish loyalist paramilitary", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)\" title=\"<PERSON> (loyalist)\"><PERSON></a>, Northern Irish loyalist paramilitary", "links": [{"title": "<PERSON> (loyalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(loyalist)"}]}, {"year": "1957", "text": "<PERSON>, English biologist and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American businessman (d. 2020)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Italian rugby player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American basketball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Italian runner", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1959", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French director and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Moroccan footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Jamaican-English sprinter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-English sprinter", "links": [{"title": "<PERSON><PERSON> Christie", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian race car driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Canadian journalist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>l\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jewell\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French director and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English director and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English engineer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American wrestler", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Jon<PERSON><PERSON> Sharkey\"><PERSON><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Jon<PERSON><PERSON> Sharkey\"><PERSON><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American victim of police brutality (d. 2012)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American victim of <a href=\"https://wikipedia.org/wiki/Police_brutality\" title=\"Police brutality\">police brutality</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American victim of <a href=\"https://wikipedia.org/wiki/Police_brutality\" title=\"Police brutality\">police brutality</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Police brutality", "link": "https://wikipedia.org/wiki/Police_brutality"}]}, {"year": "1966", "text": "<PERSON>, American football player and actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English international footballer and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English international footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American guitarist and songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Indian actor, director, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Souza_Neto\" class=\"mw-redirect\" title=\"<PERSON><PERSON> de Souza Neto\"><PERSON><PERSON>uza Neto</a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>uza_Neto\" class=\"mw-redirect\" title=\"<PERSON><PERSON> de Souza Neto\"><PERSON><PERSON>uza Neto</a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>uza Net<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>o"}]}, {"year": "1971", "text": "<PERSON>, English cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian tennis player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian choreographer and dancer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Remo_D%27Souza\" title=\"<PERSON><PERSON> D<PERSON>uza\"><PERSON><PERSON></a>, Indian choreographer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Remo_D%27Souza\" title=\"<PERSON><PERSON> D'Souza\"><PERSON><PERSON></a>, Indian choreographer and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Remo_D%27Souza"}]}, {"year": "1972", "text": "<PERSON>, American sprinter and hurdler (d. 2023)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Russian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Puerto Rican-American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Roselyn_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roselyn_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roselyn_S%C3%A1nchez"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish football manager and former player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Tayfun_Korkut\" title=\"Tayfun Korkut\"><PERSON>yfu<PERSON> Ko<PERSON></a>, Turkish football manager and former player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tayfun_Korkut\" title=\"Tayfun Korkut\"><PERSON>yfu<PERSON> Ko<PERSON>ut</a>, Turkish football manager and former player", "links": [{"title": "Tayfun Ko<PERSON>ut", "link": "https://wikipedia.org/wiki/Tayfun_<PERSON>ut"}]}, {"year": "1975", "text": "<PERSON>, American basketball player (d. 2015)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, German rower", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>cho<PERSON>-Stomporowski\" title=\"<PERSON><PERSON>utschow<PERSON>Stom<PERSON>owski\"><PERSON><PERSON></a>, German rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON>om<PERSON>owski\" title=\"<PERSON><PERSON>om<PERSON>owski\"><PERSON><PERSON></a>, German rower", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rutschow-<PERSON>owski"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian author and film producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ette\" title=\"<PERSON><PERSON>ette\"><PERSON><PERSON></a>, Canadian author and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ette\"><PERSON><PERSON></a>, Canadian author and film producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ette"}]}, {"year": "1975", "text": "<PERSON>, Chilean and American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean and American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean and American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Greek shot putter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek shot putter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, South African golfer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Swedish skier", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, German-Irish actor and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Irish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Irish actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Estonian lawyer and politician, Estonian Minister of Justice", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Estonia)\" class=\"mw-redirect\" title=\"Ministry of Justice (Estonia)\">Estonian Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Estonia)\" class=\"mw-redirect\" title=\"Ministry of Justice (Estonia)\">Estonian Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>kur"}, {"title": "Ministry of Justice (Estonia)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(Estonia)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Israeli singer and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_<PERSON><PERSON>i"}]}, {"year": "1980", "text": "<PERSON>, Scottish journalist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Scottish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Scottish journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1980", "text": "<PERSON>, Canadian director and screenwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American race car driver (d. 2004)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Wair<PERSON><PERSON>_Koopu\" title=\"Wairangi Koopu\"><PERSON><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wair<PERSON><PERSON>_Koopu\" title=\"Wairang<PERSON> Koopu\"><PERSON><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wair<PERSON><PERSON>_<PERSON>u"}]}, {"year": "1980", "text": "<PERSON>, Mexican international footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian stand-up comedian, television presenter and actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (comedian)\"><PERSON><PERSON><PERSON></a>, Indian stand-up comedian, television presenter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(comedian)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (comedian)\"><PERSON><PERSON><PERSON></a>, Indian stand-up comedian, television presenter and actor", "links": [{"title": "<PERSON><PERSON><PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(comedian)"}]}, {"year": "1982", "text": "<PERSON>, Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Spanish tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Ivorian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Ukrainian pole vaulter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Engin_Ats%C3%BCr\" title=\"Engin Atsür\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Engin_Ats%C3%BCr\" title=\"Engin Atsür\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Engin_Ats%C3%BCr"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Hungarian diver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/N%C3%B3<PERSON>_<PERSON>\" title=\"Nóra Barta\"><PERSON><PERSON><PERSON></a>, Hungarian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%B3ra_Bart<PERSON>\" title=\"Nóra Barta\"><PERSON><PERSON><PERSON></a>, Hungarian diver", "links": [{"title": "<PERSON>ó<PERSON>", "link": "https://wikipedia.org/wiki/N%C3%B3ra_Barta"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%A9my_Morel"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Moy%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Moy%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_Moy%C3%A1"}]}, {"year": "1985", "text": "<PERSON>, Zimbabwean-Scottish rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-Scottish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss figure skater", "html": "1985 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss figure skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Dutch footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Latvian basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andris_Biedri%C5%86%C5%A1"}]}, {"year": "1987", "text": "<PERSON>, Paraguayan footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/1987\" title=\"1987\">1987</a> - <a href=\"https://wikipedia.org/wiki/Pablo_C%C3%A9sar_Aguilar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1987\" title=\"1987\">1987</a> - <a href=\"https://wikipedia.org/wiki/Pablo_C%C3%A9sar_Aguilar\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "1987", "link": "https://wikipedia.org/wiki/1987"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_C%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish Professional Golfer, winner of the 2019 Open Championship and European Team Member for the 2021 and 2023 Ryder Cups", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Professional Golfer, winner of the <a href=\"https://wikipedia.org/wiki/2019_Open_Championship\" title=\"2019 Open Championship\">2019 Open Championship</a> and European Team Member for the <a href=\"https://wikipedia.org/wiki/2021_Ryder_Cup\" title=\"2021 Ryder Cup\">2021</a> and <a href=\"https://wikipedia.org/wiki/2023_Ryder_Cup\" title=\"2023 Ryder Cup\">2023</a> Ryder Cups", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Professional Golfer, winner of the <a href=\"https://wikipedia.org/wiki/2019_Open_Championship\" title=\"2019 Open Championship\">2019 Open Championship</a> and European Team Member for the <a href=\"https://wikipedia.org/wiki/2021_Ryder_Cup\" title=\"2021 Ryder Cup\">2021</a> and <a href=\"https://wikipedia.org/wiki/2023_Ryder_Cup\" title=\"2023 Ryder Cup\">2023</a> Ryder Cups", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2019 Open Championship", "link": "https://wikipedia.org/wiki/2019_Open_Championship"}, {"title": "2021 Ryder Cup", "link": "https://wikipedia.org/wiki/2021_Ryder_Cup"}, {"title": "2023 Ryder Cup", "link": "https://wikipedia.org/wiki/2023_Ryder_Cup"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian gymnast", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ye<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ye<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ye<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Bosnian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Miralem_Pjani%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miralem_Pjani%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miralem_Pjani%C4%87"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Amr_El_Solia\" title=\"Amr El Solia\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amr_El_Solia\" title=\"Amr El Solia\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "Amr El Solia", "link": "https://wikipedia.org/wiki/Amr_El_Solia"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Quavo\" title=\"Quavo\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quavo\" title=\"Quavo\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "Quavo", "link": "https://wikipedia.org/wiki/Quavo"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Trinidadian javelin thrower", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Kesh<PERSON>_Walcott\" title=\"Keshorn Walcott\"><PERSON><PERSON><PERSON></a>, Trinidadian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kesh<PERSON>_Walcott\" title=\"Keshorn Walcott\"><PERSON><PERSON><PERSON></a>, Trinidadian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>cott"}]}, {"year": "1993", "text": "<PERSON>, Argentine footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Cameroonian basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American soccer player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Cameroonian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>ana"}]}, {"year": "1997", "text": "<PERSON>, American race car driver", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American baseball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Riley\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Riley\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Spanish footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2000)\" title=\"<PERSON> (footballer, born 2000)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2000)\" title=\"<PERSON> (footballer, born 2000)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer, born 2000)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_2000)"}]}, {"year": "2002", "text": "<PERSON>, American actress", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Russian tennis player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Czech tennis player", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1"}]}], "Deaths": [{"year": "670", "text": "<PERSON> ibn <PERSON> the second Shia Imam (b. 624)", "html": "670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> the second Shia Imam (b. 624)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a> the second Shia Imam (b. 624)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "870", "text": "Æbbe the Younger, Frankish abbess", "html": "870 - <a href=\"https://wikipedia.org/wiki/%C3%86bbe_the_Younger\" title=\"Æbbe the Younger\">Æbbe the Younger</a>, Frankish abbess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86bbe_the_Younger\" title=\"<PERSON><PERSON> the Younger\"><PERSON><PERSON> the Younger</a>, Frankish abbess", "links": [{"title": "<PERSON><PERSON> the Younger", "link": "https://wikipedia.org/wiki/%C3%86bbe_the_Younger"}]}, {"year": "872", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish general", "html": "872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_al-Turki\" title=\"<PERSON><PERSON><PERSON><PERSON> al-Turki\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_al-Turki\" title=\"<PERSON><PERSON><PERSON><PERSON> al-Turki\"><PERSON><PERSON><PERSON><PERSON>Tu<PERSON></a>, Turkish general", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_al-Tu<PERSON>i"}]}, {"year": "968", "text": "<PERSON>, Chinese chancellor (b. 891)", "html": "968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chancellor (b. 891)", "links": [{"title": "Yuan <PERSON>hao", "link": "https://wikipedia.org/wiki/Yuan_<PERSON>"}]}, {"year": "991", "text": "<PERSON><PERSON>, Byzantine general", "html": "991 - <a href=\"https://wikipedia.org/wiki/Bardas_Skleros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Byzantine general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bardas_Skleros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Byzantine general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bardas_Skleros"}]}, {"year": "1118", "text": "<PERSON>, king of Jerusalem", "html": "1118 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> I</a>, king of Jerusalem", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> I</a>, king of Jerusalem", "links": [{"title": "<PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_I_of_Jerusalem"}]}, {"year": "1244", "text": "<PERSON>, Danish botanical and medical author", "html": "1244 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A6ng\" title=\"<PERSON>\"><PERSON></a>, Danish botanical and medical author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A6ng\" title=\"<PERSON>\"><PERSON></a>, Danish botanical and medical author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>estr%C3%A6ng"}]}, {"year": "1272", "text": "<PERSON>, 1st Earl of Cornwall, English husband of <PERSON><PERSON> of Provence (b. 1209)", "html": "1272 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Cornwall\"><PERSON>, 1st Earl of Cornwall</a>, English husband of <a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">Sanchia of Provence</a> (b. 1209)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall\" class=\"mw-redirect\" title=\"<PERSON>, 1st Earl of Cornwall\"><PERSON>, 1st Earl of Cornwall</a>, English husband of <a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">Sanchia of Provence</a> (b. 1209)", "links": [{"title": "<PERSON>, 1st Earl of Cornwall", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Cornwall"}, {"title": "Sanchia of Provence", "link": "https://wikipedia.org/wiki/Sanchia_of_Provence"}]}, {"year": "1335", "text": "<PERSON> of Bohemia (b. 1265)", "html": "1335 - <a href=\"https://wikipedia.org/wiki/Henry_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (b. 1265)", "links": [{"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/Henry_of_Bohemia"}]}, {"year": "1412", "text": "<PERSON><PERSON>, Spanish explorer and author", "html": "1412 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>%C3%A1lez_de_Clavijo\" title=\"<PERSON><PERSON>lavijo\"><PERSON><PERSON></a>, Spanish explorer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>%C3%A1lez_de_Clavijo\" title=\"<PERSON><PERSON>lavijo\"><PERSON><PERSON></a>, Spanish explorer and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ruy_Gonz%C3%<PERSON>lez_de_Clavijo"}]}, {"year": "1416", "text": "<PERSON>, king of Aragon (b. 1379)", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon\" title=\"<PERSON> of Aragon\"><PERSON></a>, king of Aragon (b. 1379)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON></a>, king of Aragon (b. 1379)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}]}, {"year": "1502", "text": "<PERSON>, prince of Wales (b. 1486)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON></a>, prince of Wales (b. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON></a>, prince of Wales (b. 1486)", "links": [{"title": "<PERSON>, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Wales"}]}, {"year": "1507", "text": "<PERSON> of Paola, Italian friar and saint, founded the Order of the Minims (b. 1416)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pa<PERSON>\" title=\"<PERSON> of Paola\"><PERSON> of Paola</a>, Italian friar and saint, founded the <a href=\"https://wikipedia.org/wiki/Minim_(religious_order)\" class=\"mw-redirect\" title=\"Minim (religious order)\">Order of the Minims</a> (b. 1416)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Paola\"><PERSON> of Paola</a>, Italian friar and saint, founded the <a href=\"https://wikipedia.org/wiki/Minim_(religious_order)\" class=\"mw-redirect\" title=\"Minim (religious order)\">Order of the Minims</a> (b. 1416)", "links": [{"title": "<PERSON> Paola", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>m (religious order)", "link": "https://wikipedia.org/wiki/Minim_(religious_order)"}]}, {"year": "1511", "text": "<PERSON>, Lord of Lippe, German nobleman (b. 1428)", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe\" title=\"<PERSON>, Lord of Lippe\"><PERSON>, Lord of Lippe</a>, German nobleman (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe\" title=\"<PERSON>, Lord of Lippe\"><PERSON>, Lord of Lippe</a>, German nobleman (b. 1428)", "links": [{"title": "<PERSON>, Lord of Lippe", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_of_Lippe"}]}, {"year": "1640", "text": "<PERSON><PERSON><PERSON>, Polish author and poet (b. 1595)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and poet (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author and poet (b. 1595)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, Holy Roman Emperor (b. 1608)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1608)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1657", "text": "<PERSON><PERSON><PERSON>, French priest, founded the Society of Saint-Sulpice (b. 1608)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Saint-Sulpice\" class=\"mw-redirect\" title=\"Society of Saint-Sulpice\">Society of Saint-Sulpice</a> (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Saint-Sulpice\" class=\"mw-redirect\" title=\"Society of Saint-Sulpice\">Society of Saint-Sulpice</a> (b. 1608)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Society of Saint-Sulpice", "link": "https://wikipedia.org/wiki/Society_of_Saint-Sulpice"}]}, {"year": "1672", "text": "<PERSON>, Filipino missionary and saint (b. 1654)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino missionary and saint (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino missionary and saint (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, Spanish Jesuit missionary (b. 1627)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_San_Vitores\" title=\"Diego Luis de San Vitores\"><PERSON></a>, Spanish Jesuit missionary (b. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Vitores\" title=\"Diego Luis de San Vitores\"><PERSON> Vitores</a>, Spanish Jesuit missionary (b. 1627)", "links": [{"title": "Diego <PERSON> San Vitores", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Vitor<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, English politician, Governor of the Province of Massachusetts Bay (b. 1647)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1647)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1647)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of the Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay"}]}, {"year": "1742", "text": "<PERSON>, Scottish physician and anatomist (b. 1675)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and anatomist (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, Scottish physician and anatomist (b. 1675)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>(physician)"}]}, {"year": "1747", "text": "<PERSON>, German-English botanist and mycologist (b. 1684)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English botanist and mycologist (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English botanist and mycologist (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, English historian and author (b. 1686)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, English general and politician, Governor of the Province of Massachusetts Bay (b. 1719)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay\" class=\"mw-redirect\" title=\"Governor of the Province of Massachusetts Bay\">Governor of the Province of Massachusetts Bay</a> (b. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of the Province of Massachusetts Bay", "link": "https://wikipedia.org/wiki/Governor_of_the_Province_of_Massachusetts_Bay"}]}, {"year": "1791", "text": "<PERSON><PERSON>, comte <PERSON>, French journalist and politician (b. 1749)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>,_comte_<PERSON>_Mira<PERSON>au\" title=\"<PERSON><PERSON>, comte de Mirabeau\"><PERSON><PERSON>, comte <PERSON></a>, French journalist and politician (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>,_comte_<PERSON>_Mira<PERSON>au\" title=\"<PERSON><PERSON>, comte de Mirabeau\"><PERSON><PERSON>, comte <PERSON>au</a>, French journalist and politician (b. 1749)", "links": [{"title": "<PERSON><PERSON>, comte <PERSON>au", "link": "https://wikipedia.org/wiki/Honor%C3%A<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, Jr., English engineer (b. 1761)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, English engineer (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, English engineer (b. 1761)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1803", "text": "Sir <PERSON>, 1st Baronet, Scottish judge and politician (b. 1721)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Scottish judge and politician (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Scottish judge and politician (b. 1721)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1817", "text": "<PERSON>, German author and academic (b. 1740)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and academic (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, German physician and educator (b. 1776)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and educator (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and educator (b. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Scottish admiral and politician (b. 1763)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish admiral and politician (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish admiral and politician (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON> <PERSON><PERSON>, American general (b. 1825)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/A._<PERSON><PERSON>_<PERSON>\" title=\"A. P. Hill\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._<PERSON>._<PERSON>\" title=\"A. P. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (b. 1825)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._P._Hill"}]}, {"year": "1872", "text": "<PERSON>, American painter and academic, invented the Morse code (b. 1791)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic, invented the <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a> (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic, invented the <a href=\"https://wikipedia.org/wiki/Morse_code\" title=\"Morse code\">Morse code</a> (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Morse code", "link": "https://wikipedia.org/wiki/Morse_code"}]}, {"year": "1891", "text": "<PERSON>, American lawyer and general (b. 1809)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and general (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Albert <PERSON>\"><PERSON></a>, American lawyer and general (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Greek playwright and politician, 249th Grand Vizier of the Ottoman Empire (b. 1823)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek playwright and politician, 249th <a href=\"https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Grand Vizier of the Ottoman Empire\">Grand Vizier of the Ottoman Empire</a> (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek playwright and politician, 249th <a href=\"https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Grand Vizier of the Ottoman Empire\">Grand Vizier of the Ottoman Empire</a> (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Grand Vizier of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Italian painter and academic (b. 1803)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and academic (b. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and academic (b. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1896", "text": "<PERSON>, American painter and academic (b. 1852)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, German author, poet, and translator, Nobel Prize laureate (b. 1830)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, and translator, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1917", "text": "<PERSON>, Welsh international rugby player (b. 1891)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international rugby player (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international rugby player (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Turkish colonel (b. 1883)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Osman\"><PERSON><PERSON></a>, Turkish colonel (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Osman\"><PERSON><PERSON></a>, Turkish colonel (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1868)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON> of Ethiopia (b. 1876)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Zewditu_I\" class=\"mw-redirect\" title=\"Zewditu I\">Zewditu I</a> of Ethiopia (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zewditu_I\" class=\"mw-redirect\" title=\"Zewditu I\">Zewditu I</a> of Ethiopia (b. 1876)", "links": [{"title": "Zewditu I", "link": "https://wikipedia.org/wiki/Zewditu_I"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian cricketer (b. 1872)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Ranjits<PERSON><PERSON><PERSON>\" title=\"Ranjits<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranjits<PERSON><PERSON><PERSON>\" title=\"Ranjits<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>ji"}]}, {"year": "1936", "text": "<PERSON>, French general (b. 1860)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Est<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Estienne\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8ne_Estienne"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, French novelist (b. 1862)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_Estauni%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French novelist (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89do<PERSON>_<PERSON>uni%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French novelist (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_E<PERSON>uni%C3%A9"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Turkish journalist, author, and poet (b. 1907)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and poet (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and poet (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German field marshal (b. 1885)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, US Air Force general (b. 1899)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, US Air Force general (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, US Air Force general (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON> <PERSON><PERSON>,  English novelist  (b. 1899)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>er\" title=\"C. S. Forester\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_Forester\" title=\"C. S. Forester\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist (b. 1899)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1972", "text": "<PERSON>, German general (b. 1884)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese martial artist and educator (b. 1887)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist and educator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese martial artist and educator (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French banker and politician, 19th President of France (b. 1911)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French banker and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French banker and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1977", "text": "<PERSON>, German academic and politician (b. 1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German academic and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German academic and politician (b. 1907)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1987", "text": "<PERSON>, American drummer, songwriter, and bandleader (b. 1917)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American drummer, songwriter, and bandleader (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American drummer, songwriter, and bandleader (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Greek singer (b. 1939)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek singer (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Spanish footballer and manager (b. 1954)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1954)\" title=\"<PERSON><PERSON> (footballer, born 1954)\"><PERSON><PERSON></a>, Spanish footballer and manager (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1954)\" title=\"<PERSON><PERSON> (footballer, born 1954)\"><PERSON><PERSON></a>, Spanish footballer and manager (b. 1954)", "links": [{"title": "<PERSON><PERSON> (footballer, born 1954)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1954)"}]}, {"year": "1992", "text": "<PERSON>, Dutch politician (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actress, consumer advocate, game show panelist, television journalist and television personality (b. 1916)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, consumer advocate, game show panelist, television journalist and television personality (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, consumer advocate, game show panelist, television journalist and television personality (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, British historian and philanthropist (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian and philanthropist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian and philanthropist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Swedish physicist and engineer, Nobel Prize laureate (b. 1908)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>nes_Alfv%C3%A9n\" title=\"Hannes Alfvén\"><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alfv%C3%A9n\" title=\"<PERSON><PERSON> Alfvén\"><PERSON><PERSON></a>, Swedish physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hannes_Alfv%C3%A9n"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Japanese director and producer (b. 1910)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and producer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director and producer (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American-German singer-songwriter (b. 1965)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pilat<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German singer-songwriter (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pilat<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German singer-songwriter (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rob_Pilatus"}]}, {"year": "2001", "text": "<PERSON>, Canadian sculptor and painter (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and painter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Filipino composer and songwriter (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino composer and songwriter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino composer and songwriter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>io"}]}, {"year": "2002", "text": "<PERSON>, American engineer and author (b. 1910)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and author (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American singer-songwriter (b. 1942)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Greek computer scientist, engineer, and academic (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek computer scientist, engineer, and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek computer scientist, engineer, and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American crime novelist (b. 1926)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lillian_O%27Donnell"}]}, {"year": "2005", "text": "<PERSON> (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON></a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Guyanese anthologist and diplomat (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Lloyd_<PERSON>war\" title=\"Lloyd Searwar\"><PERSON></a>, Guyanese anthologist and diplomat (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lloyd_Searwar\" title=\"Lloyd Searwar\"><PERSON></a>, Guyanese anthologist and diplomat (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lloyd_<PERSON>war"}]}, {"year": "2007", "text": "<PERSON>, American astronomer and academic (b. 1910)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Turkish World War I veteran (b. 1898)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON>_<PERSON>\" title=\"Yaku<PERSON>tar\"><PERSON><PERSON><PERSON></a>, Turkish World War I veteran (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish World War I veteran (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Canadian bishop (b. 1911)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian bishop (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American saxophonist and flute player (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American wrestler (b. 1970)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American businessman and philanthropist (b. 1918)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Venezuelan captain and politician (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Aguilarte\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan captain and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Aguilarte\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan captain and politician (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Aguilarte"}]}, {"year": "2012", "text": "<PERSON>, American-Mexican sculptor and illustrator (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Mexican sculptor and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Mexican sculptor and illustrator (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American graphic designer and academic (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American graphic designer and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American graphic designer and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French author and illustrator (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, French author and illustrator (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, French author and illustrator (b. 1931)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Spanish director, screenwriter, producer, and actor (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Franco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish director, screenwriter, producer, and actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Franco\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish director, screenwriter, producer, and actor (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Franco"}]}, {"year": "2013", "text": "<PERSON>, Irish-American actor (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Shea\" title=\"<PERSON>\"><PERSON></a>, Irish-American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milo_O%27Shea"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Swiss author and playwright (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/U<PERSON>_Widmer\" title=\"<PERSON><PERSON> Widmer\"><PERSON><PERSON></a>, Swiss author and playwright (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON>_Widmer\" title=\"<PERSON><PERSON> Widmer\"><PERSON><PERSON></a>, Swiss author and playwright (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_Widmer"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Portuguese actor, director, producer, and screenwriter (b. 1908)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actor, director, producer, and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese actor, director, producer, and screenwriter (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American pastor and author (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Belgian businessman and politician, Governor of Limburg (b. 1954)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Limburg\" title=\"Governor of Limburg\">Governor of Limburg</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Limburg\" title=\"Governor of Limburg\">Governor of Limburg</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Limburg", "link": "https://wikipedia.org/wiki/Governor_of_Limburg"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian comic book artist and illustrator (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian comic book artist and illustrator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian comic book artist and illustrator (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rri"}]}, {"year": "2016", "text": "<PERSON>, Armenian sergeant (b. 1996)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian sergeant (b. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian sergeant (b. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Mexican actress (b. 1937)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Alma_Delia_Fuentes\" title=\"Alma Delia Fuentes\"><PERSON></a>, Mexican actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alma_Delia_Fuentes\" title=\"Alma Delia Fuentes\"><PERSON></a>, Mexican actress (b. 1937)", "links": [{"title": "Alma Delia Fuentes", "link": "https://wikipedia.org/wiki/Alma_Delia_Fuentes"}]}, {"year": "2021", "text": "<PERSON>, British composer (b. 1952)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British composer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, American actress and comedian (b. 1928)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and comedian (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American country music songwriter and record producer (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter and record producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter and record producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Guadeloupean novelist, critic, and playwright (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Mary<PERSON>_Cond%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guadeloupean novelist, critic, and playwright (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Guadeloupean novelist, critic, and playwright (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maryse_Cond%C3%A9"}]}, {"year": "2024", "text": "<PERSON>, American playwright (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American attorney and baseball executive (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and baseball executive (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and baseball executive (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American poet (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet (b. 1941)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>(poet)"}]}, {"year": "2024", "text": "<PERSON>, Venezuelan supercentenarian (b. 1909)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan supercentenarian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_P%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan supercentenarian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_P%C3%A9rez"}]}]}}