{"date": "July 19", "url": "https://wikipedia.org/wiki/July_19", "data": {"Events": [{"year": "64", "text": "The Great Fire of Rome causes widespread devastation and rages on for six days, destroying half of the city.", "html": "64 - AD 64 - The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Rome\" title=\"Great Fire of Rome\">Great Fire of Rome</a> causes widespread devastation and rages on for six days, destroying half of the city.", "no_year_html": "AD 64 - The <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Rome\" title=\"Great Fire of Rome\">Great Fire of Rome</a> causes widespread devastation and rages on for six days, destroying half of the city.", "links": [{"title": "Great Fire of Rome", "link": "https://wikipedia.org/wiki/Great_Fire_of_Rome"}]}, {"year": "484", "text": "<PERSON><PERSON>, Roman usurper, is crowned Eastern emperor at Tarsus (modern Turkey). He is recognized in Antioch and makes it his capital.", "html": "484 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON> (usurper)\"><PERSON><PERSON></a>, Roman <a href=\"https://wikipedia.org/wiki/Usurper\" title=\"Usurper\">usurper</a>, is crowned Eastern emperor at <a href=\"https://wikipedia.org/wiki/Tarsus,_Mersin\" title=\"Tarsus, Mersin\">Tarsus</a> (modern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>). He is recognized in <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> and makes it his capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON> (usurper)\"><PERSON><PERSON></a>, Roman <a href=\"https://wikipedia.org/wiki/Usurper\" title=\"Usurper\">usurper</a>, is crowned Eastern emperor at <a href=\"https://wikipedia.org/wiki/Tarsus,_Mersin\" title=\"Tarsus, Mersin\">Tarsus</a> (modern <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>). He is recognized in <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> and makes it his capital.", "links": [{"title": "<PERSON><PERSON> (usurper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(usurper)"}, {"title": "Usurper", "link": "https://wikipedia.org/wiki/Usurper"}, {"title": "Tarsus, Mersin", "link": "https://wikipedia.org/wiki/Ta<PERSON>us,_Mersin"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}]}, {"year": "711", "text": "Umayyad conquest of Hispania: Battle of Guadalete: Umayyad forces under <PERSON><PERSON><PERSON> ibn <PERSON> defeat the Visigoths led by King <PERSON><PERSON>.", "html": "711 - <a href=\"https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania\" class=\"mw-redirect\" title=\"Umayyad conquest of Hispania\">Umayyad conquest of Hispania</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalete\" title=\"Battle of Guadalete\">Battle of Guadalete</a>: <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>d\"><PERSON><PERSON><PERSON> ibn <PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> led by King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania\" class=\"mw-redirect\" title=\"Umayyad conquest of Hispania\">Umayyad conquest of Hispania</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Guadalete\" title=\"Battle of Guadalete\">Battle of Guadalete</a>: <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> forces under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>d\"><PERSON><PERSON><PERSON> ibn <PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> led by King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Umayyad conquest of Hispania", "link": "https://wikipedia.org/wiki/Umayyad_conquest_of_Hispania"}, {"title": "Battle of Guadalete", "link": "https://wikipedia.org/wiki/Battle_of_Guadalete"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}, {"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roderic"}]}, {"year": "939", "text": "Battle of Simancas: King <PERSON><PERSON> II of León defeats the Moorish army under <PERSON><PERSON><PERSON> III near the city of Simancas.", "html": "939 - <a href=\"https://wikipedia.org/wiki/Battle_of_Simancas\" title=\"Battle of Simancas\">Battle of Simancas</a>: King <a href=\"https://wikipedia.org/wiki/Ram<PERSON>_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II of León</a> defeats the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> army under <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON> III</a> near the city of <a href=\"https://wikipedia.org/wiki/Simancas\" title=\"Simancas\">Simancas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Simancas\" title=\"Battle of Simancas\">Battle of Simancas</a>: King <a href=\"https://wikipedia.org/wiki/Ram<PERSON>_II_of_Le%C3%B3n\" title=\"<PERSON><PERSON> II of León\"><PERSON><PERSON> II of León</a> defeats the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moorish</a> army under <PERSON><PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III\"><PERSON><PERSON><PERSON><PERSON> III</a> near the city of <a href=\"https://wikipedia.org/wiki/Simancas\" title=\"Simancas\">Simancas</a>.", "links": [{"title": "Battle of Simancas", "link": "https://wikipedia.org/wiki/Battle_of_Simancas"}, {"title": "Ramiro II of León", "link": "https://wikipedia.org/wiki/Ramiro_II_of_Le%C3%B3n"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Simancas", "link": "https://wikipedia.org/wiki/Simancas"}]}, {"year": "998", "text": "Arab-Byzantine wars: Battle of Apamea: Fatimids defeat a Byzantine army near Apamea.", "html": "998 - <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Apamea\" title=\"Battle of Apamea\">Battle of Apamea</a>: <a href=\"https://wikipedia.org/wiki/Fatimids\" class=\"mw-redirect\" title=\"Fatimids\">Fatimids</a> defeat a <a href=\"https://wikipedia.org/wiki/Byzantine\" class=\"mw-redirect\" title=\"Byzantine\">Byzantine</a> army near <a href=\"https://wikipedia.org/wiki/Apamea,_Syria\" title=\"Apamea, Syria\">Apamea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Apamea\" title=\"Battle of Apamea\">Battle of Apamea</a>: <a href=\"https://wikipedia.org/wiki/Fatimids\" class=\"mw-redirect\" title=\"Fatimids\">Fatimids</a> defeat a <a href=\"https://wikipedia.org/wiki/Byzantine\" class=\"mw-redirect\" title=\"Byzantine\">Byzantine</a> army near <a href=\"https://wikipedia.org/wiki/Apamea,_Syria\" title=\"Apamea, Syria\">Apamea</a>.", "links": [{"title": "Arab-Byzantine wars", "link": "https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars"}, {"title": "Battle of Apamea", "link": "https://wikipedia.org/wiki/Battle_of_Apamea"}, {"title": "Fatimids", "link": "https://wikipedia.org/wiki/Fatimids"}, {"title": "Byzantine", "link": "https://wikipedia.org/wiki/Byzantine"}, {"title": "Apamea, Syria", "link": "https://wikipedia.org/wiki/Apamea,_Syria"}]}, {"year": "1333", "text": "Wars of Scottish Independence: Battle of Halidon Hill: The English win a decisive victory over the Scots.", "html": "1333 - <a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Halidon_Hill\" title=\"Battle of Halidon Hill\">Battle of Halidon Hill</a>: The English win a decisive victory over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scots</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_Scottish_Independence\" title=\"Wars of Scottish Independence\">Wars of Scottish Independence</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Halidon_Hill\" title=\"Battle of Halidon Hill\">Battle of Halidon Hill</a>: The English win a decisive victory over the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scots</a>.", "links": [{"title": "Wars of Scottish Independence", "link": "https://wikipedia.org/wiki/Wars_of_Scottish_Independence"}, {"title": "Battle of Halidon Hill", "link": "https://wikipedia.org/wiki/Battle_of_Halidon_Hill"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}]}, {"year": "1544", "text": "Italian War of 1542-46: The first Siege of Boulogne begins.", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346\" class=\"mw-redirect\" title=\"Italian War of 1542-46\">Italian War of 1542-46</a>: The first <a href=\"https://wikipedia.org/wiki/Sieges_of_Boulogne_(1544%E2%80%9346)\" class=\"mw-redirect\" title=\"Sieges of Boulogne (1544-46)\">Siege of Boulogne</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346\" class=\"mw-redirect\" title=\"Italian War of 1542-46\">Italian War of 1542-46</a>: The first <a href=\"https://wikipedia.org/wiki/Sieges_of_Boulogne_(1544%E2%80%9346)\" class=\"mw-redirect\" title=\"Sieges of Boulogne (1544-46)\">Siege of Boulogne</a> begins.", "links": [{"title": "Italian War of 1542-46", "link": "https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346"}, {"title": "Sieges of Boulogne (1544-46)", "link": "https://wikipedia.org/wiki/Sieges_of_Boulogne_(1544%E2%80%9346)"}]}, {"year": "1545", "text": "The Tudor warship Mary Rose sinks off Portsmouth; in 1982 the wreck is salvaged in one of the most complex and expensive projects in the history of maritime archaeology.", "html": "1545 - The <a href=\"https://wikipedia.org/wiki/Tudor_period\" title=\"Tudor period\">Tudor</a> warship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> sinks off <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a>; in 1982 the wreck is salvaged in one of the most complex and expensive projects in the history of maritime archaeology.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tudor_period\" title=\"Tudor period\">Tudor</a> warship <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> sinks off <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a>; in 1982 the wreck is salvaged in one of the most complex and expensive projects in the history of maritime archaeology.", "links": [{"title": "Tudor period", "link": "https://wikipedia.org/wiki/Tudor_period"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Portsmouth", "link": "https://wikipedia.org/wiki/Portsmouth"}]}, {"year": "1553", "text": "The attempt to install <PERSON> as Queen of England   collapses after only nine days.", "html": "1553 - The attempt to install <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a> as <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">Queen of England</a> collapses after only nine days.", "no_year_html": "The attempt to install <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a> as <a href=\"https://wikipedia.org/wiki/Queen_regnant\" title=\"Queen regnant\">Queen of England</a> collapses after only nine days.", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Queen regnant", "link": "https://wikipedia.org/wiki/Queen_regnant"}]}, {"year": "1588", "text": "Anglo-Spanish War: Battle of Gravelines: The Spanish Armada is sighted in the English Channel.", "html": "1588 - <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Gravelines_(1588)\" class=\"mw-redirect\" title=\"Battle of Gravelines (1588)\">Battle of Gravelines</a>: The <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> is sighted in the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)\" title=\"Anglo-Spanish War (1585-1604)\">Anglo-Spanish War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Gravelines_(1588)\" class=\"mw-redirect\" title=\"Battle of Gravelines (1588)\">Battle of Gravelines</a>: The <a href=\"https://wikipedia.org/wiki/Spanish_Armada\" title=\"Spanish Armada\">Spanish Armada</a> is sighted in the <a href=\"https://wikipedia.org/wiki/English_Channel\" title=\"English Channel\">English Channel</a>.", "links": [{"title": "Anglo-Spanish War (1585-1604)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1585%E2%80%931604)"}, {"title": "Battle of Gravelines (1588)", "link": "https://wikipedia.org/wiki/Battle_of_Gravelines_(1588)"}, {"title": "Spanish Armada", "link": "https://wikipedia.org/wiki/Spanish_Armada"}, {"title": "English Channel", "link": "https://wikipedia.org/wiki/English_Channel"}]}, {"year": "1701", "text": "Representatives of the Iroquois Confederacy sign the Nanfan Treaty, ceding a large territory north of the Ohio River to England.", "html": "1701 - Representatives of the <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois Confederacy</a> sign the <a href=\"https://wikipedia.org/wiki/Nanfan_Treaty\" title=\"Nanfan Treaty\">Nanfan Treaty</a>, ceding a large territory north of the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> to <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "Representatives of the <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois Confederacy</a> sign the <a href=\"https://wikipedia.org/wiki/Nanfan_Treaty\" title=\"Nanfan Treaty\">Nanfan Treaty</a>, ceding a large territory north of the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a> to <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "Iroquois", "link": "https://wikipedia.org/wiki/Iroquois"}, {"title": "Nanfan Treaty", "link": "https://wikipedia.org/wiki/Nanfan_Treaty"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1702", "text": "Great Northern War: A numerically superior Polish-Saxon army of <PERSON> the Strong, operating from an advantageous defensive position, is defeated by a Swedish army half its size under the command of King <PERSON> in the Battle of Klissow.", "html": "1702 - <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: A numerically superior Polish-Saxon army of <a href=\"https://wikipedia.org/wiki/Augustus_II_the_Strong\" title=\"Augustus II the Strong\">Augustus II the Strong</a>, operating from an advantageous defensive position, is defeated by a Swedish army half its size under the command of King <a href=\"https://wikipedia.org/wiki/<PERSON>_XII_of_Sweden\" title=\"Charles XII of Sweden\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Klissow\" class=\"mw-redirect\" title=\"Battle of Klissow\">Battle of Klissow</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>: A numerically superior Polish-Saxon army of <a href=\"https://wikipedia.org/wiki/<PERSON>_II_the_Strong\" title=\"Augustus II the Strong\">Augustus II the Strong</a>, operating from an advantageous defensive position, is defeated by a Swedish army half its size under the command of King <a href=\"https://wikipedia.org/wiki/<PERSON>_XII_of_Sweden\" title=\"Charles XII of Sweden\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Klissow\" class=\"mw-redirect\" title=\"Battle of Klissow\">Battle of Klissow</a>.", "links": [{"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "<PERSON> the Strong", "link": "https://wikipedia.org/wiki/<PERSON>_II_the_Strong"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Battle of Klissow", "link": "https://wikipedia.org/wiki/Battle_of_Klissow"}]}, {"year": "1817", "text": "Unsuccessful in his attempt to conquer the Kingdom of Hawaiʻi for the Russian-American Company, <PERSON> is forced to admit defeat and leave Kauaʻi.", "html": "1817 - Unsuccessful in his <a href=\"https://wikipedia.org/wiki/Sch%C3%A4ffer_affair\" title=\"Schäffer affair\">attempt to conquer</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaiʻi</a> for the <a href=\"https://wikipedia.org/wiki/Russian-American_Company\" title=\"Russian-American Company\">Russian-American Company</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%A4ffer\" title=\"<PERSON>\"><PERSON></a> is forced to admit defeat and leave <a href=\"https://wikipedia.org/wiki/Kauai\" title=\"Kauai\">Kauaʻi</a>.", "no_year_html": "Unsuccessful in his <a href=\"https://wikipedia.org/wiki/Sch%C3%A4ffer_affair\" title=\"Schäffer affair\">attempt to conquer</a> the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaiʻi</a> for the <a href=\"https://wikipedia.org/wiki/Russian-American_Company\" title=\"Russian-American Company\">Russian-American Company</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%A4ffer\" title=\"<PERSON>\"><PERSON></a> is forced to admit defeat and leave <a href=\"https://wikipedia.org/wiki/Kauai\" title=\"Kauai\">Kauaʻi</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> affair", "link": "https://wikipedia.org/wiki/Sch%C3%A4ffer_affair"}, {"title": "Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Kingdom_of_Hawaii"}, {"title": "Russian-American Company", "link": "https://wikipedia.org/wiki/Russian-American_Company"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4ffer"}, {"title": "Kauai", "link": "https://wikipedia.org/wiki/Kauai"}]}, {"year": "1821", "text": "Coronation of <PERSON> of the United Kingdom.", "html": "1821 - Coronation of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"George IV of the United Kingdom\"><PERSON> of the United Kingdom</a>.", "no_year_html": "Coronation of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom\" class=\"mw-redirect\" title=\"<PERSON> IV of the United Kingdom\"><PERSON> of the United Kingdom</a>.", "links": [{"title": "<PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_the_United_Kingdom"}]}, {"year": "1832", "text": "The British Medical Association is founded as the Provincial Medical and Surgical Association by Sir <PERSON> at a meeting in the Board Room of the Worcester Infirmary.", "html": "1832 - The <a href=\"https://wikipedia.org/wiki/British_Medical_Association\" title=\"British Medical Association\">British Medical Association</a> is founded as the Provincial Medical and Surgical Association by Sir <a href=\"https://wikipedia.org/wiki/<PERSON>(English_physician)\" title=\"<PERSON> (English physician)\"><PERSON></a> at a meeting in the Board Room of the <a href=\"https://wikipedia.org/wiki/Worcester,_England\" title=\"Worcester, England\">Worcester</a> Infirmary.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/British_Medical_Association\" title=\"British Medical Association\">British Medical Association</a> is founded as the Provincial Medical and Surgical Association by Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_(English_physician)\" title=\"<PERSON> (English physician)\"><PERSON></a> at a meeting in the Board Room of the <a href=\"https://wikipedia.org/wiki/Worcester,_England\" title=\"Worcester, England\">Worcester</a> Infirmary.", "links": [{"title": "British Medical Association", "link": "https://wikipedia.org/wiki/British_Medical_Association"}, {"title": "<PERSON> (English physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_physician)"}, {"title": "Worcester, England", "link": "https://wikipedia.org/wiki/Worcester,_England"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON>'s steamship the SS Great Britain is launched, becoming the first ocean-going craft with an iron hull and screw propeller, becoming the largest vessel afloat in the world.", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Isambard_Kingdom_Brunel\" title=\"Isambard Kingdom Brunel\"><PERSON><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamship</a> the <a href=\"https://wikipedia.org/wiki/SS_Great_Britain\" title=\"SS Great Britain\">SS <i>Great Britain</i></a> is launched, becoming the first ocean-going craft with an iron hull and <a href=\"https://wikipedia.org/wiki/Screw_propeller\" class=\"mw-redirect\" title=\"Screw propeller\">screw propeller</a>, becoming the largest vessel afloat in the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isambard_Kingdom_Brunel\" title=\"Isambard Kingdom Brunel\"><PERSON><PERSON><PERSON>'s</a> <a href=\"https://wikipedia.org/wiki/Steamboat\" title=\"Steamboat\">steamship</a> the <a href=\"https://wikipedia.org/wiki/SS_Great_Britain\" title=\"SS Great Britain\">SS <i>Great Britain</i></a> is launched, becoming the first ocean-going craft with an iron hull and <a href=\"https://wikipedia.org/wiki/Screw_propeller\" class=\"mw-redirect\" title=\"Screw propeller\">screw propeller</a>, becoming the largest vessel afloat in the world.", "links": [{"title": "Isambard Kingdom Brunel", "link": "https://wikipedia.org/wiki/Isambard_Kingdom_Brunel"}, {"title": "Steamboat", "link": "https://wikipedia.org/wiki/Steamboat"}, {"title": "SS Great Britain", "link": "https://wikipedia.org/wiki/SS_Great_Britain"}, {"title": "Screw propeller", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1845", "text": "Great New York City Fire of 1845: The last great fire to affect Manhattan begins early in the morning and is subdued that afternoon. The fire kills four firefighters and 26 civilians and destroys 345 buildings.", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Great_New_York_City_Fire_of_1845\" title=\"Great New York City Fire of 1845\">Great New York City Fire of 1845</a>: The last great fire to affect <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> begins early in the morning and is subdued that afternoon. The fire kills four firefighters and 26 civilians and destroys 345 buildings.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_New_York_City_Fire_of_1845\" title=\"Great New York City Fire of 1845\">Great New York City Fire of 1845</a>: The last great fire to affect <a href=\"https://wikipedia.org/wiki/Manhattan\" title=\"Manhattan\">Manhattan</a> begins early in the morning and is subdued that afternoon. The fire kills four firefighters and 26 civilians and destroys 345 buildings.", "links": [{"title": "Great New York City Fire of 1845", "link": "https://wikipedia.org/wiki/Great_New_York_City_Fire_of_1845"}, {"title": "Manhattan", "link": "https://wikipedia.org/wiki/Manhattan"}]}, {"year": "1848", "text": "Women's rights: A two-day Women's Rights Convention opens in Seneca Falls, New York.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: A two-day <a href=\"https://wikipedia.org/wiki/Seneca_Falls_Convention\" title=\"Seneca Falls Convention\">Women's Rights Convention</a> opens in <a href=\"https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York\" class=\"mw-redirect\" title=\"Seneca Falls (hamlet), New York\">Seneca Falls, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Women%27s_rights\" title=\"Women's rights\">Women's rights</a>: A two-day <a href=\"https://wikipedia.org/wiki/Seneca_Falls_Convention\" title=\"Seneca Falls Convention\">Women's Rights Convention</a> opens in <a href=\"https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York\" class=\"mw-redirect\" title=\"Seneca Falls (hamlet), New York\">Seneca Falls, New York</a>.", "links": [{"title": "Women's rights", "link": "https://wikipedia.org/wiki/Women%27s_rights"}, {"title": "Seneca Falls Convention", "link": "https://wikipedia.org/wiki/Seneca_Falls_Convention"}, {"title": "Seneca Falls (hamlet), New York", "link": "https://wikipedia.org/wiki/Seneca_Falls_(hamlet),_New_York"}]}, {"year": "1863", "text": "American Civil War: <PERSON>'s Raid: At Buffington Island in Ohio, Confederate General <PERSON>'s raid into the north is mostly thwarted when a large group of his men are captured while trying to escape across the Ohio River.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Morgan%27s_Raid\" title=\"<PERSON>'s Raid\"><PERSON>'s Raid</a>: At <a href=\"https://wikipedia.org/wiki/Buffington_Island\" title=\"Buffington Island\">Buffington Island</a> in <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s raid into the north is mostly thwarted when a large group of his men are <a href=\"https://wikipedia.org/wiki/Battle_of_Buffington_Island\" title=\"Battle of Buffington Island\">captured</a> while trying to escape across the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Morgan%27s_Raid\" title=\"<PERSON>'s Raid\"><PERSON>'s Raid</a>: At <a href=\"https://wikipedia.org/wiki/Buffington_Island\" title=\"Buffington Island\">Buffington Island</a> in <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s raid into the north is mostly thwarted when a large group of his men are <a href=\"https://wikipedia.org/wiki/Battle_of_Buffington_Island\" title=\"Battle of Buffington Island\">captured</a> while trying to escape across the <a href=\"https://wikipedia.org/wiki/Ohio_River\" title=\"Ohio River\">Ohio River</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Morgan's Raid", "link": "https://wikipedia.org/wiki/Morgan%27s_Raid"}, {"title": "Buffington Island", "link": "https://wikipedia.org/wiki/Buffington_Island"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Buffington Island", "link": "https://wikipedia.org/wiki/Battle_of_Buffington_Island"}, {"title": "Ohio River", "link": "https://wikipedia.org/wiki/Ohio_River"}]}, {"year": "1864", "text": "Taiping Rebellion: Third Battle of Nanking: The Qing dynasty finally defeats the Taiping Heavenly Kingdom.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Nanking\" class=\"mw-redirect\" title=\"Third Battle of Nanking\">Third Battle of Nanking</a>: The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> finally defeats the <a href=\"https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom\" title=\"Taiping Heavenly Kingdom\">Taiping Heavenly Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Third_Battle_of_Nanking\" class=\"mw-redirect\" title=\"Third Battle of Nanking\">Third Battle of Nanking</a>: The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> finally defeats the <a href=\"https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom\" title=\"Taiping Heavenly Kingdom\">Taiping Heavenly Kingdom</a>.", "links": [{"title": "Taiping Rebellion", "link": "https://wikipedia.org/wiki/Taiping_Rebellion"}, {"title": "Third Battle of Nanking", "link": "https://wikipedia.org/wiki/Third_Battle_of_Nanking"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Taiping Heavenly Kingdom", "link": "https://wikipedia.org/wiki/Taiping_Heavenly_Kingdom"}]}, {"year": "1870", "text": "Franco-Prussian War: France declares war on Prussia.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: France declares war on <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: France declares war on <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a>.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1900", "text": "The first line of the Paris Métro opens for operation.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Paris_M%C3%A9tro_Line_1\" title=\"Paris Métro Line 1\">first line</a> of the <a href=\"https://wikipedia.org/wiki/Paris_M%C3%A9tro\" title=\"Paris Métro\">Paris Métro</a> opens for operation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paris_M%C3%A9tro_Line_1\" title=\"Paris Métro Line 1\">first line</a> of the <a href=\"https://wikipedia.org/wiki/Paris_M%C3%A9tro\" title=\"Paris Métro\">Paris Métro</a> opens for operation.", "links": [{"title": "Paris Métro Line 1", "link": "https://wikipedia.org/wiki/Paris_M%C3%A9tro_Line_1"}, {"title": "Paris Métro", "link": "https://wikipedia.org/wiki/Paris_M%C3%A9tro"}]}, {"year": "1903", "text": "<PERSON> wins the first Tour de France.", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1903_Tour_de_France\" title=\"1903 Tour de France\">first Tour de France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/1903_Tour_de_France\" title=\"1903 Tour de France\">first Tour de France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1903 Tour de France", "link": "https://wikipedia.org/wiki/1903_Tour_de_France"}]}, {"year": "1916", "text": "World War I: Battle of Fromelles: British and Australian troops attack German trenches as part of the Battle of the Somme.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fromelles\" class=\"mw-redirect\" title=\"Battle of Fromelles\">Battle of Fromelles</a>: <a href=\"https://wikipedia.org/wiki/61st_(2nd_South_Midland)_Division\" title=\"61st (2nd South Midland) Division\">British</a> and <a href=\"https://wikipedia.org/wiki/First_Australian_Imperial_Force\" title=\"First Australian Imperial Force\">Australian</a> troops attack <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> trenches as part of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Fromelles\" class=\"mw-redirect\" title=\"Battle of Fromelles\">Battle of Fromelles</a>: <a href=\"https://wikipedia.org/wiki/61st_(2nd_South_Midland)_Division\" title=\"61st (2nd South Midland) Division\">British</a> and <a href=\"https://wikipedia.org/wiki/First_Australian_Imperial_Force\" title=\"First Australian Imperial Force\">Australian</a> troops attack <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> trenches as part of the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Somme\" title=\"Battle of the Somme\">Battle of the Somme</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Fromelles", "link": "https://wikipedia.org/wiki/Battle_of_Fromelles"}, {"title": "61st (2nd South Midland) Division", "link": "https://wikipedia.org/wiki/61st_(2nd_South_Midland)_Division"}, {"title": "First Australian Imperial Force", "link": "https://wikipedia.org/wiki/First_Australian_Imperial_Force"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Battle of the Somme", "link": "https://wikipedia.org/wiki/Battle_of_the_Somme"}]}, {"year": "1934", "text": "The rigid airship <PERSON> Macon surprised the USS Houston near Clipperton Island with a mail delivery for President <PERSON>, demonstrating its potential for tracking ships at sea.", "html": "1934 - The rigid airship <a href=\"https://wikipedia.org/wiki/USS_Macon_(ZRS-5)\" title=\"USS Macon (ZRS-5)\">USS <i>Macon</i></a> surprised the <a href=\"https://wikipedia.org/wiki/USS_Houston_(CA-30)\" title=\"USS Houston (CA-30)\">USS <i>Houston</i></a> near <a href=\"https://wikipedia.org/wiki/Clipperton_Island\" title=\"Clipperton Island\">Clipperton Island</a> with a mail delivery for President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, demonstrating its potential for tracking ships at sea.", "no_year_html": "The rigid airship <a href=\"https://wikipedia.org/wiki/USS_Macon_(ZRS-5)\" title=\"USS Macon (ZRS-5)\">USS <i>Macon</i></a> surprised the <a href=\"https://wikipedia.org/wiki/USS_Houston_(CA-30)\" title=\"USS Houston (CA-30)\">USS <i>Houston</i></a> near <a href=\"https://wikipedia.org/wiki/Clipperton_Island\" title=\"Clipperton Island\">Clipperton Island</a> with a mail delivery for President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, demonstrating its potential for tracking ships at sea.", "links": [{"title": "USS Macon (ZRS-5)", "link": "https://wikipedia.org/wiki/USS_Macon_(ZRS-5)"}, {"title": "USS Houston (CA-30)", "link": "https://wikipedia.org/wiki/USS_Houston_(CA-30)"}, {"title": "Clipperton Island", "link": "https://wikipedia.org/wiki/Clipperton_Island"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "Spanish Civil War: The CNT and UGT call a general strike in Spain - mobilizing workers' militias against the Nationalist forces.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo\" title=\"Confederación Nacional del Trabajo\">CNT</a> and <a href=\"https://wikipedia.org/wiki/Uni%C3%B3n_General_de_Trabajadores\" title=\"Unión General de Trabajadores\">UGT</a> call a <a href=\"https://wikipedia.org/wiki/General_strike\" title=\"General strike\">general strike</a> in <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a> - mobilizing workers' militias against the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalist forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo\" title=\"Confederación Nacional del Trabajo\">CNT</a> and <a href=\"https://wikipedia.org/wiki/Uni%C3%B3n_General_de_Trabajadores\" title=\"Unión General de Trabajadores\">UGT</a> call a <a href=\"https://wikipedia.org/wiki/General_strike\" title=\"General strike\">general strike</a> in <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Spain</a> - mobilizing workers' militias against the <a href=\"https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)\" title=\"Nationalist faction (Spanish Civil War)\">Nationalist forces</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Confederación Nacional del Trabajo", "link": "https://wikipedia.org/wiki/Confederaci%C3%B3n_Nacional_del_Trabajo"}, {"title": "Unión General de Trabajadores", "link": "https://wikipedia.org/wiki/Uni%C3%B3n_General_<PERSON>_<PERSON>ajador<PERSON>"}, {"title": "General strike", "link": "https://wikipedia.org/wiki/General_strike"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}, {"title": "Nationalist faction (Spanish Civil War)", "link": "https://wikipedia.org/wiki/Nationalist_faction_(Spanish_Civil_War)"}]}, {"year": "1940", "text": "World War II: Battle of Cape Spada: The Royal Navy and the Regia Marina clash; the Italian light cruiser <PERSON><PERSON><PERSON><PERSON> sinks, with 121 casualties.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Spada\" title=\"Battle of Cape Spada\">Battle of Cape Spada</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> clash; the Italian light cruiser <i><a href=\"https://wikipedia.org/wiki/Bartolomeo_Colleoni#Italian_cruiser_Bartolomeo_Colleoni\" title=\"Bartolomeo Colleoni\">Bart<PERSON>meo Colleoni</a></i> sinks, with 121 casualties.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cape_Spada\" title=\"Battle of Cape Spada\">Battle of Cape Spada</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> and the <a href=\"https://wikipedia.org/wiki/Regia_Marina\" title=\"Regia Marina\">Regia Marina</a> clash; the Italian light cruiser <i><a href=\"https://wikipedia.org/wiki/Bartolomeo_Colleoni#Italian_cruiser_Bartolomeo_Colleoni\" title=\"Bartolomeo Colleoni\">Bart<PERSON>meo Colleoni</a></i> sinks, with 121 casualties.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Cape Spada", "link": "https://wikipedia.org/wiki/Battle_of_Cape_Spada"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Regia Marina", "link": "https://wikipedia.org/wiki/Regia_Marina"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Colleoni#Italian_cruiser_Bart<PERSON><PERSON>o_Colleoni"}]}, {"year": "1940", "text": "Field Marshal <PERSON><PERSON><PERSON>: First occasion in World War II that <PERSON> appoints field marshals due to military achievements.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/1940_Field_Marshal_Ceremony\" title=\"1940 Field Marshal Ceremony\">Field Marshal Ce<PERSON>ony</a>: First occasion in World War II that <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> appoints field marshals due to military achievements.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1940_Field_Marshal_Ceremony\" title=\"1940 Field Marshal Ceremony\">Field Marshal <PERSON></a>: First occasion in World War II that <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> appoints field marshals due to military achievements.", "links": [{"title": "1940 Field Marshal <PERSON>", "link": "https://wikipedia.org/wiki/1940_Field_Marshal_<PERSON>remony"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "World War II: Army order 112 forms the Intelligence Corps of the British Army.", "html": "1940 - World War II: Army order 112 forms the <a href=\"https://wikipedia.org/wiki/Intelligence_Corps_(United_Kingdom)\" title=\"Intelligence Corps (United Kingdom)\">Intelligence Corps</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "no_year_html": "World War II: Army order 112 forms the <a href=\"https://wikipedia.org/wiki/Intelligence_Corps_(United_Kingdom)\" title=\"Intelligence Corps (United Kingdom)\">Intelligence Corps</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "links": [{"title": "Intelligence Corps (United Kingdom)", "link": "https://wikipedia.org/wiki/Intelligence_Corps_(United_Kingdom)"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1942", "text": "World War II: The Second Happy Time of <PERSON>'s submarines comes to an end, as the increasingly effective American convoy system compels them to return to the central Atlantic.", "html": "1942 - World War II: The <a href=\"https://wikipedia.org/wiki/Second_Happy_Time\" title=\"Second Happy Time\">Second Happy Time</a> of <PERSON>'s submarines comes to an end, as the increasingly effective American convoy system compels them to return to the central Atlantic.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Second_Happy_Time\" title=\"Second Happy Time\">Second Happy Time</a> of <PERSON>'s submarines comes to an end, as the increasingly effective American convoy system compels them to return to the central Atlantic.", "links": [{"title": "Second Happy Time", "link": "https://wikipedia.org/wiki/Second_Happy_Time"}]}, {"year": "1943", "text": "World War II: Rome is heavily bombed by more than 500 Allied aircraft, inflicting thousands of casualties.", "html": "1943 - World War II: Rome is <a href=\"https://wikipedia.org/wiki/Bombing_of_Rome_in_World_War_II\" title=\"Bombing of Rome in World War II\">heavily bombed</a> by more than 500 Allied aircraft, inflicting thousands of casualties.", "no_year_html": "World War II: Rome is <a href=\"https://wikipedia.org/wiki/Bombing_of_Rome_in_World_War_II\" title=\"Bombing of Rome in World War II\">heavily bombed</a> by more than 500 Allied aircraft, inflicting thousands of casualties.", "links": [{"title": "Bombing of Rome in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Rome_in_World_War_II"}]}, {"year": "1947", "text": "Prime Minister of the shadow Burmese government, <PERSON><PERSON><PERSON> and eight others are assassinated.", "html": "1947 - Prime Minister of the shadow <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burmese</a> government, Bogyoke <a href=\"https://wikipedia.org/wiki/Aung_San\" title=\"Aung San\">Aung San</a> and eight others are <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a>.", "no_year_html": "Prime Minister of the shadow <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burmese</a> government, Bogyoke <a href=\"https://wikipedia.org/wiki/Aung_San\" title=\"Aung San\">Aung San</a> and eight others are <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a>.", "links": [{"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Aung San", "link": "https://wikipedia.org/wiki/Aung_San"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}]}, {"year": "1947", "text": "Korean politician <PERSON><PERSON><PERSON> is assassinated.", "html": "1947 - Korean politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung\" title=\"<PERSON><PERSON><PERSON>-hyung\"><PERSON><PERSON><PERSON></a> is assassinated.", "no_year_html": "Korean politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung\" title=\"<PERSON><PERSON><PERSON>-hyung\"><PERSON><PERSON><PERSON></a> is assassinated.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung"}]}, {"year": "1952", "text": "Opening of the Summer Olympics in Helsinki, Finland.", "html": "1952 - Opening of the <a href=\"https://wikipedia.org/wiki/1952_Summer_Olympics\" title=\"1952 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland.", "no_year_html": "Opening of the <a href=\"https://wikipedia.org/wiki/1952_Summer_Olympics\" title=\"1952 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland.", "links": [{"title": "1952 Summer Olympics", "link": "https://wikipedia.org/wiki/1952_Summer_Olympics"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}]}, {"year": "1957", "text": "The largely autobiographical novel The Ordeal of <PERSON> by <PERSON> was published.", "html": "1957 - The largely <a href=\"https://wikipedia.org/wiki/Autobiographical_novel\" title=\"Autobiographical novel\">autobiographical novel</a> <i><a href=\"https://wikipedia.org/wiki/The_Ordeal_of_<PERSON>_<PERSON>\" title=\"The Ordeal of <PERSON>\">The Ordeal of <PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> was published.", "no_year_html": "The largely <a href=\"https://wikipedia.org/wiki/Autobiographical_novel\" title=\"Autobiographical novel\">autobiographical novel</a> <i><a href=\"https://wikipedia.org/wiki/The_Ordeal_of_<PERSON>_<PERSON>\" title=\"The Ordeal of <PERSON>\">The Ordeal of <PERSON></a></i> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> was published.", "links": [{"title": "Autobiographical novel", "link": "https://wikipedia.org/wiki/Autobiographical_novel"}, {"title": "The Ordeal of <PERSON>", "link": "https://wikipedia.org/wiki/The_Ordeal_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "Tunisia imposes a blockade on the French naval base at Bizerte; the French would capture the entire town four days later.", "html": "1961 - Tunisia <a href=\"https://wikipedia.org/wiki/Bizerte_crisis\" title=\"Bizerte crisis\">imposes a blockade</a> on the French naval base at <a href=\"https://wikipedia.org/wiki/Bizerte\" title=\"Bizerte\">Bizerte</a>; the French would capture the entire town four days later.", "no_year_html": "Tunisia <a href=\"https://wikipedia.org/wiki/Bizerte_crisis\" title=\"Bizerte crisis\">imposes a blockade</a> on the French naval base at <a href=\"https://wikipedia.org/wiki/Bizerte\" title=\"Bizerte\">Bizerte</a>; the French would capture the entire town four days later.", "links": [{"title": "Bizerte crisis", "link": "https://wikipedia.org/wiki/Bizerte_crisis"}, {"title": "Bizerte", "link": "https://wikipedia.org/wiki/B<PERSON>te"}]}, {"year": "1963", "text": "<PERSON> flies a North American X-15 to a record altitude of 106,010 meters (347,800 feet) on X-15 Flight 90. Exceeding an altitude of 100 km, this flight qualifies as a human spaceflight under international convention.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies a <a href=\"https://wikipedia.org/wiki/North_American_X-15\" title=\"North American X-15\">North American X-15</a> to a record altitude of 106,010 meters (347,800 feet) on <a href=\"https://wikipedia.org/wiki/X-15_Flight_90\" title=\"X-15 Flight 90\">X-15 Flight 90</a>. Exceeding an altitude of 100 km, this flight qualifies as a <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">human spaceflight</a> under international convention.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> flies a <a href=\"https://wikipedia.org/wiki/North_American_X-15\" title=\"North American X-15\">North American X-15</a> to a record altitude of 106,010 meters (347,800 feet) on <a href=\"https://wikipedia.org/wiki/X-15_Flight_90\" title=\"X-15 Flight 90\">X-15 Flight 90</a>. Exceeding an altitude of 100 km, this flight qualifies as a <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">human spaceflight</a> under international convention.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "North American X-15", "link": "https://wikipedia.org/wiki/North_American_X-15"}, {"title": "X-15 Flight 90", "link": "https://wikipedia.org/wiki/X-15_Flight_90"}, {"title": "Human spaceflight", "link": "https://wikipedia.org/wiki/Human_spaceflight"}]}, {"year": "1964", "text": "Vietnam War: At a rally in Saigon, South Vietnamese Prime Minister <PERSON><PERSON><PERSON><PERSON> calls for expanding the war into North Vietnam.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: At a rally in <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_City\" title=\"Ho Chi Minh City\">Saigon</a>, <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> calls for expanding the war into <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: At a rally in <a href=\"https://wikipedia.org/wiki/Ho_Chi_Minh_City\" title=\"Ho Chi Minh City\">Saigon</a>, <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnamese</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> calls for expanding the war into <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Ho Chi Minh City", "link": "https://wikipedia.org/wiki/Ho_Chi_Minh_City"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Prime minister", "link": "https://wikipedia.org/wiki/Prime_minister"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1967", "text": "Piedmont Airlines Flight 22, a Piedmont Airlines Boeing 727-22 and a twin-engine Cessna 310 collided over Hendersonville, North Carolina, USA. Both aircraft were destroyed and all passengers and crew were killed, including <PERSON>, an advisor to <PERSON>.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_Flight_22\" title=\"Piedmont Airlines Flight 22\">Piedmont Airlines Flight 22</a>, a <a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_(1948-1989)\" class=\"mw-redirect\" title=\"Piedmont Airlines (1948-1989)\">Piedmont Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_727-22\" class=\"mw-redirect\" title=\"Boeing 727-22\">Boeing 727-22</a> and a twin-engine <a href=\"https://wikipedia.org/wiki/Cessna_310\" title=\"Cessna 310\">Cessna 310</a> collided over <a href=\"https://wikipedia.org/wiki/Hendersonville,_North_Carolina\" title=\"Hendersonville, North Carolina\">Hendersonville, North Carolina</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">USA</a>. Both aircraft were destroyed and all passengers and crew were killed, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, an advisor to <a href=\"https://wikipedia.org/wiki/Robert_McNamara\" title=\"Robert McNamara\">Robert McNamara</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_Flight_22\" title=\"Piedmont Airlines Flight 22\">Piedmont Airlines Flight 22</a>, a <a href=\"https://wikipedia.org/wiki/Piedmont_Airlines_(1948-1989)\" class=\"mw-redirect\" title=\"Piedmont Airlines (1948-1989)\">Piedmont Airlines</a> <a href=\"https://wikipedia.org/wiki/Boeing_727-22\" class=\"mw-redirect\" title=\"Boeing 727-22\">Boeing 727-22</a> and a twin-engine <a href=\"https://wikipedia.org/wiki/Cessna_310\" title=\"Cessna 310\">Cessna 310</a> collided over <a href=\"https://wikipedia.org/wiki/Hendersonville,_North_Carolina\" title=\"Hendersonville, North Carolina\">Hendersonville, North Carolina</a>, <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">USA</a>. Both aircraft were destroyed and all passengers and crew were killed, including <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, an advisor to <a href=\"https://wikipedia.org/wiki/Robert_McNamara\" title=\"Robert McNamara\">Robert McNamara</a>.", "links": [{"title": "Piedmont Airlines Flight 22", "link": "https://wikipedia.org/wiki/Piedmont_Airlines_Flight_22"}, {"title": "Piedmont Airlines (1948-1989)", "link": "https://wikipedia.org/wiki/Piedmont_Airlines_(1948-1989)"}, {"title": "Boeing 727-22", "link": "https://wikipedia.org/wiki/Boeing_727-22"}, {"title": "Cessna 310", "link": "https://wikipedia.org/wiki/Cessna_310"}, {"title": "Hendersonville, North Carolina", "link": "https://wikipedia.org/wiki/Hendersonville,_North_Carolina"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "Chappaquiddick incident: U.S. Senator <PERSON> crashes his car into a tidal pond at Chappaquiddick Island, Massachusetts, killing his passenger <PERSON>.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Chappaquiddick_incident\" title=\"Chappaquiddick incident\">Chappaquiddick incident</a>: U.S. Senator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crashes his car into a tidal pond at <a href=\"https://wikipedia.org/wiki/Chappaquiddick_Island,_Massachusetts\" class=\"mw-redirect\" title=\"Chappaquiddick Island, Massachusetts\">Chappaquiddick Island, Massachusetts</a>, killing his passenger <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chappaquiddick_incident\" title=\"Chappaquiddick incident\">Chappaquiddick incident</a>: U.S. Senator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> crashes his car into a tidal pond at <a href=\"https://wikipedia.org/wiki/Chappaquiddick_Island,_Massachusetts\" class=\"mw-redirect\" title=\"Chappaquiddick Island, Massachusetts\">Chappaquiddick Island, Massachusetts</a>, killing his passenger <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Chappaquiddick incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_incident"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chappaquiddick Island, Massachusetts", "link": "https://wikipedia.org/wiki/Chappaquiddick_Island,_Massachusetts"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Dhofar Rebellion: British SAS units help the Omani government against Popular Front for the Liberation of Oman rebels in the Battle of Mirbat.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Dhofar_Rebellion\" class=\"mw-redirect\" title=\"Dhofar Rebellion\">Dhofar Rebellion</a>: British <a href=\"https://wikipedia.org/wiki/Special_Air_Service\" title=\"Special Air Service\">SAS</a> units help the <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Omani</a> government against <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Oman\" title=\"Popular Front for the Liberation of Oman\">Popular Front for the Liberation of Oman</a> rebels in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mirbat\" title=\"Battle of Mirbat\">Battle of Mirbat</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dhofar_Rebellion\" class=\"mw-redirect\" title=\"Dhofar Rebellion\">Dhofar Rebellion</a>: British <a href=\"https://wikipedia.org/wiki/Special_Air_Service\" title=\"Special Air Service\">SAS</a> units help the <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Omani</a> government against <a href=\"https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Oman\" title=\"Popular Front for the Liberation of Oman\">Popular Front for the Liberation of Oman</a> rebels in the <a href=\"https://wikipedia.org/wiki/Battle_of_Mirbat\" title=\"Battle of Mirbat\">Battle of Mirbat</a>.", "links": [{"title": "Dhofar Rebellion", "link": "https://wikipedia.org/wiki/Dhofar_Rebellion"}, {"title": "Special Air Service", "link": "https://wikipedia.org/wiki/Special_Air_Service"}, {"title": "Oman", "link": "https://wikipedia.org/wiki/Oman"}, {"title": "Popular Front for the Liberation of Oman", "link": "https://wikipedia.org/wiki/Popular_Front_for_the_Liberation_of_Oman"}, {"title": "Battle of Mirbat", "link": "https://wikipedia.org/wiki/Battle_of_Mirbat"}]}, {"year": "1976", "text": "Sagarmatha National Park in Nepal is created.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Sagarmatha_National_Park\" title=\"Sagarmatha National Park\">Sagarmatha National Park</a> in Nepal is created.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sagarmatha_National_Park\" title=\"Sagarmatha National Park\">Sagarmatha National Park</a> in Nepal is created.", "links": [{"title": "Sagarmatha National Park", "link": "https://wikipedia.org/wiki/Sagarmatha_National_Park"}]}, {"year": "1977", "text": "The world's first Global Positioning System (GPS) signal was transmitted from Navigation Technology Satellite 2 (NTS-2) and received at Rockwell Collins in Cedar Rapids, Iowa, at 12:41 a.m. Eastern time (ET).", "html": "1977 - The world's first <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">Global Positioning System</a> (GPS) signal was transmitted from Navigation Technology Satellite 2 (NTS-2) and received at <a href=\"https://wikipedia.org/wiki/Rockwell_Collins\" title=\"Rockwell Collins\">Rockwell Collins</a> in <a href=\"https://wikipedia.org/wiki/Cedar_Rapids,_Iowa\" title=\"Cedar Rapids, Iowa\">Cedar Rapids, Iowa</a>, at 12:41 a.m. Eastern time (ET).", "no_year_html": "The world's first <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">Global Positioning System</a> (GPS) signal was transmitted from Navigation Technology Satellite 2 (NTS-2) and received at <a href=\"https://wikipedia.org/wiki/Rockwell_Collins\" title=\"Rockwell Collins\">Rockwell Collins</a> in <a href=\"https://wikipedia.org/wiki/Cedar_Rapids,_Iowa\" title=\"Cedar Rapids, Iowa\">Cedar Rapids, Iowa</a>, at 12:41 a.m. Eastern time (ET).", "links": [{"title": "Global Positioning System", "link": "https://wikipedia.org/wiki/Global_Positioning_System"}, {"title": "<PERSON><PERSON> Collins", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Collins"}, {"title": "Cedar Rapids, Iowa", "link": "https://wikipedia.org/wiki/Cedar_Rapids,_Iowa"}]}, {"year": "1979", "text": "The Sandinista rebels overthrow the government of the <PERSON><PERSON><PERSON> family in Nicaragua.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista</a> rebels overthrow the <a href=\"https://wikipedia.org/wiki/Forms_of_government\" class=\"mw-redirect\" title=\"Forms of government\">government</a> of the <a href=\"https://wikipedia.org/wiki/Anastasio_Somoza_Debayle\" title=\"Anastasio Somoza <PERSON>bayle\">Somo<PERSON></a> family in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sandinista_National_Liberation_Front\" title=\"Sandinista National Liberation Front\">Sandinista</a> rebels overthrow the <a href=\"https://wikipedia.org/wiki/Forms_of_government\" class=\"mw-redirect\" title=\"Forms of government\">government</a> of the <a href=\"https://wikipedia.org/wiki/Anastasio_Somoza_Debayle\" title=\"Anastasio Somoza <PERSON>bayle\"><PERSON><PERSON><PERSON></a> family in <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Sandinista National Liberation Front", "link": "https://wikipedia.org/wiki/Sandinista_National_Liberation_Front"}, {"title": "Forms of government", "link": "https://wikipedia.org/wiki/Forms_of_government"}, {"title": "Anastasio So<PERSON>", "link": "https://wikipedia.org/wiki/Anastasio_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1979", "text": "The oil tanker SS Atlantic Empress collides with another oil tanker, causing the largest ever ship-borne oil spill.", "html": "1979 - The oil tanker <i><a href=\"https://wikipedia.org/wiki/SS_Atlantic_Empress\" title=\"SS Atlantic Empress\">SS Atlantic Empress</a></i> collides with another oil tanker, causing the largest ever <a href=\"https://wikipedia.org/wiki/List_of_oil_spills\" title=\"List of oil spills\">ship-borne oil spill</a>.", "no_year_html": "The oil tanker <i><a href=\"https://wikipedia.org/wiki/SS_Atlantic_Empress\" title=\"SS Atlantic Empress\">SS Atlantic Empress</a></i> collides with another oil tanker, causing the largest ever <a href=\"https://wikipedia.org/wiki/List_of_oil_spills\" title=\"List of oil spills\">ship-borne oil spill</a>.", "links": [{"title": "SS Atlantic Empress", "link": "https://wikipedia.org/wiki/SS_Atlantic_Empress"}, {"title": "List of oil spills", "link": "https://wikipedia.org/wiki/List_of_oil_spills"}]}, {"year": "1980", "text": "Opening of the Summer Olympics in Moscow.", "html": "1980 - Opening of the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "no_year_html": "Opening of the <a href=\"https://wikipedia.org/wiki/1980_Summer_Olympics\" title=\"1980 Summer Olympics\">Summer Olympics</a> in <a href=\"https://wikipedia.org/wiki/Moscow\" title=\"Moscow\">Moscow</a>.", "links": [{"title": "1980 Summer Olympics", "link": "https://wikipedia.org/wiki/1980_Summer_Olympics"}, {"title": "Moscow", "link": "https://wikipedia.org/wiki/Moscow"}]}, {"year": "1981", "text": "In a private meeting with U.S. President <PERSON>, French President <PERSON> reveals the existence of the Farewell Dossier, a collection of documents showing the Soviet Union had been stealing American technological research and development.", "html": "1981 - In a private meeting with U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French President <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reveals the existence of the <i><a href=\"https://wikipedia.org/wiki/Farewell_Dossier\" title=\"Farewell Dossier\">Farewell Dossier</a></i>, a collection of documents showing the Soviet Union had been stealing American technological research and development.", "no_year_html": "In a private meeting with U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French President <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reveals the existence of the <i><a href=\"https://wikipedia.org/wiki/Farewell_Dossier\" title=\"Farewell Dossier\">Farewell Dossier</a></i>, a collection of documents showing the Soviet Union had been stealing American technological research and development.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Farewell_Dossier"}]}, {"year": "1982", "text": "In one of the first militant attacks by Hezbollah, <PERSON>, president of the American University of Beirut, is kidnapped.", "html": "1982 - In one of the first militant attacks by <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/American_University_of_Beirut\" title=\"American University of Beirut\">American University of Beirut</a>, is kidnapped.", "no_year_html": "In one of the first militant attacks by <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of the <a href=\"https://wikipedia.org/wiki/American_University_of_Beirut\" title=\"American University of Beirut\">American University of Beirut</a>, is kidnapped.", "links": [{"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "American University of Beirut", "link": "https://wikipedia.org/wiki/American_University_of_Beirut"}]}, {"year": "1983", "text": "The first three-dimensional reconstruction of a human head in a CT is published.", "html": "1983 - The first <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">three-dimensional reconstruction</a> of a human head in a <a href=\"https://wikipedia.org/wiki/CT_scan\" title=\"CT scan\">CT</a> is published.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">three-dimensional reconstruction</a> of a human head in a <a href=\"https://wikipedia.org/wiki/CT_scan\" title=\"CT scan\">CT</a> is published.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "CT scan", "link": "https://wikipedia.org/wiki/CT_scan"}]}, {"year": "1985", "text": "The Val di Stava dam collapses killing 268 people in Val di Stava, Italy.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/Val_di_Stava_dam_collapse\" title=\"Val di Stava dam collapse\">Val di Stava dam collapses</a> killing 268 people in Val di Stava, Italy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Val_di_Stava_dam_collapse\" title=\"Val di Stava dam collapse\">Val di Stava dam collapses</a> killing 268 people in Val di Stava, Italy.", "links": [{"title": "Val di Stava dam collapse", "link": "https://wikipedia.org/wiki/Val_di_Stava_dam_collapse"}]}, {"year": "1989", "text": "United Airlines Flight 232 crashes in Sioux City, Iowa killing 111.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_232\" title=\"United Airlines Flight 232\">United Airlines Flight 232</a> crashes in Sioux City, Iowa killing 111.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_232\" title=\"United Airlines Flight 232\">United Airlines Flight 232</a> crashes in Sioux City, Iowa killing 111.", "links": [{"title": "United Airlines Flight 232", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_232"}]}, {"year": "1992", "text": "A car bomb kills Judge <PERSON> and five members of his escort.", "html": "1992 - A car bomb <a href=\"https://wikipedia.org/wiki/Massacre_of_Via_D%27Amelio\" class=\"mw-redirect\" title=\"Massacre of Via D'Amelio\">kills Judge <PERSON></a> and five members of his escort.", "no_year_html": "A car bomb <a href=\"https://wikipedia.org/wiki/Massacre_of_Via_D%27Amelio\" class=\"mw-redirect\" title=\"Massacre of Via D'Amelio\">kills Judge <PERSON></a> and five members of his escort.", "links": [{"title": "Massacre of Via D'Amelio", "link": "https://wikipedia.org/wiki/Massacre_of_Via_D%27Amelio"}]}, {"year": "1997", "text": "The Troubles: The Provisional Irish Republican Army resumes a ceasefire to end their 25-year paramilitary campaign to end British rule in Northern Ireland.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> resumes a ceasefire to end their <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army_campaign_1969%E2%80%9397\" class=\"mw-redirect\" title=\"Provisional Irish Republican Army campaign 1969-97\">25-year paramilitary campaign</a> to end British rule in Northern Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> resumes a ceasefire to end their <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army_campaign_1969%E2%80%9397\" class=\"mw-redirect\" title=\"Provisional Irish Republican Army campaign 1969-97\">25-year paramilitary campaign</a> to end British rule in Northern Ireland.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Provisional Irish Republican Army campaign 1969-97", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army_campaign_1969%E2%80%9397"}]}, {"year": "2011", "text": "Guinean President <PERSON> survives an attempted assassination and coup d'état at his residence in Conakry.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinean</a> President <a href=\"https://wikipedia.org/wiki/Alpha_Cond%C3%A9\" title=\"Alpha Condé\"><PERSON></a> survives an attempted assassination and coup d'état at his <a href=\"https://wikipedia.org/wiki/Presidential_Palace,_Conakry\" title=\"Presidential Palace, Conakry\">residence</a> in <a href=\"https://wikipedia.org/wiki/Conakry\" title=\"Conakry\">Con<PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guinea\" title=\"Guinea\">Guinean</a> President <a href=\"https://wikipedia.org/wiki/Alpha_Cond%C3%A9\" title=\"Alpha Condé\"><PERSON></a> survives an attempted assassination and coup d'état at his <a href=\"https://wikipedia.org/wiki/Presidential_Palace,_Conakry\" title=\"Presidential Palace, Conakry\">residence</a> in <a href=\"https://wikipedia.org/wiki/Conakry\" title=\"Conakry\">Con<PERSON><PERSON></a>.", "links": [{"title": "Guinea", "link": "https://wikipedia.org/wiki/Guinea"}, {"title": "Alpha Condé", "link": "https://wikipedia.org/wiki/Alpha_Cond%C3%A9"}, {"title": "Presidential Palace, Conakry", "link": "https://wikipedia.org/wiki/Presidential_Palace,_Conakry"}, {"title": "Conakry", "link": "https://wikipedia.org/wiki/Conakry"}]}, {"year": "2012", "text": "Syrian civil war: The People's Protection Units (YPG) capture the city of Kobanî without resistance, starting the Rojava conflict in Northeast Syria.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the city of <a href=\"https://wikipedia.org/wiki/Koban%C3%AE\" class=\"mw-redirect\" title=\"Kobanî\">Kobanî</a> without resistance, starting the <a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a> in Northeast Syria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syrian_civil_war\" title=\"Syrian civil war\">Syrian civil war</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Protection_Units\" class=\"mw-redirect\" title=\"People's Protection Units\">People's Protection Units</a> (YPG) capture the city of <a href=\"https://wikipedia.org/wiki/Koban%C3%AE\" class=\"mw-redirect\" title=\"Kobanî\">Kobanî</a> without resistance, starting the <a href=\"https://wikipedia.org/wiki/Rojava_conflict\" title=\"Rojava conflict\">Rojava conflict</a> in Northeast Syria.", "links": [{"title": "Syrian civil war", "link": "https://wikipedia.org/wiki/Syrian_civil_war"}, {"title": "People's Protection Units", "link": "https://wikipedia.org/wiki/People%27s_Protection_Units"}, {"title": "Kobanî", "link": "https://wikipedia.org/wiki/Koban%C3%AE"}, {"title": "Rojava conflict", "link": "https://wikipedia.org/wiki/Rojava_conflict"}]}, {"year": "2014", "text": "Gunmen in Egypt's western desert province of New Valley Governorate attack a military checkpoint, killing at least 21 soldiers. Egypt reportedly declares a state of emergency on its border with Sudan.", "html": "2014 - Gunmen in Egypt's western desert province of <a href=\"https://wikipedia.org/wiki/New_Valley_Governorate\" title=\"New Valley Governorate\">New Valley Governorate</a> <a href=\"https://wikipedia.org/wiki/July_2014_Al-Wadi_Al-Gedid_attack\" class=\"mw-redirect\" title=\"July 2014 Al-Wadi Al-Gedid attack\">attack</a> a military checkpoint, killing at least 21 soldiers. Egypt reportedly declares a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> on its border with Sudan.", "no_year_html": "Gunmen in Egypt's western desert province of <a href=\"https://wikipedia.org/wiki/New_Valley_Governorate\" title=\"New Valley Governorate\">New Valley Governorate</a> <a href=\"https://wikipedia.org/wiki/July_2014_Al-Wadi_Al-Gedid_attack\" class=\"mw-redirect\" title=\"July 2014 Al-Wadi Al-Gedid attack\">attack</a> a military checkpoint, killing at least 21 soldiers. Egypt reportedly declares a <a href=\"https://wikipedia.org/wiki/State_of_emergency\" title=\"State of emergency\">state of emergency</a> on its border with Sudan.", "links": [{"title": "New Valley Governorate", "link": "https://wikipedia.org/wiki/New_Valley_Governorate"}, {"title": "July 2014 Al-Wadi Al-Gedid attack", "link": "https://wikipedia.org/wiki/July_2014_Al-Wadi_Al-Gedid_attack"}, {"title": "State of emergency", "link": "https://wikipedia.org/wiki/State_of_emergency"}]}, {"year": "2018", "text": "The Knesset passes the controversial Nationality Bill, which defines the State of Israel as the nation-state of the Jewish people.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> passes the controversial <a href=\"https://wikipedia.org/wiki/Basic_Law:_Israel_as_the_Nation-State_of_the_Jewish_People\" title=\"Basic Law: Israel as the Nation-State of the Jewish People\">Nationality Bill</a>, which defines the <a href=\"https://wikipedia.org/wiki/State_of_Israel\" class=\"mw-redirect\" title=\"State of Israel\">State of Israel</a> as the <a href=\"https://wikipedia.org/wiki/Nation-state\" class=\"mw-redirect\" title=\"Nation-state\">nation-state</a> of the <a href=\"https://wikipedia.org/wiki/Jewish_people\" class=\"mw-redirect\" title=\"Jewish people\">Jewish people</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Knesset\" title=\"Knesset\">Knesset</a> passes the controversial <a href=\"https://wikipedia.org/wiki/Basic_Law:_Israel_as_the_Nation-State_of_the_Jewish_People\" title=\"Basic Law: Israel as the Nation-State of the Jewish People\">Nationality Bill</a>, which defines the <a href=\"https://wikipedia.org/wiki/State_of_Israel\" class=\"mw-redirect\" title=\"State of Israel\">State of Israel</a> as the <a href=\"https://wikipedia.org/wiki/Nation-state\" class=\"mw-redirect\" title=\"Nation-state\">nation-state</a> of the <a href=\"https://wikipedia.org/wiki/Jewish_people\" class=\"mw-redirect\" title=\"Jewish people\">Jewish people</a>.", "links": [{"title": "Knesset", "link": "https://wikipedia.org/wiki/Knesset"}, {"title": "Basic Law: Israel as the Nation-State of the Jewish People", "link": "https://wikipedia.org/wiki/Basic_Law:_Israel_as_the_Nation-State_of_the_Jewish_People"}, {"title": "State of Israel", "link": "https://wikipedia.org/wiki/State_of_Israel"}, {"title": "Nation-state", "link": "https://wikipedia.org/wiki/Nation-state"}, {"title": "Jewish people", "link": "https://wikipedia.org/wiki/Jewish_people"}]}, {"year": "2024 ─ A faulty software update by CrowdStrike, an American cybersecurity company, causes global computer outages.[14]", "text": null, "html": "2024 ─ A faulty software update by CrowdStrike, an American cybersecurity company, causes global computer outages.[14] - <a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> ─ A faulty <a href=\"https://wikipedia.org/wiki/Patch_(computing)\" title=\"Patch (computing)\">software update</a> by <a href=\"https://wikipedia.org/wiki/CrowdStrike\" title=\"CrowdStrike\">CrowdStrike</a>, an American <a href=\"https://wikipedia.org/wiki/Computer_security\" title=\"Computer security\">cybersecurity</a> company, causes <a href=\"https://wikipedia.org/wiki/2024_CrowdStrike_incident\" class=\"mw-redirect\" title=\"2024 CrowdStrike incident\">global computer outages</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> ─ A faulty <a href=\"https://wikipedia.org/wiki/Patch_(computing)\" title=\"Patch (computing)\">software update</a> by <a href=\"https://wikipedia.org/wiki/CrowdStrike\" title=\"CrowdStrike\">CrowdStrike</a>, an American <a href=\"https://wikipedia.org/wiki/Computer_security\" title=\"Computer security\">cybersecurity</a> company, causes <a href=\"https://wikipedia.org/wiki/2024_CrowdStrike_incident\" class=\"mw-redirect\" title=\"2024 CrowdStrike incident\">global computer outages</a>.", "links": [{"title": "2024", "link": "https://wikipedia.org/wiki/2024"}, {"title": "<PERSON> (computing)", "link": "https://wikipedia.org/wiki/Patch_(computing)"}, {"title": "CrowdStrike", "link": "https://wikipedia.org/wiki/CrowdStrike"}, {"title": "Computer security", "link": "https://wikipedia.org/wiki/Computer_security"}, {"title": "2024 CrowdStrike incident", "link": "https://wikipedia.org/wiki/2024_CrowdStrike_incident"}]}, {"year": "2024 ─ The International Court of Justice (ICJ) delivered a ruling stating that Israel should end its illegal occupation of the Palestinian territories. The ICJ identified that all member states of the UN are under an obligation not to recognize the occupation as legal nor \"render aid or assistance\" toward maintaining Israel's presence in the occupied territories.[15][16]", "text": null, "html": "2024 ─ The International Court of Justice (ICJ) delivered a ruling stating that Israel should end its illegal occupation of the Palestinian territories. The ICJ identified that all member states of the UN are under an obligation not to recognize the occupation as legal nor \"render aid or assistance\" toward maintaining Israel's presence in the occupied territories.[15][16] - 2024 ─ The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> (ICJ) delivered a <a href=\"https://wikipedia.org/wiki/ICJ_case_on_Israel%27s_occupation_of_the_Palestinian_territories\" title=\"ICJ case on Israel's occupation of the Palestinian territories\">ruling</a> stating that <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> should end its illegal occupation of the <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestinian territories</a>. The ICJ identified that all member states of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a> are under an obligation not to recognize the occupation as legal nor \"render aid or assistance\" toward maintaining Israel's presence in the occupied territories.", "no_year_html": "2024 ─ The <a href=\"https://wikipedia.org/wiki/International_Court_of_Justice\" title=\"International Court of Justice\">International Court of Justice</a> (ICJ) delivered a <a href=\"https://wikipedia.org/wiki/ICJ_case_on_Israel%27s_occupation_of_the_Palestinian_territories\" title=\"ICJ case on Israel's occupation of the Palestinian territories\">ruling</a> stating that <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> should end its illegal occupation of the <a href=\"https://wikipedia.org/wiki/Palestinian_territories\" class=\"mw-redirect\" title=\"Palestinian territories\">Palestinian territories</a>. The ICJ identified that all member states of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">UN</a> are under an obligation not to recognize the occupation as legal nor \"render aid or assistance\" toward maintaining Israel's presence in the occupied territories.", "links": [{"title": "International Court of Justice", "link": "https://wikipedia.org/wiki/International_Court_of_Justice"}, {"title": "ICJ case on Israel's occupation of the Palestinian territories", "link": "https://wikipedia.org/wiki/ICJ_case_on_Israel%27s_occupation_of_the_Palestinian_territories"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Palestinian territories", "link": "https://wikipedia.org/wiki/Palestinian_territories"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}], "Births": [{"year": "810", "text": "<PERSON>, Persian scholar (d. 870)", "html": "810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian scholar (d. 870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Persian scholar (d. 870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1223", "text": "<PERSON><PERSON><PERSON>, sultan of Egypt (d. 1277)", "html": "1223 - <a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\"><PERSON><PERSON><PERSON></a>, sultan of Egypt (d. 1277)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baibars\" class=\"mw-redirect\" title=\"Baibars\"><PERSON><PERSON><PERSON></a>, sultan of Egypt (d. 1277)", "links": [{"title": "Baibars", "link": "https://wikipedia.org/wiki/Baibars"}]}, {"year": "1420", "text": "<PERSON>, Marquis of Montferrat (d. 1483)", "html": "1420 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat\" title=\"<PERSON>, Marquis of Montferrat\"><PERSON>, Marquis of Montferrat</a> (d. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat\" title=\"<PERSON>, Marquis of Montferrat\"><PERSON>, Marquis of Montferrat</a> (d. 1483)", "links": [{"title": "<PERSON>, Marquis of Montferrat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Marquis_of_Montferrat"}]}, {"year": "1569", "text": "<PERSON>, Dutch theologian (d. 1622)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch theologian (d. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1670", "text": "<PERSON>, English singer-songwriter (d. 1758)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter (d. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, Italian missionary and painter (d. 1766)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" class=\"mw-redirect\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, Italian missionary and painter (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)\" class=\"mw-redirect\" title=\"<PERSON> (Jesuit)\"><PERSON></a>, Italian missionary and painter (d. 1766)", "links": [{"title": "<PERSON> (Jesuit)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit)"}]}, {"year": "1744", "text": "<PERSON>, German author and poet (d. 1806)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1759", "text": "<PERSON><PERSON>, Austrian pianist and composer (d. 1782)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian pianist and composer (d. 1782)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>brug<PERSON>"}]}, {"year": "1759", "text": "<PERSON><PERSON><PERSON> of Sarov, Russian monk and saint (d. 1833)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Sarov\" title=\"<PERSON><PERSON><PERSON> of Sarov\"><PERSON><PERSON><PERSON> of Sarov</a>, Russian monk and saint (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Sarov\" title=\"<PERSON><PERSON><PERSON> of Sarov\"><PERSON><PERSON><PERSON> of Sarov</a>, Russian monk and saint (d. 1833)", "links": [{"title": "<PERSON><PERSON><PERSON> of Sarov", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, Irish-Canadian colonel and politician (d. 1853)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Upper_Canada)\" title=\"<PERSON> (Upper Canada)\"><PERSON></a>, Irish-Canadian colonel and politician (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Upper_Canada)\" title=\"<PERSON> (Upper Canada)\"><PERSON></a>, Irish-Canadian colonel and politician (d. 1853)", "links": [{"title": "<PERSON> (Upper Canada)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Upper_Canada)"}]}, {"year": "1794", "text": "<PERSON>, Mexican politician and president (d. 1864)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and president (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican politician and president (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, English painter, engraver, and illustrator (d. 1854)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter, engraver, and illustrator (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, English painter, engraver, and illustrator (d. 1854)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(painter)"}]}, {"year": "1800", "text": "<PERSON>, Venezuelan general and politician, 1st President of Ecuador (d. 1864)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Ecuador\" title=\"President of Ecuador\">President of Ecuador</a> (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON>"}, {"title": "President of Ecuador", "link": "https://wikipedia.org/wiki/President_of_Ecuador"}]}, {"year": "1814", "text": "<PERSON>, American businessman, founded the Colt's Manufacturing Company (d. 1862)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Samuel Colt\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company\" title=\"Colt's Manufacturing Company\">Colt's Manufacturing Company</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Samuel_<PERSON>\" title=\"Samuel Colt\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company\" title=\"Colt's Manufacturing Company\">Colt's Manufacturing Company</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Colt's Manufacturing Company", "link": "https://wikipedia.org/wiki/Colt%27s_Manufacturing_Company"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, Swiss author, poet, and playwright (d. 1890)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author, poet, and playwright (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author, poet, and playwright (d. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1822", "text": "Princess <PERSON> of Cambridge (d. 1916)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Princess_Augusta_of_Cambridge\" title=\"Princess <PERSON> of Cambridge\">Princess <PERSON> of Cambridge</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Augusta_of_Cambridge\" title=\"Princess <PERSON> of Cambridge\">Princess <PERSON> of Cambridge</a> (d. 1916)", "links": [{"title": "Princess <PERSON> of Cambridge", "link": "https://wikipedia.org/wiki/Princess_Augusta_of_Cambridge"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Indian soldier (d. 1857)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\"><PERSON><PERSON></a>, Indian soldier (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\"><PERSON><PERSON></a>, Indian soldier (d. 1857)", "links": [{"title": "Mangal <PERSON>", "link": "https://wikipedia.org/wiki/Mangal_Pandey"}]}, {"year": "1834", "text": "<PERSON>, French painter, sculptor, and illustrator (d. 1917)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter, sculptor, and illustrator (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter, sculptor, and illustrator (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON>, Guatemalan president (d. 1885)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemalan</a> <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">president</a> (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemalan</a> <a href=\"https://wikipedia.org/wiki/President_of_Guatemala\" title=\"President of Guatemala\">president</a> (d. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "President of Guatemala", "link": "https://wikipedia.org/wiki/President_of_Guatemala"}]}, {"year": "1842", "text": "<PERSON>, English-American lawyer and politician, 38th Governor of Massachusetts (d. 1896)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Greenhalge"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1846", "text": "<PERSON>, American astronomer and physicist (d. 1919)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and physicist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and physicist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, French scholar and critic (d. 1906)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French scholar and critic (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French scholar and critic (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_Bruneti%C3%A8re"}]}, {"year": "1860", "text": "<PERSON>, American woman, tried and acquitted for the murders of her father and step-mother in 1892 (d. 1927)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woman, tried and acquitted for the murders of her father and step-mother in 1892 (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American woman, tried and acquitted for the murders of her father and step-mother in 1892 (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON><PERSON><PERSON>, English astronomer (d. 1920)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English astronomer (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English astronomer (d. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, French mineralogist and crystallographer (d. 1933)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mineralogist and crystallographer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mineralogist and crystallographer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American surgeon, co-founder of the Mayo Clinic (d. 1939)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon, co-founder of the <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon, co-founder of the <a href=\"https://wikipedia.org/wiki/Mayo_Clinic\" title=\"Mayo Clinic\">Mayo Clinic</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayo Clinic", "link": "https://wikipedia.org/wiki/Mayo_Clinic"}]}, {"year": "1868", "text": "<PERSON>, American soprano and educator (d. 1944)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jenkins"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Greek general and politician, Greek Minister of Transport (d. 1927)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>enophon_Stratigos\" title=\"Xenophon Stratigos\"><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Infrastructure,_Transport_and_Networks_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Infrastructure, Transport and Networks (Greece)\">Greek Minister of Transport</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Stratigo<PERSON>\" title=\"Xeno<PERSON> Stratigos\"><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Infrastructure,_Transport_and_Networks_(Greece)\" class=\"mw-redirect\" title=\"Ministry of Infrastructure, Transport and Networks (Greece)\">Greek Minister of Transport</a> (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>eno<PERSON>_<PERSON>rat<PERSON>s"}, {"title": "Ministry of Infrastructure, Transport and Networks (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Infrastructure,_Transport_and_Networks_(Greece)"}]}, {"year": "1875", "text": "<PERSON>, American poet and activist (d. 1935)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and activist (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American religious leader, 10th President of The Church of Jesus Christ of Latter-day Saints (d. 1972)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 10th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 10th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1877", "text": "<PERSON>, English cricketer (d. 1949)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German physicist and philosopher (d. 1963)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and philosopher (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and philosopher (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Austrian-American animator and producer (d. 1972)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r\" title=\"Max Fleischer\"><PERSON></a>, Austrian-American animator and producer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r\" title=\"Max Fleischer\"><PERSON></a>, Austrian-American animator and producer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Duke of Saxe-Coburg and Gotha, British-born German nobleman and Nazi politician (d. 1954)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Duke of Saxe-Coburg and Gotha\"><PERSON>, Duke of Saxe-Coburg and Gotha</a>, British-born German nobleman and <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi</a> politician (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha\" title=\"<PERSON>, Duke of Saxe-Coburg and Gotha\"><PERSON>, Duke of Saxe-Coburg and Gotha</a>, British-born German nobleman and <a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi</a> politician (d. 1954)", "links": [{"title": "<PERSON>, Duke of Saxe-Coburg and Gotha", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Coburg_and_Gotha"}, {"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}]}, {"year": "1886", "text": "<PERSON>, Hungarian-Israeli mathematician and academic (d. 1957)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli mathematician and academic (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Israeli mathematician and academic (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON>, German physician (d. 1945)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enno_Lolling"}]}, {"year": "1890", "text": "<PERSON> of Greece (d. 1947)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> II of Greece\"><PERSON> of Greece</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Greece\" title=\"<PERSON> II of Greece\"><PERSON> of Greece</a> (d. 1947)", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Greece"}]}, {"year": "1892", "text": "<PERSON>, Canadian ice hockey player and coach (d. 1957)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Russian actor, playwright, and poet (d. 1930)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor, playwright, and poet (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian actor, playwright, and poet (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Russian mathematician and academic (d. 1959)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Bangladeshi-Pakistani politician, 2nd Prime Minister of Pakistan (d. 1965)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi-Pakistani politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi-Pakistani politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1894", "text": "<PERSON>, American physicist and inventor of the microwave oven (d. 1969)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and inventor of the <a href=\"https://wikipedia.org/wiki/Microwave_oven\" title=\"Microwave oven\">microwave oven</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and inventor of the <a href=\"https://wikipedia.org/wiki/Microwave_oven\" title=\"Microwave oven\">microwave oven</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Microwave oven", "link": "https://wikipedia.org/wiki/Microwave_oven"}]}, {"year": "1895", "text": "<PERSON>, Chinese painter and academic (d. 1953)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and academic (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter and academic (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English film producer (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, English film producer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)\" title=\"<PERSON> (film producer)\"><PERSON></a>, English film producer (d. 1985)", "links": [{"title": "<PERSON> (film producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_producer)"}]}, {"year": "1896", "text": "<PERSON><PERSON> <PERSON><PERSON>, Scottish physician and novelist (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish physician and novelist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> C<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Scottish physician and novelist (d. 1981)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>in"}]}, {"year": "1896", "text": "<PERSON>, American baseball player and sailor (d. 1977)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sailor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sailor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German-American sociologist and philosopher (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sociologist and philosopher (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American sociologist and philosopher (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Indian physician, author, poet, and playwright (d. 1979)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician, author, poet, and playwright (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian physician, author, poet, and playwright (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian singer, director, producer, and screenwriter (d. 1968)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer, director, producer, and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer, director, producer, and screenwriter (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1904", "text": "<PERSON>, American lawyer and farmer (d. 1985)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and farmer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and farmer (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actress (d. 1972)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American contactee (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American contactee (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American contactee (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German engineer, developed Amphicar (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, developed <a href=\"https://wikipedia.org/wiki/Amphicar\" title=\"Amphicar\">Amphicar</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, developed <a href=\"https://wikipedia.org/wiki/Amphicar\" title=\"Amphicar\">Amphicar</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Amphicar", "link": "https://wikipedia.org/wiki/Amphicar"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Indian poet and author (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Balam<PERSON> Amma\"><PERSON><PERSON><PERSON></a>, Indian poet and author (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American prelate (d. 2016)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American prelate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American prelate (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actress and screenwriter (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American baseball player (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Finnish painter (d. 2017)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/%C3%85ke_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish painter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish painter (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%85ke_<PERSON>man"}]}, {"year": "1916", "text": "<PERSON>, American baseball player and manager (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_C<PERSON>rretta"}]}, {"year": "1917", "text": "<PERSON>, American captain and politician, 13th United States Ambassador to the United Nations (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 13th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1919", "text": "<PERSON>, English-American actress (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Greek poet and author (d. 2005)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek poet and author (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ouris"}]}, {"year": "1919", "text": "<PERSON>, English-Canadian soldier, publisher, and politician, 4th Mayor of Mississauga (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian soldier, publisher, and politician, 4th <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian soldier, publisher, and politician, 4th <a href=\"https://wikipedia.org/wiki/Mayor_of_Mississauga\" title=\"Mayor of Mississauga\">Mayor of Mississauga</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Mississauga", "link": "https://wikipedia.org/wiki/Mayor_of_Mississauga"}]}, {"year": "1920", "text": "<PERSON>, American violinist, composer, and conductor (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and conductor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and conductor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Salvadoran-American metallurgist and engineer (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American metallurgist and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran-American metallurgist and engineer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American evangelist, author, radio host (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist, author, radio host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Camping\"><PERSON></a>, American evangelist, author, radio host (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French soldier, race car driver, and politician (d. 1993)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, race car driver, and politician (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, race car driver, and politician (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>t"}]}, {"year": "1921", "text": "<PERSON>, American novelist, short story writer, and playwright (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist, short story writer, and playwright (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist, short story writer, and playwright (d. 2019)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1921", "text": "<PERSON><PERSON>, American physicist and academic, Nobel Prize laureate (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sussman Yalow\"><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Sussman Yalow\"><PERSON><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1922", "text": "<PERSON>, American lieutenant, historian, and politician (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, historian, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, historian, and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American professor, registered nurse, and the widow of baseball player <PERSON>", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professor, registered nurse, and the widow of baseball player <PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professor, registered nurse, and the widow of baseball player <PERSON>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English historian (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American basketball player and coach (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American author and poet (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and poet (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and poet (d. 2004)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1923", "text": "<PERSON>, American lawyer and journalist (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American baseball player and sportscaster (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soldier, lawyer, and politician, 40th United States Secretary of the Interior (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 40th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 40th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior\" title=\"United States Secretary of the Interior\">United States Secretary of the Interior</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Interior", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Interior"}]}, {"year": "1924", "text": "<PERSON>, American actor and producer (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ngle"}]}, {"year": "1924", "text": "<PERSON>, American director, producer, and screenwriter (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American director, producer, and screenwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American director, producer, and screenwriter (d. 2014)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON>, American singer (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress, singer, and dancer (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American author", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, South Korean long-distance runner and a two-time national champion in the marathon (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>il\" title=\"<PERSON>il\"><PERSON></a>, South Korean long-distance runner and a two-time national champion in the <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>il\" title=\"<PERSON>il\"><PERSON></a>, South Korean long-distance runner and a two-time national champion in the <a href=\"https://wikipedia.org/wiki/Marathon\" title=\"Marathon\">marathon</a> (d. 2020)", "links": [{"title": "<PERSON>il", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-chil"}, {"title": "Marathon", "link": "https://wikipedia.org/wiki/Marathon"}]}, {"year": "1929", "text": "<PERSON>, Austrian engineer and businessman, co-founded Glock Ges.m.b.H. (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Glock_Ges.m.b.H.\" title=\"Glock Ges.m.b.H.\">Glock Ges.m.b.H.</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Glock_Ges.m.b.H.\" title=\"Glock Ges.m.b.H.\">Glock Ges.m.b.H.</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>lock Ges.m.b.H.", "link": "https://wikipedia.org/wiki/Glock_Ges.m.b.H."}]}, {"year": "1929", "text": "<PERSON><PERSON>, Bahamian politician", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Or<PERSON>_Turnquest\" title=\"Orville Turnquest\"><PERSON><PERSON></a>, Bahamian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Or<PERSON>_Turnquest\" title=\"Orville Turnquest\"><PERSON><PERSON></a>, Bahamian politician", "links": [{"title": "Orville Turnquest", "link": "https://wikipedia.org/wiki/Orville_Turnquest"}]}, {"year": "1932", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1996)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Swedish biologist and photographer (d. 1987)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biologist and photographer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biologist and photographer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Portuguese lawyer and politician, 111th Prime Minister of Portugal (d. 1980)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro\" class=\"mw-redirect\" title=\"<PERSON> de Sá Carneiro\"><PERSON></a>, Portuguese lawyer and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro\" class=\"mw-redirect\" title=\"<PERSON> Sá Carneiro\"><PERSON></a>, Portuguese lawyer and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Portugal\" title=\"Prime Minister of Portugal\">Prime Minister of Portugal</a> (d. 1980)", "links": [{"title": "<PERSON> Carneiro", "link": "https://wikipedia.org/wiki/Francisco_de_S%C3%A1_Carneiro"}, {"title": "Prime Minister of Portugal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Portugal"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and golfer (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and golfer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and golfer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English pharmacologist and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> IV\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor (d. 1993)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Indian astrophysicist and astronomer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian astrophysicist and astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian astrophysicist and astronomer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English poet and academic (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American singer and actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Dutch politician and diplomat, European Commissioner for Digital Economy and Society", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Digital_Economy_and_Society\" class=\"mw-redirect\" title=\"European Commissioner for Digital Economy and Society\">European Commissioner for Digital Economy and Society</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/European_Commissioner_for_Digital_Economy_and_Society\" class=\"mw-redirect\" title=\"European Commissioner for Digital Economy and Society\">European Commissioner for Digital Economy and Society</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "European Commissioner for Digital Economy and Society", "link": "https://wikipedia.org/wiki/European_Commissioner_for_Digital_Economy_and_Society"}]}, {"year": "1943", "text": "<PERSON>, Singaporean sculptor and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean sculptor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean sculptor and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Italian journalist and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor and singer (d. 1986)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>re"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Estonian chess player (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Canadian actress, director, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish singer-songwriter and musician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Romanian tennis player and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Ilie_N%C4%83stase\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_N%C4%83stase\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian tennis player and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilie_N%C4%83stase"}]}, {"year": "1947", "text": "<PERSON>, Canadian director and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Forcier\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Forcier\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Forcier"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German footballer and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON>rgen_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hans-<PERSON>%C3%<PERSON>rgen_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hans-J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American guitarist and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter, guitarist, producer, and astrophysicist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, producer, and astrophysicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, producer, and astrophysicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1948", "text": "<PERSON>, American keyboard player and songwriter (d. 1980)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, South African politician, 3rd President of South Africa", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Kgal<PERSON>_<PERSON>\" title=\"Kgal<PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kgal<PERSON>_<PERSON>\" title=\"Kgal<PERSON>tl<PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON><PERSON>_<PERSON>"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian politician, Norwegian Minister of Finance", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Norway)\" title=\"Minister of Finance (Norway)\">Norwegian Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Norway)\" title=\"Minister of Finance (Norway)\">Norwegian Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Finance (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Norway)"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English director and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American guitarist and songwriter (d. 1990)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON> American novelist and short story writer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> American novelist and short story writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American playwright (d. 2012)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American playwright (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_O%27Donnell"}]}, {"year": "1954", "text": "<PERSON>, American screenwriter and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27D<PERSON>_(writer)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Serbian-American journalist and historian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Sr%C4%91a_Trifkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-American journalist and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sr%C4%91a_Trifkovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian-American journalist and historian", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sr%C4%91a_Trifkovi%C4%87"}]}, {"year": "1955", "text": "<PERSON>, Indian cricketer and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian lawyer and politician, 24th Premier of Ontario", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}]}, {"year": "1956", "text": "<PERSON>, American computer scientist, designed the IMAP (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, designed the <a href=\"https://wikipedia.org/wiki/IMAP\" class=\"mw-redirect\" title=\"IMAP\">IMAP</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, designed the <a href=\"https://wikipedia.org/wiki/IMAP\" class=\"mw-redirect\" title=\"IMAP\">IMAP</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "IMAP", "link": "https://wikipedia.org/wiki/IMAP"}]}, {"year": "1958", "text": "<PERSON>, Australian tennis player and sportscaster (d. 2013)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and sportscaster (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American wrestler", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1958", "text": "<PERSON>, American conductor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, American conductor", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_(conductor)"}]}, {"year": "1959", "text": "<PERSON>, Argentinian director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Egyptian-Canadian director, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Atom_Egoyan\" title=\"Atom Egoyan\"><PERSON><PERSON></a>, Egyptian-Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atom_Egoyan\" title=\"Atom Egoyan\"><PERSON><PERSON></a>, Egyptian-Canadian director, producer, and screenwriter", "links": [{"title": "Atom Egoyan", "link": "https://wikipedia.org/wiki/Atom_Egoyan"}]}, {"year": "1960", "text": "<PERSON>, English drummer and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Hars<PERSON>_<PERSON>le\" title=\"<PERSON><PERSON><PERSON> Bhogle\"><PERSON><PERSON><PERSON></a>, Indian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rs<PERSON>_<PERSON><PERSON>le\" title=\"<PERSON><PERSON><PERSON> Bhogle\"><PERSON><PERSON><PERSON></a>, Indian journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>le"}]}, {"year": "1961", "text": "<PERSON>, Russian gymnast", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American comedian, actress, and author", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian director and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Mariage\" title=\"<PERSON><PERSON><PERSON><PERSON> Maria<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_Mariage\" title=\"<PERSON><PERSON><PERSON><PERSON> Maria<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Mariage"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hideo_Nakata"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1963", "text": "<PERSON>, Swiss musician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Australian author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nd%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%8D"}]}, {"year": "1965", "text": "<PERSON>, Scottish musician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Claus<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Israeli model and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Canadian comedian, screenwriter, and television host", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian comedian, screenwriter, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian comedian, screenwriter, and television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>ier"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Czech footballer and manager", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American comedian, actor, and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and author", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1969", "text": "<PERSON>, American cinematographer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matthew_Libatique"}]}, {"year": "1970", "text": "<PERSON>, American poker player and software designer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and software designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and software designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, New Zealand politician, 42nd Prime Minister of New Zealand", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 42nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1970", "text": "<PERSON>, Scottish lawyer and politician, First Minister of Scotland", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nicola_<PERSON>"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "1971", "text": "<PERSON>, Estonian tennis player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Ukrainian boxer and politician, Mayor of Kyiv", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian boxer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Kyiv\" title=\"Mayor of Kyiv\">Mayor of Kyiv</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian boxer and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Kyiv\" title=\"Mayor of Kyiv\">Mayor of Kyiv</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Kyiv", "link": "https://wikipedia.org/wiki/Mayor_of_Kyiv"}]}, {"year": "1971", "text": "<PERSON>, American wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Australian television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rowntree\" title=\"<PERSON><PERSON><PERSON> Rowntree\"><PERSON><PERSON><PERSON></a>, Australian television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rowntree\" title=\"<PERSON><PERSON><PERSON> Rowntree\"><PERSON><PERSON><PERSON></a>, Australian television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Montserratian cricketer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>roy Weekes\"><PERSON><PERSON></a>, Montserratian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>roy Weekes\"><PERSON><PERSON></a>, Montserratian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Danish footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English keyboard player and songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1974", "text": "<PERSON>, Mexican wrestler", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Rey_Bucanero\" title=\"Rey Bucanero\"><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>o\" title=\"Rey Bucanero\"><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rey_<PERSON>o"}]}, {"year": "1974", "text": "<PERSON>, German footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>do\" title=\"Francisco Copado\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Copado\" title=\"Francisco Copado\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Copado"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian ice dancer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9e_Pich%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9e_Pich%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9e_Pich%C3%A9"}]}, {"year": "1974", "text": "<PERSON>, American tennis player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Santos\" title=\"Go<PERSON>lo de los Santos\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Gonzalo de los Santos\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American wrestler and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English cricketer and journalist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and journalist", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Josu%C3%A9_<PERSON>un<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Josu%C3%A9_<PERSON>uncia<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Josu%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)"}]}, {"year": "1980", "text": "<PERSON>, Belgian tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Italian race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Nen%C3%AA_(footballer,_born_1981)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nen%C3%AA_(footballer,_born_1981)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1981)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/Nen%C3%AA_(footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON>, Jamaican cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Jamaican cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1981", "text": "<PERSON>, Australian rugby player and sportscaster", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gobble\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gobble\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Gr%C3%A9gory_Vignal\" title=\"<PERSON><PERSON><PERSON><PERSON> Vignal\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gr%C3%A9gory_Vignal\" title=\"<PERSON><PERSON><PERSON><PERSON> Vignal\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gr%C3%A9gory_Vignal"}]}, {"year": "1982", "text": "<PERSON>, American drummer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christopher Bear\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bear\" title=\"Christopher Bear\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Coke\" title=\"Phil Coke\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil_Coke\" title=\"Phil Coke\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English television host and actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian voice actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Byrne\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Byrne\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ryan_O%27Byrne"}]}, {"year": "1984", "text": "<PERSON>, Welsh footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>Mar<PERSON>_<PERSON>\" title=\"LaMar<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>M<PERSON><PERSON>_<PERSON>\" title=\"LaMar<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "La<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/LaMarcus_<PERSON>dridge"}]}, {"year": "1985", "text": "<PERSON>, Chinese footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Russian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Kuzin<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Iranian footballer (d. 2015)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian footballer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Greco\" title=\"Leandro Greco\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Greco\" title=\"Leand<PERSON> Greco\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian wrestler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Brazilian-American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gomes\"><PERSON></a>, Brazilian-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gomes\"><PERSON></a>, Brazilian-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American mixed martial artist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1988", "text": "<PERSON>, American comedian and actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fkreutz\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fkreutz\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fkreutz"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Kov%C3%A1%C5%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Kov%C3%A1%C5%99\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakub_Kov%C3%A1%C5%99"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian-New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Turkish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Eray_%C4%B0%C5%9Fcan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eray_%C4%B0%C5%9Fcan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eray_%C4%B0%C5%9Fcan"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American YouTuber and streamer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and streamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Bissau-Guinean footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1998)\" title=\"<PERSON><PERSON> (footballer, born 1998)\"><PERSON><PERSON></a>, Bissau-Guinean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1998)\" title=\"<PERSON><PERSON> (footballer, born 1998)\"><PERSON><PERSON></a>, Bissau-Guinean footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1998)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1998)"}]}, {"year": "2003", "text": "<PERSON>, American Olympic diver", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Downs\"><PERSON></a>, American Olympic diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tyler Downs\"><PERSON></a>, American Olympic diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "514", "text": "<PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "514 - <a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Symmachus\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Symmachus\" title=\"Pope Symmachus\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Symmachus"}]}, {"year": "806", "text": "<PERSON>, Chinese general (b. 778)", "html": "806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>gu\" title=\"Li Shigu\"><PERSON></a>, Chinese general (b. 778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>gu\" title=\"Li Shigu\"><PERSON></a>, Chinese general (b. 778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Shigu"}]}, {"year": "973", "text": "<PERSON><PERSON><PERSON><PERSON>, Korean monk and poet (b. 917)", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON>yunyeo\" class=\"mw-redirect\" title=\"<PERSON>yunye<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean monk and poet (b. 917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yunyeo\" class=\"mw-redirect\" title=\"<PERSON>yunye<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Korean monk and poet (b. 917)", "links": [{"title": "Kyunyeo", "link": "https://wikipedia.org/wiki/K<PERSON>yeo"}]}, {"year": "998", "text": "<PERSON>, Byzantine general (b. 940)", "html": "998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general (b. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine general (b. 940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1030", "text": "<PERSON><PERSON><PERSON><PERSON>, French bishop", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(bishop_of_Laon)\" title=\"<PERSON><PERSON><PERSON><PERSON> (bishop of Laon)\"><PERSON><PERSON><PERSON><PERSON></a>, French bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(bishop_of_Laon)\" title=\"<PERSON><PERSON><PERSON><PERSON> (bishop of Laon)\"><PERSON><PERSON><PERSON><PERSON></a>, French bishop", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (bishop of Laon)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(bishop_of_Laon)"}]}, {"year": "1234", "text": "<PERSON><PERSON><PERSON>, Dutch nobleman (b. 1210)", "html": "1234 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> IV, Count of Holland\"><PERSON><PERSON><PERSON> IV</a>, Dutch nobleman (b. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV,_Count_of_Holland\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Count of Holland\"><PERSON><PERSON><PERSON> IV</a>, Dutch nobleman (b. 1210)", "links": [{"title": "<PERSON><PERSON><PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/Flor<PERSON>_IV,_Count_of_Holland"}]}, {"year": "1249", "text": "<PERSON><PERSON><PERSON>, doge of Venice", "html": "1249 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, doge of Venice", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, doge of Venice", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1333", "text": "<PERSON>, Scottish nobleman", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Atholl\" title=\"<PERSON>, Earl of Atholl\"><PERSON></a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Atholl\" title=\"<PERSON>, Earl of Atholl\"><PERSON></a>, Scottish nobleman", "links": [{"title": "<PERSON>, Earl of Atholl", "link": "https://wikipedia.org/wiki/<PERSON>,_Earl_<PERSON>_<PERSON>l"}]}, {"year": "1333", "text": "<PERSON>, Scottish nobleman", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Carrick\" title=\"<PERSON>, Earl of Carrick\"><PERSON></a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_of_Carrick\" title=\"<PERSON>, Earl of Carrick\"><PERSON></a>, Scottish nobleman", "links": [{"title": "<PERSON>, Earl of Carrick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Earl_<PERSON>_Carrick"}]}, {"year": "1333", "text": "Sir <PERSON>, Scottish nobleman", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1333)\" title=\"<PERSON> (died 1333)\">Sir <PERSON></a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1333)\" title=\"<PERSON> (died 1333)\">Sir <PERSON></a>, Scottish nobleman", "links": [{"title": "<PERSON> (died 1333)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(died_1333)"}]}, {"year": "1333", "text": "<PERSON><PERSON>, Scottish nobleman", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Earl_<PERSON>_Lennox\" title=\"<PERSON><PERSON>, Earl of Lennox\"><PERSON><PERSON></a>, Scottish nobleman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Earl_<PERSON>_Lennox\" title=\"<PERSON><PERSON>, Earl of Lennox\"><PERSON><PERSON></a>, Scottish nobleman", "links": [{"title": "<PERSON><PERSON>, Earl of Lennox", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_of_Lennox"}]}, {"year": "1333", "text": "<PERSON>, 4th Earl <PERSON> Sutherland", "html": "1333 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Sutherland\" title=\"<PERSON>, 4th Earl <PERSON>\"><PERSON>, 4th Earl <PERSON> Sutherland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_Sutherland\" title=\"<PERSON>, 4th Earl <PERSON>\"><PERSON>, 4th Earl <PERSON></a>", "links": [{"title": "<PERSON>, 4th Earl <PERSON> Sutherland", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_<PERSON>_<PERSON>"}]}, {"year": "1374", "text": "<PERSON><PERSON>, Italian poet and scholar (b. 1304)", "html": "1374 - <a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\"><PERSON><PERSON></a>, Italian poet and scholar (b. 1304)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petrarch\" title=\"Petrarch\"><PERSON><PERSON></a>, Italian poet and scholar (b. 1304)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petrarch"}]}, {"year": "1415", "text": "<PERSON><PERSON> of Lancaster, Portuguese queen (b. 1360)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> of Lancaster\"><PERSON><PERSON> of Lancaster</a>, Portuguese queen (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> of Lancaster\"><PERSON><PERSON> of Lancaster</a>, Portuguese queen (b. 1360)", "links": [{"title": "<PERSON><PERSON> of Lancaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Lancaster"}]}, {"year": "1543", "text": "<PERSON>, English daughter of <PERSON>, Countess of Wiltshire (b. 1499)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire\" title=\"<PERSON>, Countess of Wiltshire\"><PERSON>, Countess of Wiltshire</a> (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire\" title=\"<PERSON>, Countess of Wiltshire\"><PERSON>, Countess of Wiltshire</a> (b. 1499)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, Countess of Wiltshire", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Wiltshire"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON>, Italian philosopher and academic (b. 1550)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON><PERSON><PERSON> (philosopher)\"><PERSON><PERSON><PERSON></a>, Italian philosopher and academic (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON><PERSON><PERSON> (philosopher)\"><PERSON><PERSON><PERSON></a>, Italian philosopher and academic (b. 1550)", "links": [{"title": "<PERSON><PERSON><PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(philosopher)"}]}, {"year": "1742", "text": "<PERSON>, English poet and author (b. 1675)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1810", "text": "<PERSON> of Mecklenburg-Strelitz, Prussian queen (b. 1776)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>, Prussian queen (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a>, Prussian queen (b. 1776)", "links": [{"title": "<PERSON> of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mecklenburg-Strelitz"}]}, {"year": "1814", "text": "<PERSON>, English navigator and cartographer (b. 1774)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and cartographer (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English navigator and cartographer (b. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican general and emperor (b. 1783)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"<PERSON><PERSON><PERSON><PERSON> de Iturbide\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general and emperor (b. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide\" title=\"<PERSON><PERSON><PERSON><PERSON> de Iturbide\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican general and emperor (b. 1783)", "links": [{"title": "Agustín de Iturbide", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_de_Iturbide"}]}, {"year": "1838", "text": "<PERSON>, French physicist and chemist (b. 1785)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and chemist (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, American journalist and critic (b. 1810)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Russian poet and translator (b. 1787)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and translator (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and translator (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Swiss statistician and politician (b. 1796)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss statistician and politician (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss statistician and politician (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Russian mathematician and academic (b. 1847)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mathematician and academic (b. 1847)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English criminal and failed regicide (b. 1824)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal and failed regicide (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal and failed regicide (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, American publisher and religious leader (b. 1859)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and religious leader (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and religious leader (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Colombian lawyer and politician, 15th President of Colombia (b. 1852)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Cl%C3%ADmaco_Calder%C3%B3n\" title=\"Clímaco <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Colombian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%ADmaco_Calder%C3%B3n\" title=\"Clímaco <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Colombian lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1852)", "links": [{"title": "Clímaco <PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%ADmaco_Calder%C3%B3n"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1925", "text": "<PERSON>, British lawyer (b. 1851)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British lawyer (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British lawyer (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Scottish-New Zealand politician, 13th Prime Minister of New Zealand (b. 1844)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Finnish historian and academic (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish historian and academic (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish historian and academic (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American poet and author (b. 1850)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose <PERSON> Thorpe\"><PERSON></a>, American poet and author (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rose <PERSON> Thorpe\"><PERSON></a>, American poet and author (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thorpe"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Serbian painter, victim of Genocide of Serbs", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C5%A0piro_Bocari%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian painter, victim of <a href=\"https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia\" title=\"Genocide of Serbs in the Independent State of Croatia\">Genocide of Serbs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0<PERSON>ro_<PERSON>ri%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian painter, victim of <a href=\"https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia\" title=\"Genocide of Serbs in the Independent State of Croatia\">Genocide of Serbs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%A0piro_Bocari%C4%87"}, {"title": "Genocide of Serbs in the Independent State of Croatia", "link": "https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Russian captain and pilot (b. 1916)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian captain and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian captain and pilot (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Italian poet and opera librettist (b. 1873)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and opera librettist (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and opera librettist (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Burmese educator and politician (b. 1898)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/U_Razak\" title=\"U Razak\"><PERSON></a>, Burmese educator and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U_Razak\" title=\"U Razak\"><PERSON></a>, Burmese educator and politician (b. 1898)", "links": [{"title": "U <PERSON>zak", "link": "https://wikipedia.org/wiki/U_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Burmese general and politician (b. 1915)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Aung_San\" title=\"Aung San\"><PERSON><PERSON></a>, Burmese general and politician (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aung_San\" title=\"Aung San\"><PERSON><PERSON></a>, Burmese general and politician (b. 1915)", "links": [{"title": "Aung San", "link": "https://wikipedia.org/wiki/Aung_San"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, South Korean politician (b. 1886)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung\" title=\"<PERSON><PERSON><PERSON>-hyung\"><PERSON><PERSON><PERSON></a>, South Korean politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung\" title=\"<PERSON><PERSON><PERSON>-hyung\"><PERSON><PERSON><PERSON></a>, South Korean politician (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-hyung"}]}, {"year": "1963", "text": "<PERSON>, English priest (b. 1884)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English priest (b. 1884)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, South Korean journalist and politician, 1st President of South Korea (b. 1875)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\"><PERSON><PERSON><PERSON></a>, South Korean journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yn<PERSON>_<PERSON>\" title=\"Syngman Rhee\"><PERSON><PERSON><PERSON></a>, South Korean journalist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1967", "text": "<PERSON>, United States Assistant Secretary of Defense for International Security Affairs and an advisor to <PERSON> (b. 1921)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_Defense_for_International_Security_Affairs\" class=\"mw-redirect\" title=\"United States Assistant Secretary of Defense for International Security Affairs\">United States Assistant Secretary of Defense for International Security Affairs</a> and an advisor to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_Defense_for_International_Security_Affairs\" class=\"mw-redirect\" title=\"United States Assistant Secretary of Defense for International Security Affairs\">United States Assistant Secretary of Defense for International Security Affairs</a> and an advisor to <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of Defense for International Security Affairs", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_Defense_for_International_Security_Affairs"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American poet and politician, 66th Lieutenant Governor of Connecticut (b. 1884)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and politician, 66th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet and politician, 66th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Lieutenant Governor of Connecticut\">Lieutenant Governor of Connecticut</a> (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Connecticut", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Connecticut"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Greek soldier and author (b. 1890)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek soldier and author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek soldier and author (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Hungarian-American soccer player and coach (b. 1904)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ern%C5%91_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soccer player and coach (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ern%C5%91_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American soccer player and coach (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ern%C5%91_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1928)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frizzell\" title=\"<PERSON><PERSON> Frizzell\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Frizzell\" title=\"<PERSON><PERSON> Frizzell\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lefty_Frizzell"}]}, {"year": "1977", "text": "<PERSON>, Estonian geographer, author, and poet (b. 1912)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian geographer, author, and poet (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian geographer, author, and poet (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American journalist and author (b. 1901)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American journalist and author (b. 1901)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Turkish jurist and politician, 13th Prime Minister of Turkey (b. 1912)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_E<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish jurist and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_E<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish jurist and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nihat_Erim"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1980", "text": "<PERSON>, German-American political scientist, philosopher, and academic (b. 1904)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist, philosopher, and academic (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American political scientist, philosopher, and academic (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian tenor (b. 1919)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American physicist and mathematician (b. 1930)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Russian actress (b. 1896)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian actress (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>,  Iraqi writer and translator (b. 1895)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi writer and translator (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi writer and translator (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Polish author (b. 1938)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish author (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish businessman and politician, President of the Republic of Poland (b. 1913)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"President of the Republic of Poland\">President of the Republic of Poland</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Republic of Poland", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Poland"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1907)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Italian lawyer and judge (b. 1940)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and judge (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian author and academic (b. 1896)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Polish-American baseball player, coach, and manager (b. 1921)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American baseball player, coach, and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American baseball player, coach, and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American historian, scholar, and activist (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, scholar, and activist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, scholar, and activist (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American evangelist and author, founded the Campus Crusade for Christ (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author, founded the <a href=\"https://wikipedia.org/wiki/Campus_Crusade_for_Christ\" class=\"mw-redirect\" title=\"Campus Crusade for Christ\">Campus Crusade for Christ</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and author, founded the <a href=\"https://wikipedia.org/wiki/Campus_Crusade_for_Christ\" class=\"mw-redirect\" title=\"Campus Crusade for Christ\">Campus Crusade for Christ</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Campus Crusade for Christ", "link": "https://wikipedia.org/wiki/Campus_Crusade_for_Christ"}]}, {"year": "2003", "text": "<PERSON>, Swiss politician, President of the Swiss National Council (b. 1908)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_National_Council\" class=\"mw-redirect\" title=\"President of the Swiss National Council\">President of the Swiss National Council</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_National_Council\" class=\"mw-redirect\" title=\"President of the Swiss National Council\">President of the Swiss National Council</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Swiss National Council", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_National_Council"}]}, {"year": "2004", "text": "<PERSON>, Canadian sculptor (b. 1902)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American entomologist, mountaineer, and DDT advocate (b. 1919)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entomologist and mountaineer)\"><PERSON><PERSON></a>, American entomologist, mountaineer, and DDT advocate (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (entomologist and mountaineer)\"><PERSON><PERSON></a>, American entomologist, mountaineer, and DDT advocate (b. 1919)", "links": [{"title": "<PERSON><PERSON> (entomologist and mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(entomologist_and_mountaineer)"}]}, {"year": "2004", "text": "<PERSON>, American priest and journalist (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and journalist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and journalist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 70th Prime Minister of Japan (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Zenk%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 70th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zenk%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 70th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zenk%C5%8D_Suzuki"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "2005", "text": "<PERSON>, American author and screenwriter (b. 1933)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON> <PERSON><PERSON>, Bangladeshi journalist, lawyer, and politician (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/A._<PERSON>._Faezul_Huq\" title=\"A. K. Faezul Huq\"><PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi journalist, lawyer, and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._<PERSON>._F<PERSON>zul_<PERSON>q\" title=\"<PERSON><PERSON> <PERSON>. Fae<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Bangladeshi journalist, lawyer, and politician (b. 1945)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>q"}]}, {"year": "2007", "text": "<PERSON>, Argentinian cartoonist (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian cartoonist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian cartoonist (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, Brazilian comedian and actress (b. 1907)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Der<PERSON>_<PERSON>n%C3%A7alves\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian comedian and actress (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Der<PERSON>_<PERSON>n%C3%A7alves\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian comedian and actress (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dercy_Gon%C3%A7alves"}]}, {"year": "2009", "text": "<PERSON>, American author and educator (b. 1930)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English race car driver (b. 1991)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress, author, television screenwriter and director (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, author, television screenwriter and director (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, author, television screenwriter and director (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9ci<PERSON>_<PERSON><PERSON>ry"}]}, {"year": "2010", "text": "<PERSON>, Australian author and playwright (b. 1917)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Bangladeshi director and playwright (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi director and playwright (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi director and playwright (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American comedian, actor, and screenwriter (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian, actor, and screenwriter (b. 1952)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "2012", "text": "<PERSON>, Iranian meteorologist and academic (b. 1912)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian meteorologist and academic (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian meteorologist and academic (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Egyptian general and politician, 16th Vice President of Egypt (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Egyptian general and politician, 16th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Egypt\" class=\"mw-redirect\" title=\"Vice President of Egypt\">Vice President of Egypt</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Egyptian general and politician, 16th <a href=\"https://wikipedia.org/wiki/Vice_President_of_Egypt\" class=\"mw-redirect\" title=\"Vice President of Egypt\">Vice President of Egypt</a> (b. 1935)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}, {"title": "Vice President of Egypt", "link": "https://wikipedia.org/wiki/Vice_President_of_Egypt"}]}, {"year": "2012", "text": "<PERSON>, American businesswoman, co-founded Sylvia's Restaurant of Harlem (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Sylvia%27s_Restaurant_of_Harlem\" title=\"Sylvia's Restaurant of Harlem\"><PERSON>'s Restaurant of Harlem</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Sylvia%27s_Restaurant_of_Harlem\" title=\"Sylvia's Restaurant of Harlem\"><PERSON>'s Restaurant of Harlem</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sylvia's Restaurant of Harlem", "link": "https://wikipedia.org/wiki/Sylvia%27s_Restaurant_of_Harlem"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Islamic cleric (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>pov\" title=\"<PERSON><PERSON><PERSON> Yakupov\"><PERSON><PERSON><PERSON></a>, Islamic cleric (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v\" title=\"<PERSON><PERSON><PERSON> Yakupov\"><PERSON><PERSON><PERSON></a>, Islamic cleric (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "2013", "text": "<PERSON>, Russian singer-songwriter (b. 1973)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Canadian-American wrestler and trainer (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Geeto_Mongol\" title=\"Geeto Mongol\"><PERSON><PERSON> Mongol</a>, Canadian-American wrestler and trainer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Geeto_Mongol\" title=\"Geeto Mongol\"><PERSON><PERSON></a>, Canadian-American wrestler and trainer (b. 1931)", "links": [{"title": "Geeto Mongol", "link": "https://wikipedia.org/wiki/Geeto_Mongol"}]}, {"year": "2013", "text": "<PERSON>, English actor, director, and screenwriter (b. 1952)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German footballer and manager (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh-American soccer player and manager (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American soccer player and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American soccer player and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nam"}]}, {"year": "2013", "text": "<PERSON>, Swiss geologist and academic (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss geologist and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss geologist and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Turkish author (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_E<PERSON>il\" title=\"<PERSON><PERSON> Erbil\"><PERSON><PERSON></a>, Turkish author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il\" title=\"<PERSON><PERSON> Erbil\"><PERSON><PERSON></a>, Turkish author (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>il"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Brazilian theologian (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>ubem_Alves\" title=\"Rubem Alves\"><PERSON><PERSON><PERSON></a>, Brazilian theologian (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>m_Alves\" title=\"<PERSON>ubem Alves\"><PERSON><PERSON><PERSON></a>, Brazilian theologian (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON> Al<PERSON>", "link": "https://wikipedia.org/wiki/Rubem_Alves"}]}, {"year": "2014", "text": "<PERSON>, American child actress and child model (b. 1992)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Mc<PERSON>ole Bart<PERSON>\"><PERSON></a>, American child actress and child model (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Mc<PERSON>ole Bart<PERSON>\"><PERSON></a>, American child actress and child model (b. 1992)", "links": [{"title": "<PERSON> Mc<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American political scientist and academic (b. 1917)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American political scientist and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American political scientist and academic (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American pediatrician and author (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American actor (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Polish biologist (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish biologist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish biologist (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English footballer and manager (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager (b. 1924)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Swedish journalist (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Ingemar_Odlander\" title=\"Ingemar Odlander\">Ingemar <PERSON></a>, Swedish journalist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingemar_Odlander\" title=\"Ingemar Odlander\">In<PERSON><PERSON></a>, Swedish journalist (b. 1936)", "links": [{"title": "Ingemar <PERSON>", "link": "https://wikipedia.org/wiki/Ingemar_Odlander"}]}, {"year": "2014", "text": "<PERSON>, English cricketer (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Dutch politician (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>leg<PERSON>rt"}]}, {"year": "2014", "text": "<PERSON>, American baseball player, coach, and journalist (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and journalist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and journalist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American composer and conductor (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian swimmer and journalist (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian swimmer and journalist (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian swimmer and journalist (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, pianist, and producer (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Carmino_Ravosa\" title=\"Carmino Ravosa\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carmino_Ravosa\" title=\"Carmino Ravosa\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carmino_Ravosa"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian journalist and politician, 2nd Speaker of the Duma (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Duma\" class=\"mw-redirect\" title=\"Speaker of the Duma\">Speaker of the Duma</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Duma\" class=\"mw-redirect\" title=\"Speaker of the Duma\">Speaker of the Duma</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Duma", "link": "https://wikipedia.org/wiki/Speaker_of_the_<PERSON><PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor, director, and producer (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American producer, director, voice actor, editor, writer, cartoonist, animator, and cinematographer (b. 1967)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer, director, voice actor, editor, writer, cartoonist, animator, and cinematographer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer, director, voice actor, editor, writer, cartoonist, animator, and cinematographer (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Kazakhstani figure skater (b. 1993)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ten\" title=\"Denis Ten\"><PERSON></a>, Kazakhstani figure skater (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denis_Ten\" title=\"Denis Ten\"><PERSON></a>, Kazakhstani figure skater (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Dutch actor, director, and producer (b. 1944)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch actor, director, and producer (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Malian musician (b. 1965)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Toumani_Diabat%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian musician (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toumani_Diabat%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian musician (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toumani_Diabat%C3%A9"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Ukrainian linguist and politician (b. 1964)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian linguist and politician (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian linguist and politician (b. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>ion"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Australian athlete and administrator (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian athlete and administrator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian athlete and administrator (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer and politician (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese politician, General Secretary of the Communist Party of Vietnam (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ph%C3%BA_Tr%E1%BB%8Dng\" title=\"Nguyễn <PERSON> Trọng\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician, <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_Vietnam\" title=\"General Secretary of the Communist Party of Vietnam\">General Secretary of the Communist Party of Vietnam</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ph%C3%BA_Tr%E1%BB%8Dng\" title=\"Nguyễn Phú Trọng\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese politician, <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_Vietnam\" title=\"General Secretary of the Communist Party of Vietnam\">General Secretary of the Communist Party of Vietnam</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ph%C3%BA_Tr%E1%BB%8Dng"}, {"title": "General Secretary of the Communist Party of Vietnam", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_Vietnam"}]}, {"year": "2024", "text": "<PERSON>, Welsh snooker player and police officer (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and police officer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and police officer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American political scientist and anthropologist (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and anthropologist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and anthropologist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, South African actress (b. 1973)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Esta_TerBlanche\" title=\"Esta TerBlanche\"><PERSON><PERSON></a>, South African actress (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>B<PERSON>\" title=\"<PERSON>sta TerBlanche\"><PERSON><PERSON></a>, South African actress (b. 1973)", "links": [{"title": "Esta TerBlanche", "link": "https://wikipedia.org/wiki/Esta_TerBlanche"}]}]}}