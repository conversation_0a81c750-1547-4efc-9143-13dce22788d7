{"date": "April 14", "url": "https://wikipedia.org/wiki/April_14", "data": {"Events": [{"year": "43 BC", "text": "Legions loyal to the Roman Senate, commanded by <PERSON>, defeat the forces of <PERSON> in the Battle of Forum Gallorum.", "html": "43 BC - 43 BC - Legions loyal to the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>, commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_V<PERSON>us_<PERSON>_<PERSON>ianus\" title=\"<PERSON>\"><PERSON></a>, defeat the forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Forum_Gallorum\" title=\"Battle of Forum Gallorum\">Battle of Forum Gallorum</a>.", "no_year_html": "43 BC - Legions loyal to the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a>, commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_Vibius_<PERSON>_<PERSON>ianus\" title=\"<PERSON>ibi<PERSON>ianus\"><PERSON></a>, defeat the forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Forum_Gallorum\" title=\"Battle of Forum Gallorum\">Battle of Forum Gallorum</a>.", "links": [{"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>ianus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Forum Gallorum", "link": "https://wikipedia.org/wiki/Battle_of_Forum_Gallorum"}]}, {"year": "69", "text": "<PERSON><PERSON><PERSON><PERSON>, commanding Rhine-based armies, defeats Roman emperor <PERSON><PERSON><PERSON> in the First Battle of Bedriacum to take power over Rome.", "html": "69 - <a href=\"https://wikipedia.org/wiki/Vitellius\" title=\"Vitelli<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, commanding <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a>-based armies, defeats Roman emperor <a href=\"https://wikipedia.org/wiki/Otho\" title=\"Otho\">Oth<PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bedriacum#First_Battle_of_Bedriacum\" title=\"Battle of Bedriacum\">First Battle of Bedriacum</a> to take power over <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vitelli<PERSON>\" title=\"Vitelli<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, commanding <a href=\"https://wikipedia.org/wiki/Rhine\" title=\"Rhine\">Rhine</a>-based armies, defeats Roman emperor <a href=\"https://wikipedia.org/wiki/Otho\" title=\"Otho\">Oth<PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Bedriacum#First_Battle_of_Bedriacum\" title=\"Battle of Bedriacum\">First Battle of Bedriacum</a> to take power over <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vitellius"}, {"title": "Rhine", "link": "https://wikipedia.org/wiki/Rhine"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otho"}, {"title": "Battle of Bedriacum", "link": "https://wikipedia.org/wiki/Battle_of_Bedriacum#First_Battle_of_Bedriacum"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}]}, {"year": "966", "text": "Following his marriage to the Christian <PERSON><PERSON><PERSON><PERSON><PERSON> of Bohemia, the pagan ruler of the Polans, <PERSON><PERSON><PERSON><PERSON>, converts to Christianity, an event considered to be the founding of the Polish state.", "html": "966 - Following his marriage to the Christian <a href=\"https://wikipedia.org/wiki/Doubravka_of_Bohemia\" title=\"Doubravka of Bohemia\"><PERSON>ub<PERSON><PERSON><PERSON> of Bohemia</a>, the <a href=\"https://wikipedia.org/wiki/Paganism\" title=\"Paganism\">pagan</a> ruler of the <a href=\"https://wikipedia.org/wiki/Polans_(western)\" title=\"Polans (western)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mieszko_I\" title=\"<PERSON>eszko I\"><PERSON><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Christianization_of_Poland#Baptism\" title=\"Christianization of Poland\">converts to Christianity</a>, an event considered to be the <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty\" title=\"History of Poland during the Piast dynasty\">founding of the Polish state</a>.", "no_year_html": "Following his marriage to the Christian <a href=\"https://wikipedia.org/wiki/Doubravka_of_Bohemia\" title=\"Doubravka of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Bohemia</a>, the <a href=\"https://wikipedia.org/wiki/Paganism\" title=\"Paganism\">pagan</a> ruler of the <a href=\"https://wikipedia.org/wiki/Polans_(western)\" title=\"Polans (western)\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mieszko_I\" title=\"<PERSON><PERSON>z<PERSON> I\"><PERSON><PERSON><PERSON><PERSON> I</a>, <a href=\"https://wikipedia.org/wiki/Christianization_of_Poland#Baptism\" title=\"Christianization of Poland\">converts to Christianity</a>, an event considered to be the <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty\" title=\"History of Poland during the Piast dynasty\">founding of the Polish state</a>.", "links": [{"title": "Doubravka of Bohemia", "link": "https://wikipedia.org/wiki/Doubravka_of_Bohemia"}, {"title": "Paganism", "link": "https://wikipedia.org/wiki/Paganism"}, {"title": "Polans (western)", "link": "https://wikipedia.org/wiki/Pol<PERSON>_(western)"}, {"title": "Mieszko I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Christianization of Poland", "link": "https://wikipedia.org/wiki/Christianization_of_Poland#Baptism"}, {"title": "History of Poland during the Piast dynasty", "link": "https://wikipedia.org/wiki/History_of_Poland_during_the_Piast_dynasty"}]}, {"year": "972", "text": "<PERSON>, Co-Emperor of the Holy Roman Empire, marries Byzantine princess <PERSON><PERSON><PERSON>. She is crowned empress by Pope <PERSON> in Rome the same day.", "html": "972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>, Co-<a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>, marries Byzantine princess <a href=\"https://wikipedia.org/wiki/Theo<PERSON>nu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>. She is crowned empress by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_John_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> in Rome the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a>, Co-<a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a>, marries Byzantine princess <a href=\"https://wikipedia.org/wiki/Theophanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>. She is crowned empress by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_John_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a> in Rome the same day.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>phanu"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1395", "text": "Tokhtamysh-Timur war: At the Battle of the Terek River, <PERSON><PERSON> defeats the army of the Golden Horde, beginning the khanate's permanent military decline.", "html": "1395 - <a href=\"https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war\" title=\"Tokhtamysh-Timur war\">Tokhtamysh-Timur war</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Terek_River\" title=\"Battle of the Terek River\">Battle of the Terek River</a>, <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON><PERSON></a> defeats the army of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a>, beginning the khanate's permanent military decline.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war\" title=\"Tokhtamysh-Timur war\">Tokhtamysh-Timur war</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Terek_River\" title=\"Battle of the Terek River\">Battle of the Terek River</a>, <a href=\"https://wikipedia.org/wiki/Timur\" title=\"Timur\"><PERSON><PERSON></a> defeats the army of the <a href=\"https://wikipedia.org/wiki/Golden_Horde\" title=\"Golden Horde\">Golden Horde</a>, beginning the khanate's permanent military decline.", "links": [{"title": "Tokhtamysh-Timur war", "link": "https://wikipedia.org/wiki/Tokhtamysh%E2%80%93Timur_war"}, {"title": "Battle of the Terek River", "link": "https://wikipedia.org/wiki/Battle_of_the_Terek_River"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ur"}, {"title": "Golden Horde", "link": "https://wikipedia.org/wiki/Golden_Horde"}]}, {"year": "1471", "text": "In England, the Yorkists under <PERSON> defeat the Lancastrians under the <PERSON> at the Battle of Barnet; the <PERSON> is killed and <PERSON> resumes the throne.", "html": "1471 - In England, the <a href=\"https://wikipedia.org/wiki/House_of_York\" title=\"House of York\">Yorkists</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV</a> defeat the <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lancastrians</a> under the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\">Earl of Warwick</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Barnet\" title=\"Battle of Barnet\">Battle of Barnet</a>; the Earl is killed and <PERSON> resumes the throne.", "no_year_html": "In England, the <a href=\"https://wikipedia.org/wiki/House_of_York\" title=\"House of York\">Yorkists</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"<PERSON> IV of England\"><PERSON> IV</a> defeat the <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lancastrians</a> under the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\">Earl of Warwick</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Barnet\" title=\"Battle of Barnet\">Battle of Barnet</a>; the Earl is killed and <PERSON> resumes the throne.", "links": [{"title": "House of York", "link": "https://wikipedia.org/wiki/House_of_York"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "House of Lancaster", "link": "https://wikipedia.org/wiki/House_of_Lancaster"}, {"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}, {"title": "Battle of Barnet", "link": "https://wikipedia.org/wiki/Battle_of_Barnet"}]}, {"year": "1561", "text": "A celestial phenomenon is reported over Nuremberg, described as an aerial battle.", "html": "1561 - A <a href=\"https://wikipedia.org/wiki/1561_celestial_phenomenon_over_Nuremberg\" title=\"1561 celestial phenomenon over Nuremberg\">celestial phenomenon</a> is reported over <a href=\"https://wikipedia.org/wiki/Nuremberg\" title=\"Nuremberg\">Nuremberg</a>, described as an aerial battle.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1561_celestial_phenomenon_over_Nuremberg\" title=\"1561 celestial phenomenon over Nuremberg\">celestial phenomenon</a> is reported over <a href=\"https://wikipedia.org/wiki/Nuremberg\" title=\"Nuremberg\">Nuremberg</a>, described as an aerial battle.", "links": [{"title": "1561 celestial phenomenon over Nuremberg", "link": "https://wikipedia.org/wiki/1561_celestial_phenomenon_over_Nuremberg"}, {"title": "Nuremberg", "link": "https://wikipedia.org/wiki/Nuremberg"}]}, {"year": "1639", "text": "Thirty Years' War: Forces of the Holy Roman Empire and Electorate of Saxony are defeated by the Swedes at the Battle of Chemnitz, ending the military effectiveness of the Saxon army for the rest of the war and allowing the Swedes to advance into Bohemia.", "html": "1639 - <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: Forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Electorate of Saxony</a> are defeated by the Swedes at the <a href=\"https://wikipedia.org/wiki/Battle_of_Chemnitz\" title=\"Battle of Chemnitz\">Battle of Chemnitz</a>, ending the military effectiveness of the Saxon army for the rest of the war and allowing the Swedes to advance into <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>: Forces of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Electorate of Saxony</a> are defeated by the Swedes at the <a href=\"https://wikipedia.org/wiki/Battle_of_Chemnitz\" title=\"Battle of Chemnitz\">Battle of Chemnitz</a>, ending the military effectiveness of the Saxon army for the rest of the war and allowing the Swedes to advance into <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a>.", "links": [{"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Electorate of Saxony", "link": "https://wikipedia.org/wiki/Electorate_of_Saxony"}, {"title": "Battle of Chemnitz", "link": "https://wikipedia.org/wiki/Battle_of_Chemnitz"}, {"title": "Bohemia", "link": "https://wikipedia.org/wiki/Bohemia"}]}, {"year": "1775", "text": "The Society for the Relief of Free Negroes Unlawfully Held in Bondage, the first abolition society in North America, is organized in Philadelphia by <PERSON> and <PERSON>.", "html": "1775 - The <a href=\"https://wikipedia.org/wiki/Pennsylvania_Abolition_Society\" title=\"Pennsylvania Abolition Society\">Society for the Relief of Free Negroes Unlawfully Held in Bondage</a>, the first <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolition</a> society in North America, is organized in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benjamin <PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pennsylvania_Abolition_Society\" title=\"Pennsylvania Abolition Society\">Society for the Relief of Free Negroes Unlawfully Held in Bondage</a>, the first <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_States\" title=\"Abolitionism in the United States\">abolition</a> society in North America, is organized in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benjamin Rush\"><PERSON></a>.", "links": [{"title": "Pennsylvania Abolition Society", "link": "https://wikipedia.org/wiki/Pennsylvania_Abolition_Society"}, {"title": "Abolitionism in the United States", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_States"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "The French troops led by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> defeat the slaves settlers in the Siege of Port-au-Prince.", "html": "1793 - The French troops led by <a href=\"https://wikipedia.org/wiki/L%C3%A9ger-F%C3%A9licit%C3%A9_Sonthonax\" title=\"Léger-Félicité Sonthonax\">Léger-Félicité Sonthonax</a> defeat the slaves settlers in the <a href=\"https://wikipedia.org/wiki/Siege_of_Port-au-Prince_(1793)\" title=\"Siege of Port-au-Prince (1793)\">Siege of Port-au-Prince</a>.", "no_year_html": "The French troops led by <a href=\"https://wikipedia.org/wiki/L%C3%A9ger-F%C3%A9licit%C3%A9_Sonthonax\" title=\"Léger-Félicité Sonthonax\">Léger-Félicité Sonthonax</a> defeat the slaves settlers in the <a href=\"https://wikipedia.org/wiki/Siege_of_Port-au-Prince_(1793)\" title=\"Siege of Port-au-Prince (1793)\">Siege of Port-au-Prince</a>.", "links": [{"title": "Léger<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9ger-F%C3%A9licit%C3%A9_Sonthonax"}, {"title": "Siege of Port-au-Prince (1793)", "link": "https://wikipedia.org/wiki/Siege_of_Port-au-Prince_(1793)"}]}, {"year": "1816", "text": "<PERSON><PERSON>, a slave in British-ruled Barbados, leads a slave rebellion, for which he is remembered as the country's first national hero.", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Bussa%27s_rebellion#Bussa\" title=\"<PERSON><PERSON>'s rebellion\"><PERSON><PERSON></a>, a slave in British-ruled <a href=\"https://wikipedia.org/wiki/Barbados\" title=\"Barbados\">Barbados</a>, <a href=\"https://wikipedia.org/wiki/Bussa%27s_rebellion\" title=\"<PERSON><PERSON>'s rebellion\">leads a slave rebellion</a>, for which he is remembered as the country's first national hero.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bussa%27s_rebellion#Bussa\" title=\"<PERSON><PERSON>'s rebellion\"><PERSON><PERSON></a>, a slave in British-ruled <a href=\"https://wikipedia.org/wiki/Barbados\" title=\"Barbados\">Barbados</a>, <a href=\"https://wikipedia.org/wiki/Bussa%27s_rebellion\" title=\"<PERSON><PERSON>'s rebellion\">leads a slave rebellion</a>, for which he is remembered as the country's first national hero.", "links": [{"title": "<PERSON><PERSON>'s rebellion", "link": "https://wikipedia.org/wiki/Bussa%27s_rebellion#Bussa"}, {"title": "Barbados", "link": "https://wikipedia.org/wiki/Barbados"}, {"title": "<PERSON><PERSON>'s rebellion", "link": "https://wikipedia.org/wiki/Bussa%27s_rebellion"}]}, {"year": "1849", "text": "Hungary declares itself independent of Austria with <PERSON><PERSON> as its leader.", "html": "1849 - Hungary <a href=\"https://wikipedia.org/wiki/Hungarian_Declaration_of_Independence\" title=\"Hungarian Declaration of Independence\">declares itself independent</a> of <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austria</a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lajos <PERSON>\"><PERSON><PERSON></a> as its leader.", "no_year_html": "Hungary <a href=\"https://wikipedia.org/wiki/Hungarian_Declaration_of_Independence\" title=\"Hungarian Declaration of Independence\">declares itself independent</a> of <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austria</a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lajos <PERSON>\"><PERSON><PERSON></a> as its leader.", "links": [{"title": "Hungarian Declaration of Independence", "link": "https://wikipedia.org/wiki/Hungarian_Declaration_of_Independence"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_<PERSON>uth"}]}, {"year": "1858", "text": "The 1858 Christiania fire severely destroys several city blocks near Stortorvet in Christiania, Norway, and about 1,000 people lose their homes.", "html": "1858 - The <a href=\"https://wikipedia.org/wiki/1858_Christiania_fire\" title=\"1858 Christiania fire\">1858 Christiania fire</a> severely destroys several city blocks near <a href=\"https://wikipedia.org/wiki/Stortorvet\" title=\"Stortorvet\">Stortorvet</a> in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Christiania</a>, Norway, and about 1,000 people lose their homes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1858_Christiania_fire\" title=\"1858 Christiania fire\">1858 Christiania fire</a> severely destroys several city blocks near <a href=\"https://wikipedia.org/wiki/Stortorvet\" title=\"Stortorvet\">Stortorvet</a> in <a href=\"https://wikipedia.org/wiki/Oslo\" title=\"Oslo\">Christiania</a>, Norway, and about 1,000 people lose their homes.", "links": [{"title": "1858 Christiania fire", "link": "https://wikipedia.org/wiki/1858_Christiania_fire"}, {"title": "Stortorvet", "link": "https://wikipedia.org/wiki/Stortorvet"}, {"title": "Oslo", "link": "https://wikipedia.org/wiki/Oslo"}]}, {"year": "1865", "text": "U.S. President <PERSON> is shot in Ford's Theatre by <PERSON>; <PERSON> dies the following day.", "html": "1865 - U.S. President <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\"><PERSON> is shot in Ford's Theatre by <PERSON></a>; <PERSON> dies the following day.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\"><PERSON> is shot in Ford's Theatre by <PERSON></a>; <PERSON> dies the following day.", "links": [{"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, the U.S. Secretary of State, and his family are attacked at home by <PERSON>.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">U.S. Secretary of State</a>, and his family are <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>#<PERSON>_attacks_Seward\" title=\"Assassination of Abraham <PERSON>\">attacked at home</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">U.S. Secretary of State</a>, and his family are <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>#<PERSON>_attacks_Seward\" title=\"Assassination of Abraham <PERSON>\">attacked at home</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_(conspirator)\" title=\"<PERSON> (conspirator)\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>#<PERSON>_attacks_Seward"}, {"title": "<PERSON> (conspirator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conspirator)"}]}, {"year": "1881", "text": "The Four Dead in Five Seconds Gunfight occurs in El Paso, Texas.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/Four_Dead_in_Five_Seconds_Gunfight\" title=\"Four Dead in Five Seconds Gunfight\">Four Dead in Five Seconds Gunfight</a> occurs in <a href=\"https://wikipedia.org/wiki/El_Paso,_Texas\" title=\"El Paso, Texas\">El Paso, Texas</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Four_Dead_in_Five_Seconds_Gunfight\" title=\"Four Dead in Five Seconds Gunfight\">Four Dead in Five Seconds Gunfight</a> occurs in <a href=\"https://wikipedia.org/wiki/El_Paso,_Texas\" title=\"El Paso, Texas\">El Paso, Texas</a>.", "links": [{"title": "Four Dead in Five Seconds Gunfight", "link": "https://wikipedia.org/wiki/Four_Dead_in_Five_Seconds_Gunfight"}, {"title": "El Paso, Texas", "link": "https://wikipedia.org/wiki/El_Paso,_Texas"}]}, {"year": "1890", "text": "The Pan-American Union is founded by the First International Conference of American States in Washington, D.C.", "html": "1890 - The <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Pan-American Union</a> is founded by the First <a href=\"https://wikipedia.org/wiki/Pan-American_Conference\" title=\"Pan-American Conference\">International Conference of American States</a> in Washington, D.C.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organization_of_American_States\" title=\"Organization of American States\">Pan-American Union</a> is founded by the First <a href=\"https://wikipedia.org/wiki/Pan-American_Conference\" title=\"Pan-American Conference\">International Conference of American States</a> in Washington, D.C.", "links": [{"title": "Organization of American States", "link": "https://wikipedia.org/wiki/Organization_of_American_States"}, {"title": "Pan-American Conference", "link": "https://wikipedia.org/wiki/Pan-American_Conference"}]}, {"year": "1894", "text": "The first ever commercial motion picture house opens in New York City, United States. It uses ten Kinetoscopes, devices for peep-show viewing of films.", "html": "1894 - The first ever commercial <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture</a> house opens in New York City, United States. It uses ten <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">Kinetoscopes</a>, devices for peep-show viewing of films.", "no_year_html": "The first ever commercial <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion picture</a> house opens in New York City, United States. It uses ten <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">Kinetoscopes</a>, devices for peep-show viewing of films.", "links": [{"title": "Film", "link": "https://wikipedia.org/wiki/Film"}, {"title": "Kinetoscope", "link": "https://wikipedia.org/wiki/Kinetoscope"}]}, {"year": "1895", "text": "The 1895 Ljubljana earthquake, both the most and last destructive earthquake in the area, occurs.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/1895_Ljubljana_earthquake\" title=\"1895 Ljubljana earthquake\">1895 Ljubljana earthquake</a>, both the most and last destructive earthquake in the area, occurs.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1895_Ljubljana_earthquake\" title=\"1895 Ljubljana earthquake\">1895 Ljubljana earthquake</a>, both the most and last destructive earthquake in the area, occurs.", "links": [{"title": "1895 Ljubljana earthquake", "link": "https://wikipedia.org/wiki/1895_Ljubljana_earthquake"}]}, {"year": "1900", "text": "The world's fair Exposition Universelle opens in Paris.", "html": "1900 - The world's fair <a href=\"https://wikipedia.org/wiki/Exposition_Universelle_(1900)\" title=\"Exposition Universelle (1900)\">Exposition Universelle</a> opens in Paris.", "no_year_html": "The world's fair <a href=\"https://wikipedia.org/wiki/Exposition_Universelle_(1900)\" title=\"Exposition Universelle (1900)\">Exposition Universelle</a> opens in Paris.", "links": [{"title": "Exposition Universelle (1900)", "link": "https://wikipedia.org/wiki/Exposition_Universelle_(1900)"}]}, {"year": "1906", "text": "The first meeting of the Azusa Street Revival, which will launch Pentecostalism as a worldwide movement, is held in Los Angeles.", "html": "1906 - The first meeting of the <a href=\"https://wikipedia.org/wiki/Azusa_Street_Revival\" title=\"Azusa Street Revival\">Azusa Street Revival</a>, which will launch <a href=\"https://wikipedia.org/wiki/Pentecostalism\" title=\"Pentecostalism\">Pentecostalism</a> as a worldwide movement, is held in Los Angeles.", "no_year_html": "The first meeting of the <a href=\"https://wikipedia.org/wiki/Azusa_Street_Revival\" title=\"Azusa Street Revival\">Azusa Street Revival</a>, which will launch <a href=\"https://wikipedia.org/wiki/Pentecostalism\" title=\"Pentecostalism\">Pentecostalism</a> as a worldwide movement, is held in Los Angeles.", "links": [{"title": "Azusa Street Revival", "link": "https://wikipedia.org/wiki/A<PERSON>sa_Street_Revival"}, {"title": "Pentecostalism", "link": "https://wikipedia.org/wiki/Pentecostalism"}]}, {"year": "1908", "text": "Hauser Dam, a steel dam on the Missouri River in Montana, fails, sending a surge of water 25 to 30 feet (7.6 to 9.1 m) high downstream.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Hauser_Dam\" title=\"Hauser Dam\">Hauser Dam</a>, a <a href=\"https://wikipedia.org/wiki/Steel_dam\" title=\"Steel dam\">steel dam</a> on the <a href=\"https://wikipedia.org/wiki/Missouri_River\" title=\"Missouri River\">Missouri River</a> in <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, fails, sending a surge of water 25 to 30 feet (7.6 to 9.1 m) high downstream.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hauser_Dam\" title=\"Hauser Dam\">Hauser Dam</a>, a <a href=\"https://wikipedia.org/wiki/Steel_dam\" title=\"Steel dam\">steel dam</a> on the <a href=\"https://wikipedia.org/wiki/Missouri_River\" title=\"Missouri River\">Missouri River</a> in <a href=\"https://wikipedia.org/wiki/Montana\" title=\"Montana\">Montana</a>, fails, sending a surge of water 25 to 30 feet (7.6 to 9.1 m) high downstream.", "links": [{"title": "Hauser Dam", "link": "https://wikipedia.org/wiki/<PERSON>user_<PERSON>"}, {"title": "Steel dam", "link": "https://wikipedia.org/wiki/Steel_dam"}, {"title": "Missouri River", "link": "https://wikipedia.org/wiki/Missouri_River"}, {"title": "Montana", "link": "https://wikipedia.org/wiki/Montana"}]}, {"year": "1909", "text": "Muslims in the Ottoman Empire begin a massacre of Armenians in Adana.", "html": "1909 - Muslims in the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> begin <a href=\"https://wikipedia.org/wiki/Adana_massacre\" title=\"Adana massacre\">a massacre</a> of <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenians</a> in <a href=\"https://wikipedia.org/wiki/Adana\" title=\"Adana\">Adana</a>.", "no_year_html": "Muslims in the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> begin <a href=\"https://wikipedia.org/wiki/Adana_massacre\" title=\"Adana massacre\">a massacre</a> of <a href=\"https://wikipedia.org/wiki/Armenians\" title=\"Armenians\">Armenians</a> in <a href=\"https://wikipedia.org/wiki/Adana\" title=\"Adana\">Adana</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Adana massacre", "link": "https://wikipedia.org/wiki/Adana_massacre"}, {"title": "Armenians", "link": "https://wikipedia.org/wiki/Armenians"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adana"}]}, {"year": "1912", "text": "The British passenger liner RMS Titanic hits an iceberg in the North Atlantic and begins to sink.", "html": "1912 - The British passenger liner <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a> hits an iceberg in the North Atlantic and begins to sink.", "no_year_html": "The British passenger liner <a href=\"https://wikipedia.org/wiki/Titanic\" title=\"Titanic\">RMS <i>Titanic</i></a> hits an iceberg in the North Atlantic and begins to sink.", "links": [{"title": "Titanic", "link": "https://wikipedia.org/wiki/Titanic"}]}, {"year": "1928", "text": "The Bremen, a German Junkers W 33 type aircraft, reaches Greenly Island, Canada, completing the first successful transatlantic aeroplane flight from east to west.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/Bremen_(aircraft)\" title=\"Bremen (aircraft)\">Bremen</a>, a German <a href=\"https://wikipedia.org/wiki/Junkers_W_33\" title=\"Junkers W 33\">Junkers W 33</a> type aircraft, reaches <a href=\"https://wikipedia.org/wiki/Greenly_Island,_Canada\" title=\"Greenly Island, Canada\">Greenly Island, Canada</a>, completing the first successful transatlantic aeroplane flight from east to west.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bremen_(aircraft)\" title=\"Bremen (aircraft)\">Bremen</a>, a German <a href=\"https://wikipedia.org/wiki/Junkers_W_33\" title=\"Junkers W 33\">Junkers W 33</a> type aircraft, reaches <a href=\"https://wikipedia.org/wiki/Greenly_Island,_Canada\" title=\"Greenly Island, Canada\">Greenly Island, Canada</a>, completing the first successful transatlantic aeroplane flight from east to west.", "links": [{"title": "Bremen (aircraft)", "link": "https://wikipedia.org/wiki/Bremen_(aircraft)"}, {"title": "Junkers W 33", "link": "https://wikipedia.org/wiki/Junkers_W_33"}, {"title": "Greenly Island, Canada", "link": "https://wikipedia.org/wiki/Greenly_Island,_Canada"}]}, {"year": "1929", "text": "The inaugural Monaco Grand Prix takes place in the Principality of Monaco. <PERSON> wins driving a Bugatti Type 35.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/1929_Monaco_Grand_Prix\" title=\"1929 Monaco Grand Prix\">inaugural Monaco Grand Prix</a> takes place in the <a href=\"https://wikipedia.org/wiki/Monaco\" title=\"Monaco\">Principality of Monaco</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins driving a <a href=\"https://wikipedia.org/wiki/Bugatti_Type_35\" title=\"Bugatti Type 35\">Bugatti Type 35</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1929_Monaco_Grand_Prix\" title=\"1929 Monaco Grand Prix\">inaugural Monaco Grand Prix</a> takes place in the <a href=\"https://wikipedia.org/wiki/Monaco\" title=\"Monaco\">Principality of Monaco</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins driving a <a href=\"https://wikipedia.org/wiki/Bugatti_Type_35\" title=\"Bugatti Type 35\">Bugatti Type 35</a>.", "links": [{"title": "1929 Monaco Grand Prix", "link": "https://wikipedia.org/wiki/1929_Monaco_Grand_Prix"}, {"title": "Monaco", "link": "https://wikipedia.org/wiki/Monaco"}, {"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Bugatti Type 35", "link": "https://wikipedia.org/wiki/Bugatti_Type_35"}]}, {"year": "1931", "text": "The Second Spanish Republic is proclaimed and king <PERSON> goes to exile. Meanwhile, in Barcelona, <PERSON><PERSON> proclaims the Catalan Republic.", "html": "1931 - The <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a> is proclaimed and king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alfonso XIII\"><PERSON></a> goes to exile. Meanwhile, in <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, <a href=\"https://wikipedia.org/wiki/Francesc_Maci%C3%A0\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1931)\" title=\"Catalan Republic (1931)\">Catalan Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a> is proclaimed and king <a href=\"https://wikipedia.org/wiki/Alfonso_XIII\" title=\"Alfonso XIII\"><PERSON></a> goes to exile. Meanwhile, in <a href=\"https://wikipedia.org/wiki/Barcelona\" title=\"Barcelona\">Barcelona</a>, <a href=\"https://wikipedia.org/wiki/Francesc_Maci%C3%A0\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> proclaims the <a href=\"https://wikipedia.org/wiki/Catalan_Republic_(1931)\" title=\"Catalan Republic (1931)\">Catalan Republic</a>.", "links": [{"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}, {"title": "Alfonso XIII", "link": "https://wikipedia.org/wiki/Alfonso_XIII"}, {"title": "Barcelona", "link": "https://wikipedia.org/wiki/Barcelona"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Francesc_Maci%C3%A0"}, {"title": "Catalan Republic (1931)", "link": "https://wikipedia.org/wiki/Catalan_Republic_(1931)"}]}, {"year": "1935", "text": "The Black Sunday dust storm, considered one of the worst storms of the Dust Bowl, sweeps across the Oklahoma and Texas panhandles and neighboring areas.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Black_Sunday_(storm)\" title=\"Black Sunday (storm)\">Black Sunday dust storm</a>, considered one of the worst storms of the <a href=\"https://wikipedia.org/wiki/Dust_Bowl\" title=\"Dust Bowl\">Dust Bowl</a>, sweeps across <a href=\"https://wikipedia.org/wiki/Oklahoma_Panhandle\" class=\"mw-redirect\" title=\"Oklahoma Panhandle\">the Oklahoma</a> and <a href=\"https://wikipedia.org/wiki/Texas_Panhandle\" class=\"mw-redirect\" title=\"Texas Panhandle\">Texas</a> panhandles and neighboring areas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Black_Sunday_(storm)\" title=\"Black Sunday (storm)\">Black Sunday dust storm</a>, considered one of the worst storms of the <a href=\"https://wikipedia.org/wiki/Dust_Bowl\" title=\"Dust Bowl\">Dust Bowl</a>, sweeps across <a href=\"https://wikipedia.org/wiki/Oklahoma_Panhandle\" class=\"mw-redirect\" title=\"Oklahoma Panhandle\">the Oklahoma</a> and <a href=\"https://wikipedia.org/wiki/Texas_Panhandle\" class=\"mw-redirect\" title=\"Texas Panhandle\">Texas</a> panhandles and neighboring areas.", "links": [{"title": "Black Sunday (storm)", "link": "https://wikipedia.org/wiki/Black_Sunday_(storm)"}, {"title": "Dust Bowl", "link": "https://wikipedia.org/wiki/Dust_Bowl"}, {"title": "Oklahoma Panhandle", "link": "https://wikipedia.org/wiki/Oklahoma_Panhandle"}, {"title": "Texas Panhandle", "link": "https://wikipedia.org/wiki/Texas_Panhandle"}]}, {"year": "1940", "text": "World War II: Royal Marines land in Namsos, Norway, preceding a larger force which will arrive two days later.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Royal_Marines\" title=\"Royal Marines\">Royal Marines</a> land in <a href=\"https://wikipedia.org/wiki/Namsos_(town)\" title=\"Namsos (town)\">Namsos</a>, Norway, preceding a larger force which will arrive two days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Royal_Marines\" title=\"Royal Marines\">Royal Marines</a> land in <a href=\"https://wikipedia.org/wiki/Namsos_(town)\" title=\"Namsos (town)\">Namsos</a>, Norway, preceding a larger force which will arrive two days later.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Royal Marines", "link": "https://wikipedia.org/wiki/Royal_Marines"}, {"title": "Namsos (town)", "link": "https://wikipedia.org/wiki/Namsos_(town)"}]}, {"year": "1941", "text": "World War II: German and Italian forces attack Tobruk, Libya.", "html": "1941 - World War II: German and Italian forces <a href=\"https://wikipedia.org/wiki/Siege_of_Tobruk\" title=\"Siege of Tobruk\">attack Tobruk</a>, Libya.", "no_year_html": "World War II: German and Italian forces <a href=\"https://wikipedia.org/wiki/Siege_of_Tobruk\" title=\"Siege of Tobruk\">attack Tobruk</a>, Libya.", "links": [{"title": "Siege of Tobruk", "link": "https://wikipedia.org/wiki/Siege_of_Tobruk"}]}, {"year": "1944", "text": "Bombay explosion: A massive explosion in Bombay harbor kills 300 and causes economic damage valued at 20 million pounds.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/1944_Bombay_explosion\" title=\"1944 Bombay explosion\">Bombay explosion</a>: A massive explosion in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a> harbor kills 300 and causes economic damage valued at 20 million pounds.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1944_Bombay_explosion\" title=\"1944 Bombay explosion\">Bombay explosion</a>: A massive explosion in <a href=\"https://wikipedia.org/wiki/Mumbai\" title=\"Mumbai\">Bombay</a> harbor kills 300 and causes economic damage valued at 20 million pounds.", "links": [{"title": "1944 Bombay explosion", "link": "https://wikipedia.org/wiki/1944_Bombay_explosion"}, {"title": "Mumbai", "link": "https://wikipedia.org/wiki/Mumbai"}]}, {"year": "1945", "text": "Razing of Friesoythe: The 4th Canadian (Armoured) Division deliberately destroys the German town of Friesoythe on the orders of Major General <PERSON>.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Razing_of_Friesoythe\" title=\"Razing of Friesoythe\">Razing of Friesoythe</a>: The <a href=\"https://wikipedia.org/wiki/4th_Canadian_Division\" title=\"4th Canadian Division\">4th Canadian (Armoured) Division</a> deliberately destroys the German town of <a href=\"https://wikipedia.org/wiki/Friesoythe\" title=\"Friesoythe\">Fr<PERSON>oythe</a> on the orders of Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Razing_of_Friesoythe\" title=\"Razing of Friesoythe\">Razing of Friesoythe</a>: The <a href=\"https://wikipedia.org/wiki/4th_Canadian_Division\" title=\"4th Canadian Division\">4th Canadian (Armoured) Division</a> deliberately destroys the German town of <a href=\"https://wikipedia.org/wiki/Friesoythe\" title=\"Friesoythe\"><PERSON><PERSON><PERSON>the</a> on the orders of Major General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Ra<PERSON> of Friesoythe", "link": "https://wikipedia.org/wiki/Ra<PERSON>_of_<PERSON><PERSON><PERSON>"}, {"title": "4th Canadian Division", "link": "https://wikipedia.org/wiki/4th_Canadian_Division"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>the"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "The Soviet satellite Sputnik 2 falls from orbit after a mission duration of 162 days. This was the first spacecraft to carry a living animal, a female dog named <PERSON><PERSON>, who likely lived only a few hours.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> satellite <a href=\"https://wikipedia.org/wiki/Sputnik_2\" title=\"Sputnik 2\">Sputnik 2</a> falls from orbit after a mission duration of 162 days. This was the first spacecraft to carry a living animal, a <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs\" title=\"Soviet space dogs\">female dog</a> named <a href=\"https://wikipedia.org/wiki/Laika\" title=\"Laika\"><PERSON><PERSON></a>, who likely lived only a few hours.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> satellite <a href=\"https://wikipedia.org/wiki/Sputnik_2\" title=\"Sputnik 2\">Sputnik 2</a> falls from orbit after a mission duration of 162 days. This was the first spacecraft to carry a living animal, a <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs\" title=\"Soviet space dogs\">female dog</a> named <a href=\"https://wikipedia.org/wiki/Laika\" title=\"Laika\"><PERSON><PERSON></a>, who likely lived only a few hours.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Sputnik 2", "link": "https://wikipedia.org/wiki/Sputnik_2"}, {"title": "Soviet space dogs", "link": "https://wikipedia.org/wiki/Soviet_space_dogs"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laika"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> overthrows <PERSON> and installs himself as the new President of Togo, a title he will hold for the next 38 years.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma\" title=\"Gnassingbé Eyadéma\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> overthrows <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and installs himself as the new <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Togo\" title=\"List of presidents of Togo\">President of Togo</a>, a title he will hold for the next 38 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma\" title=\"Gnassingbé Eyadéma\"><PERSON><PERSON><PERSON><PERSON><PERSON> E<PERSON>déma</a> overthrows <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and installs himself as the new <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Togo\" title=\"List of presidents of Togo\">President of Togo</a>, a title he will hold for the next 38 years.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gnassingb%C3%A9_Eyad%C3%A9ma"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of presidents of Togo", "link": "https://wikipedia.org/wiki/List_of_presidents_of_Togo"}]}, {"year": "1978", "text": "Tbilisi demonstrations: Thousands of Georgians demonstrate against Soviet attempts to change the constitutional status of the Georgian language.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/1978_Georgian_demonstrations\" title=\"1978 Georgian demonstrations\">Tbilisi demonstrations</a>: Thousands of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgians</a> demonstrate against Soviet attempts to change the constitutional status of the <a href=\"https://wikipedia.org/wiki/Georgian_language\" title=\"Georgian language\">Georgian language</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1978_Georgian_demonstrations\" title=\"1978 Georgian demonstrations\">Tbilisi demonstrations</a>: Thousands of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgians</a> demonstrate against Soviet attempts to change the constitutional status of the <a href=\"https://wikipedia.org/wiki/Georgian_language\" title=\"Georgian language\">Georgian language</a>.", "links": [{"title": "1978 Georgian demonstrations", "link": "https://wikipedia.org/wiki/1978_Georgian_demonstrations"}, {"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "Georgian language", "link": "https://wikipedia.org/wiki/Georgian_language"}]}, {"year": "1979", "text": "The Progressive Alliance of Liberia stages a protest, without a permit, against an increase in rice prices proposed by the government, with clashes between protestors and the police resulting in over 70 deaths and over 500 injuries.", "html": "1979 - The <a href=\"https://wikipedia.org/wiki/Progressive_Alliance_of_Liberia\" title=\"Progressive Alliance of Liberia\">Progressive Alliance of Liberia</a> stages a protest, without a permit, against an increase in rice prices proposed by the government, with clashes between protestors and the police resulting in over 70 deaths and over 500 injuries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Progressive_Alliance_of_Liberia\" title=\"Progressive Alliance of Liberia\">Progressive Alliance of Liberia</a> stages a protest, without a permit, against an increase in rice prices proposed by the government, with clashes between protestors and the police resulting in over 70 deaths and over 500 injuries.", "links": [{"title": "Progressive Alliance of Liberia", "link": "https://wikipedia.org/wiki/Progressive_Alliance_of_Liberia"}]}, {"year": "1981", "text": "STS-1: The first operational Space Shuttle, Columbia completes its first test flight.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/STS-1\" title=\"STS-1\">STS-1</a>: The first operational <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a>, <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i> completes its first test flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/STS-1\" title=\"STS-1\">STS-1</a>: The first operational <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a>, <i><a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Columbia</a></i> completes its first test flight.", "links": [{"title": "STS-1", "link": "https://wikipedia.org/wiki/STS-1"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}]}, {"year": "1986", "text": "The heaviest hailstones ever recorded, each weighing 1 kilogram (2.2 lb), fall on the Gopalganj district of Bangladesh, killing 92.", "html": "1986 - The heaviest <a href=\"https://wikipedia.org/wiki/Hail\" title=\"Hail\">hailstones</a> ever recorded, each weighing 1 kilogram (2.2 lb), fall on the <a href=\"https://wikipedia.org/wiki/Gopalganj_District,_Bangladesh\" title=\"Gopalganj District, Bangladesh\">Gopalganj district of Bangladesh</a>, killing 92.", "no_year_html": "The heaviest <a href=\"https://wikipedia.org/wiki/Hail\" title=\"Hail\">hailstones</a> ever recorded, each weighing 1 kilogram (2.2 lb), fall on the <a href=\"https://wikipedia.org/wiki/Gopalganj_District,_Bangladesh\" title=\"Gopalganj District, Bangladesh\">Gopalganj district of Bangladesh</a>, killing 92.", "links": [{"title": "<PERSON>l", "link": "https://wikipedia.org/wiki/Hail"}, {"title": "Gopalganj District, Bangladesh", "link": "https://wikipedia.org/wiki/Gopalganj_District,_Bangladesh"}]}, {"year": "1988", "text": "The USS Samuel <PERSON> strikes a mine in the Persian Gulf during Operation Earnest Will.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON>_<PERSON>_(FFG-58)\" title=\"USS <PERSON> (FFG-58)\">USS <i><PERSON></i></a> strikes a <a href=\"https://wikipedia.org/wiki/Naval_mine\" title=\"Naval mine\">mine</a> in the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a> during <a href=\"https://wikipedia.org/wiki/Operation_Earnest_Will\" title=\"Operation Earnest Will\">Operation Earnest Will</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/USS_<PERSON>_<PERSON>_<PERSON>_(FFG-58)\" title=\"USS Samuel <PERSON> (FFG-58)\">USS <i><PERSON></i></a> strikes a <a href=\"https://wikipedia.org/wiki/Naval_mine\" title=\"Naval mine\">mine</a> in the <a href=\"https://wikipedia.org/wiki/Persian_Gulf\" title=\"Persian Gulf\">Persian Gulf</a> during <a href=\"https://wikipedia.org/wiki/Operation_Earnest_Will\" title=\"Operation Earnest Will\">Operation Earnest Will</a>.", "links": [{"title": "USS <PERSON> (FFG-58)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_(FFG-58)"}, {"title": "Naval mine", "link": "https://wikipedia.org/wiki/Naval_mine"}, {"title": "Persian Gulf", "link": "https://wikipedia.org/wiki/Persian_Gulf"}, {"title": "Operation Earnest Will", "link": "https://wikipedia.org/wiki/Operation_E<PERSON>nest_Will"}]}, {"year": "1988", "text": "In a United Nations ceremony in Geneva, Switzerland, the Soviet Union signs an agreement pledging to withdraw its troops from Afghanistan.", "html": "1988 - In a <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> ceremony in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> signs <a href=\"https://wikipedia.org/wiki/Geneva_Accords_(1988)\" title=\"Geneva Accords (1988)\">an agreement</a> pledging to <a href=\"https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan\" title=\"Soviet withdrawal from Afghanistan\">withdraw its troops</a> from <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Afghanistan</a>.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> ceremony in <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, Switzerland, the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> signs <a href=\"https://wikipedia.org/wiki/Geneva_Accords_(1988)\" title=\"Geneva Accords (1988)\">an agreement</a> pledging to <a href=\"https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan\" title=\"Soviet withdrawal from Afghanistan\">withdraw its troops</a> from <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan\" title=\"Democratic Republic of Afghanistan\">Afghanistan</a>.", "links": [{"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Geneva Accords (1988)", "link": "https://wikipedia.org/wiki/Geneva_Accords_(1988)"}, {"title": "Soviet withdrawal from Afghanistan", "link": "https://wikipedia.org/wiki/Soviet_withdrawal_from_Afghanistan"}, {"title": "Democratic Republic of Afghanistan", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_Afghanistan"}]}, {"year": "1991", "text": "The Republic of Georgia introduces the post of President following its declaration of independence from the Soviet Union.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Republic of Georgia</a> introduces the post of <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President</a> following its declaration of independence from the Soviet Union.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Republic of Georgia</a> introduces the post of <a href=\"https://wikipedia.org/wiki/President_of_Georgia\" title=\"President of Georgia\">President</a> following its declaration of independence from the Soviet Union.", "links": [{"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "President of Georgia", "link": "https://wikipedia.org/wiki/President_of_Georgia"}]}, {"year": "1994", "text": "In a friendly fire incident during Operation Provide Comfort in northern Iraq, two U.S. Air Force aircraft mistakenly shoot-down two U.S. Army helicopters, killing 26 people.", "html": "1994 - In a <a href=\"https://wikipedia.org/wiki/Friendly_fire\" title=\"Friendly fire\">friendly fire</a> incident during <a href=\"https://wikipedia.org/wiki/Operation_Provide_Comfort\" title=\"Operation Provide Comfort\">Operation Provide Comfort</a> in northern <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, two <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">U.S. Air Force</a> aircraft mistakenly <a href=\"https://wikipedia.org/wiki/1994_Black_Hawk_shootdown_incident\" title=\"1994 Black Hawk shootdown incident\">shoot-down</a> two <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> helicopters, killing 26 people.", "no_year_html": "In a <a href=\"https://wikipedia.org/wiki/Friendly_fire\" title=\"Friendly fire\">friendly fire</a> incident during <a href=\"https://wikipedia.org/wiki/Operation_Provide_Comfort\" title=\"Operation Provide Comfort\">Operation Provide Comfort</a> in northern <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, two <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">U.S. Air Force</a> aircraft mistakenly <a href=\"https://wikipedia.org/wiki/1994_Black_Hawk_shootdown_incident\" title=\"1994 Black Hawk shootdown incident\">shoot-down</a> two <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> helicopters, killing 26 people.", "links": [{"title": "Friendly fire", "link": "https://wikipedia.org/wiki/Friendly_fire"}, {"title": "Operation Provide Comfort", "link": "https://wikipedia.org/wiki/Operation_Provide_Comfort"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "1994 Black Hawk shootdown incident", "link": "https://wikipedia.org/wiki/1994_Black_Hawk_shootdown_incident"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}]}, {"year": "1997", "text": "<PERSON><PERSON>, daughter of Taiwanese artiste <PERSON><PERSON> is kidnapped on her way to school, preceding her murder.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>_<PERSON>-yen\" title=\"Murder of <PERSON><PERSON>yen\"><PERSON><PERSON></a>, daughter of Taiwanese artiste <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bing\" title=\"<PERSON><PERSON>-bing\"><PERSON><PERSON></a> is kidnapped on her way to school, preceding her murder.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>_<PERSON>-yen\" title=\"Murder of <PERSON><PERSON>n\"><PERSON><PERSON></a>, daughter of Taiwanese artiste <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bing\" title=\"<PERSON><PERSON>-bing\"><PERSON><PERSON>-<PERSON></a> is kidnapped on her way to school, preceding her murder.", "links": [{"title": "Murder of <PERSON><PERSON>yen", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON><PERSON>_Hsiao-yen"}, {"title": "<PERSON><PERSON>g", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-bing"}]}, {"year": "1999", "text": "NATO mistakenly bombs a convoy of ethnic Albanian refugees. Yugoslav officials say 75 people were killed.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> mistakenly bombs a convoy of ethnic <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> refugees. <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Yugoslav</a> officials say 75 people were killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> mistakenly bombs a convoy of ethnic <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> refugees. <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Yugoslav</a> officials say 75 people were killed.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Albanians", "link": "https://wikipedia.org/wiki/Albanians"}, {"title": "Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Serbia_and_Montenegro"}]}, {"year": "1999", "text": "A severe hailstorm strikes Sydney, Australia causing A$2.3 billion in insured damages, the most costly natural disaster in Australian history.", "html": "1999 - A <a href=\"https://wikipedia.org/wiki/1999_Sydney_hailstorm\" title=\"1999 Sydney hailstorm\">severe hailstorm</a> strikes <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>, Australia causing <a href=\"https://wikipedia.org/wiki/Australian_dollar\" title=\"Australian dollar\">A$</a>2.3 billion in insured damages, the most costly natural disaster in Australian history.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1999_Sydney_hailstorm\" title=\"1999 Sydney hailstorm\">severe hailstorm</a> strikes <a href=\"https://wikipedia.org/wiki/Sydney\" title=\"Sydney\">Sydney</a>, Australia causing <a href=\"https://wikipedia.org/wiki/Australian_dollar\" title=\"Australian dollar\">A$</a>2.3 billion in insured damages, the most costly natural disaster in Australian history.", "links": [{"title": "1999 Sydney hailstorm", "link": "https://wikipedia.org/wiki/1999_Sydney_hailstorm"}, {"title": "Sydney", "link": "https://wikipedia.org/wiki/Sydney"}, {"title": "Australian dollar", "link": "https://wikipedia.org/wiki/Australian_dollar"}]}, {"year": "2002", "text": "Venezuelan president <PERSON> returns to office two days after being ousted and arrested by the country's military.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> president <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to office two days after being ousted and arrested by the country's military.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuelan</a> president <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> returns to office two days after being ousted and arrested by the country's military.", "links": [{"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}]}, {"year": "2003", "text": "The Human Genome Project is completed with 99% of the human genome sequenced to an accuracy of 99.99%.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Human_Genome_Project\" title=\"Human Genome Project\">Human Genome Project</a> is completed with 99% of the <a href=\"https://wikipedia.org/wiki/Human_genome\" title=\"Human genome\">human genome</a> sequenced to an accuracy of 99.99%.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Human_Genome_Project\" title=\"Human Genome Project\">Human Genome Project</a> is completed with 99% of the <a href=\"https://wikipedia.org/wiki/Human_genome\" title=\"Human genome\">human genome</a> sequenced to an accuracy of 99.99%.", "links": [{"title": "Human Genome Project", "link": "https://wikipedia.org/wiki/Human_Genome_Project"}, {"title": "Human genome", "link": "https://wikipedia.org/wiki/Human_genome"}]}, {"year": "2003", "text": "U.S. troops in Baghdad capture <PERSON>, leader of the Palestinian group that killed an American on the hijacked cruise liner MS Achille Lauro in 1985.", "html": "2003 - U.S. troops in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> capture <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the Palestinian group that killed an American on the hijacked cruise liner <a href=\"https://wikipedia.org/wiki/MS_Achille_<PERSON>ro\" title=\"MS Achi<PERSON>\">MS <i><PERSON><PERSON><PERSON></i></a> in <a href=\"https://wikipedia.org/wiki/1985\" title=\"1985\">1985</a>.", "no_year_html": "U.S. troops in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a> capture <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, leader of the Palestinian group that killed an American on the hijacked cruise liner <a href=\"https://wikipedia.org/wiki/MS_Achi<PERSON>_<PERSON>\" title=\"MS Achille <PERSON>\">MS <i><PERSON><PERSON><PERSON></i></a> in <a href=\"https://wikipedia.org/wiki/1985\" title=\"1985\">1985</a>.", "links": [{"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MS <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "1985", "link": "https://wikipedia.org/wiki/1985"}]}, {"year": "2005", "text": "The Oregon Supreme Court nullifies marriage licenses issued to same-sex couples a year earlier by Multnomah County.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Oregon_Supreme_Court\" title=\"Oregon Supreme Court\">Oregon Supreme Court</a> nullifies marriage licenses issued to same-sex couples a year earlier by <a href=\"https://wikipedia.org/wiki/Multnomah_County,_Oregon\" title=\"Multnomah County, Oregon\">Multnomah County</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Oregon_Supreme_Court\" title=\"Oregon Supreme Court\">Oregon Supreme Court</a> nullifies marriage licenses issued to same-sex couples a year earlier by <a href=\"https://wikipedia.org/wiki/Multnomah_County,_Oregon\" title=\"Multnomah County, Oregon\">Multnomah County</a>.", "links": [{"title": "Oregon Supreme Court", "link": "https://wikipedia.org/wiki/Oregon_Supreme_Court"}, {"title": "Multnomah County, Oregon", "link": "https://wikipedia.org/wiki/Multnomah_County,_Oregon"}]}, {"year": "2006", "text": "Twin blasts triggered by crude bombs during <PERSON><PERSON> prayer in the Jama Masjid mosque in Delhi injure 13 people.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/2006_Jama_Masjid_bombings\" title=\"2006 Jama Masjid bombings\">Twin blasts</a> triggered by crude bombs during <a href=\"https://wikipedia.org/wiki/Asr_prayer\" class=\"mw-redirect\" title=\"Asr prayer\">Asr prayer</a> in the <a href=\"https://wikipedia.org/wiki/Jama_Masjid,_Delhi\" class=\"mw-redirect\" title=\"Jama Masjid, Delhi\">Jama Masjid</a> mosque in Delhi injure 13 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2006_Jama_Masjid_bombings\" title=\"2006 Jama Masjid bombings\">Twin blasts</a> triggered by crude bombs during <a href=\"https://wikipedia.org/wiki/Asr_prayer\" class=\"mw-redirect\" title=\"Asr prayer\">Asr prayer</a> in the <a href=\"https://wikipedia.org/wiki/Jama_Masjid,_Delhi\" class=\"mw-redirect\" title=\"Jama Masjid, Delhi\">Jama Masjid</a> mosque in Delhi injure 13 people.", "links": [{"title": "2006 Jama Masjid bombings", "link": "https://wikipedia.org/wiki/2006_Jama_Masjid_bombings"}, {"title": "Asr prayer", "link": "https://wikipedia.org/wiki/Asr_prayer"}, {"title": "Jama Masjid, Delhi", "link": "https://wikipedia.org/wiki/Jama_Masjid,_Delhi"}]}, {"year": "2014", "text": "Two bombs detonate at a bus station in Nyanya, Nigeria, killing at least 88 people and injuring hundreds. <PERSON><PERSON> claims responsibility.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/April_2014_Nyanya_bombing\" title=\"April 2014 Nyanya bombing\">Two bombs detonate</a> at a bus station in <a href=\"https://wikipedia.org/wiki/New_Nyanya\" title=\"New Nyanya\">Nyanya</a>, Nigeria, killing at least 88 people and injuring hundreds. <a href=\"https://wikipedia.org/wiki/Boko_Haram\" title=\"Boko Haram\"><PERSON><PERSON></a> claims responsibility.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/April_2014_Nyanya_bombing\" title=\"April 2014 Nyanya bombing\">Two bombs detonate</a> at a bus station in <a href=\"https://wikipedia.org/wiki/New_Nyanya\" title=\"New Nyanya\">Nyanya</a>, Nigeria, killing at least 88 people and injuring hundreds. <a href=\"https://wikipedia.org/wiki/Bo<PERSON>_Haram\" title=\"Boko Haram\"><PERSON><PERSON></a> claims responsibility.", "links": [{"title": "April 2014 <PERSON><PERSON><PERSON> bombing", "link": "https://wikipedia.org/wiki/April_2014_<PERSON><PERSON><PERSON>_bombing"}, {"title": "New Nyanya", "link": "https://wikipedia.org/wiki/New_Nyanya"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}]}, {"year": "2014", "text": "<PERSON><PERSON> abducts 276 girls from a school in Chibok, Nigeria.", "html": "2014 - <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Chibok_schoolgirls_kidnapping\" title=\"Chibok schoolgirls kidnapping\">abducts 276 girls</a> from a school in <a href=\"https://wikipedia.org/wiki/Chibok\" title=\"Chibok\">Chibok</a>, Nigeria.", "no_year_html": "<PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Chibok_schoolgirls_kidnapping\" title=\"Chibok schoolgirls kidnapping\">abducts 276 girls</a> from a school in <a href=\"https://wikipedia.org/wiki/Chibok\" title=\"Chibok\">Chibok</a>, Nigeria.", "links": [{"title": "Chibok schoolgirls kidnapping", "link": "https://wikipedia.org/wiki/Chibok_schoolgirls_kidnapping"}, {"title": "Chibok", "link": "https://wikipedia.org/wiki/Chibok"}]}, {"year": "2016", "text": "The foreshock of a major earthquake occurs in Kumamoto, Japan.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/2016_Kumamoto_earthquakes#April_14_foreshock\" title=\"2016 Kumamoto earthquakes\">foreshock of a major earthquake</a> occurs in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Kumamoto\">Kumamoto</a>, Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2016_Kumamoto_earthquakes#April_14_foreshock\" title=\"2016 Kumamoto earthquakes\">foreshock of a major earthquake</a> occurs in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Kumamoto\">Kumamoto</a>, Japan.", "links": [{"title": "2016 Kumamoto earthquakes", "link": "https://wikipedia.org/wiki/2016_Kumamoto_earthquakes#April_14_foreshock"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2022", "text": "Russian invasion of Ukraine: The Russian warship Moskva sinks.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Ukraine\" title=\"Russian invasion of Ukraine\">Russian invasion of Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Moskva\" title=\"Sinking of the Moskva\">The Russian warship <i>Moskva</i> sinks</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Ukraine\" title=\"Russian invasion of Ukraine\">Russian invasion of Ukraine</a>: <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Moskva\" title=\"Sinking of the Moskva\">The Russian warship <i>Moskva</i> sinks</a>.", "links": [{"title": "Russian invasion of Ukraine", "link": "https://wikipedia.org/wiki/Russian_invasion_of_Ukraine"}, {"title": "Sinking of the Moskva", "link": "https://wikipedia.org/wiki/Sinking_of_the_Moskva"}]}, {"year": "2023", "text": "The Jupiter Icy Moons Explorer (JUICE) is launched by the European Space Agency.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/Jupiter_Icy_Moons_Explorer\" title=\"Jupiter Icy Moons Explorer\">Jupiter Icy Moons Explorer</a> (JUICE) is launched by the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Jupiter_Icy_Moons_Explorer\" title=\"Jupiter Icy Moons Explorer\">Jupiter Icy Moons Explorer</a> (JUICE) is launched by the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a>.", "links": [{"title": "Jupiter Icy Moons Explorer", "link": "https://wikipedia.org/wiki/Jupiter_Icy_Moons_Explorer"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}]}, {"year": "2024", "text": "Flooding in the Persian Gulf starts, killing 19 in Oman.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Persian_Gulf_floods\" title=\"2024 Persian Gulf floods\">Flooding in the Persian Gulf</a> starts, killing 19 in <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Persian_Gulf_floods\" title=\"2024 Persian Gulf floods\">Flooding in the Persian Gulf</a> starts, killing 19 in <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>.", "links": [{"title": "2024 Persian Gulf floods", "link": "https://wikipedia.org/wiki/2024_Persian_Gulf_floods"}, {"title": "Oman", "link": "https://wikipedia.org/wiki/Oman"}]}], "Births": [{"year": "1126", "text": "<PERSON><PERSON><PERSON>, Andalusian Arab physician and philosopher (d. 1198)", "html": "1126 - <a href=\"https://wikipedia.org/wiki/Averro<PERSON>\" title=\"Averro<PERSON>\"><PERSON><PERSON><PERSON></a>, Andalusian Arab physician and philosopher (d. 1198)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Averroes\" title=\"Averro<PERSON>\"><PERSON><PERSON><PERSON></a>, Andalusian Arab physician and philosopher (d. 1198)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Averroes"}]}, {"year": "1204", "text": "<PERSON>, king of Castile (d. 1217)", "html": "1204 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> (d. 1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> (d. 1217)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}]}, {"year": "1331", "text": "<PERSON><PERSON><PERSON>, French Roman Catholic saint (d. 1414)", "html": "1331 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Roman Catholic saint (d. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French Roman Catholic saint (d. 1414)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1527", "text": "<PERSON>, Flemish cartographer and geographer (d. 1598)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish cartographer and geographer (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish cartographer and geographer (d. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1572", "text": "<PERSON>, Austrian mathematician, philosopher, and academic (d. 1632)", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit_theologian)\" title=\"<PERSON> (Jesuit theologian)\"><PERSON></a>, Austrian mathematician, philosopher, and academic (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit_theologian)\" title=\"<PERSON> (Jesuit theologian)\"><PERSON></a>, Austrian mathematician, philosopher, and academic (d. 1632)", "links": [{"title": "<PERSON> (Jesuit theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jesuit_theologian)"}]}, {"year": "1578", "text": "<PERSON> of Spain (d. 1621)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a> (d. 1621)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1629", "text": "<PERSON><PERSON>, Dutch mathematician, astronomer, and physicist (d. 1695)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician, astronomer, and physicist (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician, astronomer, and physicist (d. 1695)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1669", "text": "<PERSON>, Swedish general and politician (d. 1741)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>ardie\" title=\"<PERSON>ard<PERSON>\"><PERSON></a>, Swedish general and politician (d. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>ardie\" title=\"<PERSON>\"><PERSON></a>, Swedish general and politician (d. 1741)", "links": [{"title": "<PERSON> Gardie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, English iron master (d. 1717)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English iron master (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English iron master (d. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, French playwright and songwriter (d. 1783)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French playwright and songwriter (d. 1783)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French playwright and songwriter (d. 1783)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Coll%C3%A9"}]}, {"year": "1714", "text": "<PERSON>, Scottish minister and author (d. 1788)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gib\"><PERSON></a>, Scottish minister and author (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adam Gib\"><PERSON></a>, Scottish minister and author (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ib"}]}, {"year": "1738", "text": "<PERSON>, 3rd Duke of Portland, English politician, Prime Minister of the United Kingdom (d. 1809)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland\" title=\"<PERSON>, 3rd Duke of Portland\"><PERSON>, 3rd Duke of Portland</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland\" title=\"<PERSON>, 3rd Duke of Portland\"><PERSON>, 3rd Duke of Portland</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1809)", "links": [{"title": "<PERSON>, 3rd Duke of Portland", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Portland"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1769", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French general (d. 1799)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Barth%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barth%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Barth%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON>, French politician, Prime Minister of France (d. 1854)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8le\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8le\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A8le"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1788", "text": "<PERSON>, American politician, 2nd Vice-president of Texas (d. 1870)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Texas\" title=\"President of the Republic of Texas\">Vice-president of Texas</a> (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Texas\" title=\"President of the Republic of Texas\">Vice-president of Texas</a> (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Republic of Texas", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Texas"}]}, {"year": "1800", "text": "<PERSON>, English engineer (d. 1865)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, Portuguese-New Zealand soldier, explorer, and politician, 11th Prime Minister of New Zealand (d. 1898)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-New Zealand soldier, explorer, and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-New Zealand soldier, explorer, and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1814", "text": "<PERSON>, Georgian publicist and author (d. 1887)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian publicist and author (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian publicist and author (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON>, American educator, author, editor, and publisher (d. 1901)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator, author, editor, and publisher (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator, author, editor, and publisher (d. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, English general, ethnologist, and archaeologist (d. 1900)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general, ethnologist, and archaeologist (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general, ethnologist, and archaeologist (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus_Pitt_Rivers"}]}, {"year": "1852", "text": "<PERSON>, Australian biologist (d. 1941)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian biologist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Estonian pastor and poet (d. 1923)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian pastor and poet (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian pastor and poet (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "Princess <PERSON> of the United Kingdom (d. 1944)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Princess_Beatrice_of_the_United_Kingdom\" title=\"Princess Beatrice of the United Kingdom\">Princess Beatrice of the United Kingdom</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Beatrice_of_the_United_Kingdom\" title=\"Princess Beatrice of the United Kingdom\">Princess Beatrice of the United Kingdom</a> (d. 1944)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Beatrice_of_the_United_Kingdom"}]}, {"year": "1865", "text": "<PERSON>, English architect, and designer and painter of pottery (d. 1960)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, and designer and painter of pottery (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, and designer and painter of pottery (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American educator (d. 1936)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, German architect, designed the AEG turbine factory (d. 1940)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/AEG_turbine_factory\" title=\"AEG turbine factory\">AEG turbine factory</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect, designed the <a href=\"https://wikipedia.org/wiki/AEG_turbine_factory\" title=\"AEG turbine factory\">AEG turbine factory</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "AEG turbine factory", "link": "https://wikipedia.org/wiki/AEG_turbine_factory"}]}, {"year": "1870", "text": "<PERSON>, Russian painter and educator (d. 1905)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, Australian cricketer and coach (d. 1929)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer and coach (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer and coach (d. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Indian-English scholar and translator (d. 1953)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English scholar and translator (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English scholar and translator (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Finnish politician (d. 1918)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, English barrister and one time owner of Stonehenge (d. 1934)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister and one time owner of <a href=\"https://wikipedia.org/wiki/Stonehenge\" title=\"Stonehenge\">Stonehenge</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister and one time owner of <a href=\"https://wikipedia.org/wiki/Stonehenge\" title=\"Stonehenge\">Stonehenge</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Stonehenge", "link": "https://wikipedia.org/wiki/Stonehenge"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Maldivian poet and scholar (d. 1948)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maldivian poet and scholar (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maldivian poet and scholar (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, German-Austrian physicist and philosopher (d. 1936)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian physicist and philosopher (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Austrian physicist and philosopher (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, German philologist and scholar (d. 1956)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist and scholar (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian poet and translator (d. 1928)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_T%C3%B3th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian poet and translator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81rp%C3%A1d_T%C3%B3th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian poet and translator (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81rp%C3%A1d_T%C3%B3th"}]}, {"year": "1889", "text": "<PERSON>, English historian and academic (d. 1975)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian economist, jurist, and politician, 1st Indian Minister of Law and Justice (d. 1956)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian economist, jurist, and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Law_and_Justice_(India)\" class=\"mw-redirect\" title=\"Minister of Law and Justice (India)\">Indian Minister of Law and Justice</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian economist, jurist, and politician, 1st <a href=\"https://wikipedia.org/wiki/Minister_of_Law_and_Justice_(India)\" class=\"mw-redirect\" title=\"Minister of Law and Justice (India)\">Indian Minister of Law and Justice</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Law and Justice (India)", "link": "https://wikipedia.org/wiki/Minister_of_Law_and_Justice_(India)"}]}, {"year": "1891", "text": "<PERSON>, Finnish wrestler (d. 1958)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Spanish bullfighter (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Australian archaeologist and philologist (d. 1957)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian archaeologist and philologist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Australian archaeologist and philologist (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American actress (d. 1972)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian educationist (d. 1977)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Shivrampant_<PERSON>le\" title=\"Shivrampant Damle\">Shi<PERSON><PERSON><PERSON><PERSON></a>, Indian educationist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shivrampant_<PERSON>le\" title=\"Shivrampant Damle\">Shiv<PERSON><PERSON><PERSON></a>, Indian educationist (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shivrampant_Damle"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player, coach, and referee (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player, coach, and referee (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player, coach, and referee (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, French philosopher and academic (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Swedish discus thrower and triathlete (d. 2002)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish discus thrower and triathlete (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish discus thrower and triathlete (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English actor, director, and producer (d. 2000)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American author and educator (d. 1999)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German sprinter (d. 1987)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sprinter (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905 v", "text": "<PERSON>, French author and activist (d. 1999)", "html": "1905 v - 1905 v - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and activist (d. 1999)", "no_year_html": "1905 v - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and activist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON> of Saudi Arabia, Saudi Arabian king (d. 1975)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Faisal_of_Saudi_Arabia\" title=\"Faisal of Saudi Arabia\"><PERSON><PERSON><PERSON> of Saudi Arabia</a>, Saudi Arabian king (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faisal_of_Saudi_Arabia\" title=\"<PERSON>ais<PERSON> of Saudi Arabia\"><PERSON><PERSON><PERSON> of Saudi Arabia</a>, Saudi Arabian king (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON>ais<PERSON>_of_Saudi_Arabia"}]}, {"year": "1907", "text": "<PERSON>, Haitian physician and politician, 40th President of Haiti (d. 1971)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian physician and politician, 40th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian physician and politician, 40th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1912", "text": "<PERSON>, French photographer and journalist (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and journalist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Estonian footballer (d. 1978)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French conductor (d. 2008)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French conductor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian telegraphist and politician, 29th Australian Minister for Foreign Affairs (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian telegraphist and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian telegraphist and politician, 29th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister for Foreign Affairs</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1917", "text": "<PERSON>, English actress (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball executive (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball executive (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actress and singer (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American actress and singer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American actress and singer (d. 2015)", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani-Indian singer (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Shamshad_Begum\" title=\"Shamshad Begum\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani-Indian singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shamshad_Begum\" title=\"Shamshad Begum\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani-Indian singer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shamshad_Begum"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Indian author and playwright (d. 1975)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and playwright (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and playwright (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>ma"}]}, {"year": "1920", "text": "<PERSON><PERSON>, English lawyer, historian, and author (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer, historian, and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Guest\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer, historian, and author (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Guest"}]}, {"year": "1921", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1922", "text": "<PERSON>, American actress (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Argentinian golfer (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian golfer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American trumpet player and composer (d. 1994)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American trumpet player and composer (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor and producer (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Baroness <PERSON>, English philosopher, and academic (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English philosopher, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English philosopher, and academic (d. 2019)", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Zimbabwean minister and politician, 1st Prime Minister of Zimbabwe Rhodesia (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe_Rhodesia\" title=\"Prime Minister of Zimbabwe Rhodesia\">Prime Minister of Zimbabwe Rhodesia</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean minister and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe_Rhodesia\" title=\"Prime Minister of Zimbabwe Rhodesia\">Prime Minister of Zimbabwe Rhodesia</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Abel_<PERSON>zorewa"}, {"title": "Prime Minister of Zimbabwe Rhodesia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe_Rhodesia"}]}, {"year": "1925", "text": "<PERSON>, American soldier and actor (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Steiger"}]}, {"year": "1926", "text": "<PERSON>, New Zealand author (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, New Zealand author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, New Zealand author (d. 2013)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1926", "text": "<PERSON>, Czech director, producer, and screenwriter (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech director, producer, and screenwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Daniel\"><PERSON></a>, Czech director, producer, and screenwriter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress and singer (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress and author (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, New Zealand chemist and academic, Nobel Prize laureate (d. 2007)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1927", "text": "<PERSON><PERSON>, French actress and singer (d. 1995)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress and singer (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English director, producer, and screenwriter (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, African-American singer-songwriter (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American singer-songwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American singer-songwriter (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German priest and theologian (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German priest and theologian (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lawyer and politician, 21st United States Deputy Attorney General (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Attorney_General\" title=\"United States Deputy Attorney General\">United States Deputy Attorney General</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Deputy_Attorney_General\" title=\"United States Deputy Attorney General\">United States Deputy Attorney General</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Deputy Attorney General", "link": "https://wikipedia.org/wiki/United_States_Deputy_Attorney_General"}]}, {"year": "1930", "text": "<PERSON>, French mountaineer (d. 2007)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor and author (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/Bradford_Dillman\" title=\"<PERSON>man\"><PERSON></a>, American actor and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dillman\" title=\"<PERSON>man\"><PERSON></a>, American actor and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bradford_Dillman"}]}, {"year": "1931", "text": "<PERSON>, English admiral (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian ice hockey player (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian lawyer and politician, 27th Premier of British Columbia (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_British_Columbia\" title=\"Premier of British Columbia\">Premier of British Columbia</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of British Columbia", "link": "https://wikipedia.org/wiki/Premier_of_British_Columbia"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Egyptian academic and politician, 47th Prime Minister of Egypt (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian academic and politician, 47th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> E<PERSON>d\"><PERSON><PERSON></a>, Egyptian academic and politician, 47th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Egypt\" title=\"Prime Minister of Egypt\">Prime Minister of Egypt</a> (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atef_Ebeid"}, {"title": "Prime Minister of Egypt", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Egypt"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American singer-songwriter and musician (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and musician (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lynn"}]}, {"year": "1932", "text": "<PERSON>, Scottish businessman and politician, Lord Lieutenant of Renfrewshire", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Renfrewshire\" title=\"Lord Lieutenant of Renfrewshire\">Lord Lieutenant of Renfrewshire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Renfrewshire\" title=\"Lord Lieutenant of Renfrewshire\">Lord Lieutenant of Renfrewshire</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Renfrewshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Renfrewshire"}]}, {"year": "1933", "text": "<PERSON>, Northern Irish racing driver (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Paddy <PERSON>\"><PERSON></a>, Northern Irish racing driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Paddy <PERSON>\"><PERSON></a>, Northern Irish racing driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rk"}]}, {"year": "1933", "text": "<PERSON>, Russian author (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Armenian-Russian nuclear physicist", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Russian nuclear physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Armenian-Russian nuclear physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American philosopher and theorist (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher and theorist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American philosopher and theorist (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>-<PERSON><PERSON>, Baroness <PERSON> of Ilton, English table tennis player, swimmer, and politician (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON><PERSON>_of_Ilton\" title=\"<PERSON>, Baroness <PERSON> of Ilton\"><PERSON>-<PERSON>, Baroness <PERSON> of Ilton</a>, English table tennis player, swimmer, and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON><PERSON>_of_Ilton\" title=\"<PERSON>, Baroness <PERSON> of Ilton\"><PERSON>, Baroness <PERSON> of Ilton</a>, English table tennis player, swimmer, and politician (d. 2023)", "links": [{"title": "<PERSON>-<PERSON><PERSON>, Baroness <PERSON> of Ilton", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_Baroness_<PERSON>_of_Ilton"}]}, {"year": "1935", "text": "<PERSON>, English bishop", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1935", "text": "<PERSON>, Swiss pseudohistorian and author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4niken\" title=\"<PERSON>\"><PERSON></a>, Swiss <a href=\"https://wikipedia.org/wiki/Pseudohistory\" title=\"Pseudohistory\">pseudohistorian</a> and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4niken\" title=\"<PERSON>\"><PERSON></a>, Swiss <a href=\"https://wikipedia.org/wiki/Pseudohistory\" title=\"Pseudohistory\">pseudohistorian</a> and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A4niken"}, {"title": "Pseudohistory", "link": "https://wikipedia.org/wiki/Pseudohistory"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American actress and singer (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American golfer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American-Italian soldier, police officer and lecturer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian soldier, police officer and lecturer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian soldier, police officer and lecturer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Israeli businessman, founded the Scailex Corporation (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli businessman, founded the <a href=\"https://wikipedia.org/wiki/Scailex_Corporation\" title=\"Scailex Corporation\">Scailex Corporation</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli businessman, founded the <a href=\"https://wikipedia.org/wiki/Scailex_Corporation\" title=\"Scailex Corporation\">Scailex Corporation</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>"}, {"title": "Scailex Corporation", "link": "https://wikipedia.org/wiki/Scailex_Corporation"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Austrian mountaineer (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian mountaineer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian mountaineer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Turkish author and academic (d. 2001)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C5%9Fan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C5%9Fan\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish author and academic (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Co%C5%9Fan"}]}, {"year": "1938", "text": "<PERSON>, Australian politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Indian-English actress and activist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English actress and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English actress and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON> of Thornes, English archbishop and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Thornes\" title=\"<PERSON>, Baron <PERSON> of Thornes\"><PERSON>, Baron <PERSON> of Thornes</a>, English archbishop and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Thornes\" title=\"<PERSON>, Baron <PERSON> of Thornes\"><PERSON>, Baron <PERSON> of Thornes</a>, English archbishop and academic", "links": [{"title": "<PERSON>, Baron <PERSON> of Thornes", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Thornes"}]}, {"year": "1940", "text": "<PERSON>, English physician and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, English physician and academic", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and manager (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Soviet high jumper (d. 2003)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet high jumper (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Soviet high jumper (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Russian engineer and astronaut", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish politician, Swedish Minister of Enterprise and Innovation", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Enterprise_and_Innovation_(Sweden)\" class=\"mw-redirect\" title=\"Ministry of Enterprise and Innovation (Sweden)\">Swedish Minister of Enterprise and Innovation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (politician)\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Enterprise_and_Innovation_(Sweden)\" class=\"mw-redirect\" title=\"Ministry of Enterprise and Innovation (Sweden)\">Swedish Minister of Enterprise and Innovation</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>_(politician)"}, {"title": "Ministry of Enterprise and Innovation (Sweden)", "link": "https://wikipedia.org/wiki/Ministry_of_Enterprise_and_Innovation_(Sweden)"}]}, {"year": "1944", "text": "<PERSON>, English journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Samoan economist and politician, 8th Prime Minister of Samoa", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Malielegaoi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>a <PERSON> Sailele Malielegaoi\"><PERSON><PERSON><PERSON><PERSON></a>, Samoan economist and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Malielegaoi\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>lele Malielegaoi\"><PERSON><PERSON><PERSON><PERSON></a>, Samoan economist and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Samoa\" title=\"Prime Minister of Samoa\">Prime Minister of Samoa</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>oi"}, {"title": "Prime Minister of Samoa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Samoa"}]}, {"year": "1945", "text": "<PERSON>, English guitarist and songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>more\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian producer, director and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian producer, director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian producer, director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, French-American author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Guil<PERSON>\"><PERSON><PERSON><PERSON></a>, French-American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Guil<PERSON>\"><PERSON><PERSON><PERSON></a>, French-American author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Cypriot economist and politician, Cypriot Minister of Finance", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Finance of Cyprus\">Cypriot Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot economist and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Finance of Cyprus\">Cypriot Minister of Finance</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ministers of Finance of Cyprus", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Finance_of_Cyprus"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Norwegian pianist and orchestra leader", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and orchestra leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and orchestra leader", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, French journalist and politician (d. 2014)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Australian cricketer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American model, actress, and photographer (d. 2001)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and photographer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and photographer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Justice", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Greece)\" title=\"Ministry of Justice (Greece)\">Greek Minister of Justice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice_(Greece)\" title=\"Ministry of Justice (Greece)\">Greek Minister of Justice</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Papaligouras"}, {"title": "Ministry of Justice (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Justice_(Greece)"}]}, {"year": "1949", "text": "<PERSON>, English author and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American-British economist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-British economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-British economist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English actor and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English motorcycle racer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor and director", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American physician and geneticist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and geneticist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and geneticist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Hungarian author (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Esterh%C3%A1zy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Esterh%C3%A1zy\" title=\"<PERSON><PERSON><PERSON> E<PERSON>h<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_Esterh%C3%A1zy"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, English footballer (d. 2012)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Spanish politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1lez_Navas\" title=\"<PERSON>\"><PERSON></a>, Spanish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>%C3%A1lez_Navas\" title=\"<PERSON>\"><PERSON></a>, Spanish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1lez_Navas"}]}, {"year": "1951", "text": "<PERSON>, English cellist, conductor, and educator", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist, conductor, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist, conductor, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, <PERSON> of Vernham Dean, English politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_Vernham_Dean\" title=\"<PERSON>, Baroness <PERSON> of Vernham Dean\"><PERSON>, Baroness <PERSON> of Vernham Dean</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON><PERSON>_of_Vernham_Dean\" title=\"<PERSON>, Baroness <PERSON> of Vernham Dean\"><PERSON>, Baroness <PERSON> of Vernham Dean</a>, English politician", "links": [{"title": "<PERSON>, <PERSON> of Vernham Dean", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON><PERSON>_of_Vernham_Dean"}]}, {"year": "1952", "text": "<PERSON>, American bass player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Irish footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Sullivan"}]}, {"year": "1952", "text": "<PERSON>, Scottish bishop", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Scottish bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Scottish bishop", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Japanese director, screenwriter, and illustrator", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, screenwriter, and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, screenwriter, and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Croatian lawyer and politician, 8th President of Croatian Parliament (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0prem\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Croatian_Parliament\" class=\"mw-redirect\" title=\"President of Croatian Parliament\">President of Croatian Parliament</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0prem\" title=\"<PERSON>\"><PERSON></a>, Croatian lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Croatian_Parliament\" class=\"mw-redirect\" title=\"President of Croatian Parliament\">President of Croatian Parliament</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_%C5%A0prem"}, {"title": "President of Croatian Parliament", "link": "https://wikipedia.org/wiki/President_of_Croatian_Parliament"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Canadian actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American make-up artist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American make-up artist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American make-up artist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Russian pianist, composer, and conductor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English musician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English musician", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>(bassist)"}]}, {"year": "1959", "text": "<PERSON>, American sportscaster and producer (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%A9r%C3%A8se_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>h%C3%A9r%C3%A8se_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "Marie-Thérès<PERSON> Fortin", "link": "https://wikipedia.org/wiki/Marie-Th%C3%A9r%C3%A8se_Fortin"}]}, {"year": "1960", "text": "<PERSON>, American actor and comedian", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Burmese historian and journalist (d. 2021)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Kywe\" title=\"<PERSON><PERSON> My<PERSON> K<PERSON>\"><PERSON><PERSON></a>, Burmese historian and journalist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Kywe\" title=\"<PERSON><PERSON> Kywe\"><PERSON><PERSON></a>, Burmese historian and journalist (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/My<PERSON>_My<PERSON>_Kywe"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese graphic artist, programmer, and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese graphic artist, programmer, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese graphic artist, programmer, and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, South African cricketer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Symcox"}]}, {"year": "1961", "text": "<PERSON>, Scottish actor and director", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian athlete", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American wrestler (d. 2007)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2007)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>(wrestler)"}]}, {"year": "1964", "text": "<PERSON>, American race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American tennis player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Welsh international footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh international footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American director and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian cricketer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian lawyer and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Swedish ski jumper", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6v\" title=\"<PERSON>\"><PERSON></a>, Swedish ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6v\" title=\"<PERSON>\"><PERSON></a>, Swedish ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%B6v"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and sportscaster", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Italian international footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, French-Australian actress, comedian, singer and writer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Australian actress, comedian, singer and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Australian actress, comedian, singer and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Anthony <PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Anthony <PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Dutch-American bass player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-American bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Vebj%C3%B8<PERSON>_<PERSON><PERSON>\" title=\"V<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vebj%C3%B8<PERSON>_<PERSON><PERSON>\" title=\"V<PERSON><PERSON><PERSON><PERSON>\">V<PERSON><PERSON><PERSON><PERSON></a>, Norwegian journalist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vebj%C3%B8rn_<PERSON><PERSON><PERSON>k"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese singer and actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kudo\"><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>do\"><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>do"}]}, {"year": "1971", "text": "<PERSON>, Colombian footballer and manager (d. 2012)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Dominican-American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_P%C3%A<PERSON><PERSON>_(pitcher)"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English-Scottish footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Scottish footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1972", "text": "<PERSON>, Dominican baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roberto_Mej%C3%ADa"}]}, {"year": "1972", "text": "<PERSON>, American rock climber and BASE jumper (d. 2015)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock climber and <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE</a> jumper (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock climber and <a href=\"https://wikipedia.org/wiki/BASE_jumping\" title=\"BASE jumping\">BASE</a> jumper (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "BASE jumping", "link": "https://wikipedia.org/wiki/BASE_jumping"}]}, {"year": "1973", "text": "<PERSON>, Argentinian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Japanese video game director and writer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game director and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game director and writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American tenor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_(tenor)"}]}, {"year": "1974", "text": "<PERSON>, American rapper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Da_Brat\" title=\"Da Brat\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Da_Brat\" title=\"Da Brat\"><PERSON></a>, American rapper", "links": [{"title": "Da Brat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rat"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American wrestler", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1975", "text": "<PERSON>, Brazilian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luciano_Almeida"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Israeli-American composer and academic", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Av<PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-American composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Av<PERSON>_<PERSON>\" title=\"Av<PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-American composer and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Av<PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Brazilian mixed martial artist and boxer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian mixed martial artist and boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish singer-songwriter and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Christian_%C3%84lvestam\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_%C3%84lvestam\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_%C3%84lvestam"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English model, actress, and fashion designer, co-founded Marchesa", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model, actress, and fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Marchesa_(brand)\" title=\"March<PERSON><PERSON> (brand)\">March<PERSON>a</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English model, actress, and fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/Marches<PERSON>_(brand)\" title=\"Marches<PERSON> (brand)\">Marchesa</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Marchesa (brand)", "link": "https://wikipedia.org/wiki/Marchesa_(brand)"}]}, {"year": "1976", "text": "<PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kyle_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>-<PERSON>, Haitian hurdler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Haitian hurdler", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American basketball player (d. 2014)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Estonian biathlete", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian biathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian politician, 41st Premier of Queensland", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 41st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and model", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Cypriot footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, New Zealand rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/No%C3%A9_Pamarot\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%A9_Pamarot\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%A9_Pamarot"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Turkish basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Kerem_Tun%C3%A7eri\" title=\"<PERSON><PERSON> Tunçeri\"><PERSON><PERSON></a>, Turkish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kerem_Tun%C3%A7eri\" title=\"<PERSON>rem Tunçeri\"><PERSON><PERSON></a>, Turkish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kerem_Tun%C3%A7eri"}]}, {"year": "1980", "text": "<PERSON>, American-Canadian singer-songwriter and guitarist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, New Zealand rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1980)\" title=\"<PERSON> (rugby league, born 1980)\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1980)\" title=\"<PERSON> (rugby league, born 1980)\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON> (rugby league, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league,_born_1980)"}]}, {"year": "1981", "text": "<PERSON>, German rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_G%C3%BCng%C3%B6r\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_G%C3%BCng%C3%B6r\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_G%C3%BCng%C3%B6r"}]}, {"year": "1981", "text": "<PERSON>, English director and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)\" title=\"<PERSON> (theatre director)\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)\" title=\"<PERSON> (theatre director)\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON> (theatre director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theatre_director)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/U%C4%9Fur_Boral\" title=\"Uğ<PERSON> Boral\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U%C4%9Fur_Boral\" title=\"Uğ<PERSON> Boral\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U%C4%9Fur_Boral"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian volleyball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON><PERSON>an<PERSON>\"><PERSON><PERSON></a>, Brazilian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A7a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>an%C3%A7a"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Italian triple jumper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian triple jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian triple jumper", "links": [{"title": "Simona <PERSON>", "link": "https://wikipedia.org/wiki/Simona_La_Mantia"}]}, {"year": "1983", "text": "<PERSON>, Scottish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Ghanaian-American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Georgian basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian speed skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian sumo wrestler, the 70th Yokozuna", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Harumafuji_K%C5%8Dhei\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler, the 70th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\">Yo<PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harumaf<PERSON>ji_K%C5%8Dhei\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler, the 70th <a href=\"https://wikipedia.org/wiki/Yokozuna\" class=\"mw-redirect\" title=\"Yokozuna\">Yo<PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Harumafuji_K%C5%8Dhei"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yokozuna"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hig<PERSON>\" title=\"<PERSON> Thigpen\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>hig<PERSON>\" title=\"<PERSON> Thigpen\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thigpen"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Matt_<PERSON>\" title=\"Matt <PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matt_<PERSON>\" title=\"Matt <PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American jockey (d. 2011)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Austrian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Kenyan runner", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Czech heptathlete", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Klu%C4%8Dinov%C3%A1\" title=\"<PERSON>š<PERSON> Klučinová\"><PERSON><PERSON><PERSON>lučinov<PERSON></a>, Czech heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eli%C5%A1ka_Klu%C4%8Dinov%C3%A1\" title=\"<PERSON>š<PERSON> Klučinová\"><PERSON><PERSON><PERSON> Klučinová</a>, Czech heptathlete", "links": [{"title": "Eliška Klučinová", "link": "https://wikipedia.org/wiki/Eli%C5%A1ka_Klu%C4%8Dinov%C3%A1"}]}, {"year": "1988", "text": "<PERSON>, Canadian football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brad_<PERSON>poli"}]}, {"year": "1988", "text": "<PERSON>, French footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_footballer)\" title=\"<PERSON> (French footballer)\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_footballer)\" title=\"<PERSON> (French footballer)\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON> (French footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(French_footballer)"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mayfield\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian rugby sevens player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby sevens player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian rugby sevens player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Young\" title=\"<PERSON> Young\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chase_Young\" title=\"Chase Young\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Patrick Surtain II\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Patrick Surtain II\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Surtain_II"}]}], "Deaths": [{"year": "911", "text": "Pope <PERSON><PERSON><PERSON>, pope of the Roman Catholic Church", "html": "911 - <a href=\"https://wikipedia.org/wiki/Pope_Ser<PERSON>us_III\" title=\"Pope Sergius III\">Pope <PERSON><PERSON><PERSON></a>, pope of the Roman Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Ser<PERSON>us_III\" title=\"Pope Sergius III\">Pope <PERSON><PERSON><PERSON> III</a>, pope of the Roman Catholic Church", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_Ser<PERSON><PERSON>_III"}]}, {"year": "1070", "text": "<PERSON>, Duke of Lorraine (b. c. 1030)", "html": "1070 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. c. 1030)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (b. c. 1030)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine"}]}, {"year": "1099", "text": "<PERSON>, Bishop of Utrecht (b. before 1040)", "html": "1099 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Bishop_of_Utrecht)\" class=\"mw-redirect\" title=\"<PERSON> (Bishop of Utrecht)\"><PERSON>, Bishop of Utrecht</a> (b. before 1040)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Bishop_of_Utrecht)\" class=\"mw-redirect\" title=\"<PERSON> (Bishop of Utrecht)\"><PERSON>, Bishop of Utrecht</a> (b. before 1040)", "links": [{"title": "<PERSON> (Bishop of Utrecht)", "link": "https://wikipedia.org/wiki/<PERSON>_(Bishop_of_Utrecht)"}]}, {"year": "1132", "text": "<PERSON><PERSON><PERSON> of Kiev (b. 1076)", "html": "1132 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Kiev\" title=\"<PERSON><PERSON><PERSON> I of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a> (b. 1076)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Kiev\" title=\"<PERSON><PERSON><PERSON> of Kiev\"><PERSON><PERSON><PERSON> of Kiev</a> (b. 1076)", "links": [{"title": "<PERSON><PERSON><PERSON> I of Kiev", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Kiev"}]}, {"year": "1279", "text": "<PERSON><PERSON><PERSON>, Duke of Greater Poland (b. 1224)", "html": "1279 - <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_the_Pious\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, Duke of Greater Poland (b. 1224)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_the_Pious\" title=\"<PERSON><PERSON><PERSON> the Pious\"><PERSON><PERSON><PERSON> the Pious</a>, Duke of Greater Poland (b. 1224)", "links": [{"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_the_<PERSON>ous"}]}, {"year": "1322", "text": "<PERSON>, 1st Baron <PERSON>, English soldier and politician, Lord Warden of the Cinque Ports (b. 1275)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_Badlesmere\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1275)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_Badlesmere\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1275)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1345", "text": "<PERSON>, English bishop and politician, Lord Chancellor of The United Kingdom (b. 1287)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of The United Kingdom</a> (b. 1287)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of The United Kingdom</a> (b. 1287)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1424", "text": "<PERSON>, English countess (b. 1372)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English countess (b. 1372)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English countess (b. 1372)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1433", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch saint (b. 1380)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/Lidwina\" title=\"Lidwin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch saint (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lidwina\" title=\"Lidwin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch saint (b. 1380)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lidwina"}]}, {"year": "1471", "text": "<PERSON>, 16th Earl of Warwick, English nobleman, known as \"the Kingmaker\" (b. 1428)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, English nobleman, known as \"the Kingmaker\" (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, English nobleman, known as \"the Kingmaker\" (b. 1428)", "links": [{"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}]}, {"year": "1471", "text": "<PERSON>, 1st Marquess of Montagu (b. 1431)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montagu\" title=\"<PERSON>, 1st Marquess of Montagu\"><PERSON>, 1st Marquess of Montagu</a> (b. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montagu\" title=\"<PERSON>, 1st Marquess of Montagu\"><PERSON>, 1st Marquess of Montagu</a> (b. 1431)", "links": [{"title": "<PERSON>, 1st Marquess of Montagu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Montagu"}]}, {"year": "1480", "text": "<PERSON>, Scottish statesman and prelate (b. c. 1415)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish statesman and prelate (b. c. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish statesman and prelate (b. c. 1415)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1488", "text": "<PERSON><PERSON><PERSON>, Lord of Imola and Forli (b. 1443)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/Girolamo_Riario\" title=\"Girolamo Riario\"><PERSON><PERSON><PERSON></a>, Lord of Imola and Forli (b. 1443)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Girolamo_Riario\" title=\"Girolamo Riario\"><PERSON><PERSON><PERSON></a>, Lord of Imola and Forli (b. 1443)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Girolamo_Riario"}]}, {"year": "1574", "text": "<PERSON> Nassau (b. 1538)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Louis_of_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a> (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_of_Nassau\" title=\"<PERSON> of Nassau\"><PERSON> of Nassau</a> (b. 1538)", "links": [{"title": "Louis of Nassau", "link": "https://wikipedia.org/wiki/Louis_of_Nassau"}]}, {"year": "1578", "text": "<PERSON>, 4th Earl of Bothwell, English husband of <PERSON>, Queen of Scots (b. 1534)", "html": "1578 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Bothwell\" title=\"<PERSON>, 4th Earl of Bothwell\"><PERSON>, 4th Earl of Bothwell</a>, English husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (b. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Bothwell\" title=\"<PERSON>, 4th Earl of Bothwell\"><PERSON>, 4th Earl of Bothwell</a>, English husband of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a> (b. 1534)", "links": [{"title": "<PERSON>, 4th Earl of Bothwell", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Bothwell"}, {"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}]}, {"year": "1587", "text": "<PERSON>, 3rd Earl of Rutland (b. 1548)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Rutland\" title=\"<PERSON>, 3rd Earl of Rutland\"><PERSON>, 3rd Earl of Rutland</a> (b. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Rutland\" title=\"<PERSON>, 3rd Earl of Rutland\"><PERSON>, 3rd Earl of Rutland</a> (b. 1548)", "links": [{"title": "<PERSON>, 3rd Earl of Rutland", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Rutland"}]}, {"year": "1599", "text": "<PERSON>, English politician (b. 1540)", "html": "1599 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON>, Italian violin maker (b. 1540)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Gaspar<PERSON>_da_Sal%C3%B2\" title=\"<PERSON><PERSON><PERSON> da Salò\"><PERSON><PERSON><PERSON> Salò</a>, Italian violin maker (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>par<PERSON>_da_Sal%C3%B2\" title=\"<PERSON><PERSON><PERSON> da Salò\"><PERSON><PERSON><PERSON> Salò</a>, Italian violin maker (b. 1540)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gasparo_da_Sal%C3%B2"}]}, {"year": "1649", "text": "<PERSON><PERSON>, crypto-Jewish martyr", "html": "1649 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Trevi%C3%B1o_de_Sobremonte\" title=\"<PERSON><PERSON> Sobremonte\"><PERSON><PERSON> Sobremonte</a>, crypto-Jewish martyr", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Trevi%C3%B1o_de_Sobremonte\" title=\"<PERSON><PERSON> Sobremonte\"><PERSON><PERSON> Sobremonte</a>, crypto-Jewish martyr", "links": [{"title": "<PERSON>ás Trevi<PERSON> de Sobremonte", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Trevi%C3%B1o_de_Sobremonte"}]}, {"year": "1662", "text": "<PERSON>, 1st Viscount <PERSON> and <PERSON><PERSON>, English politician (b. 1582)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON><PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON><PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Viscount <PERSON><PERSON> and <PERSON><PERSON></a>, English politician (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON><PERSON>_and_<PERSON><PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON><PERSON> and <PERSON><PERSON>\"><PERSON>, 1st Viscount <PERSON> and <PERSON><PERSON></a>, English politician (b. 1582)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON><PERSON>_and_<PERSON><PERSON>"}]}, {"year": "1682", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian priest and saint (b. 1620)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/Avvakum\" title=\"Avvakum\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian priest and saint (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avvakum\" title=\"Avvakum\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian priest and saint (b. 1620)", "links": [{"title": "Avvakum", "link": "https://wikipedia.org/wiki/Avvakum"}]}, {"year": "1721", "text": "<PERSON>, French politician, Controller-General of Finances (b. 1652)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (b. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Controller-General of Finances", "link": "https://wikipedia.org/wiki/Controller-General_of_Finances"}]}, {"year": "1740", "text": "<PERSON> <PERSON>, English philanthropist (b. 1672)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English philanthropist (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English philanthropist (b. 1672)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1759", "text": "<PERSON>, German-English organist and composer (b. 1685)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Handel\"><PERSON></a>, German-English organist and composer (b. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Handel\"><PERSON></a>, German-English organist and composer (b. 1685)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English poet and playwright (b. 1715)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (b. 1715)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1792", "text": "<PERSON>, Slovak-Hungarian astronomer and priest (b. 1720)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Maximilian_<PERSON>\" title=\"Maximilian Hell\"><PERSON></a>, Slovak-Hungarian astronomer and priest (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maximilian_Hell\" title=\"Maximilian Hell\"><PERSON></a>, Slovak-Hungarian astronomer and priest (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maximilian_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, Austrian violinist and composer (b. 1801)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian violinist and composer (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American-Canadian politician (b. 1777)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Charles <PERSON> Church\"><PERSON></a>, American-Canadian politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_Church\" title=\"Charles Lot Church\"><PERSON></a>, American-Canadian politician (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Lot_Church"}]}, {"year": "1886", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Dutch novelist (b. 1812)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>boom-To<PERSON>aint\" title=\"<PERSON>-Touss<PERSON>\"><PERSON>-<PERSON></a>, Dutch novelist (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>boom-To<PERSON>t\" title=\"<PERSON>-To<PERSON>\"><PERSON>-<PERSON></a>, Dutch novelist (b. 1812)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>m-<PERSON><PERSON><PERSON>t"}]}, {"year": "1888", "text": "<PERSON>, Polish chemist (b. 1824)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish chemist (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON>\"><PERSON></a>, Polish chemist (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_Czyrnia%C5%84ski"}]}, {"year": "1910", "text": "<PERSON>, Russian painter and sculptor (b. 1856)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and sculptor (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and sculptor (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, American baseball player and journalist (b. 1880)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and journalist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and journalist (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Canadian lawyer and jurist, 4th Chief Justice of Canada (b. 1836)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist, 4th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_<PERSON>z%C3%A9<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1912", "text": "<PERSON>, French politician, 50th Prime Minister of France (b. 1835)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1914", "text": "<PERSON>, English activist, co-founded the Fabian Society (b. 1855)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, co-founded the <a href=\"https://wikipedia.org/wiki/Fabian_Society\" title=\"Fabian Society\">Fabian Society</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, co-founded the <a href=\"https://wikipedia.org/wiki/Fabian_Society\" title=\"Fabian Society\">Fabian Society</a> (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fabian Society", "link": "https://wikipedia.org/wiki/Fabian_Society"}]}, {"year": "1916", "text": "<PERSON>, Norwegian suffragist and women's rights activist (b. 1847)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian suffragist and women's rights activist (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian suffragist and women's rights activist (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON> <PERSON><PERSON>, Polish physician and linguist, created <PERSON><PERSON><PERSON><PERSON> (b. 1859)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Polish physician and linguist, created <a href=\"https://wikipedia.org/wiki/Esperanto\" title=\"Esperanto\">Esperanto</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Polish physician and linguist, created <a href=\"https://wikipedia.org/wiki/Esperanto\" title=\"Esperanto\">Esperanto</a> (b. 1859)", "links": [{"title": "L. L. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Esperanto", "link": "https://wikipedia.org/wiki/Esperanto"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian judge and politician, 6th Lieutenant Governor of Quebec (b. 1837)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian judge and politician, 6th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian judge and politician, 6th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1837)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Auguste-R%C3%A9al_Angers"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1925", "text": "<PERSON>, American painter (b. 1856)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Georgian-Russian actor, playwright, and poet (b. 1893)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Russian actor, playwright, and poet (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-Russian actor, playwright, and poet (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, German philologist, historian, and educator (b. 1851)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, historian, and educator (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philologist, historian, and educator (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, German-American mathematician and academic (b. 1882)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American mathematician and academic (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Swedish figure skater and architect (b. 1893)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish figure skater and architect (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish figure skater and architect (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gillis_Grafstr%C3%B6m"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Georgian-Russian lieutenant (b. 1907)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-Russian lieutenant (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian-Russian lieutenant (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Indian guru and philosopher (b. 1879)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian guru and philosopher (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian guru and philosopher (b. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Canadian-American director, producer, and screenwriter (b. 1881)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director, producer, and screenwriter (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Christie"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Indian engineer and scholar (b. 1860)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian engineer and scholar (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian engineer and scholar (b. 1860)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Indian monk and historian (b. 1893)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian monk and historian (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian monk and historian (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Russian-Dutch mathematician and theorist (b. 1876)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Dutch mathematician and theorist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Dutch mathematician and theorist (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1964", "text": "<PERSON>, American biologist and author (b. 1907)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and author (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player (b. 1911)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Benton"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Spanish actress (b. 1900)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1oz_Sampedro\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1oz_Sampedro\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish actress (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mu%C3%B1oz_Sampedro"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, German-Swiss mountaineer, geologist, and explorer (b. 1886)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnter_Dyhrenfurth\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss mountaineer, geologist, and explorer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON>nter_D<PERSON>furth\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss mountaineer, geologist, and explorer (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BC<PERSON>_<PERSON>rth"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actor (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> March\"><PERSON><PERSON></a>, American actor (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> March\"><PERSON><PERSON></a>, American actor (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_March"}]}, {"year": "1976", "text": "<PERSON>, Mexican author and activist (b. 1914)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Revueltas\" title=\"<PERSON>\"><PERSON></a>, Mexican author and activist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Revueltas\" title=\"<PERSON>\"><PERSON></a>, Mexican author and activist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Revueltas"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and manager (b. 1915)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON> <PERSON><PERSON>, English educator and critic (b. 1895)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English educator and critic (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English educator and critic (b. 1895)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English bassist (The Pretenders) (b. 1952)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist (<a href=\"https://wikipedia.org/wiki/The_Pretenders\" title=\"The Pretenders\">The Pretenders</a>) (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bassist (<a href=\"https://wikipedia.org/wiki/The_Pretenders\" title=\"The Pretenders\">The Pretenders</a>) (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Pretenders", "link": "https://wikipedia.org/wiki/The_Pretenders"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Italian journalist and author (b. 1920)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and author (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, founder of Dunnes Stores (b. 1908)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman,_born_1908)\" title=\"<PERSON> (businessman, born 1908)\"><PERSON></a>, founder of Dunnes Stores (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman,_born_1908)\" title=\"<PERSON> (businessman, born 1908)\"><PERSON></a>, founder of Dunnes Stores (b. 1908)", "links": [{"title": "<PERSON> (businessman, born 1908)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman,_born_1908)"}]}, {"year": "1986", "text": "<PERSON>, French novelist and philosopher (b. 1908)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and philosopher (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist and philosopher (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American singer (b. 1931)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian politician, 3rd Governor of Ogun State (b. 1927)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Onabanjo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Ogun_State\" class=\"mw-redirect\" title=\"Governor of Ogun State\">Governor of Ogun State</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_On<PERSON>jo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Ogun_State\" class=\"mw-redirect\" title=\"Governor of Ogun State\">Governor of Ogun State</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Onabanjo"}, {"title": "Governor of Ogun State", "link": "https://wikipedia.org/wiki/Governor_of_Ogun_State"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, centre-left Italian politician (b. 1899)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, centre-left Italian politician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, centre-left Italian politician (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian radio broadcaster and feminist and peace activist (b. 1898)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio broadcaster and feminist and peace activist (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio broadcaster and feminist and peace activist (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Pakistani chemist and scholar (b. 1897)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Salimuzzam<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani chemist and scholar (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sal<PERSON>uz<PERSON><PERSON>_<PERSON>\" title=\"Salimuzzam<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Pakistani chemist and scholar (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salimuzzaman_<PERSON>di<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American actor, folk singer, and writer (b. 1909)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, folk singer, and writer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, folk singer, and writer (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress and screenwriter (b. 1911)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English singer-songwriter and actor (b. 1931)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American television announcer (b. 1924)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television announcer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American computer programmer, co-created the zip file format (b. 1962)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, co-created the <a href=\"https://wikipedia.org/wiki/Zip_(file_format)\" class=\"mw-redirect\" title=\"Zip (file format)\">zip file format</a> (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer programmer, co-created the <a href=\"https://wikipedia.org/wiki/Zip_(file_format)\" class=\"mw-redirect\" title=\"Zip (file format)\">zip file format</a> (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Zip (file format)", "link": "https://wikipedia.org/wiki/Zip_(file_format)"}]}, {"year": "2000", "text": "<PERSON> <PERSON><PERSON>, Swiss lawyer and politician (b. 1905)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>_<PERSON>\" title=\"August <PERSON><PERSON>\">August <PERSON><PERSON></a>, Swiss lawyer and politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>_<PERSON>\" title=\"August <PERSON><PERSON>\">August <PERSON><PERSON></a>, Swiss lawyer and politician (b. 1905)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, English footballer (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Wilf_<PERSON>\" title=\"Wilf <PERSON>\">Wil<PERSON></a>, English footballer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilf_<PERSON>\" title=\"Wilf <PERSON>\">Wil<PERSON></a>, English footballer (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilf_Mannion"}]}, {"year": "2001", "text": "<PERSON>, Scottish footballer (b. 1939)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter (b. 1927)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Finnish politician (b. 1941)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, English-Canadian television producer, co-founded the Cookie Jar Group (b. 1953)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian television producer, co-founded the <a href=\"https://wikipedia.org/wiki/Cookie_Jar_Group\" title=\"Cookie Jar Group\">Cookie Jar Group</a> (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian television producer, co-founded the <a href=\"https://wikipedia.org/wiki/Cookie_Jar_Group\" title=\"Cookie Jar Group\">Cookie Jar Group</a> (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>ie <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie_<PERSON>_Group"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Kosovo politician (b. 1936)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovo politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kosovo politician (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian journalist, author, and activist (b. 1924)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>, Canadian journalist, author, and activist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON><PERSON>\">June <PERSON></a>, Canadian journalist, author, and activist (b. 1924)", "links": [{"title": "June <PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer and ukulele player (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and ukulele player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ho\"><PERSON></a>, American singer and ukulele player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French historian and economist (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and economist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian and economist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_R%C3%A9mond"}]}, {"year": "2008", "text": "<PERSON>, American baseball player and manager (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American animator and voice actor (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator and voice actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American animator and voice actor (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, French author (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Pakistani theologian and scholar (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani theologian and scholar (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani theologian and scholar (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Polish-French psychologist and author (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, Polish-French psychologist and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(psychologist)\" title=\"<PERSON> (psychologist)\"><PERSON></a>, Polish-French psychologist and author (b. 1923)", "links": [{"title": "<PERSON> (psychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychologist)"}]}, {"year": "2010", "text": "<PERSON>, American singer-songwriter and bass player (b. 1962)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian Roman Catholic bishop (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Roman Catholic bishop (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian Roman Catholic bishop (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON>ard"}]}, {"year": "2012", "text": "<PERSON>, Canadian actor (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Italian footballer (b. 1986)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (b. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Israeli businessman, founded the Scailex Corporation (b. 1937)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli businessman, founded the <a href=\"https://wikipedia.org/wiki/Scailex_Corporation\" title=\"Scailex Corporation\">Scailex Corporation</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli businessman, founded the <a href=\"https://wikipedia.org/wiki/Scailex_Corporation\" title=\"Scailex Corporation\">Scailex Corporation</a> (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_<PERSON>"}, {"title": "Scailex Corporation", "link": "https://wikipedia.org/wiki/Scailex_Corporation"}]}, {"year": "2013", "text": "<PERSON>, English conductor and educator (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and educator (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor and educator (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian businessman, founded RPG Group (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/R._P._Goenka\" title=\"R. P. Goenka\">R<PERSON> P<PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/RPG_Group\" title=\"RPG Group\">RPG Group</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._P._Goenka\" title=\"R. P. Goenka\">R<PERSON> P<PERSON></a>, Indian businessman, founded <a href=\"https://wikipedia.org/wiki/RPG_Group\" title=\"RPG Group\">RPG Group</a> (b. 1930)", "links": [{"title": "R. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>._Goenka"}, {"title": "RPG Group", "link": "https://wikipedia.org/wiki/RPG_Group"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter (b. 1945)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Peruvian politician, 121st Prime Minister of Peru (b. 1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 121st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Peruvian politician, 121st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Peru\" title=\"Prime Minister of Peru\">Prime Minister of Peru</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Peru", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Peru"}]}, {"year": "2013", "text": "<PERSON>, American politician (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)\" title=\"<PERSON> (Ohio politician)\"><PERSON></a>, American politician (b. 1943)", "links": [{"title": "<PERSON> (Ohio politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Ohio_politician)"}]}, {"year": "2014", "text": "<PERSON>, Romanian poet and critic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American-Canadian author (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Canadian author (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English businessman and academic (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, German journalist and author (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1960)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American singer (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Italian cardinal and theologian (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and theologian (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal and theologian (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Swedish actress (b.1935)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (b.1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (b.1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American public health researcher (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Onofrio\" title=\"<PERSON>\"><PERSON></a>, American public health researcher (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Onofrio\" title=\"<PERSON>\"><PERSON></a>, American public health researcher (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carol_D%27Onofrio"}]}, {"year": "2021", "text": "<PERSON>, American mastermind of the world's largest Ponzi scheme (b. 1938)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mastermind of the world's largest <a href=\"https://wikipedia.org/wiki/Ponzi_scheme\" title=\"Ponzi scheme\">Ponzi scheme</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mastermind of the world's largest <a href=\"https://wikipedia.org/wiki/Ponzi_scheme\" title=\"Ponzi scheme\">Ponzi scheme</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ponzi scheme", "link": "https://wikipedia.org/wiki/Ponzi_scheme"}]}, {"year": "2022", "text": "<PERSON>, Canadian ice hockey player and sportscaster (b. 1957)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Finnish politician (b. 1948)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Nigerian saxophonist, singer (b. 1943)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Julius\"><PERSON></a>, Nigerian saxophonist, singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Orlando Julius\"><PERSON></a>, Nigerian saxophonist, singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Julius"}]}, {"year": "2023", "text": "<PERSON>, Irish guitarist (<PERSON>) (b. 1976)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (<a href=\"https://wikipedia.org/wiki/The_Script\" title=\"The Script\">The Script</a>) (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish guitarist (<a href=\"https://wikipedia.org/wiki/The_Script\" title=\"The Script\">The Script</a>) (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Script", "link": "https://wikipedia.org/wiki/The_Script"}]}, {"year": "2024", "text": "<PERSON>, American baseball player (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}