{"date": "January 28", "url": "https://wikipedia.org/wiki/January_28", "data": {"Events": [{"year": "98", "text": "On the death of <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> is declared Roman emperor in Cologne, the seat of his government in lower Germany.", "html": "98 - On the death of <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\">Nerva</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is declared Roman emperor in <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, the seat of his government in lower Germany.", "no_year_html": "On the death of <a href=\"https://wikipedia.org/wiki/Nerva\" title=\"Nerva\">Nerva</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is declared Roman emperor in <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>, the seat of his government in lower Germany.", "links": [{"title": "Nerva", "link": "https://wikipedia.org/wiki/Nerva"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Cologne", "link": "https://wikipedia.org/wiki/Cologne"}]}, {"year": "814", "text": "The death of <PERSON><PERSON><PERSON><PERSON>, the first Holy Roman Emperor, brings about the accession of his son <PERSON> as ruler of the Frankish Empire.", "html": "814 - The death of <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, brings about the accession of his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> Pi<PERSON>\"><PERSON> the <PERSON></a> as ruler of the <a href=\"https://wikipedia.org/wiki/Carolingian_Empire\" title=\"Carolingian Empire\">Frankish Empire</a>.", "no_year_html": "The death of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"Charle<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the first <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, brings about the accession of his son <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Pi<PERSON>\" title=\"<PERSON> Pi<PERSON>\"><PERSON> the Pi<PERSON></a> as ruler of the <a href=\"https://wikipedia.org/wiki/Carolingian_Empire\" title=\"Carolingian Empire\">Frankish Empire</a>.", "links": [{"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Carolingian Empire", "link": "https://wikipedia.org/wiki/Carolingian_Empire"}]}, {"year": "1069", "text": "<PERSON>, appointed Earl of Northumbria by <PERSON> the Conqueror, rides into Durham, England, where he is defeated and killed by rebels. This incident leads to the Harrying of the North.", "html": "1069 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, appointed <a href=\"https://wikipedia.org/wiki/Earl_of_Northumbria\" title=\"Earl of Northumbria\">Earl of Northumbria</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, rides into <a href=\"https://wikipedia.org/wiki/Durham,_England\" title=\"Durham, England\">Durham, England</a>, where he is defeated and killed by rebels. This incident leads to the <a href=\"https://wikipedia.org/wiki/Harrying_of_the_North\" title=\"Harrying of the North\"><PERSON><PERSON> of the North</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, appointed <a href=\"https://wikipedia.org/wiki/Earl_<PERSON>_Northumbria\" title=\"Earl of Northumbria\">Earl of Northumbria</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Con<PERSON>or\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, rides into <a href=\"https://wikipedia.org/wiki/Durham,_England\" title=\"Durham, England\">Durham, England</a>, where he is defeated and killed by rebels. This incident leads to the <a href=\"https://wikipedia.org/wiki/Harrying_of_the_North\" title=\"Harrying of the North\">Harry<PERSON> of the North</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Earl of Northumbria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Northumbria"}, {"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Durham, England", "link": "https://wikipedia.org/wiki/Durham,_England"}, {"title": "Harrying of the North", "link": "https://wikipedia.org/wiki/Harrying_of_the_North"}]}, {"year": "1077", "text": "Walk to Canossa: The excommunication of <PERSON>, Holy Roman Emperor, is lifted after he humbles himself before <PERSON> <PERSON> at Canossa in Italy.", "html": "1077 - <a href=\"https://wikipedia.org/wiki/Walk_to_Canossa\" class=\"mw-redirect\" title=\"Walk to Canossa\">Walk to Canossa</a>: The <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, is lifted after he humbles himself before <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VII\" title=\"<PERSON> Gregory VII\"><PERSON> VII</a> at Canossa in Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walk_to_Canossa\" class=\"mw-redirect\" title=\"Walk to Canossa\">Walk to Canossa</a>: The <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunication</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_IV,_Holy_Roman_Emperor\" title=\"<PERSON> IV, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, is lifted after he humbles himself before <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory VII\"><PERSON> VII</a> at Canossa in Italy.", "links": [{"title": "Walk to Canossa", "link": "https://wikipedia.org/wiki/Walk_to_Canossa"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1393", "text": "King <PERSON> of France was nearly killed when several other dancers' costumes caught fire during a masquerade ball in Paris.", "html": "1393 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VI of France\">King <PERSON> VI of France</a> was nearly killed when <a href=\"https://wikipedia.org/wiki/Ba<PERSON>_des_Ardents\" title=\"Ba<PERSON> des Ardents\">several other dancers' costumes caught fire</a> during a <a href=\"https://wikipedia.org/wiki/Masquerade_ball\" title=\"Masquerade ball\">masquerade ball</a> in Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> VI of France\">King <PERSON> VI of France</a> was nearly killed when <a href=\"https://wikipedia.org/wiki/Ba<PERSON>_des_Ardents\" title=\"Ba<PERSON> des Ardents\">several other dancers' costumes caught fire</a> during a <a href=\"https://wikipedia.org/wiki/Masquerade_ball\" title=\"Masquerade ball\">masquerade ball</a> in Paris.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_VI_of_France"}, {"title": "Bal des Ardents", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ts"}, {"title": "Masquerade ball", "link": "https://wikipedia.org/wiki/Masquerade_ball"}]}, {"year": "1521", "text": "The Diet of Worms begins, lasting until May 25.", "html": "1521 - The <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a> begins, lasting until <a href=\"https://wikipedia.org/wiki/May_25\" title=\"May 25\">May 25</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a> begins, lasting until <a href=\"https://wikipedia.org/wiki/May_25\" title=\"May 25\">May 25</a>.", "links": [{"title": "Diet of Worms", "link": "https://wikipedia.org/wiki/Diet_of_Worms"}, {"title": "May 25", "link": "https://wikipedia.org/wiki/May_25"}]}, {"year": "1547", "text": "<PERSON>, the nine-year-old son of <PERSON>, becomes King of England on his father's death.", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" class=\"mw-redirect\" title=\"<PERSON> VI of England\"><PERSON> VI</a>, the nine-year-old son of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON></a>, becomes King of England on his father's death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"Edward VI of England\"><PERSON> VI</a>, the nine-year-old son of <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON></a>, becomes King of England on his father's death.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1568", "text": "The Edict of Torda prohibits the persecution of individuals on religious grounds in <PERSON>'s Eastern Hungarian Kingdom.", "html": "1568 - The <a href=\"https://wikipedia.org/wiki/Edict_of_Torda\" title=\"Edict of Torda\">Edict of Torda</a> prohibits the persecution of individuals on religious grounds in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1polya\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Eastern_Hungarian_Kingdom\" title=\"Eastern Hungarian Kingdom\">Eastern Hungarian Kingdom</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Edict_of_Torda\" title=\"Edict of Torda\">Edict of Torda</a> prohibits the persecution of individuals on religious grounds in <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1polya\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Eastern_Hungarian_Kingdom\" title=\"Eastern Hungarian Kingdom\">Eastern Hungarian Kingdom</a>.", "links": [{"title": "Edict of Torda", "link": "https://wikipedia.org/wiki/Edict_of_Torda"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>%C3%A1polya"}, {"title": "Eastern Hungarian Kingdom", "link": "https://wikipedia.org/wiki/Eastern_Hungarian_Kingdom"}]}, {"year": "1573", "text": "Articles of the Warsaw Confederation are signed, sanctioning freedom of religion in Poland.", "html": "1573 - Articles of the <a href=\"https://wikipedia.org/wiki/Warsaw_Confederation\" title=\"Warsaw Confederation\">Warsaw Confederation</a> are signed, sanctioning <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> in Poland.", "no_year_html": "Articles of the <a href=\"https://wikipedia.org/wiki/Warsaw_Confederation\" title=\"Warsaw Confederation\">Warsaw Confederation</a> are signed, sanctioning <a href=\"https://wikipedia.org/wiki/Freedom_of_religion\" title=\"Freedom of religion\">freedom of religion</a> in Poland.", "links": [{"title": "Warsaw Confederation", "link": "https://wikipedia.org/wiki/Warsaw_Confederation"}, {"title": "Freedom of religion", "link": "https://wikipedia.org/wiki/Freedom_of_religion"}]}, {"year": "1591", "text": "Execution of <PERSON>, accused of witchcraft in Edinburgh.", "html": "1591 - Execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, accused of <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Edinburgh.", "no_year_html": "Execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, accused of <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in Edinburgh.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}]}, {"year": "1624", "text": "Sir <PERSON> founds the first British colony in the Caribbean, on the island of Saint Kitts.", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\">Sir <PERSON></a> founds the first British colony in the Caribbean, on the island of <a href=\"https://wikipedia.org/wiki/Saint_Kitts\" title=\"Saint Kitts\">Saint Kitts</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\">Sir <PERSON></a> founds the first British colony in the Caribbean, on the island of <a href=\"https://wikipedia.org/wiki/Saint_Kitts\" title=\"Saint Kitts\">Saint Kitts</a>.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}, {"title": "Saint Kitts", "link": "https://wikipedia.org/wiki/Saint_Kitts"}]}, {"year": "1671", "text": "Original city of Panama (founded in 1519) is destroyed by a fire when privateer <PERSON> sacks and sets fire to it. The site of the previously devastated city is still in ruins (see Panama Viejo).", "html": "1671 - Original city of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> (founded in 1519) is destroyed by a fire when privateer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition\" title=\"<PERSON>'s Panama expedition\">sacks and sets fire to it</a>. The site of the previously devastated city is still in ruins (see <a href=\"https://wikipedia.org/wiki/Panama_Viejo\" class=\"mw-redirect\" title=\"Panama Viejo\">Panama Viejo</a>).", "no_year_html": "Original city of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> (founded in 1519) is destroyed by a fire when privateer <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition\" title=\"<PERSON>'s Panama expedition\">sacks and sets fire to it</a>. The site of the previously devastated city is still in ruins (see <a href=\"https://wikipedia.org/wiki/Panama_Viejo\" class=\"mw-redirect\" title=\"Panama Viejo\">Panama Viejo</a>).", "links": [{"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s Panama expedition", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_Panama_expedition"}, {"title": "Panama Viejo", "link": "https://wikipedia.org/wiki/Panama_Viejo"}]}, {"year": "1724", "text": "The Russian Academy of Sciences is founded in St. Petersburg, Russia, by <PERSON>, and implemented by Senate decree. It is called the St. Petersburg Academy of Sciences until 1917.", "html": "1724 - The <a href=\"https://wikipedia.org/wiki/Russian_Academy_of_Sciences\" title=\"Russian Academy of Sciences\">Russian Academy of Sciences</a> is founded in <a href=\"https://wikipedia.org/wiki/St._Petersburg\" class=\"mw-redirect\" title=\"St. Petersburg\">St. Petersburg</a>, Russia, by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, and implemented by Senate decree. It is called the St. Petersburg Academy of Sciences until 1917.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_Academy_of_Sciences\" title=\"Russian Academy of Sciences\">Russian Academy of Sciences</a> is founded in <a href=\"https://wikipedia.org/wiki/St._Petersburg\" class=\"mw-redirect\" title=\"St. Petersburg\">St. Petersburg</a>, Russia, by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, and implemented by Senate decree. It is called the St. Petersburg Academy of Sciences until 1917.", "links": [{"title": "Russian Academy of Sciences", "link": "https://wikipedia.org/wiki/Russian_Academy_of_Sciences"}, {"title": "St. Petersburg", "link": "https://wikipedia.org/wiki/St._Petersburg"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}]}, {"year": "1754", "text": "Sir <PERSON> coins the word serendipity in a letter to a friend.", "html": "1754 - Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> coins the word <i><a href=\"https://wikipedia.org/wiki/Serendipity\" title=\"Serendipity\">serendipity</a></i> in a letter to a friend.", "no_year_html": "Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> coins the word <i><a href=\"https://wikipedia.org/wiki/Serendipity\" title=\"Serendipity\">serendipity</a></i> in a letter to a friend.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Serendipity", "link": "https://wikipedia.org/wiki/Serendipity"}]}, {"year": "1813", "text": "<PERSON>'s Pride and Prejudice is first published in the United Kingdom.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jane <PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Pride_and_Prejudice_(novel)\" class=\"mw-redirect\" title=\"Pride and Prejudice (novel)\">Pride and Prejudice</a></i> is first published in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jane Austen\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Pride_and_Prejudice_(novel)\" class=\"mw-redirect\" title=\"Pride and Prejudice (novel)\">Pride and Prejudice</a></i> is first published in the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pride and Prejudice (novel)", "link": "https://wikipedia.org/wiki/Pride_and_Prejudice_(novel)"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1846", "text": "The Battle of Aliwal, India, is won by British troops commanded by Sir <PERSON>.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Aliwal\" title=\"Battle of Aliwal\">Battle of Aliwal</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, is won by British troops commanded by <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Aliwal\" title=\"Battle of Aliwal\">Battle of Aliwal</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, is won by British troops commanded by <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>.", "links": [{"title": "Battle of Aliwal", "link": "https://wikipedia.org/wiki/Battle_of_Aliwal"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1851", "text": "Northwestern University becomes the first chartered university in Illinois.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a> becomes the first chartered university in <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a> becomes the first chartered university in <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a>.", "links": [{"title": "Northwestern University", "link": "https://wikipedia.org/wiki/Northwestern_University"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}]}, {"year": "1855", "text": "A locomotive on the Panama Canal Railway runs from the Atlantic Ocean to the Pacific Ocean for the first time.", "html": "1855 - A locomotive on the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Railway\" title=\"Panama Canal Railway\">Panama Canal Railway</a> runs from the Atlantic Ocean to the Pacific Ocean for the first time.", "no_year_html": "A locomotive on the <a href=\"https://wikipedia.org/wiki/Panama_Canal_Railway\" title=\"Panama Canal Railway\">Panama Canal Railway</a> runs from the Atlantic Ocean to the Pacific Ocean for the first time.", "links": [{"title": "Panama Canal Railway", "link": "https://wikipedia.org/wiki/Panama_Canal_Railway"}]}, {"year": "1871", "text": "Franco-Prussian War: The Siege of Paris ends in French defeat and an armistice.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a> ends in French defeat and an <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)\" class=\"mw-redirect\" title=\"Siege of Paris (1870-71)\">Siege of Paris</a> ends in French defeat and an <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a>.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Siege of Paris (1870-71)", "link": "https://wikipedia.org/wiki/Siege_of_Paris_(1870%E2%80%9371)"}, {"title": "Armistice", "link": "https://wikipedia.org/wiki/Armistice"}]}, {"year": "1878", "text": "Yale Daily News becomes the first independent daily college newspaper in the United States.", "html": "1878 - <i><a href=\"https://wikipedia.org/wiki/Yale_Daily_News\" title=\"Yale Daily News\">Yale Daily News</a></i> becomes the first independent daily <a href=\"https://wikipedia.org/wiki/Student_newspaper\" class=\"mw-redirect\" title=\"Student newspaper\">college newspaper</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Yale_Daily_News\" title=\"Yale Daily News\">Yale Daily News</a></i> becomes the first independent daily <a href=\"https://wikipedia.org/wiki/Student_newspaper\" class=\"mw-redirect\" title=\"Student newspaper\">college newspaper</a> in the <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">United States</a>.", "links": [{"title": "Yale Daily News", "link": "https://wikipedia.org/wiki/Yale_Daily_News"}, {"title": "Student newspaper", "link": "https://wikipedia.org/wiki/Student_newspaper"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}]}, {"year": "1896", "text": "<PERSON> of East Peckham, Kent, becomes the first person to be convicted of speeding. He was fined one shilling, plus costs, for speeding at 8 mph (13 km/h), thereby exceeding the contemporary speed limit of 2 mph (3.2 km/h).", "html": "1896 - <PERSON> of <a href=\"https://wikipedia.org/wiki/East_Peckham\" title=\"East Peckham\">East Peckham</a>, Kent, becomes the first person to be convicted of <a href=\"https://wikipedia.org/wiki/Speed_limit\" title=\"Speed limit\">speeding</a>. He was fined one <a href=\"https://wikipedia.org/wiki/Shilling_(British_coin)\" title=\"Shilling (British coin)\">shilling</a>, plus costs, for speeding at 8 mph (13 km/h), thereby exceeding the contemporary speed limit of 2 mph (3.2 km/h).", "no_year_html": "<PERSON> of <a href=\"https://wikipedia.org/wiki/East_Peckham\" title=\"East Peckham\">East Peckham</a>, Kent, becomes the first person to be convicted of <a href=\"https://wikipedia.org/wiki/Speed_limit\" title=\"Speed limit\">speeding</a>. He was fined one <a href=\"https://wikipedia.org/wiki/Shilling_(British_coin)\" title=\"Shilling (British coin)\">shilling</a>, plus costs, for speeding at 8 mph (13 km/h), thereby exceeding the contemporary speed limit of 2 mph (3.2 km/h).", "links": [{"title": "East Peckham", "link": "https://wikipedia.org/wiki/East_Peckham"}, {"title": "Speed limit", "link": "https://wikipedia.org/wiki/Speed_limit"}, {"title": "Shilling (British coin)", "link": "https://wikipedia.org/wiki/Shilling_(British_coin)"}]}, {"year": "1902", "text": "The Carnegie Institution of Washington is founded in Washington, D.C., with a $10 million gift from <PERSON>.", "html": "1902 - The <a href=\"https://wikipedia.org/wiki/Carnegie_Institution_for_Science\" title=\"Carnegie Institution for Science\">Carnegie Institution of Washington</a> is founded in Washington, D.C., with a $10 million gift from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Carnegie_Institution_for_Science\" title=\"Carnegie Institution for Science\">Carnegie Institution of Washington</a> is founded in Washington, D.C., with a $10 million gift from <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Carnegie Institution for Science", "link": "https://wikipedia.org/wiki/Carnegie_Institution_for_Science"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "Members of the Portuguese Republican Party fail in their attempted coup d'état against the administrative dictatorship of Prime Minister <PERSON>.", "html": "1908 - Members of the <a href=\"https://wikipedia.org/wiki/Portuguese_Republican_Party\" title=\"Portuguese Republican Party\">Portuguese Republican Party</a> fail in their <a href=\"https://wikipedia.org/wiki/Municipal_Library_Elevator_Coup\" title=\"Municipal Library Elevator Coup\">attempted coup d'état</a> against the administrative dictatorship of Prime Minister <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Members of the <a href=\"https://wikipedia.org/wiki/Portuguese_Republican_Party\" title=\"Portuguese Republican Party\">Portuguese Republican Party</a> fail in their <a href=\"https://wikipedia.org/wiki/Municipal_Library_Elevator_Coup\" title=\"Municipal Library Elevator Coup\">attempted coup d'état</a> against the administrative dictatorship of Prime Minister <a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Portuguese Republican Party", "link": "https://wikipedia.org/wiki/Portuguese_Republican_Party"}, {"title": "Municipal Library Elevator Coup", "link": "https://wikipedia.org/wiki/Municipal_Library_Elevator_Coup"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Franco"}]}, {"year": "1909", "text": "United States troops leave Cuba, with the exception of Guantanamo Bay Naval Base, after being there since the Spanish-American War.", "html": "1909 - United States troops leave <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, with the exception of <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantanamo Bay Naval Base</a>, after being there since the <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>.", "no_year_html": "United States troops leave <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>, with the exception of <a href=\"https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base\" title=\"Guantanamo Bay Naval Base\">Guantanamo Bay Naval Base</a>, after being there since the <a href=\"https://wikipedia.org/wiki/Spanish%E2%80%93American_War\" title=\"Spanish-American War\">Spanish-American War</a>.", "links": [{"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Guantanamo Bay Naval Base", "link": "https://wikipedia.org/wiki/Guantanamo_Bay_Naval_Base"}, {"title": "Spanish-American War", "link": "https://wikipedia.org/wiki/Spanish%E2%80%93American_War"}]}, {"year": "1915", "text": "An act of the U.S. Congress creates the United States Coast Guard as a branch of the United States Armed Forces.", "html": "1915 - An act of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> creates the <a href=\"https://wikipedia.org/wiki/United_States_Coast_Guard\" title=\"United States Coast Guard\">United States Coast Guard</a> as a branch of the United States Armed Forces.", "no_year_html": "An act of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> creates the <a href=\"https://wikipedia.org/wiki/United_States_Coast_Guard\" title=\"United States Coast Guard\">United States Coast Guard</a> as a branch of the United States Armed Forces.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "United States Coast Guard", "link": "https://wikipedia.org/wiki/United_States_Coast_Guard"}]}, {"year": "1916", "text": "The Canadian province of Manitoba grants women the right to vote and run for office in provincial elections (although still excluding women of Indigenous or Asian heritage), marking the first time women in Canada are granted voting rights.", "html": "1916 - The Canadian province of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> grants women the right to vote and run for office in provincial elections (although still excluding women of Indigenous or Asian heritage), marking the first time <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_Canada\" title=\"Women's suffrage in Canada\">women in Canada are granted voting rights</a>.", "no_year_html": "The Canadian province of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> grants women the right to vote and run for office in provincial elections (although still excluding women of Indigenous or Asian heritage), marking the first time <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_Canada\" title=\"Women's suffrage in Canada\">women in Canada are granted voting rights</a>.", "links": [{"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}, {"title": "Women's suffrage in Canada", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_Canada"}]}, {"year": "1918", "text": "Finnish Civil War: The Red Guard rebels seize control of the capital, Helsinki; members of the Senate of Finland go underground.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guard rebels</a> seize control of the capital, <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>; members of the <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Senate of Finland</a> go underground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_Civil_War\" title=\"Finnish Civil War\">Finnish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Guards_(Finland)\" title=\"Red Guards (Finland)\">Red Guard rebels</a> seize control of the capital, <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>; members of the <a href=\"https://wikipedia.org/wiki/Senate_of_Finland\" title=\"Senate of Finland\">Senate of Finland</a> go underground.", "links": [{"title": "Finnish Civil War", "link": "https://wikipedia.org/wiki/Finnish_Civil_War"}, {"title": "Red Guards (Finland)", "link": "https://wikipedia.org/wiki/Red_Guards_(Finland)"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "Senate of Finland", "link": "https://wikipedia.org/wiki/Senate_of_Finland"}]}, {"year": "1919", "text": "The Order of the White Rose of Finland is established by Baron <PERSON><PERSON><PERSON>, the regent of the Kingdom of Finland.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Order_of_the_White_Rose_of_Finland\" title=\"Order of the White Rose of Finland\">Order of the White Rose of Finland</a> is established by Baron <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)\" title=\"Kingdom of Finland (1918)\">Kingdom of Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Order_of_the_White_Rose_of_Finland\" title=\"Order of the White Rose of Finland\">Order of the White Rose of Finland</a> is established by Baron <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Regent\" title=\"Regent\">regent</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)\" title=\"Kingdom of Finland (1918)\">Kingdom of Finland</a>.", "links": [{"title": "Order of the White Rose of Finland", "link": "https://wikipedia.org/wiki/Order_of_the_White_Rose_of_Finland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Regent"}, {"title": "Kingdom of Finland (1918)", "link": "https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)"}]}, {"year": "1920", "text": "Foundation of the Spanish Legion.", "html": "1920 - Foundation of the <a href=\"https://wikipedia.org/wiki/Spanish_Legion\" title=\"Spanish Legion\">Spanish Legion</a>.", "no_year_html": "Foundation of the <a href=\"https://wikipedia.org/wiki/Spanish_Legion\" title=\"Spanish Legion\">Spanish Legion</a>.", "links": [{"title": "Spanish Legion", "link": "https://wikipedia.org/wiki/Spanish_Legion"}]}, {"year": "1922", "text": "Knickerbocker Storm: Washington, D.C.'s biggest snowfall, causes a disaster when the roof of the Knickerbocker Theatre collapses, killing over 100 people.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Knicker<PERSON>cker_Storm\" class=\"mw-redirect\" title=\"Knickerbocker Storm\">Knickerbocker Storm</a>: Washington, D.C.'s biggest snowfall, causes a disaster when the roof of the <a href=\"https://wikipedia.org/wiki/Knickerbocker_Theatre_(Washington,_D.C.)\" title=\"Knickerbocker Theatre (Washington, D.C.)\">Knickerbocker Theatre</a> collapses, killing over 100 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Knickerbocker_Storm\" class=\"mw-redirect\" title=\"Knickerbocker Storm\">Knickerbocker Storm</a>: Washington, D.C.'s biggest snowfall, causes a disaster when the roof of the <a href=\"https://wikipedia.org/wiki/Knickerbocker_Theatre_(Washington,_D.C.)\" title=\"Knickerbocker Theatre (Washington, D.C.)\">Knickerbocker Theatre</a> collapses, killing over 100 people.", "links": [{"title": "Knickerbocker Storm", "link": "https://wikipedia.org/wiki/K<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Knickerbocker Theatre (Washington, D.C.)", "link": "https://wikipedia.org/wiki/Knickerbocker_Theatre_(Washington,_D.C.)"}]}, {"year": "1932", "text": "Japanese forces attack Shanghai.", "html": "1932 - Japanese forces <a href=\"https://wikipedia.org/wiki/January_28_Incident\" class=\"mw-redirect\" title=\"January 28 Incident\">attack Shanghai</a>.", "no_year_html": "Japanese forces <a href=\"https://wikipedia.org/wiki/January_28_Incident\" class=\"mw-redirect\" title=\"January 28 Incident\">attack Shanghai</a>.", "links": [{"title": "January 28 Incident", "link": "https://wikipedia.org/wiki/January_28_Incident"}]}, {"year": "1933", "text": "The name Pakistan is coined by <PERSON><PERSON><PERSON><PERSON> and is accepted by Indian Muslims who then thereby adopted it further for the Pakistan Movement seeking independence.", "html": "1933 - The name <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> is <a href=\"https://wikipedia.org/wiki/Neologism\" title=\"Neologism\">coined</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> and is accepted by <a href=\"https://wikipedia.org/wiki/Indian_Muslims\" class=\"mw-redirect\" title=\"Indian Muslims\">Indian Muslims</a> who then thereby adopted it further for the <a href=\"https://wikipedia.org/wiki/Pakistan_Movement\" title=\"Pakistan Movement\">Pakistan Movement</a> seeking independence.", "no_year_html": "The name <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> is <a href=\"https://wikipedia.org/wiki/Neologism\" title=\"Neologism\">coined</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and is accepted by <a href=\"https://wikipedia.org/wiki/Indian_Muslims\" class=\"mw-redirect\" title=\"Indian Muslims\">Indian Muslims</a> who then thereby adopted it further for the <a href=\"https://wikipedia.org/wiki/Pakistan_Movement\" title=\"Pakistan Movement\">Pakistan Movement</a> seeking independence.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Neologism", "link": "https://wikipedia.org/wiki/Neologism"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Indian Muslims", "link": "https://wikipedia.org/wiki/Indian_Muslims"}, {"title": "Pakistan Movement", "link": "https://wikipedia.org/wiki/Pakistan_Movement"}]}, {"year": "1935", "text": "Iceland becomes the first Western country to legalize therapeutic abortion.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> becomes the first Western country to legalize therapeutic <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> becomes the first Western country to legalize therapeutic <a href=\"https://wikipedia.org/wiki/Abortion\" title=\"Abortion\">abortion</a>.", "links": [{"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Abortion", "link": "https://wikipedia.org/wiki/Abortion"}]}, {"year": "1938", "text": "The World Land Speed Record on a public road is broken by <PERSON> in the Mercedes-Benz W125 Rekordwagen at a speed of 432.7 kilometres per hour (268.9 mph).", "html": "1938 - The <a href=\"https://wikipedia.org/wiki/World_Land_Speed_Record\" class=\"mw-redirect\" title=\"World Land Speed Record\">World Land Speed Record</a> on a <a href=\"https://wikipedia.org/wiki/Public_road\" class=\"mw-redirect\" title=\"Public road\">public road</a> is broken by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Mercedes-Benz_W125_Rekordwagen\" title=\"Mercedes-Benz W125 Rekordwagen\">Mercedes-Benz W125 Rekordwagen</a> at a speed of 432.7 kilometres per hour (268.9 mph).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/World_Land_Speed_Record\" class=\"mw-redirect\" title=\"World Land Speed Record\">World Land Speed Record</a> on a <a href=\"https://wikipedia.org/wiki/Public_road\" class=\"mw-redirect\" title=\"Public road\">public road</a> is broken by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Mercedes-Benz_W125_Rekordwagen\" title=\"Mercedes-Benz W125 Rekordwagen\">Mercedes-Benz W125 Rekordwagen</a> at a speed of 432.7 kilometres per hour (268.9 mph).", "links": [{"title": "World Land Speed Record", "link": "https://wikipedia.org/wiki/World_Land_Speed_Record"}, {"title": "Public road", "link": "https://wikipedia.org/wiki/Public_road"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mercedes-Benz W125 Rekordwagen", "link": "https://wikipedia.org/wiki/Mercedes-Benz_W125_Rekordwagen"}]}, {"year": "1941", "text": "Franco-Thai War: Final air battle of the conflict. A Japanese-mediated armistice goes into effect later in the day.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Franco-Thai_War\" title=\"Franco-Thai War\">Franco-Thai War</a>: Final air battle of the conflict. A Japanese-mediated <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a> goes into effect later in the day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Thai_War\" title=\"Franco-Thai War\">Franco-Thai War</a>: Final air battle of the conflict. A Japanese-mediated <a href=\"https://wikipedia.org/wiki/Armistice\" title=\"Armistice\">armistice</a> goes into effect later in the day.", "links": [{"title": "Franco-Thai War", "link": "https://wikipedia.org/wiki/Franco-Thai_War"}, {"title": "Armistice", "link": "https://wikipedia.org/wiki/Armistice"}]}, {"year": "1945", "text": "World War II: Supplies begin to reach the Republic of China over the newly reopened Burma Road.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Supplies begin to reach the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> over the newly reopened <a href=\"https://wikipedia.org/wiki/Burma_Road\" title=\"Burma Road\">Burma Road</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Supplies begin to reach the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Republic of China</a> over the newly reopened <a href=\"https://wikipedia.org/wiki/Burma_Road\" title=\"Burma Road\">Burma Road</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}, {"title": "Burma Road", "link": "https://wikipedia.org/wiki/Burma_Road"}]}, {"year": "1956", "text": "<PERSON> makes his first national television appearance.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\">Elvis Presley</a> makes his first national television appearance.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\">Elvis <PERSON></a> makes his first national television appearance.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "The Lego company patents the design of its Lego bricks, still compatible with bricks produced today.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Lego\" title=\"Lego\">Lego</a> company patents the design of its Lego bricks, still compatible with bricks produced today.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lego\" title=\"Lego\">Lego</a> company patents the design of its Lego bricks, still compatible with bricks produced today.", "links": [{"title": "Lego", "link": "https://wikipedia.org/wiki/Lego"}]}, {"year": "1960", "text": "The National Football League announces expansion teams for Dallas to start in the 1960 NFL season and Minneapolis-St. Paul for the 1961 NFL season.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> announces expansion teams for <a href=\"https://wikipedia.org/wiki/Dallas_Cowboys\" title=\"Dallas Cowboys\">Dallas</a> to start in the 1960 NFL season and <a href=\"https://wikipedia.org/wiki/Minnesota_Vikings\" title=\"Minnesota Vikings\">Minneapolis-St. Paul</a> for the 1961 NFL season.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Football_League\" title=\"National Football League\">National Football League</a> announces expansion teams for <a href=\"https://wikipedia.org/wiki/Dallas_Cowboys\" title=\"Dallas Cowboys\">Dallas</a> to start in the 1960 NFL season and <a href=\"https://wikipedia.org/wiki/Minnesota_Vikings\" title=\"Minnesota Vikings\">Minneapolis-St. Paul</a> for the 1961 NFL season.", "links": [{"title": "National Football League", "link": "https://wikipedia.org/wiki/National_Football_League"}, {"title": "Dallas Cowboys", "link": "https://wikipedia.org/wiki/Dallas_Cowboys"}, {"title": "Minnesota Vikings", "link": "https://wikipedia.org/wiki/Minnesota_Vikings"}]}, {"year": "1964", "text": "An unarmed United States Air Force T-39 Sabreliner on a training mission is shot down over Erfurt, East Germany, by a Soviet MiG-19.", "html": "1964 - An unarmed <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/T-39_Sabreliner\" class=\"mw-redirect\" title=\"T-39 Sabreliner\">T-39 Sabreliner</a> on a training mission is <a href=\"https://wikipedia.org/wiki/1964_T-39_shootdown_incident\" title=\"1964 T-39 shootdown incident\">shot down</a> over <a href=\"https://wikipedia.org/wiki/Erfurt\" title=\"Erfurt\">Erfurt</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, by a <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet</a> <a href=\"https://wikipedia.org/wiki/MiG-19\" class=\"mw-redirect\" title=\"MiG-19\">MiG-19</a>.", "no_year_html": "An unarmed <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/T-39_Sabreliner\" class=\"mw-redirect\" title=\"T-39 Sabreliner\">T-39 Sabreliner</a> on a training mission is <a href=\"https://wikipedia.org/wiki/1964_T-39_shootdown_incident\" title=\"1964 T-39 shootdown incident\">shot down</a> over <a href=\"https://wikipedia.org/wiki/Erfurt\" title=\"Erfurt\">Erfurt</a>, <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, by a <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet</a> <a href=\"https://wikipedia.org/wiki/MiG-19\" class=\"mw-redirect\" title=\"MiG-19\">MiG-19</a>.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "T-39 Sabreliner", "link": "https://wikipedia.org/wiki/T-39_Sabreliner"}, {"title": "1964 T-39 shootdown incident", "link": "https://wikipedia.org/wiki/1964_T-39_shootdown_incident"}, {"title": "Erfurt", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Soviet Air Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Forces"}, {"title": "MiG-19", "link": "https://wikipedia.org/wiki/MiG-19"}]}, {"year": "1965", "text": "The current design of the Flag of Canada is chosen by an act of Parliament.", "html": "1965 - The current design of the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">Flag of Canada</a> is chosen by an act of <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament</a>.", "no_year_html": "The current design of the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">Flag of Canada</a> is chosen by an act of <a href=\"https://wikipedia.org/wiki/Parliament_of_Canada\" title=\"Parliament of Canada\">Parliament</a>.", "links": [{"title": "Flag of Canada", "link": "https://wikipedia.org/wiki/Flag_of_Canada"}, {"title": "Parliament of Canada", "link": "https://wikipedia.org/wiki/Parliament_of_Canada"}]}, {"year": "1977", "text": "The first day of the Great Lakes Blizzard of 1977, which dumps 3 metres (10 ft) of snow in one day in Upstate New York. Buffalo, Syracuse, Watertown, and surrounding areas are most affected.", "html": "1977 - The first day of the <a href=\"https://wikipedia.org/wiki/Great_Lakes_Blizzard_of_1977\" class=\"mw-redirect\" title=\"Great Lakes Blizzard of 1977\">Great Lakes Blizzard of 1977</a>, which dumps 3 metres (10 ft) of snow in one day in <a href=\"https://wikipedia.org/wiki/Upstate_New_York\" title=\"Upstate New York\">Upstate New York</a>. <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo</a>, <a href=\"https://wikipedia.org/wiki/Syracuse,_New_York\" title=\"Syracuse, New York\">Syracuse</a>, <a href=\"https://wikipedia.org/wiki/Watertown,_New_York\" title=\"Watertown, New York\">Watertown</a>, and surrounding areas are most affected.", "no_year_html": "The first day of the <a href=\"https://wikipedia.org/wiki/Great_Lakes_Blizzard_of_1977\" class=\"mw-redirect\" title=\"Great Lakes Blizzard of 1977\">Great Lakes Blizzard of 1977</a>, which dumps 3 metres (10 ft) of snow in one day in <a href=\"https://wikipedia.org/wiki/Upstate_New_York\" title=\"Upstate New York\">Upstate New York</a>. <a href=\"https://wikipedia.org/wiki/Buffalo,_New_York\" title=\"Buffalo, New York\">Buffalo</a>, <a href=\"https://wikipedia.org/wiki/Syracuse,_New_York\" title=\"Syracuse, New York\">Syracuse</a>, <a href=\"https://wikipedia.org/wiki/Watertown,_New_York\" title=\"Watertown, New York\">Watertown</a>, and surrounding areas are most affected.", "links": [{"title": "Great Lakes Blizzard of 1977", "link": "https://wikipedia.org/wiki/Great_Lakes_Blizzard_of_1977"}, {"title": "Upstate New York", "link": "https://wikipedia.org/wiki/Upstate_New_York"}, {"title": "Buffalo, New York", "link": "https://wikipedia.org/wiki/Buffalo,_New_York"}, {"title": "Syracuse, New York", "link": "https://wikipedia.org/wiki/Syracuse,_New_York"}, {"title": "Watertown, New York", "link": "https://wikipedia.org/wiki/Watertown,_New_York"}]}, {"year": "1980", "text": "USCGC Blackthorn collides with the tanker Capricorn while leaving Tampa, Florida and capsizes, killing 23 Coast Guard crewmembers.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/USCGC_Blackthorn\" title=\"USCGC Blackthorn\">USCGC <i>Blackthorn</i></a> collides with the tanker <i>Capricorn</i> while leaving <a href=\"https://wikipedia.org/wiki/Tampa,_Florida\" title=\"Tampa, Florida\">Tampa, Florida</a> and capsizes, killing 23 Coast Guard crewmembers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USCGC_Blackthorn\" title=\"USCGC Blackthorn\">USCGC <i>Blackthorn</i></a> collides with the tanker <i>Capricorn</i> while leaving <a href=\"https://wikipedia.org/wiki/Tampa,_Florida\" title=\"Tampa, Florida\">Tampa, Florida</a> and capsizes, killing 23 Coast Guard crewmembers.", "links": [{"title": "USCGC Blackthorn", "link": "https://wikipedia.org/wiki/USCGC_Blackthorn"}, {"title": "Tampa, Florida", "link": "https://wikipedia.org/wiki/Tampa,_Florida"}]}, {"year": "1981", "text": "<PERSON> lifts remaining domestic petroleum price and allocation controls in the United States, helping to end the 1979 energy crisis and begin the 1980s oil glut.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts remaining domestic petroleum price and allocation controls in the United States, helping to end the <a href=\"https://wikipedia.org/wiki/1979_energy_crisis\" class=\"mw-redirect\" title=\"1979 energy crisis\">1979 energy crisis</a> and begin the <a href=\"https://wikipedia.org/wiki/1980s_oil_glut\" title=\"1980s oil glut\">1980s oil glut</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> lifts remaining domestic petroleum price and allocation controls in the United States, helping to end the <a href=\"https://wikipedia.org/wiki/1979_energy_crisis\" class=\"mw-redirect\" title=\"1979 energy crisis\">1979 energy crisis</a> and begin the <a href=\"https://wikipedia.org/wiki/1980s_oil_glut\" title=\"1980s oil glut\">1980s oil glut</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1979 energy crisis", "link": "https://wikipedia.org/wiki/1979_energy_crisis"}, {"title": "1980s oil glut", "link": "https://wikipedia.org/wiki/1980s_oil_glut"}]}, {"year": "1982", "text": "US Army General <PERSON> is rescued by Italian anti-terrorism forces from captivity by the Red Brigades.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">US Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is rescued by Italian anti-<a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorism</a> forces from captivity by the <a href=\"https://wikipedia.org/wiki/Red_Brigades\" title=\"Red Brigades\">Red Brigades</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">US Army</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is rescued by Italian anti-<a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorism</a> forces from captivity by the <a href=\"https://wikipedia.org/wiki/Red_Brigades\" title=\"Red Brigades\">Red Brigades</a>.", "links": [{"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "Red Brigades", "link": "https://wikipedia.org/wiki/Red_Brigades"}]}, {"year": "1984", "text": "Tropical Storm <PERSON><PERSON><PERSON> makes landfall in southern Mozambique, eventually causing 214 deaths and some of the most severe flooding so far recorded in the region.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Tropical_Storm_Domoina\" title=\"Tropical Storm Domoina\">Tropical Storm Domoina</a> makes landfall in southern <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, eventually causing 214 deaths and some of the most severe flooding so far recorded in the region.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tropical_Storm_Domoina\" title=\"Tropical Storm Domoina\">Tropical Storm Domoina</a> makes landfall in southern <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, eventually causing 214 deaths and some of the most severe flooding so far recorded in the region.", "links": [{"title": "Tropical Storm Domoina", "link": "https://wikipedia.org/wiki/Tropical_Storm_Domoina"}, {"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}]}, {"year": "1985", "text": "Supergroup USA for Africa (United Support of Artists for Africa) records the hit single We Are the World, to help raise funds for Ethiopian famine relief.", "html": "1985 - Supergroup <a href=\"https://wikipedia.org/wiki/USA_for_Africa\" class=\"mw-redirect\" title=\"USA for Africa\">USA for Africa</a> (United Support of Artists for Africa) records the hit single <i><a href=\"https://wikipedia.org/wiki/We_Are_the_World\" title=\"We Are the World\">We Are the World</a></i>, to help raise funds for <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> <a href=\"https://wikipedia.org/wiki/Famine_relief\" title=\"Famine relief\">famine relief</a>.", "no_year_html": "Supergroup <a href=\"https://wikipedia.org/wiki/USA_for_Africa\" class=\"mw-redirect\" title=\"USA for Africa\">USA for Africa</a> (United Support of Artists for Africa) records the hit single <i><a href=\"https://wikipedia.org/wiki/We_Are_the_World\" title=\"We Are the World\">We Are the World</a></i>, to help raise funds for <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopian</a> <a href=\"https://wikipedia.org/wiki/Famine_relief\" title=\"Famine relief\">famine relief</a>.", "links": [{"title": "USA for Africa", "link": "https://wikipedia.org/wiki/USA_for_Africa"}, {"title": "We Are the World", "link": "https://wikipedia.org/wiki/We_Are_the_World"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Famine relief", "link": "https://wikipedia.org/wiki/Famine_relief"}]}, {"year": "1986", "text": "Space Shuttle program: STS-51-L mission: Space Shuttle Challenger disintegrates after liftoff, killing all seven astronauts on board.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-51-L\" title=\"STS-51-L\">STS-51-L</a> mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle <i>Challenger</i></a> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger_disaster\" title=\"Space Shuttle Challenger disaster\">disintegrates after liftoff</a>, killing all seven astronauts on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-51-L\" title=\"STS-51-L\">STS-51-L</a> mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle <i>Challenger</i></a> <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger_disaster\" title=\"Space Shuttle Challenger disaster\">disintegrates after liftoff</a>, killing all seven astronauts on board.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-51-L", "link": "https://wikipedia.org/wiki/STS-51-L"}, {"title": "Space Shuttle Challenger", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger"}, {"title": "Space Shuttle Challenger disaster", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger_disaster"}]}, {"year": "1988", "text": "In <PERSON> v Morgentaler the Supreme Court of Canada strikes down all anti-abortion laws.", "html": "1988 - In <i><a href=\"https://wikipedia.org/wiki/R_v_Morgentaler\" title=\"<PERSON> v Morgentaler\">R v Morgentaler</a></i> the Supreme Court of Canada strikes down all anti-<a href=\"https://wikipedia.org/wiki/Abortion_law\" title=\"Abortion law\">abortion laws</a>.", "no_year_html": "In <i><a href=\"https://wikipedia.org/wiki/R_v_Morgentaler\" title=\"R v Morgentaler\">R v Morgentaler</a></i> the Supreme Court of Canada strikes down all anti-<a href=\"https://wikipedia.org/wiki/Abortion_law\" title=\"Abortion law\">abortion laws</a>.", "links": [{"title": "R v Morgentaler", "link": "https://wikipedia.org/wiki/R_v_Morgentaler"}, {"title": "Abortion law", "link": "https://wikipedia.org/wiki/Abortion_law"}]}, {"year": "2002", "text": "TAME Flight 120, a Boeing 727-100, crashes in the Andes mountains in southern Colombia, killing 94.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/TAME_Flight_120\" title=\"TAME Flight 120\">TAME Flight 120</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>-100, crashes in the <a href=\"https://wikipedia.org/wiki/Andes\" title=\"Andes\">Andes</a> mountains in southern <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 94.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAME_Flight_120\" title=\"TAME Flight 120\">TAME Flight 120</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>-100, crashes in the <a href=\"https://wikipedia.org/wiki/Andes\" title=\"Andes\">Andes</a> mountains in southern <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 94.", "links": [{"title": "TAME Flight 120", "link": "https://wikipedia.org/wiki/TAME_Flight_120"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Andes", "link": "https://wikipedia.org/wiki/Andes"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "2006", "text": "The roof of one of the buildings at the Katowice International Fair in Poland collapses due to the weight of snow, killing 65 and injuring more than 170 others.", "html": "2006 - The roof of one of the buildings at the <a href=\"https://wikipedia.org/wiki/Katowice_International_Fair\" title=\"Katowice International Fair\">Katowice International Fair</a> in Poland <a href=\"https://wikipedia.org/wiki/Katowice_Trade_Hall_roof_collapse\" title=\"Katowice Trade Hall roof collapse\">collapses</a> due to the weight of snow, killing 65 and injuring more than 170 others.", "no_year_html": "The roof of one of the buildings at the <a href=\"https://wikipedia.org/wiki/Katowice_International_Fair\" title=\"Katowice International Fair\">Katowice International Fair</a> in Poland <a href=\"https://wikipedia.org/wiki/Katowice_Trade_Hall_roof_collapse\" title=\"Katowice Trade Hall roof collapse\">collapses</a> due to the weight of snow, killing 65 and injuring more than 170 others.", "links": [{"title": "Katowice International Fair", "link": "https://wikipedia.org/wiki/Katowice_International_Fair"}, {"title": "Katowice Trade Hall roof collapse", "link": "https://wikipedia.org/wiki/Katowice_Trade_Hall_roof_collapse"}]}, {"year": "2021", "text": "A nitrogen leak at a poultry food processing facility in Gainesville, Georgia kills six and injures at least ten.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/2021_Georgia_poultry_plant_accident\" title=\"2021 Georgia poultry plant accident\">nitrogen leak</a> at a poultry food processing facility in <a href=\"https://wikipedia.org/wiki/Gainesville,_Georgia\" title=\"Gainesville, Georgia\">Gainesville, Georgia</a> kills six and injures at least ten.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2021_Georgia_poultry_plant_accident\" title=\"2021 Georgia poultry plant accident\">nitrogen leak</a> at a poultry food processing facility in <a href=\"https://wikipedia.org/wiki/Gainesville,_Georgia\" title=\"Gainesville, Georgia\">Gainesville, Georgia</a> kills six and injures at least ten.", "links": [{"title": "2021 Georgia poultry plant accident", "link": "https://wikipedia.org/wiki/2021_Georgia_poultry_plant_accident"}, {"title": "Gainesville, Georgia", "link": "https://wikipedia.org/wiki/Gainesville,_Georgia"}]}, {"year": "2023", "text": "Protests begin after police beat and kill <PERSON><PERSON>.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_protests\" title=\"<PERSON><PERSON> protests\">Protests</a> begin after <a href=\"https://wikipedia.org/wiki/Memphis_Police_Department\" title=\"Memphis Police Department\">police</a> beat and <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">kill <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_protests\" title=\"<PERSON><PERSON> protests\">Protests</a> begin after <a href=\"https://wikipedia.org/wiki/Memphis_Police_Department\" title=\"Memphis Police Department\">police</a> beat and <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>\" title=\"Killing of <PERSON><PERSON>\">kill <PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> protests", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_protests"}, {"title": "Memphis Police Department", "link": "https://wikipedia.org/wiki/Memphis_Police_Department"}, {"title": "Killing of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON>_<PERSON>"}]}], "Births": [{"year": "598", "text": "<PERSON><PERSON>, emperor of the Tang dynasty (d. 649)", "html": "598 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (d. 649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\"><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a> (d. 649)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "1312", "text": "<PERSON>, queen of Navarre (d. 1349)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> II of Navarre\"><PERSON></a>, queen of Navarre (d. 1349)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Navarre\" title=\"<PERSON> of Navarre\"><PERSON></a>, queen of Navarre (d. 1349)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre"}]}, {"year": "1368", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Hanthawaddy (d. 1421)", "html": "1368 - <a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Hanthawaddy (d. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Hanthawaddy (d. 1421)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Razadarit"}]}, {"year": "1457", "text": "<PERSON>, king of England (d. 1509)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"<PERSON> VII of England\"><PERSON></a>, king of England (d. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VII of England\"><PERSON></a>, king of England (d. 1509)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1533", "text": "<PERSON>, German scientist (d. 1593)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist (d. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist (d. 1593)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON><PERSON><PERSON><PERSON>, German-Dutch mathematician and academic (d. 1610)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Dutch mathematician and academic (d. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German-Dutch mathematician and academic (d. 1610)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1582", "text": "<PERSON>, French-Scottish poet and author (d. 1621)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, French-Scottish poet and author (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, French-Scottish poet and author (d. 1621)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1600", "text": "<PERSON>, pope of the Catholic Church (d. 1669)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Clement IX\"><PERSON></a>, pope of the Catholic Church (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Clement IX\"><PERSON></a>, pope of the Catholic Church (d. 1669)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, Italian physiologist and physicist (d. 1679)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physiologist and physicist (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physiologist and physicist (d. 1679)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON>, Polish astronomer and politician (d. 1687)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and politician (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and politician (d. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1622", "text": "<PERSON><PERSON>, French astronomer and instrument maker (d. 1691)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French astronomer and instrument maker (d. 1691)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French astronomer and instrument maker (d. 1691)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, Austrian composer (d. 1766)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer (d. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON>, French mathematician and geographer (d. 1774)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and geographer (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and geographer (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1706", "text": "<PERSON>, English printer and typographer (d. 1775)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English printer and typographer (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English printer and typographer (d. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, Japanese shōgun (d. 1761)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/Tokugawa_I<PERSON>\" title=\"Tokugawa Ieshige\"><PERSON></a>, Japanese shōgun (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_<PERSON>\" title=\"Tokugawa Ieshige\">Tokugawa <PERSON></a>, Japanese shō<PERSON> (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1717", "text": "<PERSON>, Ottoman sultan (d. 1774)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mustafa III\"><PERSON></a>, Ottoman sultan (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III\" title=\"Mustafa III\"><PERSON></a>, Ottoman sultan (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_III"}]}, {"year": "1719", "text": "<PERSON>, German poet and critic (d. 1749)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON>, German poet and playwright (d. 1802)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%9Fe\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%9Fe\" title=\"<PERSON>\"><PERSON></a>, German poet and playwright (d. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%9Fe"}]}, {"year": "1755", "text": "<PERSON>, Polish-German physician, anthropologist, and paleontologist (d. 1830)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician, anthropologist, and paleontologist (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician, anthropologist, and paleontologist (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6mmerring"}]}, {"year": "1784", "text": "<PERSON>, 4th Earl of Aberdeen, Scottish politician, Prime Minister of the United Kingdom (d. 1860)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Aberdeen\" title=\"<PERSON>, 4th Earl of Aberdeen\"><PERSON>, 4th Earl of Aberdeen</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Earl_of_Aberdeen\" title=\"<PERSON>, 4th Earl of Aberdeen\"><PERSON>, 4th Earl of Aberdeen</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1860)", "links": [{"title": "<PERSON>, 4th Earl of Aberdeen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Aberdeen"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1797", "text": "<PERSON>, English lawyer and politician (d. 1867)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American lawyer and politician, 28th United States Secretary of the Treasury (d. 1905)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 28th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1822", "text": "<PERSON>, Scottish-Canadian politician, 2nd Prime Minister of Canada (d. 1892)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Scottish-Canadian politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (d. 1892)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1833", "text": "<PERSON>, English general and politician (d. 1885)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general and politician (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, Cuban journalist, poet, and theorist (d. 1895)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist, poet, and theorist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD\" title=\"<PERSON>\"><PERSON></a>, Cuban journalist, poet, and theorist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mart%C3%AD"}]}, {"year": "1853", "text": "<PERSON>, Russian philosopher, poet, and critic (d. 1900)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Russian philosopher, poet, and critic (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, Russian philosopher, poet, and critic (d. 1900)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_(philosopher)"}]}, {"year": "1855", "text": "<PERSON>, American businessman, founded the Burroughs Corporation (d. 1898)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Burroughs_Corporation\" title=\"Burroughs Corporation\">Burroughs Corporation</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Burroughs_Corporation\" title=\"Burroughs Corporation\">Burroughs Corporation</a> (d. 1898)", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Burroughs Corporation", "link": "https://wikipedia.org/wiki/Burroughs_Corporation"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, Welsh-Australian geologist and explorer (d. 1934)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Tannatt_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tannatt <PERSON>\">Tan<PERSON><PERSON></a>, Welsh-Australian geologist and explorer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>nat<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tannatt <PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh-Australian geologist and explorer (d. 1934)", "links": [{"title": "Tannatt <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, Filipino composer and educator (d. 1944)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Juli%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino composer and educator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juli%C3%A1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino composer and educator (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juli%C3%A1n_Felipe"}]}, {"year": "1863", "text": "<PERSON>, Australian-American painter (d. 1918)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American painter (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American painter (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, American businessman, founded Nash Motors (d. 1948)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Nash_Motors\" title=\"Nash Motors\">Nash Motors</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Nash_Motors\" title=\"Nash Motors\">Nash Motors</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nash Motors", "link": "https://wikipedia.org/wiki/Nash_Motors"}]}, {"year": "1865", "text": "<PERSON><PERSON>, Indian author and politician (d. 1928)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and politician (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and politician (d. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Rai"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Finnish lawyer, judge, and politician, 1st President of Finland (d. 1952)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A5hlberg\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish lawyer, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Finland\" title=\"President of Finland\">President of Finland</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Juho_St%C3%A5hlberg"}, {"title": "President of Finland", "link": "https://wikipedia.org/wiki/President_of_Finland"}]}, {"year": "1873", "text": "<PERSON><PERSON>, French novelist and journalist (d. 1954)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and journalist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist and journalist (d. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Colette"}]}, {"year": "1873", "text": "<PERSON>, Australian cricketer (d. 1940)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Scottish golfer (d. 1930)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1930)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Mexican violinist, composer, and conductor (d. 1965)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Juli%C3%A1n_Carrillo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican violinist, composer, and conductor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juli%C3%A1n_Carrillo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican violinist, composer, and conductor (d. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juli%C3%A1n_Carrillo"}]}, {"year": "1878", "text": "<PERSON>, German composer and conductor (d. 1940)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, English cricketer and coach (d. 1970)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Swiss physicist and explorer (d. 1962)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and explorer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss physicist and explorer (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON>, Armenian poet and activist (d. 1920)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian poet and activist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian poet and activist (d. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Romanian-French author and poet (d. 1973)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-French author and poet (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-French author and poet (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese engineer and academic (d. 1976)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Hidet<PERSON><PERSON>_<PERSON>\" title=\"Hidetsugu Yagi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>det<PERSON><PERSON>_<PERSON>\" title=\"Hidetsugu Yagi\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese engineer and academic (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>det<PERSON><PERSON>_Yagi"}]}, {"year": "1887", "text": "<PERSON>, Polish-American pianist and educator (d. 1982)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and educator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and educator (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Russian author and playwright (d. 1986)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and playwright (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American painter (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish author and educator (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kami%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish author and educator (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kami%C5%84ski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish author and educator (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Kami%C5%84ski"}]}, {"year": "1903", "text": "<PERSON>, Irish crystallographer and 1st female FRS (d. 1971)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish crystallographer and 1st female <a href=\"https://wikipedia.org/wiki/Royal_Society\" title=\"Royal Society\">FRS</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish crystallographer and 1st female <a href=\"https://wikipedia.org/wiki/Royal_Society\" title=\"Royal Society\">FRS</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Royal Society", "link": "https://wikipedia.org/wiki/Royal_Society"}]}, {"year": "1906", "text": "<PERSON>, Irish athlete (d. 1991)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Pat_<PERSON>%27C<PERSON>agh<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish athlete (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat_<PERSON>%27<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish athlete (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Greek general and politician (d. 1992)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mark<PERSON>_Vafiadis"}]}, {"year": "1908", "text": "<PERSON>, Turkish-French composer and historian (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French composer and historian (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish-French composer and historian (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Scottish footballer (d. 1931)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Scottish footballer (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)\" title=\"<PERSON> (footballer, born 1909)\"><PERSON></a>, Scottish footballer (d. 1931)", "links": [{"title": "<PERSON> (footballer, born 1909)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1909)"}]}, {"year": "1910", "text": "<PERSON>, Austrian actor (d. 1973)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Dutch politician, academic and author, Ya<PERSON> recipient (d. 2018)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, academic and author, <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON>\">Ya<PERSON></a> recipient (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, academic and author, <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"Ya<PERSON>\">Ya<PERSON></a> recipient (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yad_<PERSON><PERSON><PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American painter (d. 1956)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ock\"><PERSON></a>, American painter (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English puppeteer, actor, and screenwriter (d. 1989)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English puppeteer, actor, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English puppeteer, actor, and screenwriter (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, New Zealand-English lawyer and politician (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English lawyer and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> S<PERSON>\"><PERSON></a>, New Zealand-English lawyer and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American colonel and pilot (d. 2002)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and pilot (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel and pilot (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor (d. 2000)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian-American basketball player (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ytautas <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian-American basketball player (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ytaut<PERSON>_Norkus"}]}, {"year": "1922", "text": "<PERSON>, American songwriter and producer, co-founded Anna Records (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Anna_Records\" title=\"Anna Records\">Anna Records</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer, co-founded <a href=\"https://wikipedia.org/wiki/Anna_Records\" title=\"Anna Records\">Anna Records</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Anna Records", "link": "https://wikipedia.org/wiki/Anna_Records"}]}, {"year": "1922", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1924", "text": "<PERSON>, Belgian painter and poet (d. 1976)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and poet (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and poet (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Indian physicist and politician (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian physicist and politician (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Raja <PERSON></a>, Indian physicist and politician (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American race car driver (d. 1960)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Swedish actor, director, producer, and screenwriter (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, producer, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, producer, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English saxophonist (d. 1996)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English saxophonist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Japanese director, producer, and screenwriter (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese director, producer, and screenwriter (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American author and illustrator (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, English singer and clarinet player (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_Bilk\" title=\"Acker Bilk\"><PERSON><PERSON></a>, English singer and clarinet player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_Bilk\" title=\"Acker Bilk\"><PERSON><PERSON>il<PERSON></a>, English singer and clarinet player (d. 2014)", "links": [{"title": "<PERSON><PERSON> Bilk", "link": "https://wikipedia.org/wiki/A<PERSON>_Bilk"}]}, {"year": "1929", "text": "<PERSON>, American chemist", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Russian footballer and manager (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Swedish-American sculptor and illustrator (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American sculptor and illustrator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American sculptor and illustrator (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, German academic and politician, 54th President of the German Bundesrat (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_the_German_Bundesrat\" title=\"President of the German Bundesrat\">President of the German Bundesrat</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, 54th <a href=\"https://wikipedia.org/wiki/President_of_the_German_Bundesrat\" title=\"President of the German Bundesrat\">President of the German Bundesrat</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the German Bundesrat", "link": "https://wikipedia.org/wiki/President_of_the_German_Bundesrat"}]}, {"year": "1930", "text": "<PERSON>, English screenwriter, comedian and soldier", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, comedian and soldier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English screenwriter, comedian and soldier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American director and screenwriter", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jack Hill\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_<PERSON>\" title=\"Jack Hill\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Argentinian race car driver (d. 1990)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian race car driver (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English author and critic (d. 2025)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and critic (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English author and critic (d. 2025)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1935", "text": "<PERSON>, American actor (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor, director, and writer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Albanian novelist, poet, essayist, and playwright (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian novelist, poet, essayist, and playwright (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian novelist, poet, essayist, and playwright (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Czech historian and television host (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON>_%C4%8C%C3%A1slavsk%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech historian and television host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>_%C4%8C%C3%A1slavsk%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech historian and television host (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>_%C4%8C%C3%A1slavsk%C3%BD"}]}, {"year": "1937", "text": "<PERSON>, English actor (d. 2007)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Swedish-English biologist and academic, Nobel Prize laureate", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-English biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Ukrainian weightlifter and coach (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian weightlifter and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian weightlifter and coach (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Mexican businessman and philanthropist, founded Grupo Carso", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Grupo_Carso\" title=\"Grupo Carso\">Grupo Carso</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Grupo_Carso\" title=\"Grupo Carso\">Grupo Carso</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Grupo Carso", "link": "https://wikipedia.org/wiki/Grupo_Carso"}]}, {"year": "1941", "text": "<PERSON>, American actor (d. 1985)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch figure skater (d. 2024)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch figure skater (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"S<PERSON>k<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch figure skater (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sjou<PERSON><PERSON>_<PERSON>stra"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Finnish director and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian ice hockey player and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English guitarist and songwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actress and writer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Spanish businesswoman, co-founded Inditex and Zara (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa_Mera\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Inditex\" title=\"Inditex\">Inditex</a> and <a href=\"https://wikipedia.org/wiki/Zara_(retailer)\" title=\"Zara (retailer)\">Zara</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa_Mera\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Inditex\" title=\"Inditex\">Inditex</a> and <a href=\"https://wikipedia.org/wiki/Zara_(retailer)\" title=\"Zara (retailer)\">Zara</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosal%C3%ADa_Mera"}, {"title": "Inditex", "link": "https://wikipedia.org/wiki/Inditex"}, {"title": "Zara (retailer)", "link": "https://wikipedia.org/wiki/Zara_(retailer)"}]}, {"year": "1944", "text": "<PERSON>, English composer (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Swiss actress and director", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss actress and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American educator and politician, 78th Governor of New Hampshire", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 78th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 78th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Finnish politician (d. 2022)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish politician (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1948", "text": "<PERSON>, Liberian politician, 22nd President of Liberia", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a>, Liberian politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)\" title=\"<PERSON> (Liberian politician)\"><PERSON></a>, Liberian politician, 22nd <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a>", "links": [{"title": "<PERSON> (Liberian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Liberian_politician)"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1949", "text": "<PERSON>, New Zealand union leader and politician, 34th Prime Minister of New Zealand (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand union leader and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)\" title=\"<PERSON> (New Zealand politician)\"><PERSON></a>, New Zealand union leader and politician, 34th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 2020)", "links": [{"title": "<PERSON> (New Zealand politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(New_Zealand_politician)"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1949", "text": "<PERSON>, American basketball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian poet (d. 2017)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American actress, singer and model", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, singer and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON> bin <PERSON>, Bahraini king", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> bin <PERSON></a>, Bahraini king", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> bin <PERSON></a>, Bahraini king", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American colonel, physician, and astronaut", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, physician, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, physician, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Bangladeshi-English economist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi-English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi-English economist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Ukrainian general, pilot, and astronaut (d. 2018)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general, pilot, and astronaut (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian general, pilot, and astronaut (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American R&B/funk bass player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/funk bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B/funk bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American director, producer, and screenwriter (d. 2015)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1953)\" title=\"<PERSON> (ice hockey, born 1953)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1953)\" title=\"<PERSON> (ice hockey, born 1953)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1953)"}]}, {"year": "1954", "text": "<PERSON>, German theologian and historian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, French footballer and manager (d. 2013)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American pastor and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian-American businessman, co-founded Sun Microsystems", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sun_Microsystems\" title=\"Sun Microsystems\">Sun Microsystems</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sun Microsystems", "link": "https://wikipedia.org/wiki/Sun_Microsystems"}]}, {"year": "1955", "text": "<PERSON>, French lawyer and politician, 23rd President of France", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 23rd <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1956", "text": "<PERSON>, Austrian politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American composer and educator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1957", "text": "<PERSON>, Zimbabwean-South African golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-South African golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English comedian, actor, and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American director and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Filipino journalist and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, British musician and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "Norman<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and broadcaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Cage\"><PERSON></a>, American basketball player and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1963", "text": "<PERSON>, American musician and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_<PERSON>z"}]}, {"year": "1964", "text": "<PERSON>, English cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese director and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%88ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%88ka\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pivo%C5%88ka"}]}, {"year": "1967", "text": "<PERSON>, Australian footballer and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian singer-songwriter, pianist, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "DJ <PERSON><PERSON>, American DJ and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/DJ_Muggs\" title=\"DJ Muggs\">DJ <PERSON></a>, American DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_Muggs\" title=\"DJ Muggs\">DJ <PERSON><PERSON></a>, American DJ and producer", "links": [{"title": "DJ <PERSON>", "link": "https://wikipedia.org/wiki/DJ_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American rapper", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "Rakim", "link": "https://wikipedia.org/wiki/Rakim"}]}, {"year": "1969", "text": "<PERSON>, Italian swimmer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American comedian and television journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>ca\"><PERSON></a>, American comedian and television journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, American comedian and television journalist", "links": [{"title": "Mo <PERSON>", "link": "https://wikipedia.org/wiki/Mo_R<PERSON>ca"}]}, {"year": "1969", "text": "<PERSON>, American lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1<PERSON>ez\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1<PERSON>ez\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Linda_S%C3%A1nchez"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1972", "text": "<PERSON>, Russian basketball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American jurist, academic, attorney, and Associate Justice of the Supreme Court of the United States", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist, academic, attorney, and <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist, academic, attorney, and <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1972", "text": "<PERSON>, English rugby player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Dutch cyclist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and comedian", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dutch author and poet", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Magglio_Ord%C3%B3%C3%B1ez\" title=\"Magg<PERSON> Ordóñez\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magglio_Ord%C3%B3%C3%B1ez\" title=\"Magg<PERSON> Ordóñez\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player and politician", "links": [{"title": "Ma<PERSON><PERSON> Ordóñez", "link": "https://wikipedia.org/wiki/Magglio_Ord%C3%B3%C3%B1ez"}]}, {"year": "1975", "text": "<PERSON>, Portuguese-American journalist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Portuguese-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, Portuguese-American journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Junior_<PERSON>vey\" title=\"Junior Spivey\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"Junior Spivey\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Fijian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1976", "text": "<PERSON>, American rapper and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Miltiadis_Sapanis\" title=\"Miltiadis Sapanis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miltia<PERSON>_<PERSON>is\" title=\"Miltiadis Sapanis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miltiadis_Sapanis"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Latvian basketball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Sandis_Bu%C5%A1kevic<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandis_Bu%C5%<PERSON>ke<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sandis_Bu%C5%A1kevics"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer, dancer, and television personality", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Fatone\"><PERSON></a>, American singer, dancer, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Fatone\"><PERSON></a>, American singer, dancer, and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English footballer and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Senegalese footballer (d. 2020)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Senegalese footballer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Big_Freedia\" title=\"Big Freedia\">Big Freedia</a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Big_Freedia\" title=\"Big Freedia\">Big Freedia</a>, American musician", "links": [{"title": "Big Freedia", "link": "https://wikipedia.org/wiki/Big_Freedia"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Irish wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shea<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cabral\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cabral\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Angelique Cabral", "link": "https://wikipedia.org/wiki/Angelique_Cabral"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Yasuhito_End%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yasuhito_End%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yasuhito_End%C5%8D"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American journalist and author (d. 2013)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and author (d. 2013)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_(journalist)"}]}, {"year": "1981", "text": "<PERSON>, American actor and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ll"}]}, {"year": "1982", "text": "<PERSON>, American-Montenegrin basketball player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Montenegrin basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Montenegrin basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English field hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Latvian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C4%81rzi%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_D%C4%81rzi%C5%86%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lauris_D%C4%81rzi%C5%86%C5%A1"}]}, {"year": "1985", "text": "<PERSON>, English actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Australian swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English heptathlete and hurdler", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heptathlete and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heptathlete and hurdler", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian sailor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, English footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(English_footballer)\" title=\"<PERSON> (English footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (English footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(English_footballer)"}]}, {"year": "1988", "text": "<PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swedish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Argentinian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1994", "text": "<PERSON>, Russian-American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Chinese tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Colombian singer-songwriter, rapper, and actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Maluma\" title=\"Maluma\"><PERSON><PERSON></a>, Colombian singer-songwriter, rapper, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maluma\" title=\"Mal<PERSON>\"><PERSON><PERSON></a>, Colombian singer-songwriter, rapper, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maluma"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, British rhythmic gymnast", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British rhythmic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pritchard\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Pritchard\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_P<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Winter\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Winter"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Vlahovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_Vlahovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1an_Vlahovi%C4%87"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American basketball player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Swedish ice hockey player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96hgren\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96h<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%96hgren"}]}], "Deaths": [{"year": "724", "text": "<PERSON><PERSON><PERSON> <PERSON>, Umayya<PERSON> caliph (b. 687)", "html": "724 - <a href=\"https://wikipedia.org/wiki/Yazid_II\" title=\"Yazid II\">Yazid II</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad caliph</a> (b. 687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yazid_II\" title=\"Yazid II\">Yazid II</a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad caliph</a> (b. 687)", "links": [{"title": "Yazid II", "link": "https://wikipedia.org/wiki/Yazi<PERSON>_II"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}]}, {"year": "814", "text": "<PERSON><PERSON><PERSON><PERSON>, Holy Roman emperor (b. 742)", "html": "814 - <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman</a> emperor (b. 742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman</a> emperor (b. 742)", "links": [{"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}]}, {"year": "919", "text": "<PERSON>, Chinese general", "html": "919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "929", "text": "<PERSON>, founder of Chinese <PERSON> (b. 858)", "html": "929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of Chinese <a href=\"https://wikipedia.org/wiki/Jingnan\" title=\"Jingnan\">Jingnan</a> (b. 858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, founder of Chinese <a href=\"https://wikipedia.org/wiki/Jingnan\" title=\"Jingnan\">Jingnan</a> (b. 858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jingnan", "link": "https://wikipedia.org/wiki/Jingnan"}]}, {"year": "947", "text": "<PERSON>, Chinese general (b. 892)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1061", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia (b. 1031)", "html": "1061 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>hn%C4%9Bv_II,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia</a> (b. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>hn%C4%9Bv_II,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia</a> (b. 1031)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/Spytihn%C4%9Bv_II,_<PERSON>_of_Bohemia"}]}, {"year": "1142", "text": "<PERSON><PERSON>, Chinese general (b. 1103)", "html": "1142 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general (b. 1103)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general (b. 1103)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1256", "text": "<PERSON>, Count of Holland, King of Germany (b. 1227)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Holland\" title=\"<PERSON> of Holland\"><PERSON>, Count of Holland</a>, <a href=\"https://wikipedia.org/wiki/King_of_Germany\" class=\"mw-redirect\" title=\"King of Germany\">King of Germany</a> (b. 1227)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Holland\" title=\"<PERSON> of Holland\"><PERSON>, Count of Holland</a>, <a href=\"https://wikipedia.org/wiki/King_of_Germany\" class=\"mw-redirect\" title=\"King of Germany\">King of Germany</a> (b. 1227)", "links": [{"title": "<PERSON> of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Holland"}, {"title": "King of Germany", "link": "https://wikipedia.org/wiki/King_of_Germany"}]}, {"year": "1271", "text": "<PERSON> Aragon, Queen of France (b. 1247)", "html": "1271 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_France\" title=\"<PERSON> of Aragon, Queen of France\"><PERSON> of Aragon, Queen of France</a> (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_France\" title=\"<PERSON> of Aragon, Queen of France\"><PERSON> Aragon, Queen of France</a> (b. 1247)", "links": [{"title": "<PERSON> of Aragon, Queen of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Aragon,_Queen_of_France"}]}, {"year": "1290", "text": "<PERSON><PERSON><PERSON><PERSON> of Galloway, Scottish noble, mother of king <PERSON> of Scotland (b. c. 1210)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/Dervorguilla_of_Galloway\" title=\"Dervorgu<PERSON> of Galloway\"><PERSON><PERSON><PERSON><PERSON> of Galloway</a>, Scottish noble, mother of king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Scotland (b. c. 1210)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dervorguilla_of_Galloway\" title=\"Dervorgu<PERSON> of Galloway\"><PERSON><PERSON><PERSON><PERSON> of Galloway</a>, Scottish noble, mother of king <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Scotland (b. c. 1210)", "links": [{"title": "Dervorguilla of Galloway", "link": "https://wikipedia.org/wiki/Dervorguilla_of_Galloway"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1443", "text": "<PERSON>, French diplomat (b. 1365)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7on\" title=\"<PERSON>\"><PERSON></a>, French diplomat (b. 1365)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7on\" title=\"<PERSON>\"><PERSON></a>, French diplomat (b. 1365)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7on"}]}, {"year": "1501", "text": "<PERSON>, 1st Baron <PERSON>, English baron and Lord High Treasurer (b. 1433)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English baron and Lord High Treasurer (b. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English baron and Lord High Treasurer (b. 1433)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1547", "text": "<PERSON>, king of England (b. 1491)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON></a>, king of England (b. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON></a>, king of England (b. 1491)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}]}, {"year": "1613", "text": "<PERSON>, English diplomat and scholar, founded the Bodleian Library (b. 1545)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat and scholar, founded the <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> (b. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diplomat and scholar, founded the <a href=\"https://wikipedia.org/wiki/Bodleian_Library\" title=\"Bodleian Library\">Bodleian Library</a> (b. 1545)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bodleian Library", "link": "https://wikipedia.org/wiki/Bodleian_Library"}]}, {"year": "1621", "text": "<PERSON> (b. 1550)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/Pope_Paul_V\" title=\"Pope Paul V\">Pope <PERSON> V</a> (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Paul V\"><PERSON> V</a> (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON><PERSON><PERSON>, Maltese architect and sculptor (b. 1591)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese architect and sculptor (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese architect and sculptor (b. 1591)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, French politician, Lord Chancellor of France (b. 1588)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9guier\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9guier\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor_of_France\" class=\"mw-redirect\" title=\"Lord Chancellor of France\">Lord Chancellor of France</a> (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_S%C3%A9guier"}, {"title": "Lord Chancellor of France", "link": "https://wikipedia.org/wiki/Lord_Chancellor_of_France"}]}, {"year": "1681", "text": "<PERSON>, English priest and academic (b. 1619)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and academic (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, Polish astronomer and politician (b. 1611)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and politician (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and politician (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1688", "text": "<PERSON>, Flemish Jesuit missionary in China (b. 1623)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit missionary in China (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit missionary in China (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1697", "text": "Sir <PERSON>, 3rd Baronet, English general and politician (b. 1645)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English general and politician (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English general and politician (b. 1645)", "links": [{"title": "Sir <PERSON>, 3rd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet"}]}, {"year": "1754", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian-Danish historian and philosopher (b. 1684)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-Danish historian and philosopher (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian-Danish historian and philosopher (b. 1684)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ludvi<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, French geographer and cartographer (b. 1697)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Anville\" class=\"mw-redirect\" title=\"<PERSON>'Anville\"><PERSON></a>, French geographer and cartographer (b. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Anville\" class=\"mw-redirect\" title=\"<PERSON>Anville\"><PERSON></a>, French geographer and cartographer (b. 1697)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_d%27Anville"}]}, {"year": "1832", "text": "<PERSON><PERSON> <PERSON>, French general (b. 1769)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>_<PERSON>\" title=\"Augustin <PERSON>\">Augustin <PERSON></a>, French general (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augustin_<PERSON>_<PERSON>\" title=\"Augustin <PERSON>\">Augustin <PERSON></a>, French general (b. 1769)", "links": [{"title": "Augustin <PERSON>", "link": "https://wikipedia.org/wiki/August<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>, English politician, Prime Minister of the United Kingdom (b. 1782)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>\"><PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1782)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1864", "text": "<PERSON><PERSON>, French physicist and engineer (b. 1799)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Clapeyron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French physicist and engineer (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Clapeyron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French physicist and engineer (b. 1799)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Clapeyron"}]}, {"year": "1873", "text": "<PERSON>, English-Australian politician, 10th Premier of South Australia (b. 1809)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>(South_Australian_colonist)\" title=\"<PERSON> (South Australian colonist)\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(South_Australian_colonist)\" title=\"<PERSON> (South Australian colonist)\"><PERSON></a>, English-Australian politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1809)", "links": [{"title": "<PERSON> (South Australian colonist)", "link": "https://wikipedia.org/wiki/<PERSON>(South_Australian_colonist)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1903", "text": "<PERSON>, French pianist and composer (b. 1847)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Augusta_Holm%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Holm%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Holm%C3%A8s"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Belgian economist and theorist (b. 1819).", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian economist and theorist (b. 1819).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian economist and theorist (b. 1819).", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, former president of Ecuador (b. 1842)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former president of Ecuador (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, former president of Ecuador (b. 1842)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oy_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian soldier, physician, and author (b. 1872)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, physician, and author (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, physician, and author (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Turkish journalist and politician (b. 1883)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Turkish journalist and politician (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Czech soprano and poet (b. 1878)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soprano and poet (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soprano and poet (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1935", "text": "<PERSON>-<PERSON><PERSON>, Russian composer and conductor (b. 1859)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Russian composer and conductor (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Russian composer and conductor (b. 1859)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek architect and target shooter (b. 1862)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Metaxas\" title=\"<PERSON><PERSON><PERSON><PERSON> Metax<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek architect and target shooter (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Metaxas\" title=\"<PERSON><PERSON><PERSON><PERSON> Metax<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek architect and target shooter (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anastasios_Metaxas"}]}, {"year": "1938", "text": "<PERSON><PERSON>, German race car driver (b. 1909)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German race car driver (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish poet and playwright, Nobel Prize laureate (b. 1865)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/W._<PERSON>._Yeats\" title=\"W<PERSON> B<PERSON> Yeats\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON>._Yeats\" title=\"W. B. Yeats\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_Yeats"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1942", "text": "<PERSON>, American gymnast and triathlete (b. 1881)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and triathlete (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Russian sergeant and sniper (b. 1924)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sergeant and sniper (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian sergeant and sniper (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Venezuelan-French composer, conductor, and critic (b. 1875)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan-French composer, conductor, and critic (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan-French composer, conductor, and critic (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, German SS officer (b. 1906)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, French race car driver (b. 1908)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Russian mathematician and academic (b. 1883)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian journalist and politician, 9th Prime Minister of Australia (b. 1876)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Turkish philosopher and poet (b. 1879)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>zen_Tevfik\" title=\"Neyzen Tevfik\"><PERSON><PERSON><PERSON></a>, Turkish philosopher and poet (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>zen_Tevfik\" title=\"Neyzen Tevfik\"><PERSON><PERSON><PERSON></a>, Turkish philosopher and poet (b. 1879)", "links": [{"title": "Neyzen Tevfik", "link": "https://wikipedia.org/wiki/Neyzen_Tevfik"}]}, {"year": "1959", "text": "<PERSON>, American baseball player (b. 1899)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American novelist, short story writer, and folklorist (b. 1891)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and folklorist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist, short story writer, and folklorist (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, French cyclist (b. 1884)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French cyclist (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, English cricketer (b. 1888)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ich <PERSON>\"><PERSON><PERSON></a>, English cricketer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ich <PERSON>\"><PERSON><PERSON></a>, English cricketer (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Belgian-French general (b. 1867)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Maxime Weygand\"><PERSON><PERSON></a>, Belgian-French general (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d\" title=\"Maxime Weygand\"><PERSON><PERSON></a>, Belgian-French general (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maxime_Weygand"}]}, {"year": "1971", "text": "<PERSON>, English paediatrician and psychoanalyst (b. 1896)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paediatrician and psychoanalyst (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English paediatrician and psychoanalyst (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Austrian actor (b. 1910)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belgian painter and poet (b. 1924)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter and poet (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American author (b. 1903)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>. English pop star (b. 1940)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fury\"><PERSON></a>. English pop star (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Fury\"><PERSON></a>. English pop star (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian educator and politician, 15th Prime Minister of Australia (b. 1890)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1986", "text": "Space Shuttle Challenger crew\n<PERSON>, American captain, engineer, and astronaut (b. 1944)\n<PERSON><PERSON>, American educator and astronaut (b. 1948)\n<PERSON>, American physicist and astronaut (b. 1950)\n<PERSON>, American engineer and astronaut (b. 1946)\n<PERSON>, American colonel, engineer, and astronaut (b. 1949)\n<PERSON>, American colonel, pilot, and astronaut (b. 1939)\n<PERSON>, American captain, pilot, and astronaut (b. 1945)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle Challenger</a> crew\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and astronaut (b. 1948)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (b. 1950)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ellison Oniz<PERSON>\"><PERSON></a>, American engineer and astronaut (b. 1946)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Dick_Scobee\" title=\"<PERSON> Scobee\"><PERSON> Scobee</a>, American colonel, pilot, and astronaut (b. 1939)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_J._<PERSON>_(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> J. <PERSON> (astronaut)\"><PERSON> J. <PERSON></a>, American captain, pilot, and astronaut (b. 1945)</li>\n</ul>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Challenger\" title=\"Space Shuttle Challenger\">Space Shuttle Challenger</a> crew\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut (b. 1944)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and astronaut (b. 1948)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (b. 1950)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> On<PERSON>\"><PERSON></a>, American engineer and astronaut (b. 1946)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_Scobee\" title=\"<PERSON> <PERSON><PERSON>e\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1939)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON>_(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> J. Smith (astronaut)\"><PERSON> J. <PERSON></a>, American captain, pilot, and astronaut (b. 1945)</li>\n</ul>", "links": [{"title": "Space Shuttle Challenger", "link": "https://wikipedia.org/wiki/Space_Shuttle_Challenger"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (astronaut)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "<PERSON>, American captain, engineer, and astronaut (b. 1944)", "text": null, "html": "<PERSON>, American captain, engineer, and astronaut (b. 1944) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, engineer, and astronaut (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON><PERSON>, American educator and astronaut (b. 1948)", "text": null, "html": "<PERSON><PERSON>, American educator and astronaut (b. 1948) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and astronaut (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and astronaut (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American physicist and astronaut (b. 1950)", "text": null, "html": "<PERSON>, American physicist and astronaut (b. 1950) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronaut (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American engineer and astronaut (b. 1946)", "text": null, "html": "<PERSON>, American engineer and astronaut (b. 1946) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> On<PERSON>\"><PERSON></a>, American engineer and astronaut (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American colonel, engineer, and astronaut (b. 1949)", "text": null, "html": "<PERSON>, American colonel, engineer, and astronaut (b. 1949) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, engineer, and astronaut (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American colonel, pilot, and astronaut (b. 1939)", "text": null, "html": "<PERSON>, American colonel, pilot, and astronaut (b. 1939) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "<PERSON>, American captain, pilot, and astronaut (b. 1945)", "text": null, "html": "<PERSON>, American captain, pilot, and astronaut (b. 1945) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, pilot, and astronaut (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>(astronaut)\" class=\"mw-redirect\" title=\"<PERSON> (astronaut)\"><PERSON></a>, American captain, pilot, and astronaut (b. 1945)", "links": [{"title": "<PERSON> (astronaut)", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(astronaut)"}]}, {"year": "1988", "text": "<PERSON>, German physicist, politician, and atomic spy (b. 1911)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist, politician, and atomic spy (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist, politician, and atomic spy (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, 10th <PERSON><PERSON> (b. 1938)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_Lama\" title=\"<PERSON><PERSON><PERSON>, 10th Panchen <PERSON>\"><PERSON><PERSON><PERSON>, 10th <PERSON>chen <PERSON></a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 10th Panchen <PERSON>\"><PERSON><PERSON><PERSON>, 10th Panchen <PERSON></a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>, 10th <PERSON>chen <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_10th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Canadian astronomer and academic (b. 1905)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astronomer and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astronomer and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Russian-American poet and essayist, Nobel Prize laureate (b. 1940)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American poet and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American poet and essayist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American cartoonist and author (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hogarth\"><PERSON><PERSON></a>, American cartoonist and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hogarth\"><PERSON><PERSON></a>, American cartoonist and author (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h"}]}, {"year": "1996", "text": "<PERSON>, American author and illustrator, co-created <PERSON> (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Superman\">Superman</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, co-created <a href=\"https://wikipedia.org/wiki/Superman\" title=\"Superman\">Superman</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Japanese author and illustrator (b. 1938)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Shotaro_Ishinomori\" title=\"Shotaro Ishinomori\"><PERSON><PERSON></a>, Japanese author and illustrator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shotaro_Ishinomori\" title=\"Shotaro Ishinomori\"><PERSON><PERSON></a>, Japanese author and illustrator (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shotaro_Ishinomori"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Russian composer (b. 1939)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gavrilin\" title=\"<PERSON><PERSON> Gavrilin\"><PERSON><PERSON></a>, Russian composer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gavrilin\" title=\"<PERSON><PERSON> Gavrilin\"><PERSON><PERSON></a>, Russian composer (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>rilin"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Croatian author and playwright (b. 1913)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Rank<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian author and playwright (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rank<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian author and playwright (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ranko_Marinkovi%C4%87"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist and soldier (b. 1913)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>oor\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and soldier (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist and soldier (b. 1913)", "links": [{"title": "Gustaaf <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Swedish author and screenwriter (b. 1907)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and screenwriter (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and screenwriter (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish author and activist (b. 1946)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Ay%C5%9Fe_Nur_Zarakolu\" title=\"Ayşe Nur Zarakolu\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish author and activist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ay%C5%9Fe_Nur_Zarakolu\" title=\"Ayşe Nur Zarakolu\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish author and activist (b. 1946)", "links": [{"title": "Ayşe Nur Zarakolu", "link": "https://wikipedia.org/wiki/Ay%C5%9Fe_Nur_Zarakolu"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Dutch runner (b. 1957)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch runner (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American captain (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English singer-songwriter and drummer (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and drummer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and drummer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Swiss cyclist (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss cyclist (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American priest, lawyer, and politician (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, lawyer, and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, lawyer, and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Russian runner (b. 1963)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian runner (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Czech composer (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Czech composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)\" title=\"<PERSON><PERSON> (composer)\"><PERSON><PERSON></a>, Czech composer (b. 1938)", "links": [{"title": "<PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "2009", "text": "<PERSON>, German jurist (b. 1908)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German jurist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American keyboard player and songwriter (b. 1952)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and songwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Polish astronomer and astrophysicist (b. 1952)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and astrophysicist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish astronomer and astrophysicist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian adventurer and author (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian adventurer and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian adventurer and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Cuban-American boxer and coach (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rn%C3%<PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON><PERSON><PERSON> (boxer)\"><PERSON><PERSON><PERSON></a>, Cuban-American boxer and coach (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rn%C3%<PERSON><PERSON><PERSON>_(boxer)\" title=\"<PERSON><PERSON><PERSON> (boxer)\"><PERSON><PERSON><PERSON></a>, Cuban-American boxer and coach (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON> (boxer)", "link": "https://wikipedia.org/wiki/Florentino_Fern%C3%A1<PERSON><PERSON>_(boxer)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American educator and politician (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American educator and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American educator and politician (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Czech painter, illustrator, and stage designer (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Old%C5%99ich_Kulh%C3%A1nek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter, illustrator, and stage designer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old%C5%99ich_Kulh%C3%A1nek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech painter, illustrator, and stage designer (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Old%C5%99ich_Kulh%C3%A1nek"}]}, {"year": "2014", "text": "<PERSON>, American composer and conductor (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player, coach, and manager (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American composer and conductor (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Welsh poet, journalist, and geographer (b. 1949)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh poet, journalist, and geographer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh poet, journalist, and geographer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian engineer and politician, Governor of Santa Fe (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian engineer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Santa_Fe\" class=\"mw-redirect\" title=\"Governor of Santa Fe\">Governor of Santa Fe</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian engineer and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Santa_Fe\" class=\"mw-redirect\" title=\"Governor of Santa Fe\">Governor of Santa Fe</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Santa Fe", "link": "https://wikipedia.org/wiki/Governor_of_Santa_Fe"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Nigerian general, architect, and engineer (b. 1954)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian general, architect, and engineer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian general, architect, and engineer (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2015", "text": "<PERSON>, Australian historian, author, and academic (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian, author, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian, author, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, American singer (b. 1941)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1941)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American football player and coach (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American lawyer and politician, 32nd Mayor of Providence (b. 1941)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Providence,_Rhode_Island\" title=\"List of mayors of Providence, Rhode Island\">Mayor of Providence</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Providence,_Rhode_Island\" title=\"List of mayors of Providence, Rhode Island\">Mayor of Providence</a> (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Providence, Rhode Island", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Providence,_Rhode_Island"}]}, {"year": "2016", "text": "<PERSON>, New Zealand lawyer and politician, 6th Deputy Prime Minister of New Zealand (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_New_Zealand\" title=\"Deputy Prime Minister of New Zealand\">Deputy Prime Minister of New Zealand</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_New_Zealand\" title=\"Deputy Prime Minister of New Zealand\">Deputy Prime Minister of New Zealand</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_New_Zealand"}]}, {"year": "2017", "text": "<PERSON>, British journalist (b. 1940)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chancellor\"><PERSON> Chancellor</a>, British journalist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chancellor\"><PERSON></a>, British journalist (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, British musician (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British musician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON><PERSON>, Filipino rock musician (b. 1947)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino rock musician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino rock musician (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1924)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}]}}