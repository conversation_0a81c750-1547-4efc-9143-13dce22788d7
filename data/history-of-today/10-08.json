{"date": "October 8", "url": "https://wikipedia.org/wiki/October_8", "data": {"Events": [{"year": "316", "text": "<PERSON> I defeats Roman Emperor <PERSON><PERSON><PERSON>, who loses his European territories.", "html": "316 - <PERSON> I <a href=\"https://wikipedia.org/wiki/Battle_of_Cibalae\" title=\"Battle of Cibalae\">defeats</a> Roman Emperor <PERSON><PERSON><PERSON>, who loses his European territories.", "no_year_html": "<PERSON> I <a href=\"https://wikipedia.org/wiki/Battle_of_Cibalae\" title=\"Battle of Cibalae\">defeats</a> Roman Emperor <PERSON><PERSON><PERSON>, who loses his European territories.", "links": [{"title": "Battle of Cibalae", "link": "https://wikipedia.org/wiki/Battle_of_Cibalae"}]}, {"year": "451", "text": "The first session of the Council of Chalcedon begins.", "html": "451 - The first session of the <a href=\"https://wikipedia.org/wiki/Council_of_Chalcedon\" title=\"Council of Chalcedon\">Council of Chalcedon</a> begins.", "no_year_html": "The first session of the <a href=\"https://wikipedia.org/wiki/Council_of_Chalcedon\" title=\"Council of Chalcedon\">Council of Chalcedon</a> begins.", "links": [{"title": "Council of Chalcedon", "link": "https://wikipedia.org/wiki/Council_of_Chalcedon"}]}, {"year": "876", "text": "Frankish forces led by <PERSON> the <PERSON> prevent a West Frankish invasion and defeat emperor <PERSON> (\"the Bald\").", "html": "876 - Frankish forces led by <PERSON> the <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Andernach_(876)\" title=\"Battle of Andernach (876)\">prevent</a> a West Frankish invasion and defeat emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bald\" title=\"<PERSON> the Bald\"><PERSON> II</a> (\"the Bald\").", "no_year_html": "Frankish forces led by <PERSON> the <PERSON> <a href=\"https://wikipedia.org/wiki/Battle_of_Andernach_(876)\" title=\"Battle of Andernach (876)\">prevent</a> a West Frankish invasion and defeat emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bald\" title=\"<PERSON> the Bald\"><PERSON> II</a> (\"the Bald\").", "links": [{"title": "Battle of Andernach (876)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>ernach_(876)"}, {"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1075", "text": "<PERSON><PERSON><PERSON> is crowned King of Croatia.", "html": "1075 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Croatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Croatia\"><PERSON><PERSON><PERSON></a> is crowned King of Croatia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Croatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Croatia\"><PERSON><PERSON><PERSON></a> is crowned King of Croatia.", "links": [{"title": "<PERSON><PERSON><PERSON> of Croatia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>imir_of_Croatia"}]}, {"year": "1200", "text": "<PERSON> of Angoulême is crowned Queen consort of England.", "html": "1200 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Angoul%C3%AAme\" title=\"Isabella of Angoulême\"><PERSON> of Angoulême</a> is crowned Queen consort of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Angoul%C3%AAme\" title=\"Isabella of Angoulême\">Isabella of Angoulême</a> is crowned Queen consort of England.", "links": [{"title": "<PERSON> of Angoulême", "link": "https://wikipedia.org/wiki/<PERSON>_of_Angoul%C3%AAme"}]}, {"year": "1322", "text": "<PERSON><PERSON><PERSON> <PERSON> of Bribir is deposed as the Croatian Ban after the Battle of Bliska.", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Mladen_II_%C5%A0ubi%C4%87_of_Bribir\" title=\"<PERSON>laden <PERSON> Šubić of Bribir\"><PERSON><PERSON><PERSON> <PERSON> of Bribir</a> is deposed as the Croatian Ban after the <a href=\"https://wikipedia.org/wiki/Battle_of_Bliska\" title=\"Battle of Bliska\">Battle of Bliska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mladen_II_%C5%A0ubi%C4%87_of_Bribir\" title=\"<PERSON>lade<PERSON> <PERSON>ć of Bribir\"><PERSON><PERSON><PERSON> <PERSON> of Bribir</a> is deposed as the Croatian Ban after the <a href=\"https://wikipedia.org/wiki/Battle_of_Bliska\" title=\"Battle of Bliska\">Battle of Bliska</a>.", "links": [{"title": "Mladen II Šubić of Bribir", "link": "https://wikipedia.org/wiki/Mladen_II_%C5%A0ubi%C4%87_of_Bribir"}, {"title": "Battle of Bliska", "link": "https://wikipedia.org/wiki/Battle_of_Bliska"}]}, {"year": "1480", "text": "The Great Stand on the Ugra River puts an end to Tatar rule over Moscow", "html": "1480 - The <a href=\"https://wikipedia.org/wiki/Great_Stand_on_the_Ugra_River\" title=\"Great Stand on the Ugra River\">Great Stand on the Ugra River</a> puts an end to Tatar rule over Moscow", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Stand_on_the_Ugra_River\" title=\"Great Stand on the Ugra River\">Great Stand on the Ugra River</a> puts an end to Tatar rule over Moscow", "links": [{"title": "Great Stand on the Ugra River", "link": "https://wikipedia.org/wiki/Great_Stand_on_the_Ugra_River"}]}, {"year": "1573", "text": "End of the Spanish siege of Alkmaar, the first Dutch victory in the Eighty Years' War.", "html": "1573 - End of the Spanish <a href=\"https://wikipedia.org/wiki/Siege_of_Alkmaar\" title=\"Siege of Alkmaar\">siege of Alkmaar</a>, the first Dutch victory in the Eighty Years' War.", "no_year_html": "End of the Spanish <a href=\"https://wikipedia.org/wiki/Siege_of_Alkmaar\" title=\"Siege of Alkmaar\">siege of Alkmaar</a>, the first Dutch victory in the Eighty Years' War.", "links": [{"title": "Siege of Alkmaar", "link": "https://wikipedia.org/wiki/Siege_of_Alkmaar"}]}, {"year": "1645", "text": "<PERSON> opens the first lay hospital of North America in Montreal.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens the first lay hospital of North America in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> opens the first lay hospital of North America in <a href=\"https://wikipedia.org/wiki/Montreal\" title=\"Montreal\">Montreal</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montreal", "link": "https://wikipedia.org/wiki/Montreal"}]}, {"year": "1813", "text": "The Treaty of Ried is signed between Bavaria and Austria.", "html": "1813 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Ried\" title=\"Treaty of Ried\">Treaty of Ried</a> is signed between Bavaria and Austria.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Ried\" title=\"Treaty of Ried\">Treaty of Ried</a> is signed between Bavaria and Austria.", "links": [{"title": "Treaty of Ried", "link": "https://wikipedia.org/wiki/Treaty_of_Ried"}]}, {"year": "1821", "text": "The Peruvian Navy is established during the War of Independence.", "html": "1821 - The <a href=\"https://wikipedia.org/wiki/Peruvian_Navy\" title=\"Peruvian Navy\">Peruvian Navy</a> is established during the War of Independence.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peruvian_Navy\" title=\"Peruvian Navy\">Peruvian Navy</a> is established during the War of Independence.", "links": [{"title": "Peruvian Navy", "link": "https://wikipedia.org/wiki/Peruvian_Navy"}]}, {"year": "1829", "text": "<PERSON>'s <PERSON> wins the Rainhill Trials.", "html": "1829 - <PERSON>'s Rocket wins <a href=\"https://wikipedia.org/wiki/Rainhill_Trials\" class=\"mw-redirect\" title=\"Rainhill Trials\">the Rainhill Trials</a>.", "no_year_html": "<PERSON>'s Rocket wins <a href=\"https://wikipedia.org/wiki/Rainhill_Trials\" class=\"mw-redirect\" title=\"Rainhill Trials\">the Rainhill Trials</a>.", "links": [{"title": "Rainhill Trials", "link": "https://wikipedia.org/wiki/Rainhill_Trials"}]}, {"year": "1856", "text": "The Second Opium War between several western powers and China begins with the Arrow Incident.", "html": "1856 - The <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a> between several western powers and China begins with the <i>Arrow</i> Incident.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Opium_War\" title=\"Second Opium War\">Second Opium War</a> between several western powers and China begins with the <i>Arrow</i> Incident.", "links": [{"title": "Second Opium War", "link": "https://wikipedia.org/wiki/Second_Opium_War"}]}, {"year": "1862", "text": "American Civil War: The Confederate invasion of Kentucky is halted at the Battle of Perryville.", "html": "1862 - American Civil War: The Confederate invasion of Kentucky is halted at the <a href=\"https://wikipedia.org/wiki/Battle_of_Perryville\" title=\"Battle of Perryville\">Battle of Perryville</a>.", "no_year_html": "American Civil War: The Confederate invasion of Kentucky is halted at the <a href=\"https://wikipedia.org/wiki/Battle_of_Perryville\" title=\"Battle of Perryville\">Battle of Perryville</a>.", "links": [{"title": "Battle of Perryville", "link": "https://wikipedia.org/wiki/Battle_of_Perryville"}]}, {"year": "1871", "text": "Slash-and-burn land management, months of drought, and the passage of a strong cold front cause the Peshtigo Fire, the Great Chicago Fire and the Great Michigan Fires to break out.", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Slash-and-burn\" title=\"Slash-and-burn\">Slash-and-burn</a> land management, months of drought, and the passage of a strong <a href=\"https://wikipedia.org/wiki/Cold_front\" title=\"Cold front\">cold front</a> cause the <a href=\"https://wikipedia.org/wiki/Peshtigo_Fire\" class=\"mw-redirect\" title=\"Peshtigo Fire\">Peshtigo Fire</a>, the <a href=\"https://wikipedia.org/wiki/Great_Chicago_Fire\" title=\"Great Chicago Fire\">Great Chicago Fire</a> and the <a href=\"https://wikipedia.org/wiki/Great_Michigan_Fire\" title=\"Great Michigan Fire\">Great Michigan Fires</a> to break out.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slash-and-burn\" title=\"Slash-and-burn\">Slash-and-burn</a> land management, months of drought, and the passage of a strong <a href=\"https://wikipedia.org/wiki/Cold_front\" title=\"Cold front\">cold front</a> cause the <a href=\"https://wikipedia.org/wiki/Peshtigo_Fire\" class=\"mw-redirect\" title=\"Peshtigo Fire\">Peshtigo Fire</a>, the <a href=\"https://wikipedia.org/wiki/Great_Chicago_Fire\" title=\"Great Chicago Fire\">Great Chicago Fire</a> and the <a href=\"https://wikipedia.org/wiki/Great_Michigan_Fire\" title=\"Great Michigan Fire\">Great Michigan Fires</a> to break out.", "links": [{"title": "Slash-and-burn", "link": "https://wikipedia.org/wiki/Slash-and-burn"}, {"title": "Cold front", "link": "https://wikipedia.org/wiki/Cold_front"}, {"title": "Peshtigo Fire", "link": "https://wikipedia.org/wiki/Peshtigo_Fire"}, {"title": "Great Chicago Fire", "link": "https://wikipedia.org/wiki/Great_Chicago_Fire"}, {"title": "Great Michigan Fire", "link": "https://wikipedia.org/wiki/Great_Michigan_Fire"}]}, {"year": "1879", "text": "War of the Pacific: The Chilean Navy defeats the Peruvian Navy in the Battle of Angamos.", "html": "1879 - War of the Pacific: The Chilean Navy defeats the Peruvian Navy in the <a href=\"https://wikipedia.org/wiki/Battle_of_Angamos\" title=\"Battle of Angamos\">Battle of Angamos</a>.", "no_year_html": "War of the Pacific: The Chilean Navy defeats the Peruvian Navy in the <a href=\"https://wikipedia.org/wiki/Battle_of_Angamos\" title=\"Battle of Angamos\">Battle of Angamos</a>.", "links": [{"title": "Battle of Angamos", "link": "https://wikipedia.org/wiki/Battle_of_Angamos"}]}, {"year": "1895", "text": "Korean Empress <PERSON><PERSON><PERSON><PERSON><PERSON> is assassinated by Japanese infiltrators.", "html": "1895 - Korean <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Empress <PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> incident\">assassinated</a> by Japanese infiltrators.", "no_year_html": "Korean <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>\" title=\"Empress <PERSON>\">Empress <PERSON></a> is <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> incident\">assassinated</a> by Japanese infiltrators.", "links": [{"title": "Empress <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>g"}, {"title": "<PERSON><PERSON><PERSON> incident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_incident"}]}, {"year": "1912", "text": "The First Balkan War begins when Montenegro declares war against the Ottoman Empire.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a> begins when Montenegro declares war against the Ottoman Empire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a> begins when Montenegro declares war against the Ottoman Empire.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}]}, {"year": "1918", "text": "World War I: Corporal <PERSON> kills 28 German soldiers and captures 132 for which he was awarded the Medal of Honor.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Corporal <a href=\"https://wikipedia.org/wiki/Alvin_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alvin <PERSON>\"><PERSON></a> kills 28 German soldiers and captures 132 for which he was awarded the Medal of Honor.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Corporal <a href=\"https://wikipedia.org/wiki/Alvin_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Alvin <PERSON>\"><PERSON></a> kills 28 German soldiers and captures 132 for which he was awarded the Medal of Honor.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alvin_<PERSON>._York"}]}, {"year": "1921", "text": "KDKA in Pittsburgh's Forbes Field conducts the first live broadcast of a football game.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/KDKA_(AM)\" title=\"KDKA (AM)\">KDKA</a> in Pittsburgh's Forbes Field conducts the first live broadcast of a football game.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/KDKA_(AM)\" title=\"KDKA (AM)\">KDKA</a> in Pittsburgh's Forbes Field conducts the first live broadcast of a football game.", "links": [{"title": "KDKA (AM)", "link": "https://wikipedia.org/wiki/KDKA_(AM)"}]}, {"year": "1939", "text": "World War II: Germany annexes western Poland.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany <a href=\"https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany\" title=\"Polish areas annexed by Nazi Germany\">annexes western Poland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany <a href=\"https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany\" title=\"Polish areas annexed by Nazi Germany\">annexes western Poland</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Polish areas annexed by Nazi Germany", "link": "https://wikipedia.org/wiki/Polish_areas_annexed_by_Nazi_Germany"}]}, {"year": "1941", "text": "World War II: During the preliminaries of the Battle of Rostov, German forces reach the Sea of Azov with the capture of Mariupol.", "html": "1941 - World War II: During the preliminaries of the <a href=\"https://wikipedia.org/wiki/Battle_of_Rostov_(1941)\" title=\"Battle of Rostov (1941)\">Battle of Rostov</a>, German forces reach the Sea of Azov with the capture of Mariupol.", "no_year_html": "World War II: During the preliminaries of the <a href=\"https://wikipedia.org/wiki/Battle_of_Rostov_(1941)\" title=\"Battle of Rostov (1941)\">Battle of Rostov</a>, German forces reach the Sea of Azov with the capture of Mariupol.", "links": [{"title": "Battle of Rostov (1941)", "link": "https://wikipedia.org/wiki/Battle_of_Rostov_(1941)"}]}, {"year": "1943", "text": "World War II: Around 30 civilians are executed by <PERSON>'s paramilitary group in Kallikratis, Crete.", "html": "1943 - World War II: Around 30 civilians are <a href=\"https://wikipedia.org/wiki/Kallikratis_executions\" title=\"Kallikratis executions\">executed</a> by <PERSON>'s paramilitary group in Kallikratis, Crete.", "no_year_html": "World War II: Around 30 civilians are <a href=\"https://wikipedia.org/wiki/Kallikratis_executions\" title=\"Kallikratis executions\">executed</a> by <PERSON>'s paramilitary group in Kallikratis, Crete.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> executions", "link": "https://wikipedia.org/wiki/Kallikratis_executions"}]}, {"year": "1944", "text": "World War II: Captain <PERSON><PERSON> earns a Medal of Honor for his actions during the Battle of Crucifix Hill, just outside Aachen.", "html": "1944 - World War II: Captain <PERSON><PERSON> earns a Medal of Honor for his actions during the <a href=\"https://wikipedia.org/wiki/Battle_of_Crucifix_Hill\" title=\"Battle of Crucifix Hill\">Battle of Crucifix Hill</a>, just outside Aachen.", "no_year_html": "World War II: Captain <PERSON><PERSON> earns a Medal of Honor for his actions during the <a href=\"https://wikipedia.org/wiki/Battle_of_Crucifix_Hill\" title=\"Battle of Crucifix Hill\">Battle of Crucifix Hill</a>, just outside Aachen.", "links": [{"title": "Battle of Crucifix Hill", "link": "https://wikipedia.org/wiki/Battle_of_Crucifix_Hill"}]}, {"year": "1952", "text": "The Harrow and Wealdstone rail crash kills 112 people.", "html": "1952 - The <a href=\"https://wikipedia.org/wiki/Harrow_and_Wealdstone_rail_crash\" title=\"Harrow and Wealdstone rail crash\">Harrow and Wealdstone rail crash</a> kills 112 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Harrow_and_Wealdstone_rail_crash\" title=\"Harrow and Wealdstone rail crash\">Harrow and Wealdstone rail crash</a> kills 112 people.", "links": [{"title": "Harrow and Wealdstone rail crash", "link": "https://wikipedia.org/wiki/Harrow_and_Wealdstone_rail_crash"}]}, {"year": "1956", "text": "The New York Yankees's <PERSON> pitches the only perfect game in a World Series.", "html": "1956 - The New York Yankees's <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pitches the only perfect game in a <a href=\"https://wikipedia.org/wiki/World_Series\" title=\"World Series\">World Series</a>.", "no_year_html": "The New York Yankees's <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> pitches the only perfect game in a <a href=\"https://wikipedia.org/wiki/World_Series\" title=\"World Series\">World Series</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "World Series", "link": "https://wikipedia.org/wiki/World_Series"}]}, {"year": "1962", "text": "<PERSON> Spiegel publishes an article disclosing the sorry state of the Bundeswehr, and is soon accused of treason.", "html": "1962 - <i><PERSON></i> <a href=\"https://wikipedia.org/wiki/Spiegel_scandal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> scandal\">publishes an article</a> disclosing the sorry state of the <a href=\"https://wikipedia.org/wiki/Bundeswehr\" title=\"Bundeswehr\">Bundeswehr</a>, and is soon accused of treason.", "no_year_html": "<i><PERSON></i> <a href=\"https://wikipedia.org/wiki/Spiegel_scandal\" class=\"mw-redirect\" title=\"Spiegel scandal\">publishes an article</a> disclosing the sorry state of the <a href=\"https://wikipedia.org/wiki/Bundeswehr\" title=\"Bundeswehr\">Bundeswehr</a>, and is soon accused of treason.", "links": [{"title": "Spiegel scandal", "link": "https://wikipedia.org/wiki/Spiegel_scandal"}, {"title": "Bundeswehr", "link": "https://wikipedia.org/wiki/Bundeswehr"}]}, {"year": "1967", "text": "Guerrilla leader <PERSON><PERSON> and his men are captured in Bolivia.", "html": "1967 - Guerrilla leader <a href=\"https://wikipedia.org/wiki/Ch<PERSON>_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a> and his men are captured in Bolivia.", "no_year_html": "Guerrilla leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a> and his men are captured in Bolivia.", "links": [{"title": "<PERSON>e Guevara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1969", "text": "The opening rally of the Days of Rage occurs, organized by the Weather Underground in Chicago.", "html": "1969 - The opening rally of the <a href=\"https://wikipedia.org/wiki/Days_of_Rage\" title=\"Days of Rage\">Days of Rage</a> occurs, organized by the Weather Underground in Chicago.", "no_year_html": "The opening rally of the <a href=\"https://wikipedia.org/wiki/Days_of_Rage\" title=\"Days of Rage\">Days of Rage</a> occurs, organized by the Weather Underground in Chicago.", "links": [{"title": "Days of Rage", "link": "https://wikipedia.org/wiki/Days_of_Rage"}]}, {"year": "1970", "text": "<PERSON> wins the Nobel Prize in literature.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the Nobel Prize in literature.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the Nobel Prize in literature.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "Yom Kippur War: Israel loses more than 150 tanks in a failed attack on Egyptian-occupied positions.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>: Israel loses more than 150 tanks in a failed attack on Egyptian-occupied positions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yom_Kippur_War\" title=\"Yom Kippur War\">Yom Kippur War</a>: Israel loses more than 150 tanks in a failed attack on Egyptian-occupied positions.", "links": [{"title": "Yom Kippur War", "link": "https://wikipedia.org/wiki/Yom_Kippur_War"}]}, {"year": "1973", "text": "<PERSON><PERSON> begins his 48-day term as prime minister in an abortive attempt to lead Greece to parliamentary rule.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins his 48-day term as prime minister in an abortive attempt to lead Greece to parliamentary rule.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> begins his 48-day term as prime minister in an abortive attempt to lead Greece to parliamentary rule.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Franklin National Bank collapses due to fraud and mismanagement; at the time it is the largest bank failure in the history of the United States.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Franklin_National_Bank\" title=\"Franklin National Bank\">Franklin National Bank</a> collapses due to fraud and mismanagement; at the time it is the largest bank failure in the history of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franklin_National_Bank\" title=\"Franklin National Bank\">Franklin National Bank</a> collapses due to fraud and mismanagement; at the time it is the largest bank failure in the history of the United States.", "links": [{"title": "Franklin National Bank", "link": "https://wikipedia.org/wiki/Franklin_National_Bank"}]}, {"year": "1978", "text": "Australia's <PERSON> sets the current world water speed record of 275.97 knots at Blowering Dam, Australia.", "html": "1978 - Australia's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the current world water speed record of 275.97 knots at Blowering Dam, Australia.", "no_year_html": "Australia's <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> sets the current world water speed record of 275.97 knots at Blowering Dam, Australia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "Poland bans Solidarity and all other trade unions.", "html": "1982 - Poland bans <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> and all other trade unions.", "no_year_html": "Poland bans <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> and all other trade unions.", "links": [{"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}]}, {"year": "1982", "text": "After its London premiere, Cats opens on Broadway and runs for nearly 18 years before closing on September 10, 2000.", "html": "1982 - After its London premiere, <a href=\"https://wikipedia.org/wiki/Cats_(musical)\" title=\"Cats (musical)\"><i>Cats</i></a> opens on Broadway and runs for nearly 18 years before closing on September 10, 2000.", "no_year_html": "After its London premiere, <a href=\"https://wikipedia.org/wiki/Cats_(musical)\" title=\"Cats (musical)\"><i>Cats</i></a> opens on Broadway and runs for nearly 18 years before closing on September 10, 2000.", "links": [{"title": "<PERSON> (musical)", "link": "https://wikipedia.org/wiki/<PERSON>_(musical)"}]}, {"year": "1990", "text": "First Intifada: Israeli police kill 17 Palestinians and wound over 100 near the Dome of the Rock.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a>: Israeli police kill 17 Palestinians and wound over 100 near the Dome of the Rock.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Intifada\" title=\"First Intifada\">First Intifada</a>: Israeli police kill 17 Palestinians and wound over 100 near the Dome of the Rock.", "links": [{"title": "First Intifada", "link": "https://wikipedia.org/wiki/First_Intifada"}]}, {"year": "1991", "text": "Upon the expiration of the Brioni Agreement, Croatia and Slovenia sever all official relations with Yugoslavia.", "html": "1991 - Upon the expiration of the <a href=\"https://wikipedia.org/wiki/Brioni_Agreement\" title=\"Brioni Agreement\">Brioni Agreement</a>, Croatia and Slovenia sever all official relations with Yugoslavia.", "no_year_html": "Upon the expiration of the <a href=\"https://wikipedia.org/wiki/Brioni_Agreement\" title=\"Brioni Agreement\">Brioni Agreement</a>, Croatia and Slovenia sever all official relations with Yugoslavia.", "links": [{"title": "Brioni Agreement", "link": "https://wikipedia.org/wiki/Brioni_Agreement"}]}, {"year": "2001", "text": "A twin engine Cessna and a Scandinavian Airlines System jetliner collide in heavy fog during takeoff from Milan, Italy, killing 118 people.", "html": "2001 - A twin engine Cessna and a Scandinavian Airlines System jetliner <a href=\"https://wikipedia.org/wiki/2001_Linate_Airport_runway_collision\" title=\"2001 Linate Airport runway collision\">collide in heavy fog during takeoff</a> from Milan, Italy, killing 118 people.", "no_year_html": "A twin engine Cessna and a Scandinavian Airlines System jetliner <a href=\"https://wikipedia.org/wiki/2001_Linate_Airport_runway_collision\" title=\"2001 Linate Airport runway collision\">collide in heavy fog during takeoff</a> from Milan, Italy, killing 118 people.", "links": [{"title": "2001 Linate Airport runway collision", "link": "https://wikipedia.org/wiki/2001_Linate_Airport_runway_collision"}]}, {"year": "2001", "text": "U.S. President <PERSON> announces the establishment of the Office of Homeland Security.", "html": "2001 - U.S. President <PERSON> announces the establishment of the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security\" title=\"United States Department of Homeland Security\">Office of Homeland Security</a>.", "no_year_html": "U.S. President <PERSON> announces the establishment of the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security\" title=\"United States Department of Homeland Security\">Office of Homeland Security</a>.", "links": [{"title": "United States Department of Homeland Security", "link": "https://wikipedia.org/wiki/United_States_Department_of_Homeland_Security"}]}, {"year": "2005", "text": "The 7.6 Mw  Kashmir earthquake leaves 86,000-87,351 people dead, 69,000-75,266 injured, and 2.8 million homeless.", "html": "2005 - The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Kashmir_earthquake\" title=\"2005 Kashmir earthquake\">Kashmir earthquake</a> leaves 86,000-87,351 people dead, 69,000-75,266 injured, and 2.8 million homeless.", "no_year_html": "The 7.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2005_Kashmir_earthquake\" title=\"2005 Kashmir earthquake\">Kashmir earthquake</a> leaves 86,000-87,351 people dead, 69,000-75,266 injured, and 2.8 million homeless.", "links": [{"title": "2005 Kashmir earthquake", "link": "https://wikipedia.org/wiki/2005_Kashmir_earthquake"}]}, {"year": "2014", "text": "<PERSON>, the first person in the United States to be diagnosed with Ebola, dies.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person in the United States to be diagnosed with <a href=\"https://wikipedia.org/wiki/Zaire_ebolavirus\" title=\"Zaire ebolavirus\">Ebola</a>, dies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first person in the United States to be diagnosed with <a href=\"https://wikipedia.org/wiki/Zaire_ebolavirus\" title=\"Zaire ebolavirus\">Ebola</a>, dies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Zaire ebolavirus", "link": "https://wikipedia.org/wiki/Zaire_ebolavirus"}]}, {"year": "2016", "text": "In the wake of Hurricane <PERSON>, the death toll rises to nearly 900.", "html": "2016 - In the wake of <a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a>, the death toll rises to nearly 900.", "no_year_html": "In the wake of <a href=\"https://wikipedia.org/wiki/Hurricane_Matthew\" title=\"Hurricane Matthew\">Hurricane <PERSON></a>, the death toll rises to nearly 900.", "links": [{"title": "Hurricane Matthew", "link": "https://wikipedia.org/wiki/Hurricane_Matthew"}]}, {"year": "2019", "text": "About 200 Extinction Rebellion activists block the gates of Leinster House (parliament) in the Republic of Ireland.", "html": "2019 - About 200 <a href=\"https://wikipedia.org/wiki/Extinction_Rebellion\" title=\"Extinction Rebellion\">Extinction Rebellion</a> activists block the gates of <a href=\"https://wikipedia.org/wiki/Leinster_House\" title=\"Leinster House\">Leinster House</a> (parliament) in the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>.", "no_year_html": "About 200 <a href=\"https://wikipedia.org/wiki/Extinction_Rebellion\" title=\"Extinction Rebellion\">Extinction Rebellion</a> activists block the gates of <a href=\"https://wikipedia.org/wiki/Leinster_House\" title=\"Leinster House\">Leinster House</a> (parliament) in the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>.", "links": [{"title": "Extinction Rebellion", "link": "https://wikipedia.org/wiki/Extinction_Rebellion"}, {"title": "Leinster House", "link": "https://wikipedia.org/wiki/Leinster_House"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}]}, {"year": "2020", "text": "Second Nagorno-Karabakh War: Azerbaijan twice deliberately targeted the Church of the Holy Savior <PERSON><PERSON><PERSON> of Shusha.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> twice deliberately <a href=\"https://wikipedia.org/wiki/2020_Ghazanchetsots_Cathedral_shelling\" title=\"2020 Ghazanchetsots Cathedral shelling\">targeted</a> the <a href=\"https://wikipedia.org/wiki/Ghazanchetsots_Cathedral\" title=\"Ghazanchetsots Cathedral\">Church of the Holy Savior Ghazanchetsots</a> of <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shusha</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> twice deliberately <a href=\"https://wikipedia.org/wiki/2020_Ghazanchetsots_Cathedral_shelling\" title=\"2020 Ghazanchetsots Cathedral shelling\">targeted</a> the <a href=\"https://wikipedia.org/wiki/Ghazanchetsots_Cathedral\" title=\"Ghazanchetsots Cathedral\">Church of the Holy Savior Ghazanchetsots</a> of <a href=\"https://wikipedia.org/wiki/Shusha\" title=\"Shusha\">Shusha</a>.", "links": [{"title": "Second Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "2020 Ghazanchetsots Cathedral shelling", "link": "https://wikipedia.org/wiki/2020_Ghazanchetsots_Cathedral_shelling"}, {"title": "Ghazanchetsots Cathedral", "link": "https://wikipedia.org/wiki/Ghazanchetsots_Cathedral"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}], "Births": [{"year": "319 BC", "text": "<PERSON><PERSON><PERSON><PERSON> of Epirus (d. 272 BC)", "html": "319 BC - 319 BC - <a href=\"https://wikipedia.org/wiki/Pyrrhus_of_Epirus\" title=\"<PERSON><PERSON><PERSON><PERSON> of Epirus\"><PERSON><PERSON><PERSON><PERSON> of Epirus</a> (d. 272 BC)", "no_year_html": "319 BC - <a href=\"https://wikipedia.org/wiki/Pyrr<PERSON>_of_Epirus\" title=\"<PERSON><PERSON><PERSON><PERSON> of Epirus\"><PERSON><PERSON><PERSON><PERSON> of Epirus</a> (d. 272 BC)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Epirus", "link": "https://wikipedia.org/wiki/Pyrrhus_of_Epirus"}]}, {"year": "1150", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, king of Burma (d. 1211)", "html": "1150 - <a href=\"https://wikipedia.org/wiki/Narapatisithu\" title=\"Narap<PERSON><PERSON>th<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (d. 1211)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narapatisithu\" title=\"Narap<PERSON><PERSON>th<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, king of Burma (d. 1211)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narapatisithu"}]}, {"year": "1515", "text": "<PERSON>, daughter of <PERSON> (d. 1578)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, daughter of <PERSON> (d. 1578)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, daughter of <PERSON> (d. 1578)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1551", "text": "<PERSON><PERSON><PERSON>, Italian composer (d. 1618)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1618)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON>, French historian (d. 1617)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French historian (d. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1585", "text": "<PERSON>, German organist and composer (d. 1672)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCtz\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCtz\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%BCtz"}]}, {"year": "1609", "text": "<PERSON>, English physician (d. 1676)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Baptist_minister)\" title=\"<PERSON> (Baptist minister)\"><PERSON></a>, English physician (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Baptist_minister)\" title=\"<PERSON> (Baptist minister)\"><PERSON></a>, English physician (d. 1676)", "links": [{"title": "<PERSON> (Baptist minister)", "link": "https://wikipedia.org/wiki/<PERSON>_(Baptist_minister)"}]}, {"year": "1676", "text": "<PERSON>, Spanish monk and scholar (d. 1764)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro\" title=\"<PERSON>\"><PERSON></a>, Spanish monk and scholar (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro\" title=\"<PERSON>\"><PERSON></a>, Spanish monk and scholar (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3nimo_Feij%C3%B3o_y_Montenegro"}]}, {"year": "1713", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish rabbi and author (d. 1793)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>au\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish rabbi and author (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>au\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish rabbi and author (d. 1793)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Landau"}]}, {"year": "1715", "text": "<PERSON>, French scientist and missionary (d. 1774)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scientist and missionary (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French scientist and missionary (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1747", "text": "<PERSON><PERSON><PERSON>, French lawyer and politician (d. 1807)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Rewbell\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and politician (d. 1807)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON>ois_Rewbell\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and politician (d. 1807)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois_Rewbell"}]}, {"year": "1753", "text": "Princess <PERSON> of Sweden (d. 1829)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\">Princess <PERSON> of Sweden</a> (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\">Princess <PERSON> of Sweden</a> (d. 1829)", "links": [{"title": "<PERSON>, Abbess of Quedlinburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg"}]}, {"year": "1765", "text": "<PERSON><PERSON>, English-Irish lawyer and politician (d. 1831)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Irish lawyer and politician (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Irish lawyer and politician (d. 1831)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1789", "text": "<PERSON>, American lawyer and politician (d. 1874)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, English-New Zealand ornithologist and entomologist (d. 1855)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand ornithologist and entomologist (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-New Zealand ornithologist and entomologist (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, English philosopher and activist (d. 1858)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and activist (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and activist (d. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American judge and politician, 3rd Confederate States Secretary of the Treasury (d. 1905)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Confederate_States_Secretary_of_the_Treasury\" title=\"Confederate States Secretary of the Treasury\">Confederate States Secretary of the Treasury</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 3rd <a href=\"https://wikipedia.org/wiki/Confederate_States_Secretary_of_the_Treasury\" title=\"Confederate States Secretary of the Treasury\">Confederate States Secretary of the Treasury</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Confederate States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/Confederate_States_Secretary_of_the_Treasury"}]}, {"year": "1834", "text": "<PERSON>, American violinist and composer (d. 1905)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, German pianist, composer, and physicist (d. 1924)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist, composer, and physicist (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pianist, composer, and physicist (d. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Australian activist (d. 1925)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian activist (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Belgian composer (d. 1932)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian composer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian composer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, French chemist and academic (d. 1936)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A2telier\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A2telier\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A2telier"}]}, {"year": "1860", "text": "<PERSON>, British painter, printmaker and illustrator (d. 1932)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter, printmaker and illustrator (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter, printmaker and illustrator (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1948)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON><PERSON>, Canadian painter and educator (d. 1955)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and educator (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and educator (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uc"}]}, {"year": "1870", "text": "<PERSON>, French organist and composer (d. 1937)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_V<PERSON>ne"}]}, {"year": "1872", "text": "<PERSON>, American bacteriological chemist and refrigeration engineer (d. 1952)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bacteriological chemist and refrigeration engineer (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bacteriological chemist and refrigeration engineer (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Danish chemist and astronomer (d. 1967)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish chemist and astronomer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish chemist and astronomer (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON>, Russian architect and academic, designed Lenin's Mausoleum (d. 1949)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect and academic, designed <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"<PERSON>'s Mausoleum\"><PERSON>'s Mausoleum</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian architect and academic, designed <a href=\"https://wikipedia.org/wiki/Lenin%27s_Mausoleum\" title=\"Lenin's Mausoleum\"><PERSON>'s Mausoleum</a> (d. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>'s Mausoleum", "link": "https://wikipedia.org/wiki/Lenin%27s_Mausoleum"}]}, {"year": "1875", "text": "<PERSON>, English tennis player and golfer (d. 1919)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and golfer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and golfer (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, 1st Baron <PERSON>, English lieutenant and politician (d. 1966)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lieutenant and politician (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English lieutenant and politician (d. 1966)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, German-Australian painter (d. 1968)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian painter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Australian painter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Canadian-American actor (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gordon\"><PERSON><PERSON></a>, Canadian-American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gordon\"><PERSON><PERSON></a>, Canadian-American actor (d. 1956)", "links": [{"title": "Huntley Gordon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, American singer-songwriter and poet (d. 1957)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and poet (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1977)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist (d. 1977)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1883", "text": "<PERSON>, German physiologist and physician, Nobel Prize laureate (d. 1970)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physiologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1884", "text": "<PERSON><PERSON>, German field marshal (d. 1942)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (d. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American baseball player (d. 1961)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ping Bodie\"><PERSON></a>, American baseball player (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ping Bodie\"><PERSON></a>, American baseball player (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, American baseball player, manager, and team owner (d. 1972)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, manager, and team owner (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player, manager, and team owner (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German psychiatrist and author (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and author (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychiatrist and author (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Canadian engineer (d. 1983)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian engineer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian engineer (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON><PERSON>, American businessman, co-founded Delta Air Lines (d. 1966)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Woolman\" title=\"<PERSON><PERSON> <PERSON>oolman\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Woolman\" title=\"<PERSON><PERSON>ool<PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> (d. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Delta Air Lines", "link": "https://wikipedia.org/wiki/Delta_Air_Lines"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Barbadian cricketer (d. 1964)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Barbadian cricketer (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American soldier and pilot, Medal of Honor recipient (d. 1973)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and pilot, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1890", "text": "<PERSON>, Belgian cyclist (d. 1971)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Belgian cyclist (d. 1971)", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)"}]}, {"year": "1892", "text": "<PERSON>, Russian poet and author (d. 1941)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Tsvetaeva"}]}, {"year": "1893", "text": "<PERSON>, American pianist and composer (d. 1965)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pianist and composer (d. 1965)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1895", "text": "<PERSON><PERSON> <PERSON> of Albania (d. 1961)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Zog_I_of_Albania\" class=\"mw-redirect\" title=\"Zog I of Albania\"><PERSON>og <PERSON> of Albania</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zog_I_of_Albania\" class=\"mw-redirect\" title=\"Zog I of Albania\"><PERSON><PERSON> <PERSON> of Albania</a> (d. 1961)", "links": [{"title": "Zog I of Albania", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I_of_Albania"}]}, {"year": "1895", "text": "<PERSON>, Argentinian general and politician, 29th President of Argentina (d. 1974)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "1896", "text": "<PERSON>, French director, producer, and screenwriter (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Georgian-American director and screenwriter (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-American director and screenwriter (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian-American director and screenwriter (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1897", "text": "<PERSON>, French actor (d. 1953)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Norwegian composer and theorist (d. 1977)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and theorist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian composer and theorist (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eivind_<PERSON>n"}]}, {"year": "1901", "text": "<PERSON>, Australian physicist, humanitarian and politician, Governor of South Australia (d. 2000)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist, humanitarian and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist, humanitarian and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Bulgarian chess player (d. 1937)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian chess player (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian chess player (d. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French race car driver (d. 1973)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>abanto<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American author and illustrator (d. 1975)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver\" title=\"<PERSON> Shaver\"><PERSON></a>, American author and illustrator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver\" title=\"<PERSON> Shaver\"><PERSON></a>, American author and illustrator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek-Cypriot politician (d. 1988)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-Cypriot politician (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-Cypriot politician (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actor (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, French actress (d. 2011)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American soldier and politician (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Gus_Hall\" title=\"Gus Hall\"><PERSON></a>, American soldier and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gus_Hall\" title=\"Gus Hall\"><PERSON></a>, American soldier and politician (d. 2000)", "links": [{"title": "Gus Hall", "link": "https://wikipedia.org/wiki/Gus_Hall"}]}, {"year": "1910", "text": "<PERSON>, German chemist and soldier (d. 2006)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and soldier (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and soldier (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Canadian runner (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)\" class=\"mw-redirect\" title=\"<PERSON> (track and field athlete)\"><PERSON></a>, Canadian runner (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)\" class=\"mw-redirect\" title=\"<PERSON> (track and field athlete)\"><PERSON></a>, Canadian runner (d. 2003)", "links": [{"title": "<PERSON> (track and field athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(track_and_field_athlete)"}]}, {"year": "1913", "text": "<PERSON>, American pilot and engineer (d. 2000)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and engineer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Roman Catholic Archbishop of Athens (d. 1959)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman Catholic Archbishop of Athens (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman Catholic Archbishop of Athens (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American boxer (d. 1993)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American historian and author (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player, coach, and manager (d. 1976)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English biochemist and physiologist, Nobel Prize laureate (d. 1985)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Norwegian radio host and politician (d. 2012)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun\" title=\"<PERSON>dan Hegtun\"><PERSON><PERSON></a>, Norwegian radio host and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun\" title=\"<PERSON><PERSON> Hegtun\"><PERSON><PERSON></a>, Norwegian radio host and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Danish chemist and physiologist, Nobel Prize laureate (d. 2018)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish chemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish chemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1919", "text": "<PERSON>, American race car driver (d. 1955)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1955)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Japanese politician, 78th Prime Minister of Japan (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician, 78th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician, 78th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1920", "text": "<PERSON>, American journalist, photographer, and author (d. 1986)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, photographer, and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, photographer, and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Filipino lawyer and jurist (d. 2010)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and jurist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Swedish footballer, coach, and manager (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer, coach, and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer, coach, and manager (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dholm"}]}, {"year": "1922", "text": "<PERSON>, American production manager and producer (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American production manager and producer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American production manager and producer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Swiss lawyer and politician, 77th President of the Swiss Confederation (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, 77th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>s_<PERSON>gli"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian cardinal (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Alo%C3%ADsio_Lorscheider\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian cardinal (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alo%C3%ADsio_Lorscheider\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian cardinal (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alo%C3%ADsio_Lorscheider"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian poet and scholar (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Thiruna<PERSON><PERSON>_<PERSON>\" title=\"Thiruna<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and scholar (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiruna<PERSON><PERSON>_<PERSON>\" title=\"Thiruna<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and scholar (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thir<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English mathematician and statistician (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and statistician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Salvadoran economist and politician, President of El Salvador (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran economist and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON><PERSON>%C3%B1a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran economist and politician, <a href=\"https://wikipedia.org/wiki/President_of_El_Salvador\" title=\"President of El Salvador\">President of El Salvador</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_Maga%C3%B1a"}, {"title": "President of El Salvador", "link": "https://wikipedia.org/wiki/President_of_El_Salvador"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Indian police officer and actor (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian police officer and actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian police officer and actor (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American missionary and translator (d. 1956)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and translator (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary and translator (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Argentinian-English biochemist and academic, Nobel Prize laureate (d. 2002)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Brazilian footballer and manager (d. 2001)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1928)\" title=\"<PERSON><PERSON> (footballer, born 1928)\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1928)\" title=\"<PERSON><PERSON> (footballer, born 1928)\"><PERSON><PERSON></a>, Brazilian footballer and manager (d. 2001)", "links": [{"title": "<PERSON><PERSON> (footballer, born 1928)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1928)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American lieutenant and religious leader (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American lieutenant and religious leader (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American lieutenant and religious leader (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Australian cricketer", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English actor (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English politician, British Speaker of the House of Commons (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">British Speaker of the House of Commons</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)\" title=\"Speaker of the House of Commons (United Kingdom)\">British Speaker of the House of Commons</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the House of Commons (United Kingdom)", "link": "https://wikipedia.org/wiki/Speaker_of_the_House_of_Commons_(United_Kingdom)"}]}, {"year": "1930", "text": "<PERSON>, American saxophonist and composer (d. 1986)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Adams\" title=\"Pepper Adams\"><PERSON></a>, American saxophonist and composer (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pepper_Adams\" title=\"Pepper Adams\"><PERSON></a>, American saxophonist and composer (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Adams"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-English director and producer (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-English director and producer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-English director and producer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American painter and activist (d. 2024)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and activist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>gold\" title=\"<PERSON>\"><PERSON></a>, American painter and activist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Faith_Ringgold"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Japanese composer and theorist (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Take<PERSON>su\" class=\"mw-redirect\" title=\"Toru Takemitsu\"><PERSON><PERSON></a>, Japanese composer and theorist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Take<PERSON>su\" class=\"mw-redirect\" title=\"Toru Takemitsu\"><PERSON><PERSON></a>, Japanese composer and theorist (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Scottish footballer (d. 2004)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1931)\" title=\"<PERSON> (footballer, born 1931)\"><PERSON></a>, Scottish footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1931)\" title=\"<PERSON> (footballer, born 1931)\"><PERSON></a>, Scottish footballer (d. 2004)", "links": [{"title": "<PERSON> (footballer, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1931)"}]}, {"year": "1932", "text": "<PERSON>, Welsh snooker player and police officer (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and police officer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and police officer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, South African academic and politician (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African academic and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African academic and politician (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English footballer and manager (d. 1983)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and politician, 68th Governor of North Carolina (d. 2013)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1935", "text": "<PERSON>, French-English chef (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English chef (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English chef (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American journalist and businesswoman", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and businesswoman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, British ballerina and educator", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Merle_Park\" title=\"Merle Park\">Merle Park</a>, British ballerina and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merle_Park\" title=\"Merle Park\">Merle Park</a>, British ballerina and educator", "links": [{"title": "Merle Park", "link": "https://wikipedia.org/wiki/Merle_Park"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and politician, 50th Mayor of Seattle (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Seattle\" class=\"mw-redirect\" title=\"List of mayors of Seattle\">Mayor of Seattle</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Seattle\" class=\"mw-redirect\" title=\"List of mayors of Seattle\">Mayor of Seattle</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Seattle", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Seattle"}]}, {"year": "1938", "text": "<PERSON>, English author and playwright (d. 2005)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey coach and author (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey coach and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey coach and author (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Lithuanian businessman and politician, Prime Minister of Lithuania (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Bronislovas Lu<PERSON>s\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Bronislovas Lubys\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">Prime Minister of Lithuania</a> (d. 2011)", "links": [{"title": "Bronislovas Lu<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>lov<PERSON>_Lubys"}, {"title": "Prime Minister of Lithuania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lithuania"}]}, {"year": "1938", "text": "<PERSON>, Australian-American tennis player and sportscaster (d. 2025)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American tennis player and sportscaster (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American tennis player and sportscaster (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian actor, producer, and screenwriter", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian javelin thrower", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Elv%C4%ABra_Ozoli%C5%86a\" title=\"<PERSON>v<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elv%C4%ABra_Ozoli%C5%86a\" title=\"<PERSON>v<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elv%C4%ABra_Ozoli%C5%86a"}]}, {"year": "1939", "text": "<PERSON>, American author and critic (d. 2010)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American lawyer and criminal (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and criminal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and criminal (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cash\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred Cash\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English singer, guitarist, and producer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1941", "text": "<PERSON>, American minister and activist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American author (d. 2007)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author (d. 2007)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1942", "text": "<PERSON>, English actor and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, American comedian, actor, and screenwriter", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Chevy_<PERSON>\" title=\"Chevy <PERSON>\">Chev<PERSON> <PERSON></a>, American comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chevy_<PERSON>\" title=\"Chevy <PERSON>\">Chev<PERSON> <PERSON></a>, American comedian, actor, and screenwriter", "links": [{"title": "Chevy Chase", "link": "https://wikipedia.org/wiki/Chevy_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author, screenwriter, and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ine\" title=\"R. <PERSON><PERSON> St<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"R. L. St<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author, screenwriter, and producer", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ine"}]}, {"year": "1944", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Palestinian scholar, activist, and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian scholar, activist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian scholar, activist, and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, French director and producer (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director and producer (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American journalist and politician, 53rd Mayor of Cleveland", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 53rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cleveland\" class=\"mw-redirect\" title=\"List of mayors of Cleveland\">Mayor of Cleveland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 53rd <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Cleveland\" class=\"mw-redirect\" title=\"List of mayors of Cleveland\">Mayor of Cleveland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Cleveland", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Cleveland"}]}, {"year": "1946", "text": "<PERSON>, English journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, South African motorcycle racer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English archaeologist, historian, and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, English archaeologist, historian, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)\" title=\"<PERSON> (archaeologist)\"><PERSON></a>, English archaeologist, historian, and author", "links": [{"title": "<PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archaeologist)"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Belgian runner", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>ans"}]}, {"year": "1947", "text": "<PERSON>, American photographer and educator", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stephen <PERSON>\"><PERSON></a>, American photographer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stephen Shore\"><PERSON></a>, American photographer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, folk musician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, folk musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, folk musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Indian yoga guru", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Yogendra\"><PERSON><PERSON></a>, Indian yoga guru", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Yogendra\"><PERSON><PERSON></a>, Indian yoga guru", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Yogendra"}]}, {"year": "1948", "text": "<PERSON>, American journalist and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, French actress (d. 2006)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American guitarist and songwriter (d. 2004)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Scottish singer-songwriter, guitarist, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American actress and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American singer-songwriter and bass player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Kool%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" Bell'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Kool%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Kool%22_Bell"}]}, {"year": "1950", "text": "<PERSON>, English poet, author, and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American educator and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(American_politician)\" title=\"<PERSON> (American politician)\"><PERSON></a>, American educator and politician", "links": [{"title": "<PERSON> (American politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connell_(American_politician)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1951", "text": "<PERSON>, American philosopher, historian, and theorist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>on\" title=\"<PERSON>\"><PERSON></a>, American philosopher, historian, and theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>on\" title=\"<PERSON>\"><PERSON></a>, American philosopher, historian, and theorist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shannon_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, 4th Baron <PERSON>, British aristocrat and landowner (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, British aristocrat and landowner (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, British aristocrat and landowner (d. 2023)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Greek basketball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch journalist and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English composer and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Dutch race car driver and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch race car driver and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch race car driver and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, French race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alain_Fert%C3%A9"}]}, {"year": "1955", "text": "<PERSON>, American comedian and actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian politician, 42nd Premier of Tasmania", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American singer and guitarist (d. 1998)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and guitarist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and guitarist (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lonnie_<PERSON>ford"}]}, {"year": "1956", "text": "<PERSON>, American baseball player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1956", "text": "<PERSON>, American engineer and astronaut (d. 2012)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and astronaut (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>balist\" title=\"<PERSON> Zim<PERSON>ist\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Zimbalist\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>imbalist"}]}, {"year": "1957", "text": "<PERSON>, Italian footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American  journalist, author, and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist, author, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American football player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Belgian-German physician and politician, Defense Minister of Germany, President of the European Commission", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-German physician and politician, <a href=\"https://wikipedia.org/wiki/Defense_Minister_of_Germany\" class=\"mw-redirect\" title=\"Defense Minister of Germany\">Defense Minister of Germany</a>, President of the European Commission", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-German physician and politician, <a href=\"https://wikipedia.org/wiki/Defense_Minister_of_Germany\" class=\"mw-redirect\" title=\"Defense Minister of Germany\">Defense Minister of Germany</a>, President of the European Commission", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Defense Minister of Germany", "link": "https://wikipedia.org/wiki/Defense_Minister_of_Germany"}]}, {"year": "1959", "text": "<PERSON>, American golfer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"Tommy Armour III\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_III\" title=\"Tommy Armour III\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Armour_III"}]}, {"year": "1959", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Irish singer-songwriter, actor, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Friday\" title=\"<PERSON> Friday\"><PERSON> Friday</a>, Irish singer-songwriter, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Friday\" title=\"<PERSON> Friday\"><PERSON> Friday</a>, Irish singer-songwriter, actor, and producer", "links": [{"title": "<PERSON> Friday", "link": "https://wikipedia.org/wiki/<PERSON>_Friday"}]}, {"year": "1959", "text": "<PERSON>, Danish motorcycle racer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English journalist and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1959", "text": "<PERSON>, Peruvian-American colonel and astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American colonel and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American colonel and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Italian volleyball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian volleyball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian volleyball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Icelandic politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Brynd%C3%ADs_Hl%C3%B6%C3%B0versd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>löðversdóttir\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brynd%C3%ADs_Hl%C3%B6%C3%B0versd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>löðversdóttir\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON>ótti<PERSON>", "link": "https://wikipedia.org/wiki/Brynd%C3%ADs_Hl%C3%B6%C3%B0versd%C3%B3ttir"}]}, {"year": "1960", "text": "<PERSON>, American businessman, co-founded Netflix", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hastings\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Netflix\" title=\"Netflix\">Netflix</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hastings\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Netflix\" title=\"Netflix\">Netflix</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hastings"}, {"title": "Netflix", "link": "https://wikipedia.org/wiki/Netflix"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Indonesian actor and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian actor and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra<PERSON>_Karno"}]}, {"year": "1960", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian singer-songwriter and comedian", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_P%C3%A9russe\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_P%C3%A9russe\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_P%C3%A9russe"}]}, {"year": "1960", "text": "<PERSON>, English rugby player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American trumpet player and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1961", "text": "<PERSON>, New Zealand-Australian singer-songwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Australian actor and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American pianist and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress and comedian", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, British actor", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Belgian race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Chinese diver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese diver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_musician)\" title=\"<PERSON> (Oregon musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_musician)\" title=\"<PERSON> (Oregon musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (Oregon musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Oregon_musician)"}]}, {"year": "1964", "text": "<PERSON>, German author (d. 2013)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "Ce<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/CeCe_Winans"}]}, {"year": "1965", "text": "<PERSON>, American swimmer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Irish comedian, actor, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Ardal_O%27Hanlon\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arda<PERSON>_O%27Hanlon\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish comedian, actor, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ardal_O%27Hanlon"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Finnish wrestler", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and bass player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"C. J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American wrestler (d. 1994)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Barr\" title=\"Art Barr\"><PERSON></a>, American wrestler (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Barr\" title=\"Art Barr\"><PERSON></a>, American wrestler (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Barr"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actress and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Venezuelan television host and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and producer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Algerian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Ali_Benarbia\" title=\"Ali Benarbia\"><PERSON></a>, Algerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ali_Benarbia\" title=\"Ali Benarbia\"><PERSON></a>, Algerian footballer", "links": [{"title": "Ali <PERSON>", "link": "https://wikipedia.org/wiki/Ali_Benarbia"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian footballer and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian footballer and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/CL_Smooth\" title=\"CL Smooth\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CL_Smooth\" title=\"CL Smooth\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "CL Smooth", "link": "https://wikipedia.org/wiki/CL_Smooth"}]}, {"year": "1968", "text": "<PERSON><PERSON>, English keyboard player and DJ", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English keyboard player and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English keyboard player and DJ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian-American actor, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Congolese colonel", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese colonel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Congolese colonel", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, English actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, English lawyer and politician, Minister of State for Transport, Mayor of London", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of State for Transport", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Transport"}, {"title": "Mayor of London", "link": "https://wikipedia.org/wiki/Mayor_of_London"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Japanese video game designer and director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese video game designer and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, New Zealand rugby player and television host", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, New Zealand rugby player and television host", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1971", "text": "<PERSON>, English lawyer and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Turkish sociologist, author, and academic", "html": "1971 - <a href=\"https://wikipedia.org/wiki/P%C4%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish sociologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C4%B1<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish sociologist, author, and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C4%B1nar_<PERSON><PERSON>k"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American guitarist and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Slovak footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Finnish cartoonist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish cartoonist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, New Zealand actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swedish ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese hammer thrower", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese hammer thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese hammer thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian model and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Belgian politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Spanish tennis player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> Blanco\"><PERSON><PERSON></a>, Spanish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Galo Blanco\"><PERSON><PERSON></a>, Spanish tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Dutch speed skater and cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ren<PERSON>_<PERSON>\" title=\"Renate <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater and cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Renate <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater and cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, French cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American voice actress, director, and screenwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Finnish singer-songwriter and keyboard player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Italian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Antonino_D%27Agostino\" title=\"<PERSON><PERSON> D'Agostino\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antonino_D%27Agostino\" title=\"<PERSON><PERSON> D'Agostino\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antonino_D%27Agostino"}]}, {"year": "1978", "text": "<PERSON>, Irish rugby player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Driscoll"}]}, {"year": "1979", "text": "<PERSON>, English wrestler", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Pet<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, rapper, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, rapper, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, rapper, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON> <PERSON><PERSON>, American wrestler, actor, and television personality", "html": "1980 - <a href=\"https://wikipedia.org/wiki/The_Miz\" title=\"The Miz\"><PERSON> <PERSON><PERSON></a>, American wrestler, actor, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Miz\" title=\"The Miz\"><PERSON> <PERSON>z</a>, American wrestler, actor, and television personality", "links": [{"title": "The Miz", "link": "https://wikipedia.org/wiki/The_Miz"}]}, {"year": "1980", "text": "<PERSON><PERSON> <PERSON><PERSON>, Cuban-American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Cuban-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Cuban-American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Russian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Serbian race car driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Pavlovi%C4%87_(racing_driver)\" title=\"<PERSON><PERSON> (racing driver)\"><PERSON><PERSON></a>, Serbian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Pavlovi%C4%87_(racing_driver)\" title=\"<PERSON><PERSON> (racing driver)\"><PERSON><PERSON></a>, Serbian race car driver", "links": [{"title": "<PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Pavlovi%C4%87_(racing_driver)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Dutch cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Scottish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Estonian javelin thrower", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian javelin thrower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON><PERSON>_<PERSON>yar"}]}, {"year": "1983", "text": "<PERSON>, American motorcycle racer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Domenik Hixon\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Domenik Hixon\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>xon"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish singer-songwriter and rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/El<PERSON>hant\" title=\"Elliphant\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El<PERSON>hant\" title=\"Elliphan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter and rapper", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elliphant"}]}, {"year": "1985", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bruno Mars\"><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Bruno Mars\"><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Norwegian handball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian handball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>em"}]}, {"year": "1987", "text": "<PERSON>, Scottish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Lebanese footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Taylor Price\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Taylor Price\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Mahm<PERSON>_Tem%C3%BCr\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_Tem%C3%BCr\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mahmut_Tem%C3%BCr"}]}, {"year": "1989", "text": "<PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>%C3%A9"}]}, {"year": "1990", "text": "<PERSON>, Zimbabwean-Dutch triathlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-Dutch triathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean-Dutch triathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Chelsea_Gray\" title=\"Chelsea Gray\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chelsea_Gray\" title=\"<PERSON> Gray\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chelsea_Gray"}]}, {"year": "1992", "text": "<PERSON>, Portuguese tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Jo%C3%A3<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Jo%C3%A3<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Jo%C3%A3<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Lid<PERSON>ya_Marozava\" title=\"Lid<PERSON>ya Marozava\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li<PERSON><PERSON><PERSON>_Marozava\" title=\"Lidziya Marozava\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian tennis player", "links": [{"title": "Lidziya <PERSON>ava", "link": "https://wikipedia.org/wiki/Lidziya_Marozava"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Terran_Pet<PERSON>way\" title=\"Terran Pet<PERSON>way\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terran_Pet<PERSON>way\" title=\"Terran Pet<PERSON>way\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Terran_<PERSON>teway"}]}, {"year": "1993", "text": "<PERSON>, American actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Spanish tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Garbi%C3%B1e_Muguruza\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garbi%C3%B1e_Muguruza\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garbi%C3%B1e_Muguruza"}]}, {"year": "1993", "text": "<PERSON>, Hungarian model and actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actress and producer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American race car driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American rapper", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Herbo\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Herbo\"><PERSON></a>, American rapper", "links": [{"title": "G Herbo", "link": "https://wikipedia.org/wiki/G_Herbo"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Devontae Cacok\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Devon<PERSON><PERSON>_<PERSON>\" title=\"Devontae Cacok\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Devontae <PERSON>", "link": "https://wikipedia.org/wiki/Devon<PERSON>e_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Swiss ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Spanish tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>mo\" title=\"Sara Sorribes Tormo\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>mo\" title=\"Sara Sorribes Tormo\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_So<PERSON>bes_Tormo"}]}, {"year": "1996", "text": "<PERSON>, Japanese ski jumper", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Mexican tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ras_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_G%C3%B3mez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fernanda_Contreras_G%C3%B3mez"}]}, {"year": "1997", "text": "<PERSON>, American actress", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai actor and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Putthipong_<PERSON>nakul\" title=\"Putthipong Assaratanakul\">Put<PERSON><PERSON><PERSON></a>, Thai actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Putthipong_<PERSON>nakul\" title=\"Putthipong Assaratanakul\">Put<PERSON><PERSON><PERSON></a>, Thai actor and singer", "links": [{"title": "Putthipong Assaratanakul", "link": "https://wikipedia.org/wiki/Putthipong_Assaratanakul"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Brazilian rhythmic gymnast", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian rhythmic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Rossi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian rhythmic gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Camila_Rossi"}]}, {"year": "2002", "text": "<PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American football player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "2002", "text": "<PERSON><PERSON>, Chinese tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Mexican-American singer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81nge<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "705", "text": "<PERSON> <PERSON><PERSON> ibn <PERSON>, Umayyad caliph (b. 646)", "html": "705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 646)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Umayyad_Caliphate\" title=\"Umayyad Caliphate\">Umayyad</a> caliph (b. 646)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Umayyad Caliphate", "link": "https://wikipedia.org/wiki/Umayyad_Caliphate"}]}, {"year": "923", "text": "<PERSON><PERSON><PERSON>, archbishop of Salzburg", "html": "923 - <a href=\"https://wikipedia.org/wiki/Pilgrim_I_(archbishop_of_Salzburg)\" title=\"Pilgrim <PERSON> (archbishop of Salzburg)\">Pilgrim <PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Salzburg\" class=\"mw-redirect\" title=\"Archbishopric of Salzburg\">Salzburg</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pilgrim_I_(archbishop_of_Salzburg)\" title=\"Pilgrim <PERSON> (archbishop of Salzburg)\">Pilgrim <PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Archbishopric_of_Salzburg\" class=\"mw-redirect\" title=\"Archbishopric of Salzburg\">Salzburg</a>", "links": [{"title": "<PERSON><PERSON><PERSON> (archbishop of Salzburg)", "link": "https://wikipedia.org/wiki/Pilgrim_I_(archbishop_of_Salzburg)"}, {"title": "Archbishopric of Salzburg", "link": "https://wikipedia.org/wiki/Archbishopric_of_Salzburg"}]}, {"year": "951", "text": "<PERSON>, Chinese Khitan empress", "html": "951 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, Chinese Khitan empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, Chinese Khitan empress", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>"}]}, {"year": "976", "text": "<PERSON> of Zadar, queen consort of the Kingdom of Croatia", "html": "976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Zadar\" title=\"<PERSON> of Zadar\"><PERSON> of Zadar</a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">queen consort</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Croatia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Croatia (medieval)\">Kingdom of Croatia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Zadar\" title=\"<PERSON> of Zadar\"><PERSON> of Zadar</a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">queen consort</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Croatia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Croatia (medieval)\">Kingdom of Croatia</a>", "links": [{"title": "Helen of Zadar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Zada<PERSON>"}, {"title": "Queen consort", "link": "https://wikipedia.org/wiki/Queen_consort"}, {"title": "Kingdom of Croatia (medieval)", "link": "https://wikipedia.org/wiki/Kingdom_of_Croatia_(medieval)"}]}, {"year": "1281", "text": "Princess <PERSON> of Greater Poland (b. c.1245)", "html": "1281 - Princess <a href=\"https://wikipedia.org/wiki/Constance_of_Greater_Poland\" title=\"Constance of Greater Poland\"><PERSON> of Greater Poland</a> (b. c.1245)", "no_year_html": "Princess <a href=\"https://wikipedia.org/wiki/Constance_of_Greater_Poland\" title=\"Constance of Greater Poland\"><PERSON> of Greater Poland</a> (b. c.1245)", "links": [{"title": "Constance of Greater Poland", "link": "https://wikipedia.org/wiki/Constance_of_Greater_Poland"}]}, {"year": "1286", "text": "<PERSON>, Duke of Brittany (b. 1217)", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1217)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1217)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1317", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1265)", "html": "1317 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1265)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1354", "text": "<PERSON>, Roman tribune (b. c.1313)", "html": "1354 - <a href=\"https://wikipedia.org/wiki/Cola_di_Rienzo\" title=\"Cola di Rienzo\">Cola di Rienzo</a>, Roman tribune (b. c.1313)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cola_di_Rienzo\" title=\"Cola di Rienzo\">Cola di Rienzo</a>, Roman tribune (b. c.1313)", "links": [{"title": "Cola di Rienzo", "link": "https://wikipedia.org/wiki/Cola_di_<PERSON>zo"}]}, {"year": "1361", "text": "<PERSON>, 3rd Baron <PERSON>", "html": "1361 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1436", "text": "<PERSON>, Countess of Hainaut (b. 1401)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut</a> (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut</a> (b. 1401)", "links": [{"title": "<PERSON>, Countess of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1469", "text": "<PERSON><PERSON><PERSON>, artist (b. 1406)", "html": "1469 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, artist (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, artist (b. 1406)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1559", "text": "<PERSON>, Spanish nun executed by the Spanish inquisition (b. 1517),", "html": "1559 - <a href=\"https://wikipedia.org/wiki/Marina_de_Guevara\" title=\"Marina de Guevara\"><PERSON></a>, Spanish nun executed by the Spanish inquisition (b. 1517),", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_de_Gueva<PERSON>\" title=\"Marina de Guevara\"><PERSON></a>, Spanish nun executed by the Spanish inquisition (b. 1517),", "links": [{"title": "Marina de Guevara", "link": "https://wikipedia.org/wiki/Marina_de_Guevara"}]}, {"year": "1594", "text": "<PERSON><PERSON><PERSON>, ninja and thief of Japan (b. 1558)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Goemon\" title=\"<PERSON><PERSON><PERSON> Goemon\"><PERSON><PERSON><PERSON> Goemon</a>, ninja and thief of Japan (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Go<PERSON>\" title=\"<PERSON><PERSON><PERSON> Goemon\"><PERSON><PERSON><PERSON>emon</a>, ninja and thief of Japan (b. 1558)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>emon"}]}, {"year": "1621", "text": "<PERSON>, French soldier, playwright, and economist (b. 1575)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, playwright, and economist (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, playwright, and economist (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1647", "text": "<PERSON><PERSON>, Danish astronomer and mathematician (b. 1562)", "html": "1647 - <a href=\"https://wikipedia.org/wiki/Christ<PERSON>_S%C3%B8<PERSON><PERSON>_Longomontanus\" title=\"<PERSON><PERSON> Longomontanus\"><PERSON><PERSON></a>, Danish astronomer and mathematician (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_S%C3%B8<PERSON><PERSON>_Longomontanus\" title=\"<PERSON><PERSON> Longomontanus\"><PERSON><PERSON></a>, Danish astronomer and mathematician (b. 1562)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Christen_S%C3%B8<PERSON><PERSON>_Longomontanus"}]}, {"year": "1652", "text": "<PERSON>, English mathematician and astronomer (b. 1602)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (b. 1602)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1656", "text": "<PERSON>, Elector of Saxony (b. 1585)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1585)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1659", "text": "<PERSON>, French missionary, priest, and historian (b. 1603)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary, priest, and historian (b. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary, priest, and historian (b. 1603)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON><PERSON>g Emperor of China (b. 1678)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/Yongzheng_Emperor\" title=\"Yongzheng Emperor\">Yongzheng Emperor</a> of China (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yongzheng_Emperor\" title=\"Yongzheng Emperor\">Yongzheng Emperor</a> of China (b. 1678)", "links": [{"title": "Yongzheng Emperor", "link": "https://wikipedia.org/wiki/Yongzheng_Emperor"}]}, {"year": "1754", "text": "<PERSON>,  English novelist and playwright (b. 1707)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1707)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON>, French violinist and composer (b. 1711)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (b. 1711)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, American merchant and politician, 1st Governor of Massachusetts (b. 1737)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1795", "text": "<PERSON>, English minister and author (b. 1725)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON><PERSON>, Maltese general and politician (b. 1758)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese general and politician (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese general and politician (b. 1758)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Canadian lawyer and judge (b. 1777)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, Canadian lawyer and judge (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, Canadian lawyer and judge (b. 1777)", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1809", "text": "<PERSON>, Scottish orthographer, phonologist, and linguist (b. 1721)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish orthographer, phonologist, and linguist (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish orthographer, phonologist, and linguist (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, last Spanish ruler of Mexico (b. 1762)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoj%C3%BA\" title=\"<PERSON>\"><PERSON></a>, last Spanish ruler of Mexico (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donoj%C3%BA\" title=\"<PERSON>\"><PERSON></a>, last Spanish ruler of Mexico (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_O%27Donoj%C3%BA"}]}, {"year": "1834", "text": "<PERSON><PERSON><PERSON><PERSON>, French composer (b. 1775)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French composer (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French composer (b. 1775)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, American general, lawyer, and politician, 14th President of the United States (b. 1804)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1879", "text": "<PERSON>, Peruvian admiral (b. 1834)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>nario\" class=\"mw-redirect\" title=\"<PERSON>nar<PERSON>\"><PERSON></a>, Peruvian admiral (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Seminario\" class=\"mw-redirect\" title=\"<PERSON> Seminario\"><PERSON></a>, Peruvian admiral (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Seminario"}]}, {"year": "1886", "text": "<PERSON>, American lawyer and politician (b. 1819)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Austin_F._Pike\" title=\"Austin F. Pike\"><PERSON></a>, American lawyer and politician (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_F._Pike\" title=\"Austin F. Pike\"><PERSON></a>, American lawyer and politician (b. 1819)", "links": [{"title": "Austin F<PERSON> Pike", "link": "https://wikipedia.org/wiki/Austin_F._Pike"}]}, {"year": "1897", "text": "<PERSON>, Russian painter and academic (b. 1830)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1889)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Australian general and engineer (b. 1865)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and engineer (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian general and engineer (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and screenwriter (b. 1880)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Premchand\" title=\"Premchand\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Premchand\" title=\"Premchand\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and screenwriter (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Premchand"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and manager (b. 1882)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ames\" title=\"<PERSON> Ames\"><PERSON></a>, American baseball player and manager (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Ames\" title=\"Red Ames\"><PERSON></a>, American baseball player and manager (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Red_Ames"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Ottoman politician, 292nd Grand Vizier of the Ottoman Empire (b. 1845)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, 292nd <a href=\"https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Grand Vizier of the Ottoman Empire\">Grand Vizier of the Ottoman Empire</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman politician, 292nd <a href=\"https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Grand Vizier of the Ottoman Empire\">Grand Vizier of the Ottoman Empire</a> (b. 1845)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Grand Vizier of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Grand_Vizier_of_the_Ottoman_Empire"}]}, {"year": "1936", "text": "<PERSON>, American businessman (b. 1851)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Russian physicist, mathematician, and engineer (b. 1869)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, mathematician, and engineer (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, mathematician, and engineer (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American captain, lawyer, and politician (b. 1892)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Austrian author and critic (b. 1869)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and critic (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author and critic (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player and manager (b. 1877)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1877)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1953", "text": "<PERSON>, British actor (b. 1895)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English soprano (b. 1912)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ferrier\"><PERSON></a>, English soprano (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ferrier\"><PERSON></a>, English soprano (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rrier"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American accordion player (b. 1928)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American accordion player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American accordion player (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Le<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Bulgarian author and translator (b. 1886)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian author and translator (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian author and translator (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Spanish-Mexican painter (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>medios_Varo\" title=\"Remed<PERSON> Varo\"><PERSON><PERSON><PERSON></a>, Spanish-Mexican painter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>med<PERSON>_Varo\" title=\"Remedios Varo\"><PERSON><PERSON><PERSON></a>, Spanish-Mexican painter (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Remedios_Varo"}]}, {"year": "1967", "text": "<PERSON>, English soldier, lawyer, and politician, Prime Minister of the United Kingdom (b. 1883)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1970", "text": "<PERSON>, French author and poet (b. 1895)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French philosopher, playwright, and critic (b. 1889)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, playwright, and critic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher, playwright, and critic (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter (b. 1902)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American archaeologist (b. 1907)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archaeologist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archaeologist (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English air marshal (b. 1896)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician (b. 1902)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Argentinian-American actor and director (b. 1916)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American actor and director (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-American actor and director (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Baron <PERSON>, English runner and politician, Secretary of State for Commonwealth Relations, Nobel Prize laureate (b. 1889)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English runner and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations\" title=\"Secretary of State for Commonwealth Relations\">Secretary of State for Commonwealth Relations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English runner and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations\" title=\"Secretary of State for Commonwealth Relations\">Secretary of State for Commonwealth Relations</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1889)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary of State for Commonwealth Relations", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Commonwealth_Relations"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1983", "text": "<PERSON>, American actress (b. 1934)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American captain, physicist, and balloonist (b. 1919)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a>, American captain, physicist, and balloonist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a>, American captain, physicist, and balloonist (b. 1919)", "links": [{"title": "<PERSON> (balloonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)"}]}, {"year": "1985", "text": "<PERSON>, English-American mathematician and scholar (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and scholar (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and scholar (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Greek scholar and politician, 2nd President of Greece (b. 1899)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek scholar and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek scholar and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1992", "text": "<PERSON>, German lawyer and politician, 4th Chancellor of Germany, Nobel Prize laureate (b. 1913)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)\" class=\"mw-redirect\" title=\"Chancellor of Germany (Federal Republic)\">Chancellor of Germany</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (Federal Republic)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(Federal_Republic)"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1992", "text": "<PERSON>, American serial killer, torturer, and rapist (b. 1949)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer, torturer, and rapist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer, torturer, and rapist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American lawyer (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American conductor and educator (b. 1946)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and educator (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American architect, designed the Marina City Building (b. 1913)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Marina_City\" title=\"Marina City\">Marina City Building</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/Marina_City\" title=\"Marina City\">Marina City Building</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Marina City", "link": "https://wikipedia.org/wiki/Marina_City"}]}, {"year": "1999", "text": "<PERSON>, American basketball player and coach (b. 1915)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English author (b. 1937)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, First Deputy Premier of the Soviet Union (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union\" title=\"First Deputy Premier of the Soviet Union\">First Deputy Premier of the Soviet Union</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union\" title=\"First Deputy Premier of the Soviet Union\">First Deputy Premier of the Soviet Union</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Deputy Premier of the Soviet Union", "link": "https://wikipedia.org/wiki/First_Deputy_Premier_of_the_Soviet_Union"}]}, {"year": "2002", "text": "<PERSON>, English actress (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian ice hockey player (b. 1952)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American historian and author (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, New Zealand race car driver (b. 1974)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, New Zealand race car driver (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, New Zealand race car driver (b. 1974)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2007", "text": "<PERSON>, Greek painter and sculptor (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter and sculptor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek painter and sculptor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/%C3%82nge<PERSON>_Carvalho\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%82nge<PERSON>_Carvalho\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%82nge<PERSON>_Carvalho"}]}, {"year": "2008", "text": "<PERSON>, English journalist (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, English journalist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)\" title=\"<PERSON> (newscaster)\"><PERSON></a>, English journalist (b. 1938)", "links": [{"title": "<PERSON> (newscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(newscaster)"}]}, {"year": "2008", "text": "<PERSON>, Scottish-American actress (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Romanian-American biologist and physician, Nobel Prize laureate (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2010", "text": "<PERSON>, American journalist (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, British physician and author (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physician and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British physician and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American football player, coach, and manager (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Davis"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American guitarist and painter (b. 1971)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and painter (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist and painter (b. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American pianist (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist (b. 1924)", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian singer and journalist (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bhosle\"><PERSON><PERSON><PERSON></a>, Indian singer and journalist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian singer and journalist (b. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>le"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Filipino director, producer, and screenwriter (b. 1955)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino director, producer, and screenwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino director, producer, and screenwriter (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Scottish captain and author (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish captain and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish captain and author (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian politician, 20th Governor of Gujarat (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 20th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Gujarat\" class=\"mw-redirect\" title=\"List of Governors of Gujarat\">Governor of Gujarat</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 20th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Gujarat\" class=\"mw-redirect\" title=\"List of Governors of Gujarat\">Governor of Gujarat</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Governors of Gujarat", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Gujarat"}]}, {"year": "2013", "text": "<PERSON>, Irish singer-songwriter and guitarist (b. 1957)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian businessman and philanthropist (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and philanthropist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American journalist and politician (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Swiss archaeologist and philologist (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss archaeologist and philologist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss archaeologist and philologist (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and manager (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Tibetan-Chinese spiritual leader (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Akong_Rinpoche\" title=\"Akong Rinpoche\">Akong Rinpoche</a>, Tibetan-Chinese spiritual leader (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Akong_Rinpoche\" title=\"Akong Rinpoche\">Akong Rinpoche</a>, Tibetan-Chinese spiritual leader (b. 1939)", "links": [{"title": "Akong Rinpoche", "link": "https://wikipedia.org/wiki/Akong_Rinpoche"}]}, {"year": "2014", "text": "<PERSON>, Australian author and playwright (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and playwright (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Morris_Lurie"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American missionary (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American missionary (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American missionary (b. 1921)", "links": [{"title": "Alden <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American chemist and academic (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chemist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chemist and academic (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American author (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Dutch speed skater (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> van <PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch speed skater (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Welsh-English actor (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_actor)\" title=\"<PERSON> (Welsh actor)\"><PERSON></a>, Welsh-English actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_actor)\" title=\"<PERSON> (Welsh actor)\"><PERSON></a>, Welsh-English actor (b. 1926)", "links": [{"title": "<PERSON> (Welsh actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_actor)"}]}, {"year": "2015", "text": "<PERSON>, Scottish singer-songwriter (b. 1951)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter (b. 1951)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "2015", "text": "<PERSON>, American author and illustrator (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American football player and coach (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "2015", "text": "<PERSON>, American chef and author (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American professional baseball pitcher (b. 1928)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Whitey_Ford\" title=\"Whitey Ford\"><PERSON><PERSON></a>, American professional baseball pitcher (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>y_Ford\" title=\"White<PERSON> Ford\"><PERSON><PERSON></a>, American professional baseball pitcher (b. 1928)", "links": [{"title": "Whitey <PERSON>", "link": "https://wikipedia.org/wiki/Whitey_Ford"}]}, {"year": "2024", "text": "<PERSON>, American football player (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American lawyer and politician (b. 1946)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American lawyer and politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American lawyer and politician (b. 1946)", "links": [{"title": "<PERSON> (South Dakota politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)"}]}, {"year": "2024", "text": "<PERSON>, Cuban baseball player (b. 1940)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Cuban baseball player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}