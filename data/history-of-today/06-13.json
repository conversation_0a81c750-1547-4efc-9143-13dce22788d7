{"date": "June 13", "url": "https://wikipedia.org/wiki/June_13", "data": {"Events": [{"year": "313", "text": "The decisions of the Edict of Milan, signed by <PERSON> the Great and co-emperor <PERSON><PERSON>, granting religious freedom throughout the Roman Empire, are published in Nicomedia.", "html": "313 - The decisions of the <a href=\"https://wikipedia.org/wiki/Edict_of_Milan\" title=\"Edict of Milan\">Edict of Milan</a>, signed by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> and co-emperor <a href=\"https://wikipedia.org/wiki/Licinius\" title=\"<PERSON>cinius\"><PERSON><PERSON></a>, granting <a href=\"https://wikipedia.org/wiki/Religious_freedom\" class=\"mw-redirect\" title=\"Religious freedom\">religious freedom</a> throughout the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>, are published in <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a>.", "no_year_html": "The decisions of the <a href=\"https://wikipedia.org/wiki/Edict_of_Milan\" title=\"Edict of Milan\">Edict of Milan</a>, signed by <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> and co-emperor <a href=\"https://wikipedia.org/wiki/Licinius\" title=\"<PERSON>cinius\"><PERSON><PERSON></a>, granting <a href=\"https://wikipedia.org/wiki/Religious_freedom\" class=\"mw-redirect\" title=\"Religious freedom\">religious freedom</a> throughout the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a>, are published in <a href=\"https://wikipedia.org/wiki/Nicomedia\" title=\"Nicomedia\">Nicomedia</a>.", "links": [{"title": "Edict of Milan", "link": "https://wikipedia.org/wiki/Edict_of_Milan"}, {"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Licinius"}, {"title": "Religious freedom", "link": "https://wikipedia.org/wiki/Religious_freedom"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "Nicomedia", "link": "https://wikipedia.org/wiki/Nicomedia"}]}, {"year": "1325", "text": "<PERSON> begins his travels, leaving his home in Tangiers to travel to Mecca (gone 24 years).", "html": "1325 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his travels, leaving his home in <a href=\"https://wikipedia.org/wiki/Tangiers\" class=\"mw-redirect\" title=\"Tangiers\">Tangiers</a> to travel to <a href=\"https://wikipedia.org/wiki/Mecca\" title=\"Mecca\">Mecca</a> (gone 24 years).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his travels, leaving his home in <a href=\"https://wikipedia.org/wiki/Tangiers\" class=\"mw-redirect\" title=\"Tangiers\">Tangiers</a> to travel to <a href=\"https://wikipedia.org/wiki/Mecca\" title=\"Mecca\">Mecca</a> (gone 24 years).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tangiers", "link": "https://wikipedia.org/wiki/Tangiers"}, {"title": "Mecca", "link": "https://wikipedia.org/wiki/Mecca"}]}, {"year": "1381", "text": "In England, the Peasants' Revolt, led by <PERSON><PERSON>, comes to a head, as rebels set fire to the Savoy Palace.", "html": "1381 - In England, the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>, led by <a href=\"https://wikipedia.org/wiki/Wat_<PERSON>\" title=\"Wat <PERSON>\">Wat <PERSON></a>, comes to a head, as rebels set fire to the <a href=\"https://wikipedia.org/wiki/Savoy_Palace\" title=\"Savoy Palace\">Savoy Palace</a>.", "no_year_html": "In England, the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>, led by <a href=\"https://wikipedia.org/wiki/Wat_<PERSON>\" title=\"Wat <PERSON>\">Wat <PERSON></a>, comes to a head, as rebels set fire to the <a href=\"https://wikipedia.org/wiki/Savoy_Palace\" title=\"Savoy Palace\">Savoy Palace</a>.", "links": [{"title": "Peasants' Revolt", "link": "https://wikipedia.org/wiki/Peasants%27_<PERSON>olt"}, {"title": "Wat Tyler", "link": "https://wikipedia.org/wiki/Wat_<PERSON>"}, {"title": "Savoy Palace", "link": "https://wikipedia.org/wiki/Savoy_Palace"}]}, {"year": "1514", "text": "<PERSON>, at over 1,000 tons the largest warship in the world at this time, built at the new Woolwich Dockyard in England, is dedicated.", "html": "1514 - <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Dieu\" title=\"<PERSON> Grace à Dieu\"><PERSON></a></i>, at over 1,000 tons the largest <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warship</a> in the world at this time, built at the new <a href=\"https://wikipedia.org/wiki/Woolwich_Dockyard\" title=\"Woolwich Dockyard\">Woolwich Dockyard</a> in England, is dedicated.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Dieu\" title=\"Henry Grace à Dieu\"><PERSON></a></i>, at over 1,000 tons the largest <a href=\"https://wikipedia.org/wiki/Warship\" title=\"Warship\">warship</a> in the world at this time, built at the new <a href=\"https://wikipedia.org/wiki/Woolwich_Dockyard\" title=\"Woolwich Dockyard\">Woolwich Dockyard</a> in England, is dedicated.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%A0_Dieu"}, {"title": "Warship", "link": "https://wikipedia.org/wiki/Warship"}, {"title": "Woolwich Dockyard", "link": "https://wikipedia.org/wiki/Woolwich_Dockyard"}]}, {"year": "1525", "text": "<PERSON> marries <PERSON><PERSON><PERSON>, against the celibacy rule decreed by the Roman Catholic Church for priests and nuns.", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, against the <a href=\"https://wikipedia.org/wiki/Celibacy\" title=\"Celibacy\">celibacy</a> rule decreed by the Roman Catholic Church for priests and nuns.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> marries <a href=\"https://wikipedia.org/wiki/Kat<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, against the <a href=\"https://wikipedia.org/wiki/Celibacy\" title=\"Celibacy\">celibacy</a> rule decreed by the Roman Catholic Church for priests and nuns.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Celibacy"}]}, {"year": "1625", "text": "King <PERSON> of England marries Catholic princess <PERSON> of France and Navarre, at Canterbury.", "html": "1625 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> marries Catholic princess <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/Navarre\" title=\"Navarre\">Navarre</a>, at Canterbury.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> marries Catholic princess <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> and <a href=\"https://wikipedia.org/wiki/Navarre\" title=\"Navarre\">Navarre</a>, at Canterbury.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Navarre", "link": "https://wikipedia.org/wiki/Navarre"}]}, {"year": "1740", "text": "Georgia provincial governor <PERSON> begins an unsuccessful attempt to take Spanish Florida during the Siege of St. Augustine.", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Province_of_Georgia\" title=\"Province of Georgia\">Georgia</a> provincial governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins an unsuccessful attempt to take <a href=\"https://wikipedia.org/wiki/Spanish_Florida\" title=\"Spanish Florida\">Spanish Florida</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_St._Augustine_(1740)\" title=\"Siege of St. Augustine (1740)\">Siege of St. Augustine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Province_of_Georgia\" title=\"Province of Georgia\">Georgia</a> provincial governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins an unsuccessful attempt to take <a href=\"https://wikipedia.org/wiki/Spanish_Florida\" title=\"Spanish Florida\">Spanish Florida</a> during the <a href=\"https://wikipedia.org/wiki/Siege_of_St._Augustine_(1740)\" title=\"Siege of St. Augustine (1740)\">Siege of St. Augustine</a>.", "links": [{"title": "Province of Georgia", "link": "https://wikipedia.org/wiki/Province_of_Georgia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Spanish Florida", "link": "https://wikipedia.org/wiki/Spanish_Florida"}, {"title": "Siege of <PERSON>. Augustine (1740)", "link": "https://wikipedia.org/wiki/Siege_of_St._Augustine_(1740)"}]}, {"year": "1774", "text": "Rhode Island becomes the first of Britain's North American colonies to ban the importation of slaves.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a> becomes the first of Britain's North American colonies to ban the importation of slaves.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a> becomes the first of Britain's North American colonies to ban the importation of slaves.", "links": [{"title": "Rhode Island", "link": "https://wikipedia.org/wiki/Rhode_Island"}]}, {"year": "1777", "text": "American Revolutionary War: <PERSON>, Marquis <PERSON> lands near Charleston, South Carolina, in order to help the Continental Congress to train its army.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a> lands near <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, in order to help the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> to train its army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a> lands near <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>, in order to help the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> to train its army.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}]}, {"year": "1805", "text": "<PERSON> and <PERSON> Expedition: Scouting ahead of the expedition, <PERSON><PERSON><PERSON><PERSON> and four companions sight the Great Falls of the Missouri River.", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_Expedition\" title=\"<PERSON> and Clark Expedition\"><PERSON> and <PERSON> Expedition</a>: Scouting ahead of the expedition, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> and four companions sight the <a href=\"https://wikipedia.org/wiki/Great_Falls_of_the_Missouri_River\" class=\"mw-redirect\" title=\"Great Falls of the Missouri River\">Great Falls of the Missouri River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Expedition\" title=\"<PERSON> and Clark Expedition\"><PERSON> and <PERSON> Expedition</a>: Scouting ahead of the expedition, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> and four companions sight the <a href=\"https://wikipedia.org/wiki/Great_Falls_of_the_Missouri_River\" class=\"mw-redirect\" title=\"Great Falls of the Missouri River\">Great Falls of the Missouri River</a>.", "links": [{"title": "<PERSON> and Clark Expedition", "link": "https://wikipedia.org/wiki/<PERSON>_and_Clark_Expedition"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Great Falls of the Missouri River", "link": "https://wikipedia.org/wiki/Great_Falls_of_the_Missouri_River"}]}, {"year": "1850", "text": "The American League of Colored Laborers, the first African American labor union in the United States, is established in New York City.", "html": "1850 - The <a href=\"https://wikipedia.org/wiki/American_League_of_Colored_Laborers\" title=\"American League of Colored Laborers\">American League of Colored Laborers</a>, the first African American <a href=\"https://wikipedia.org/wiki/Labor_union\" class=\"mw-redirect\" title=\"Labor union\">labor union</a> in the United States, is established in New York City.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/American_League_of_Colored_Laborers\" title=\"American League of Colored Laborers\">American League of Colored Laborers</a>, the first African American <a href=\"https://wikipedia.org/wiki/Labor_union\" class=\"mw-redirect\" title=\"Labor union\">labor union</a> in the United States, is established in New York City.", "links": [{"title": "American League of Colored Laborers", "link": "https://wikipedia.org/wiki/American_League_of_Colored_Laborers"}, {"title": "Labor union", "link": "https://wikipedia.org/wiki/Labor_union"}]}, {"year": "1855", "text": "Twentieth opera of Giuseppe Verdi, Les vêpres siciliennes (\"The Sicilian Vespers\"), is premiered in Paris.", "html": "1855 - Twentieth opera of <a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Les_v%C3%AApres_siciliennes\" title=\"Les vêpres siciliennes\">Les vêpres siciliennes</a></i> (\"The Sicilian Vespers\"), is premiered in Paris.", "no_year_html": "Twentieth opera of <a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>, <i><a href=\"https://wikipedia.org/wiki/Les_v%C3%AApres_siciliennes\" title=\"Les vêpres siciliennes\">Les vêpres siciliennes</a></i> (\"The Sicilian Vespers\"), is premiered in Paris.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Les vêpres siciliennes", "link": "https://wikipedia.org/wiki/Les_v%C3%AApres_siciliennes"}]}, {"year": "1881", "text": "The USS Jeannette is crushed in an Arctic Ocean ice pack.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/USS_Jeannette_(1878)\" title=\"USS Jeannette (1878)\">USS <i><PERSON><PERSON></i></a> is crushed in an Arctic Ocean ice pack.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/USS_Jeannette_(1878)\" title=\"USS Jeannette (1878)\">USS <i><PERSON><PERSON></i></a> is crushed in an Arctic Ocean ice pack.", "links": [{"title": "USS Jeannette (1878)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1878)"}]}, {"year": "1886", "text": "A fire devastates much of Vancouver, British Columbia.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Great_Vancouver_Fire\" title=\"Great Vancouver Fire\">A fire devastates</a> much of <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a>, <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Vancouver_Fire\" title=\"Great Vancouver Fire\">A fire devastates</a> much of <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a>, <a href=\"https://wikipedia.org/wiki/British_Columbia\" title=\"British Columbia\">British Columbia</a>.", "links": [{"title": "Great Vancouver Fire", "link": "https://wikipedia.org/wiki/Great_Vancouver_Fire"}, {"title": "Vancouver", "link": "https://wikipedia.org/wiki/Vancouver"}, {"title": "British Columbia", "link": "https://wikipedia.org/wiki/British_Columbia"}]}, {"year": "1893", "text": "Grover <PERSON> notices a rough spot in his mouth and on July 1 undergoes secret, successful surgery to remove a large, cancerous portion of his jaw; the operation was not revealed to the public until 1917, nine years after the president's death.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Grover_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> notices a rough spot in his mouth and on July 1 undergoes <a href=\"https://wikipedia.org/wiki/Grover_Cleveland#Cancer\" title=\"Grover <PERSON>\">secret, successful surgery</a> to remove a large, cancerous portion of his jaw; the operation was not revealed to the public until 1917, nine years after the president's death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r_Cleveland\" title=\"Grover Cleveland\"><PERSON><PERSON></a> notices a rough spot in his mouth and on July 1 undergoes <a href=\"https://wikipedia.org/wiki/Grover_Cleveland#Cancer\" title=\"Grover <PERSON>\">secret, successful surgery</a> to remove a large, cancerous portion of his jaw; the operation was not revealed to the public until 1917, nine years after the president's death.", "links": [{"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland"}, {"title": "Grover <PERSON>", "link": "https://wikipedia.org/wiki/Grover_Cleveland#Cancer"}]}, {"year": "1895", "text": "<PERSON><PERSON> wins the world's first real automobile race. <PERSON><PERSON><PERSON> completed the 732-mile course, from Paris to Bordeaux and back, in just under 49 hours, at a then-impressive speed of about fifteen miles per hour (24 km/h).", "html": "1895 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins the world's first real automobile race. <PERSON><PERSON><PERSON> completed the 732-mile course, from Paris to Bordeaux and back, in just under 49 hours, at a then-impressive speed of about fifteen miles per hour (24 km/h).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> wins the world's first real automobile race. <PERSON><PERSON><PERSON> completed the 732-mile course, from Paris to Bordeaux and back, in just under 49 hours, at a then-impressive speed of about fifteen miles per hour (24 km/h).", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Levassor"}]}, {"year": "1898", "text": "Yukon Territory is formed, with Dawson chosen as its capital.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Yukon_Territory\" class=\"mw-redirect\" title=\"Yukon Territory\">Yukon Territory</a> is formed, with <a href=\"https://wikipedia.org/wiki/Dawson,_Yukon\" class=\"mw-redirect\" title=\"Dawson, Yukon\">Dawson</a> chosen as its capital.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yukon_Territory\" class=\"mw-redirect\" title=\"Yukon Territory\">Yukon Territory</a> is formed, with <a href=\"https://wikipedia.org/wiki/Dawson,_Yukon\" class=\"mw-redirect\" title=\"Dawson, Yukon\">Dawson</a> chosen as its capital.", "links": [{"title": "Yukon Territory", "link": "https://wikipedia.org/wiki/Yukon_Territory"}, {"title": "Dawson, Yukon", "link": "https://wikipedia.org/wiki/Dawson,_Yukon"}]}, {"year": "1917", "text": "World War I: The deadliest German air raid on London of the war is carried out by Gotha G.IV bombers and results in 162 deaths, including 46 children, and 432 injuries.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The deadliest <a href=\"https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I\" class=\"mw-redirect\" title=\"German strategic bombing during World War I\">German air raid on London</a> of the war is carried out by <a href=\"https://wikipedia.org/wiki/Gotha_G.IV\" title=\"Gotha G.IV\">Gotha G.IV</a> bombers and results in 162 deaths, including 46 children, and 432 injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The deadliest <a href=\"https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I\" class=\"mw-redirect\" title=\"German strategic bombing during World War I\">German air raid on London</a> of the war is carried out by <a href=\"https://wikipedia.org/wiki/Gotha_G.IV\" title=\"Gotha G.IV\">Gotha G.IV</a> bombers and results in 162 deaths, including 46 children, and 432 injuries.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "German strategic bombing during World War I", "link": "https://wikipedia.org/wiki/German_strategic_bombing_during_World_War_I"}, {"title": "Gotha G.IV", "link": "https://wikipedia.org/wiki/Gotha_G.IV"}]}, {"year": "1927", "text": "Aviator <PERSON> receives a ticker tape parade up 5th Avenue in New York City.", "html": "1927 - Aviator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Ticker_tape_parade\" class=\"mw-redirect\" title=\"Ticker tape parade\">ticker tape parade</a> up 5th Avenue in New York City.", "no_year_html": "Aviator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Ticker_tape_parade\" class=\"mw-redirect\" title=\"Ticker tape parade\">ticker tape parade</a> up 5th Avenue in New York City.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ticker tape parade", "link": "https://wikipedia.org/wiki/Ticker_tape_parade"}]}, {"year": "1944", "text": "World War II: The Battle of Villers-Bocage:  German tank ace <PERSON> ambushes elements of the British 7th Armoured Division, destroying up to fourteen tanks, fifteen personnel carriers and two anti-tank guns in a Tiger I tank.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Villers-Bocage\" title=\"Battle of Villers-Bocage\">Battle of Villers-Bocage</a>: German tank ace <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> ambushes elements of the <a href=\"https://wikipedia.org/wiki/British_7th_Armoured_Division\" class=\"mw-redirect\" title=\"British 7th Armoured Division\">British 7th Armoured Division</a>, destroying up to fourteen tanks, fifteen personnel carriers and two anti-tank guns in a <a href=\"https://wikipedia.org/wiki/Tiger_I\" title=\"Tiger I\">Tiger I</a> tank.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Villers-Bocage\" title=\"Battle of Villers-Bocage\">Battle of Villers-Bocage</a>: German tank ace <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> ambushes elements of the <a href=\"https://wikipedia.org/wiki/British_7th_Armoured_Division\" class=\"mw-redirect\" title=\"British 7th Armoured Division\">British 7th Armoured Division</a>, destroying up to fourteen tanks, fifteen personnel carriers and two anti-tank guns in a <a href=\"https://wikipedia.org/wiki/Tiger_I\" title=\"Tiger I\">Tiger I</a> tank.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Villers-Bocage", "link": "https://wikipedia.org/wiki/Battle_of_Villers-Bocage"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "British 7th Armoured Division", "link": "https://wikipedia.org/wiki/British_7th_Armoured_Division"}, {"title": "Tiger I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: German combat elements, reinforced by the 17th SS Panzergrenadier Division, launch a counterattack on American forces near Carentan.", "html": "1944 - World War II: German combat elements, reinforced by the <a href=\"https://wikipedia.org/wiki/17th_SS_Panzergrenadier_Division\" class=\"mw-redirect\" title=\"17th SS Panzergrenadier Division\">17th SS Panzergrenadier Division</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Bloody_Gulch\" title=\"Battle of Bloody Gulch\">launch a counterattack</a> on American forces near <a href=\"https://wikipedia.org/wiki/Carentan\" title=\"Carentan\">Carentan</a>.", "no_year_html": "World War II: German combat elements, reinforced by the <a href=\"https://wikipedia.org/wiki/17th_SS_Panzergrenadier_Division\" class=\"mw-redirect\" title=\"17th SS Panzergrenadier Division\">17th SS Panzergrenadier Division</a>, <a href=\"https://wikipedia.org/wiki/Battle_of_Bloody_Gulch\" title=\"Battle of Bloody Gulch\">launch a counterattack</a> on American forces near <a href=\"https://wikipedia.org/wiki/Carentan\" title=\"Carentan\">Carentan</a>.", "links": [{"title": "17th SS Panzergrenadier Division", "link": "https://wikipedia.org/wiki/17th_SS_Panzergrenadier_Division"}, {"title": "Battle of Bloody Gulch", "link": "https://wikipedia.org/wiki/Battle_of_Bloody_Gulch"}, {"title": "Carentan", "link": "https://wikipedia.org/wiki/Carentan"}]}, {"year": "1944", "text": "World War II: Germany launches the first V1 Flying Bomb attack on England. Only four of the eleven bombs strike their targets.", "html": "1944 - World War II: Germany launches the first <a href=\"https://wikipedia.org/wiki/V1_Flying_Bomb\" class=\"mw-redirect\" title=\"V1 Flying Bomb\">V1 Flying Bomb</a> attack on England. Only four of the eleven bombs strike their targets.", "no_year_html": "World War II: Germany launches the first <a href=\"https://wikipedia.org/wiki/V1_Flying_Bomb\" class=\"mw-redirect\" title=\"V1 Flying Bomb\">V1 Flying Bomb</a> attack on England. Only four of the eleven bombs strike their targets.", "links": [{"title": "V1 Flying Bomb", "link": "https://wikipedia.org/wiki/V1_Flying_Bomb"}]}, {"year": "1952", "text": "Catalina affair: A Swedish Douglas DC-3 is shot down by a Soviet MiG-15 fighter.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Catalina_affair\" title=\"Catalina affair\">Catalina affair</a>: A Swedish <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> is shot down by a Soviet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15\">MiG-15</a> fighter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catalina_affair\" title=\"Catalina affair\">Catalina affair</a>: A Swedish <a href=\"https://wikipedia.org/wiki/Douglas_DC-3\" title=\"Douglas DC-3\">Douglas DC-3</a> is shot down by a Soviet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MiG-15\">MiG-15</a> fighter.", "links": [{"title": "Catalina affair", "link": "https://wikipedia.org/wiki/Catalina_affair"}, {"title": "Douglas DC-3", "link": "https://wikipedia.org/wiki/Douglas_DC-3"}, {"title": "Mikoyan<PERSON><PERSON><PERSON><PERSON> MiG-15", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>_MiG-15"}]}, {"year": "1966", "text": "The United States Supreme Court rules in Miranda v. Arizona that the police must inform suspects of their Fifth Amendment rights before questioning them (colloquially known as \"Mirandizing\").", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Miranda_v._Arizona\" title=\"Miranda v. Arizona\">Miranda v. Arizona</a></i> that the police must inform suspects of their <a href=\"https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution\" title=\"Fifth Amendment to the United States Constitution\">Fifth Amendment rights</a> before questioning them (colloquially known as \"<a href=\"https://wikipedia.org/wiki/Miranda_warning\" title=\"Miranda warning\">Mirandizing</a>\").", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/Miranda_v._Arizona\" title=\"Miranda v. Arizona\">Miranda v. Arizona</a></i> that the police must inform suspects of their <a href=\"https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution\" title=\"Fifth Amendment to the United States Constitution\">Fifth Amendment rights</a> before questioning them (colloquially known as \"<a href=\"https://wikipedia.org/wiki/Miranda_warning\" title=\"Miranda warning\">Mirandizing</a>\").", "links": [{"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "<PERSON> v. Arizona", "link": "https://wikipedia.org/wiki/Miranda_v._Arizona"}, {"title": "Fifth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Fifth_Amendment_to_the_United_States_Constitution"}, {"title": "Miranda warning", "link": "https://wikipedia.org/wiki/<PERSON>_warning"}]}, {"year": "1967", "text": "U.S. President <PERSON> nominates Solicitor-General <PERSON><PERSON><PERSON> to become the first black justice on the U.S. Supreme Court.", "html": "1967 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates Solicitor-General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to become the first black justice on the <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> nominates Solicitor-General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to become the first black justice on the <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}]}, {"year": "1971", "text": "Vietnam War: The New York Times begins publication of the Pentagon Papers.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i> begins publication of the <i><a href=\"https://wikipedia.org/wiki/Pentagon_Papers\" title=\"Pentagon Papers\">Pentagon Papers</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <i><a href=\"https://wikipedia.org/wiki/The_New_York_Times\" title=\"The New York Times\">The New York Times</a></i> begins publication of the <i><a href=\"https://wikipedia.org/wiki/Pentagon_Papers\" title=\"Pentagon Papers\">Pentagon Papers</a></i>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "The New York Times", "link": "https://wikipedia.org/wiki/The_New_York_Times"}, {"title": "Pentagon Papers", "link": "https://wikipedia.org/wiki/Pentagon_Papers"}]}, {"year": "1973", "text": "In a game versus the Philadelphia Phillies at Veterans Stadium, Los Angeles Dodgers teammates <PERSON>, <PERSON>, <PERSON> and <PERSON> play together as an infield for the first time, going on to set the Major League Baseball record of staying together for .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}8+1⁄2 years.", "html": "1973 - In a game versus the <a href=\"https://wikipedia.org/wiki/Philadelphia_Phillies\" title=\"Philadelphia Phillies\">Philadelphia Phillies</a> at <a href=\"https://wikipedia.org/wiki/Veterans_Stadium\" title=\"Veterans Stadium\">Veterans Stadium</a>, <a href=\"https://wikipedia.org/wiki/Los_Angeles_Dodgers\" title=\"Los Angeles Dodgers\">Los Angeles Dodgers</a> teammates <a href=\"https://wikipedia.org/wiki/Dodger_infield_of_<PERSON><PERSON>,_<PERSON><PERSON>,_<PERSON><PERSON>_and_<PERSON>\" title=\"Dodger infield of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>\"><PERSON>, <PERSON>, <PERSON> and <PERSON> play together as an infield</a> for the first time, going on to set the <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> record of staying together for <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">8<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> years.", "no_year_html": "In a game versus the <a href=\"https://wikipedia.org/wiki/Philadelphia_Phillies\" title=\"Philadelphia Phillies\">Philadelphia Phillies</a> at <a href=\"https://wikipedia.org/wiki/Veterans_Stadium\" title=\"Veterans Stadium\">Veterans Stadium</a>, <a href=\"https://wikipedia.org/wiki/Los_Angeles_Dodgers\" title=\"Los Angeles Dodgers\">Los Angeles Dodgers</a> teammates <a href=\"https://wikipedia.org/wiki/Dodger_infield_of_<PERSON><PERSON>,_<PERSON><PERSON>,_<PERSON><PERSON>_and_<PERSON>\" title=\"Dodger infield of <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>\"><PERSON>, <PERSON>, <PERSON> and <PERSON> play together as an infield</a> for the first time, going on to set the <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> record of staying together for <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">8<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> years.", "links": [{"title": "Philadelphia Phillies", "link": "https://wikipedia.org/wiki/Philadelphia_Phillies"}, {"title": "Veterans Stadium", "link": "https://wikipedia.org/wiki/Veterans_Stadium"}, {"title": "Los Angeles Dodgers", "link": "https://wikipedia.org/wiki/Los_Angeles_Dodgers"}, {"title": "Dodger infield of Garvey, Lo<PERSON>, Ce<PERSON> and Russell", "link": "https://wikipedia.org/wiki/Dodger_infield_of_<PERSON><PERSON><PERSON>,_<PERSON><PERSON>,_<PERSON><PERSON>_and_<PERSON>"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "1977", "text": "Convicted <PERSON> assassin <PERSON> is recaptured after escaping from prison three days before.", "html": "1977 - Convicted <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a> assassin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is recaptured after escaping from prison three days before.", "no_year_html": "Convicted <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> assassin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is recaptured after escaping from prison three days before.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "At the Trooping the Colour ceremony in London, a teenager, <PERSON>, fires six blank shots at Queen Elizabeth II.", "html": "1981 - At the <a href=\"https://wikipedia.org/wiki/Trooping_the_Colour\" title=\"Trooping the Colour\">Trooping the Colour</a> ceremony in London, a teenager, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fires six blank shots at <PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Trooping_the_Colour\" title=\"Trooping the Colour\">Trooping the Colour</a> ceremony in London, a teenager, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ant\" title=\"<PERSON>\"><PERSON></a>, fires six blank shots at <PERSON> <a href=\"https://wikipedia.org/wiki/Elizabeth_II\" title=\"Elizabeth II\"><PERSON> II</a>.", "links": [{"title": "Trooping the Colour", "link": "https://wikipedia.org/wiki/Trooping_the_Colour"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON> becomes King of Saudi Arabia upon the death of his brother, <PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia\" title=\"Fahd of Saudi Arabia\">Fahd</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Saudi_Arabia\" title=\"King of Saudi Arabia\">King of Saudi Arabia</a> upon the death of his brother, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\">Khalid</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia\" title=\"Fahd of Saudi Arabia\">Fahd</a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Saudi_Arabia\" title=\"King of Saudi Arabia\">King of Saudi Arabia</a> upon the death of his brother, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\"><PERSON></a>.", "links": [{"title": "Fahd of Saudi Arabia", "link": "https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia"}, {"title": "King of Saudi Arabia", "link": "https://wikipedia.org/wiki/King_of_Saudi_Arabia"}, {"title": "<PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia"}]}, {"year": "1982", "text": "Battles of Tumbledown and Wireless Ridge, during the Falklands War.", "html": "1982 - Battles of <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Tumbledown\" title=\"Battle of Mount Tumbledown\">Tumbledown</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Wireless_Ridge\" title=\"Battle of Wireless Ridge\">Wireless Ridge</a>, during the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "no_year_html": "Battles of <a href=\"https://wikipedia.org/wiki/Battle_of_Mount_Tumbledown\" title=\"Battle of Mount Tumbledown\">Tumbledown</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Wireless_Ridge\" title=\"Battle of Wireless Ridge\">Wireless Ridge</a>, during the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "links": [{"title": "Battle of Mount Tumbledown", "link": "https://wikipedia.org/wiki/Battle_of_Mount_Tumbledown"}, {"title": "Battle of Wireless Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Wireless_Ridge"}, {"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}]}, {"year": "1983", "text": "Pioneer 10 becomes the first man-made object to leave the central Solar System when it passes beyond the orbit of Neptune.", "html": "1983 - <i><a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a></i> becomes the first man-made object to leave the central <a href=\"https://wikipedia.org/wiki/Solar_System\" title=\"Solar System\">Solar System</a> when it passes beyond the orbit of <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Pioneer_10\" title=\"Pioneer 10\">Pioneer 10</a></i> becomes the first man-made object to leave the central <a href=\"https://wikipedia.org/wiki/Solar_System\" title=\"Solar System\">Solar System</a> when it passes beyond the orbit of <a href=\"https://wikipedia.org/wiki/Neptune\" title=\"Neptune\">Neptune</a>.", "links": [{"title": "Pioneer 10", "link": "https://wikipedia.org/wiki/Pioneer_10"}, {"title": "Solar System", "link": "https://wikipedia.org/wiki/Solar_System"}, {"title": "Neptune", "link": "https://wikipedia.org/wiki/Neptune"}]}, {"year": "1990", "text": "First day of the June 1990 Mineriad in Romania. At least 240 strikers and students are arrested or killed in the chaos ensuing from the first post-Ceaușescu elections.", "html": "1990 - First day of the <a href=\"https://wikipedia.org/wiki/June_1990_Mineriad\" title=\"June 1990 Mineriad\">June 1990 Mineriad</a> in Romania. At least 240 strikers and students are arrested or killed in the chaos ensuing from the first post-<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> elections.", "no_year_html": "First day of the <a href=\"https://wikipedia.org/wiki/June_1990_Mineriad\" title=\"June 1990 Mineriad\">June 1990 Mineriad</a> in Romania. At least 240 strikers and students are arrested or killed in the chaos ensuing from the first post-<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> elections.", "links": [{"title": "June 1990 Mineriad", "link": "https://wikipedia.org/wiki/June_1990_Mineriad"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}]}, {"year": "1994", "text": "A jury in Anchorage, Alaska, blames recklessness by Exxon and Captain <PERSON> for the Exxon Valdez disaster, allowing victims of the oil spill to seek $15 billion in damages.", "html": "1994 - A jury in <a href=\"https://wikipedia.org/wiki/Anchorage,_Alaska\" title=\"Anchorage, Alaska\">Anchorage, Alaska</a>, blames recklessness by <a href=\"https://wikipedia.org/wiki/Exxon\" class=\"mw-redirect\" title=\"Exxon\">Exxon</a> and Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> for the <i><a href=\"https://wikipedia.org/wiki/Exxon_Valdez\" title=\"Exxon Valdez\">Exxon Valdez</a></i> disaster, allowing victims of the oil spill to seek $15 billion in damages.", "no_year_html": "A jury in <a href=\"https://wikipedia.org/wiki/Anchorage,_Alaska\" title=\"Anchorage, Alaska\">Anchorage, Alaska</a>, blames recklessness by <a href=\"https://wikipedia.org/wiki/Exxon\" class=\"mw-redirect\" title=\"Exxon\">Exxon</a> and Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> for the <i><a href=\"https://wikipedia.org/wiki/Exxon_Valdez\" title=\"Exxon Valdez\">Exxon Valdez</a></i> disaster, allowing victims of the oil spill to seek $15 billion in damages.", "links": [{"title": "Anchorage, Alaska", "link": "https://wikipedia.org/wiki/Anchorage,_Alaska"}, {"title": "Exxon", "link": "https://wikipedia.org/wiki/Exxon"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Exxon Valdez", "link": "https://wikipedia.org/wiki/Exxon_Valdez"}]}, {"year": "1996", "text": "The Montana Freemen surrender after an 81-day standoff with FBI agents.", "html": "1996 - The <a href=\"https://wikipedia.org/wiki/Montana_Freemen\" title=\"Montana Freemen\">Montana Freemen</a> surrender after an 81-day standoff with <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montana_Freemen\" title=\"Montana Freemen\">Montana Freemen</a> surrender after an 81-day standoff with <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agents.", "links": [{"title": "Montana Freemen", "link": "https://wikipedia.org/wiki/Montana_Freemen"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "1996", "text": "Garuda Indonesia flight 865 crashes during takeoff from Fukuoka Airport, killing three people and injuring 170.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_865\" title=\"Garuda Indonesia Flight 865\">Garuda Indonesia flight 865</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Fukuoka_Airport\" title=\"Fukuoka Airport\">Fukuoka Airport</a>, killing three people and injuring 170.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garuda_Indonesia_Flight_865\" title=\"Garuda Indonesia Flight 865\">Garuda Indonesia flight 865</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Fukuoka_Airport\" title=\"Fukuoka Airport\">Fukuoka Airport</a>, killing three people and injuring 170.", "links": [{"title": "Garuda Indonesia Flight 865", "link": "https://wikipedia.org/wiki/Garuda_Indonesia_Flight_865"}, {"title": "Fukuoka Airport", "link": "https://wikipedia.org/wiki/Fukuoka_Airport"}]}, {"year": "1997", "text": "A jury sentences <PERSON> to death for his part in the 1995 Oklahoma City bombing.", "html": "1997 - A jury sentences <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to death for his part in the <a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>.", "no_year_html": "A jury sentences <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to death for his part in the <a href=\"https://wikipedia.org/wiki/1995\" title=\"1995\">1995</a> <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1995", "link": "https://wikipedia.org/wiki/1995"}, {"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}]}, {"year": "1997", "text": "The Uphaar Cinema Fire took place at Green Park, Delhi, resulting in the deaths of 59 people and seriously injured 103 others.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Uphaar_Cinema_fire\" title=\"Uphaar Cinema fire\">Uphaar Cinema Fire</a> took place at <a href=\"https://wikipedia.org/wiki/Green_Park,_Delhi\" title=\"Green Park, Delhi\">Green Park</a>, <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, resulting in the deaths of 59 people and seriously injured 103 others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Uphaar_Cinema_fire\" title=\"Uphaar Cinema fire\">Uphaar Cinema Fire</a> took place at <a href=\"https://wikipedia.org/wiki/Green_Park,_Delhi\" title=\"Green Park, Delhi\">Green Park</a>, <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, resulting in the deaths of 59 people and seriously injured 103 others.", "links": [{"title": "Uphaar Cinema fire", "link": "https://wikipedia.org/wiki/Uphaar_Cinema_fire"}, {"title": "Green Park, Delhi", "link": "https://wikipedia.org/wiki/Green_Park,_Delhi"}, {"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}]}, {"year": "1999", "text": "BMW win 1999 24 Hours of Le Mans", "html": "1999 - <a href=\"https://wikipedia.org/wiki/BMW\" title=\"BMW\">BMW</a> win <a href=\"https://wikipedia.org/wiki/1999_24_Hours_of_Le_Mans\" title=\"1999 24 Hours of Le Mans\">1999 24 Hours of Le Mans</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BMW\" title=\"BMW\">BMW</a> win <a href=\"https://wikipedia.org/wiki/1999_24_Hours_of_Le_Mans\" title=\"1999 24 Hours of Le Mans\">1999 24 Hours of Le Mans</a>", "links": [{"title": "BMW", "link": "https://wikipedia.org/wiki/BMW"}, {"title": "1999 24 Hours of Le Mans", "link": "https://wikipedia.org/wiki/1999_24_Hours_of_Le_Mans"}]}, {"year": "2000", "text": "President <PERSON> of South Korea meets <PERSON>, leader of North Korea, for the beginning of the first ever inter-Korea summit, in the northern capital of Pyongyang.", "html": "2000 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of South Korea meets <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, leader of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>, for the beginning of the first ever <a href=\"https://wikipedia.org/wiki/Inter-Korean_summits\" title=\"Inter-Korean summits\">inter-Korea summit</a>, in the northern capital of <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of South Korea meets <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, leader of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a>, for the beginning of the first ever <a href=\"https://wikipedia.org/wiki/Inter-Korean_summits\" title=\"Inter-Korean summits\">inter-Korea summit</a>, in the northern capital of <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jung"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Inter-Korean summits", "link": "https://wikipedia.org/wiki/Inter-Korean_summits"}, {"title": "Pyongyang", "link": "https://wikipedia.org/wiki/Pyongyang"}]}, {"year": "2000", "text": "Italy pardons <PERSON><PERSON><PERSON>, the Turkish gunman who tried to kill Pope <PERSON> in 1981.", "html": "2000 - Italy pardons <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the Turkish gunman who tried to kill <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON> II\">Pope <PERSON></a> in 1981.", "no_year_html": "Italy pardons <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C4%9Fca\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the Turkish gunman who tried to kill <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> in 1981.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_A%C4%9Fca"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "The United States withdraws from the Anti-Ballistic Missile Treaty.", "html": "2002 - The United States withdraws from the <a href=\"https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty\" title=\"Anti-Ballistic Missile Treaty\">Anti-Ballistic Missile Treaty</a>.", "no_year_html": "The United States withdraws from the <a href=\"https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty\" title=\"Anti-Ballistic Missile Treaty\">Anti-Ballistic Missile Treaty</a>.", "links": [{"title": "Anti-Ballistic Missile Treaty", "link": "https://wikipedia.org/wiki/Anti-Ballistic_Missile_Treaty"}]}, {"year": "2005", "text": "The jury acquits pop singer <PERSON> of his charges for allegedly sexually molesting a child in 1993.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>\" title=\"Trial of <PERSON>\">The jury</a> <a href=\"https://wikipedia.org/wiki/Acquittal\" title=\"Acquittal\">acquits</a> pop singer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of his charges for <a href=\"https://wikipedia.org/wiki/1993_child_sexual_abuse_accusations_against_<PERSON>\" class=\"mw-redirect\" title=\"1993 child sexual abuse accusations against <PERSON>\">allegedly sexually molesting a child in 1993</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>\" title=\"Trial of <PERSON>\">The jury</a> <a href=\"https://wikipedia.org/wiki/Acquittal\" title=\"Acquittal\">acquits</a> pop singer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of his charges for <a href=\"https://wikipedia.org/wiki/1993_child_sexual_abuse_accusations_against_<PERSON>\" class=\"mw-redirect\" title=\"1993 child sexual abuse accusations against <PERSON>\">allegedly sexually molesting a child in 1993</a>.", "links": [{"title": "Trial of <PERSON>", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON>_<PERSON>"}, {"title": "Acquittal", "link": "https://wikipedia.org/wiki/Acquittal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1993 child sexual abuse accusations against <PERSON>", "link": "https://wikipedia.org/wiki/1993_child_sexual_abuse_accusations_against_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "The Al Askari Mosque is bombed for a second time.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Al_Askari_Mosque\" class=\"mw-redirect\" title=\"Al Askari Mosque\">Al Askari Mosque</a> is <a href=\"https://wikipedia.org/wiki/2007_al-Askari_Mosque_bombing\" class=\"mw-redirect\" title=\"2007 al-Askari Mosque bombing\">bombed for a second time</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Al_Askari_Mosque\" class=\"mw-redirect\" title=\"Al Askari Mosque\">Al Askari Mosque</a> is <a href=\"https://wikipedia.org/wiki/2007_al-Askari_Mosque_bombing\" class=\"mw-redirect\" title=\"2007 al-Askari Mosque bombing\">bombed for a second time</a>.", "links": [{"title": "Al Askari Mosque", "link": "https://wikipedia.org/wiki/Al_Askari_Mosque"}, {"title": "2007 al-Askari Mosque bombing", "link": "https://wikipedia.org/wiki/2007_al-Askari_Mosque_bombing"}]}, {"year": "2010", "text": "A capsule of the Japanese spacecraft Hayabusa, containing particles of the asteroid 25143 Itokawa, returns to Earth by landing in the Australian Outback.", "html": "2010 - A capsule of the Japanese spacecraft <i><a href=\"https://wikipedia.org/wiki/Hayabusa\" title=\"Hayabusa\">Hayabusa</a></i>, containing particles of the asteroid <a href=\"https://wikipedia.org/wiki/25143_Itokawa\" title=\"25143 Itokawa\">25143 Itokawa</a>, returns to Earth by landing in the Australian Outback.", "no_year_html": "A capsule of the Japanese spacecraft <i><a href=\"https://wikipedia.org/wiki/Hay<PERSON><PERSON>\" title=\"Hay<PERSON>usa\">Hay<PERSON>usa</a></i>, containing particles of the asteroid <a href=\"https://wikipedia.org/wiki/25143_Itokawa\" title=\"25143 Itokawa\">25143 Itokawa</a>, returns to Earth by landing in the Australian Outback.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hay<PERSON><PERSON>"}, {"title": "25143 Itokawa", "link": "https://wikipedia.org/wiki/25143_<PERSON><PERSON>wa"}]}, {"year": "2012", "text": "A series of bombings across Iraq, including Baghdad, Hillah and Kirkuk, kills at least 93 people and wounds over 300 others.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/13_June_2012_Iraq_attacks\" title=\"13 June 2012 Iraq attacks\">series of bombings</a> across <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, including <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, <a href=\"https://wikipedia.org/wiki/Hillah\" title=\"Hillah\">Hillah</a> and <a href=\"https://wikipedia.org/wiki/Kirkuk\" title=\"Kirkuk\">Kirk<PERSON></a>, kills at least 93 people and wounds over 300 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/13_June_2012_Iraq_attacks\" title=\"13 June 2012 Iraq attacks\">series of bombings</a> across <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, including <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, <a href=\"https://wikipedia.org/wiki/Hillah\" title=\"Hillah\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Kirkuk\" title=\"Kirkuk\"><PERSON><PERSON></a>, kills at least 93 people and wounds over 300 others.", "links": [{"title": "13 June 2012 Iraq attacks", "link": "https://wikipedia.org/wiki/13_June_2012_Iraq_attacks"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hillah"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kirkuk"}]}, {"year": "2015", "text": "A man opens fire at policemen outside the police headquarters in Dallas, Texas, while a bag containing a pipe bomb is also found. He was later shot dead by police.", "html": "2015 - A man <a href=\"https://wikipedia.org/wiki/2015_attack_on_Dallas_police\" class=\"mw-redirect\" title=\"2015 attack on Dallas police\">opens fire</a> at policemen outside the <a href=\"https://wikipedia.org/wiki/Dallas_Police_Department\" title=\"Dallas Police Department\">police headquarters in Dallas, Texas</a>, while a bag containing a <a href=\"https://wikipedia.org/wiki/Pipe_bomb\" title=\"Pipe bomb\">pipe bomb</a> is also found. He was later shot dead by police.", "no_year_html": "A man <a href=\"https://wikipedia.org/wiki/2015_attack_on_Dallas_police\" class=\"mw-redirect\" title=\"2015 attack on Dallas police\">opens fire</a> at policemen outside the <a href=\"https://wikipedia.org/wiki/Dallas_Police_Department\" title=\"Dallas Police Department\">police headquarters in Dallas, Texas</a>, while a bag containing a <a href=\"https://wikipedia.org/wiki/Pipe_bomb\" title=\"Pipe bomb\">pipe bomb</a> is also found. He was later shot dead by police.", "links": [{"title": "2015 attack on Dallas police", "link": "https://wikipedia.org/wiki/2015_attack_on_Dallas_police"}, {"title": "Dallas Police Department", "link": "https://wikipedia.org/wiki/Dallas_Police_Department"}, {"title": "Pipe bomb", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_bomb"}]}, {"year": "2018", "text": "Volkswagen is fined one billion euros over the emissions scandal.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Volkswagen\" title=\"Volkswagen\">Volkswagen</a> is fined one billion euros over the <a href=\"https://wikipedia.org/wiki/Volkswagen_emissions_scandal\" title=\"Volkswagen emissions scandal\">emissions scandal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volkswagen\" title=\"Volkswagen\">Volkswagen</a> is fined one billion euros over the <a href=\"https://wikipedia.org/wiki/Volkswagen_emissions_scandal\" title=\"Volkswagen emissions scandal\">emissions scandal</a>.", "links": [{"title": "Volkswagen", "link": "https://wikipedia.org/wiki/Volkswagen"}, {"title": "Volkswagen emissions scandal", "link": "https://wikipedia.org/wiki/Volkswagen_emissions_scandal"}]}, {"year": "2021", "text": "A gas explosion in Zhangwan district of Shiyan city, in Hubei province of China kills at least 12 people and wounds over 138 others.", "html": "2021 - A <a href=\"https://wikipedia.org/wiki/2021_Shiyan_pipeline_explosion\" title=\"2021 Shiyan pipeline explosion\">gas explosion</a> in <a href=\"https://wikipedia.org/wiki/Zhangwan_District\" class=\"mw-redirect\" title=\"Zhangwan District\">Zhangwan</a> district of Shiyan city, in Hubei province of China kills at least 12 people and wounds over 138 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2021_Shiyan_pipeline_explosion\" title=\"2021 Shiyan pipeline explosion\">gas explosion</a> in <a href=\"https://wikipedia.org/wiki/Zhangwan_District\" class=\"mw-redirect\" title=\"Zhangwan District\">Zhangwan</a> district of Shiyan city, in Hubei province of China kills at least 12 people and wounds over 138 others.", "links": [{"title": "2021 Shiyan pipeline explosion", "link": "https://wikipedia.org/wiki/2021_<PERSON><PERSON>_pipeline_explosion"}, {"title": "Zhangwan District", "link": "https://wikipedia.org/wiki/Zhangwan_District"}]}, {"year": "2023", "text": "At least 100 people are killed when a wedding boat capsizes on the Niger River in Kwara State, Nigeria.", "html": "2023 - At least 100 people are killed when a wedding boat <a href=\"https://wikipedia.org/wiki/Kwara_boat_disaster\" title=\"Kwara boat disaster\">capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Niger_River\" title=\"Niger River\">Niger River</a> in <a href=\"https://wikipedia.org/wiki/Kwara_State\" title=\"Kwara State\">Kwara State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>.", "no_year_html": "At least 100 people are killed when a wedding boat <a href=\"https://wikipedia.org/wiki/Kwara_boat_disaster\" title=\"Kwara boat disaster\">capsizes</a> on the <a href=\"https://wikipedia.org/wiki/Niger_River\" title=\"Niger River\">Niger River</a> in <a href=\"https://wikipedia.org/wiki/Kwara_State\" title=\"Kwara State\">Kwara State</a>, <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>.", "links": [{"title": "Kwara boat disaster", "link": "https://wikipedia.org/wiki/Kwara_boat_disaster"}, {"title": "Niger River", "link": "https://wikipedia.org/wiki/Niger_River"}, {"title": "Kwara State", "link": "https://wikipedia.org/wiki/Kwara_State"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}]}, {"year": "2023", "text": "Three people are killed and another three injured in an early morning stabbing and van ramming attack in Nottingham, England.", "html": "2023 - Three people are killed and another three injured in an early morning <a href=\"https://wikipedia.org/wiki/2023_Nottingham_attacks\" title=\"2023 Nottingham attacks\">stabbing and van ramming attack</a> in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "no_year_html": "Three people are killed and another three injured in an early morning <a href=\"https://wikipedia.org/wiki/2023_Nottingham_attacks\" title=\"2023 Nottingham attacks\">stabbing and van ramming attack</a> in <a href=\"https://wikipedia.org/wiki/Nottingham\" title=\"Nottingham\">Nottingham</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "links": [{"title": "2023 Nottingham attacks", "link": "https://wikipedia.org/wiki/2023_Nottingham_attacks"}, {"title": "Nottingham", "link": "https://wikipedia.org/wiki/Nottingham"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}], "Births": [{"year": "40", "text": "<PERSON><PERSON><PERSON>, Roman general (d. 93)", "html": "40 - AD 40 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general (d. 93)", "no_year_html": "AD 40 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general (d. 93)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "823", "text": "<PERSON> the <PERSON>, Holy Roman Emperor (d. 877)", "html": "823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bald\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a>, Holy Roman Emperor (d. 877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ba<PERSON>\" title=\"<PERSON> the Bald\"><PERSON> the Bald</a>, Holy Roman Emperor (d. 877)", "links": [{"title": "<PERSON> the <PERSON>ld", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "839", "text": "<PERSON> the <PERSON>, Holy Roman Emperor (d. 888)", "html": "839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fat\" title=\"<PERSON> the Fat\"><PERSON> the <PERSON></a>, Holy Roman Emperor (d. 888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Fat\"><PERSON></a>, Holy Roman Emperor (d. 888)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fat"}]}, {"year": "1367", "text": "<PERSON><PERSON><PERSON> of Joseon (d. 1422)", "html": "1367 - <a href=\"https://wikipedia.org/wiki/Taejong_of_Joseon\" title=\"Taejong of Joseon\">Taejong of Joseon</a> (d. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taejong_of_Joseon\" title=\"Taejong of Joseon\">Taejong of Joseon</a> (d. 1422)", "links": [{"title": "Taejong of Joseon", "link": "https://wikipedia.org/wiki/Taejong_of_Joseon"}]}, {"year": "1500", "text": "<PERSON> of Bavaria, pledge lord of the County of Glatz (d. 1560)", "html": "1500 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria_(1500%E2%80%931560)\" title=\"<PERSON> of Bavaria (1500-1560)\"><PERSON> of Bavaria</a>, pledge lord of the County of Glatz (d. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria_(1500%E2%80%931560)\" title=\"<PERSON> of Bavaria (1500-1560)\"><PERSON> of Bavaria</a>, pledge lord of the County of Glatz (d. 1560)", "links": [{"title": "<PERSON> of Bavaria (1500-1560)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria_(1500%E2%80%931560)"}]}, {"year": "1508", "text": "<PERSON>, Italian astronomer and philosopher (d. 1579)", "html": "1508 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian astronomer and philosopher (d. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1539", "text": "<PERSON><PERSON>, Swiss printmaker (d. 1591)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Jo<PERSON>\"><PERSON><PERSON></a>, Swiss printmaker (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Jo<PERSON>\"><PERSON><PERSON></a>, Swiss printmaker (d. 1591)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, Italian mathematician, cartographer and astronomer (d. 1617)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician, cartographer and astronomer (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician, cartographer and astronomer (d. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch astronomer and mathematician (d. 1626)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/Will<PERSON><PERSON><PERSON>_<PERSON>nell\" class=\"mw-redirect\" title=\"Will<PERSON><PERSON><PERSON> Snell\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch astronomer and mathematician (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will<PERSON><PERSON><PERSON>_<PERSON>nell\" class=\"mw-redirect\" title=\"Will<PERSON><PERSON><PERSON> Snell\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch astronomer and mathematician (d. 1626)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1595", "text": "<PERSON>, Czech physician and scientist (d. 1667)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and scientist (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and scientist (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "Sir <PERSON>, 1st Baronet, English politician (d. 1656)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English politician (d. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English politician (d. 1656)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1649", "text": "<PERSON><PERSON>, French scholar and critic (d. 1706)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French scholar and critic (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French scholar and critic (d. 1706)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1711", "text": "Sir <PERSON>, 1st Baronet, of Ewell, English banker and politician, Lord Mayor of London (d. 1773)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet,_of_Ewell\" title=\"Sir <PERSON>, 1st Baronet, of Ewell\">Sir <PERSON>, 1st Baronet, of Ewell</a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet,_of_Ewell\" title=\"Sir <PERSON>, 1st Baronet, of Ewell\">Sir <PERSON>, 1st Baronet, of Ewell</a>, English banker and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a> (d. 1773)", "links": [{"title": "Sir <PERSON>, 1st Baronet, of Ewell", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet,_of_Ewell"}, {"title": "Lord Mayor of London", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_London"}]}, {"year": "1752", "text": "<PERSON>, English novelist and playwright (d. 1840)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON><PERSON>, Czech violinist and composer (d. 1820)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Vranick%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech violinist and composer (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Vranick%C3%BD\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech violinist and composer (d. 1820)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Vranick%C3%BD"}]}, {"year": "1763", "text": "<PERSON>, Brazilian poet, academic, and politician (d. 1838)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%<PERSON><PERSON>_de_Andrada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, academic, and politician (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%<PERSON><PERSON>_de_Andrada\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian poet, academic, and politician (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Bonif%C3%A1<PERSON>_de_Andrada"}]}, {"year": "1773", "text": "<PERSON>, English physicist and physiologist (d. 1829)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English physicist and physiologist (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, English physicist and physiologist (d. 1829)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)"}]}, {"year": "1775", "text": "<PERSON><PERSON>, Polish-Lithuanian composer and politician (d. 1833)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Radziwi%C5%82%C5%82\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian composer and politician (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Radziwi%C5%82%C5%82\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Lithuanian composer and politician (d. 1833)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_Radziwi%C5%82%C5%82"}]}, {"year": "1786", "text": "<PERSON>, American general (d. 1866)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, Venezuelan general and politician, President of Venezuela (d. 1873)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_P%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_P%C3%A1ez"}, {"title": "List of Presidents of Venezuela", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela"}]}, {"year": "1809", "text": "<PERSON>, German psychiatrist and author (d. 1894)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German psychiatrist and author (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, German psychiatrist and author (d. 1894)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>(author)"}]}, {"year": "1822", "text": "<PERSON>, Latvian-German chemist and academic (d. 1894)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Latvian-German chemist and academic (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(chemist)\" title=\"<PERSON> (chemist)\"><PERSON></a>, Latvian-German chemist and academic (d. 1894)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_(chemist)"}]}, {"year": "1827", "text": "<PERSON>, German-Brazilian photographer and businessman (d. 1882)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian photographer and businessman (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian photographer and businessman (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Scottish physicist and mathematician (d. 1879)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and mathematician (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and mathematician (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, the first international Swedish fashion designer (d. 1919)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Augusta_Lundin\" title=\"<PERSON>\"><PERSON></a>, the first international Swedish fashion designer (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta_Lundin\" title=\"<PERSON> Lund<PERSON>\"><PERSON></a>, the first international Swedish fashion designer (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augusta_Lundin"}]}, {"year": "1854", "text": "<PERSON>, English engineer, founded C. A. Parsons and Company (d. 1931)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/C._A._Parsons_and_Company\" title=\"C. A. Parsons and Company\">C. A. Parsons and Company</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/C._A._Parsons_and_Company\" title=\"C. A. Parsons and Company\">C. A. Parsons and Company</a> (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "C. A. Parsons and Company", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Parsons_and_Company"}]}, {"year": "1863", "text": "<PERSON>, <PERSON><PERSON>, English fashion designer (d. 1935)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English fashion designer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, English fashion designer (d. 1935)", "links": [{"title": "<PERSON>, <PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Swedish political scientist and academic (d. 1922)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish political scientist and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Swedish political scientist and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n"}]}, {"year": "1864", "text": "<PERSON>, American historian and academic (d. 1939)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, German photographer (d. 1932)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish poet and playwright, Nobel Prize laureate (d. 1939)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/W._<PERSON>._Yeats\" title=\"W<PERSON> B<PERSON> Yeats\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON>._Yeats\" title=\"W. B. Yeats\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1939)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_Yeats"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1868", "text": "<PERSON>, American physicist and academic (d. 1919)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Belgian immunologist and microbiologist, Nobel Prize laureate (d. 1961)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian immunologist and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian immunologist and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1872", "text": "<PERSON>, American actor, director, and screenwriter (d. 1951)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Swedish actress, director, and producer (d. 1942)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish actress, director, and producer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m\" title=\"<PERSON>\"><PERSON></a>, Swedish actress, director, and producer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6m"}]}, {"year": "1875", "text": "<PERSON>, Austrian swimmer and physician (d. 1932)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Austrian swimmer and physician (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Austrian swimmer and physician (d. 1932)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>(swimmer)"}]}, {"year": "1876", "text": "<PERSON>, English chemist and statistician (d. 1937)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and statistician (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and statistician (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Go<PERSON>t"}]}, {"year": "1879", "text": "<PERSON>, Estonian businessman and politician (d. 1941)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian businessman and politician (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian businessman and politician (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek general and politician, Greek Minister for Military Affairs (d. 1929)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Charalambos_Tser<PERSON>lis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister for Military Affairs</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charalambos_Tser<PERSON>lis\" title=\"<PERSON>ral<PERSON><PERSON>ser<PERSON>lis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister for Military Affairs</a> (d. 1929)", "links": [{"title": "Charalam<PERSON>", "link": "https://wikipedia.org/wiki/Charalambos_<PERSON>lis"}, {"title": "List of defence ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece"}]}, {"year": "1884", "text": "<PERSON>, Polish painter, philosopher, and mathematician (d. 1944)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter, philosopher, and mathematician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter, philosopher, and mathematician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, French philosopher and academic (d. 1978)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON><PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Australian farmer and author (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian farmer and author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, French politician and diplomat (d. 1978)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>an%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and diplomat (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>an%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and diplomat (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>an%C3%A7<PERSON>-<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German-American author, poet, and playwright (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author, poet, and playwright (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American author, poet, and playwright (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Portuguese poet and critic (d. 1935)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese poet and critic (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese poet and critic (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Chinese painter (d. 1933)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese painter (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, South African-born British-American actor (d. 1967)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born British-American actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born British-American actor (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English engineer (d. 1963)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, English author and poet (d. 1957)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Ukrainian-American psychiatrist and physician (d. 1981)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American psychiatrist and physician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American psychiatrist and physician (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, French photographer and painter (d. 1986)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and painter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and painter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Finnish runner and coach (d. 1973)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner and coach (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner and coach (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>av<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Mexican composer, conductor, and journalist, founded the Mexican Symphonic Orchestra (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez\" title=\"<PERSON>\"><PERSON></a>, Mexican composer, conductor, and journalist, founded the <a href=\"https://wikipedia.org/wiki/Mexican_Symphonic_Orchestra\" class=\"mw-redirect\" title=\"Mexican Symphonic Orchestra\">Mexican Symphonic Orchestra</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez\" title=\"<PERSON>\"><PERSON></a>, Mexican composer, conductor, and journalist, founded the <a href=\"https://wikipedia.org/wiki/Mexican_Symphonic_Orchestra\" class=\"mw-redirect\" title=\"Mexican Symphonic Orchestra\">Mexican Symphonic Orchestra</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Ch%C3%A1vez"}, {"title": "Mexican Symphonic Orchestra", "link": "https://wikipedia.org/wiki/Mexican_Symphonic_Orchestra"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Swedish lieutenant and politician, 25th Prime Minister of Sweden (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Tage_E<PERSON>er\" title=\"Tage Erlander\"><PERSON><PERSON></a>, Swedish lieutenant and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tag<PERSON>_E<PERSON>er\" title=\"Tage Erlander\"><PERSON><PERSON></a>, Swedish lieutenant and politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1985)", "links": [{"title": "Tage Erlander", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1902", "text": "<PERSON>, American mathematician and historian (d. 2000)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and historian (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and historian (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American physicist and chemist (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and chemist (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Sri Lankan historian and author (d. 1988)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan historian and author (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan historian and author (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Austrian-Italian mathematician and statistician (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Italian mathematician and statistician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Italian mathematician and statistician (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian theorist and politician, 1st Chief Minister of Kerala (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/E._M._S._Namboodiripad\" title=\"E. M. S. Namboodiripad\">E. M<PERSON> <PERSON><PERSON>boodirip<PERSON></a>, Indian theorist and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._M._S._Namboodiripad\" title=\"E. M. S. Namboodiripad\"><PERSON><PERSON> M<PERSON> <PERSON><PERSON></a>, Indian theorist and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (d. 1998)", "links": [{"title": "E. M. S. <PERSON>", "link": "https://wikipedia.org/wiki/E._M._S._Namboodiripad"}, {"title": "Chief Minister of Kerala", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Kerala"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Spanish journalist, author, and playwright (d. 1999)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish journalist, author, and playwright (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish journalist, author, and playwright (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1995)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English activist, founded the National Viewers' and Listeners' Association (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, founded the <a href=\"https://wikipedia.org/wiki/National_Viewers%27_and_Listeners%27_Association\" title=\"National Viewers' and Listeners' Association\">National Viewers' and Listeners' Association</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist, founded the <a href=\"https://wikipedia.org/wiki/National_Viewers%27_and_Listeners%27_Association\" title=\"National Viewers' and Listeners' Association\">National Viewers' and Listeners' Association</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mary_<PERSON>"}, {"title": "National Viewers' and Listeners' Association", "link": "https://wikipedia.org/wiki/National_Viewers%27_and_Listeners%27_Association"}]}, {"year": "1911", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 1988)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1911", "text": "<PERSON>, American actor (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, German physicist and academic (d. 1977)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCller"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Canadian poet and painter (d. 1943)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>ar<PERSON>u\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and painter (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>u\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and painter (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Den<PERSON>_Garneau"}]}, {"year": "1913", "text": "<PERSON>, American radio and television host (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Israeli general, diplomat and politician (d. 2017)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general, diplomat and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli general, diplomat and politician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English-American ballet dancer and director (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American ballet dancer and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American ballet dancer and director (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American tennis player and coach (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Chinese botanist and academic (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese botanist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese botanist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English actor (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Paraguayan novelist (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Augusto Roa Basto<PERSON>\"><PERSON><PERSON></a>, Paraguayan novelist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"August<PERSON> Roa Bastos\"><PERSON><PERSON></a>, Paraguayan novelist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor and stuntman (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and stuntman (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and stuntman (d. 1996)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1918", "text": "<PERSON>, German soldier and pilot (d. 1944)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and pilot (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian-American actor (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German chemist and academic (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and engineer (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and engineer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and engineer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Swedish runner (d. 2004)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Lennart_Strand\" title=\"Lennart Strand\"><PERSON><PERSON><PERSON></a>, Swedish runner (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lennart_Strand\" title=\"Lennart Strand\"><PERSON><PERSON><PERSON></a>, Swedish runner (d. 2004)", "links": [{"title": "Len<PERSON>t Strand", "link": "https://wikipedia.org/wiki/Lennart_Strand"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, South African author (d. 1989)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African author (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American chemist and inventor (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and inventor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American actress (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON><PERSON>, French pediatrician and geneticist (d. 1994)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Le<PERSON>une\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pediatrician and geneticist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Le<PERSON>une\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French pediatrician and geneticist (d. 1994)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%B4me_Lejeune"}]}, {"year": "1926", "text": "<PERSON>, American actor and comedian (d. 1982)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Australian singer-songwriter and guitarist (d. 2003)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Slim_Dusty\" title=\"Slim Dusty\"><PERSON></a>, Australian singer-songwriter and guitarist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Slim_Dusty\" title=\"Slim Dusty\"><PERSON></a>, Australian singer-songwriter and guitarist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Italian cardinal (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>i"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Canadian pianist (d. 2009)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Mo<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian pianist (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON><PERSON>set"}]}, {"year": "1928", "text": "<PERSON>, Jr., American mathematician and academic, Nobel Prize laureate (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American mathematician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American mathematician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1929", "text": "<PERSON>, American illustrator (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American farmer and politician, 67th Governor of North Carolina (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 67th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, German painter and educator (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ubner\"><PERSON><PERSON><PERSON></a>, German painter and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Graubner\"><PERSON><PERSON><PERSON></a>, German painter and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Polish colonel and spy (d. 2004)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish colonel and spy (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish colonel and spy (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski"}]}, {"year": "1930", "text": "<PERSON>, French archaeologist, historian, and academic (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist, historian, and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French archaeologist, historian, and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Hungarian-American ballerina (d. 2009)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American ballerina (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American ballerina (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian politician (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>en"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American psychotherapist and academic", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychotherapist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American psychotherapist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1932", "text": "<PERSON>, 5th Baron <PERSON>, English politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>\" title=\"<PERSON>, 5th Baron <PERSON>\"><PERSON>, 5th Baron <PERSON></a>, English politician", "links": [{"title": "<PERSON>, 5th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Baron_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer and actor (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American baseball player and coach (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right_fielder)\" class=\"mw-redirect\" title=\"<PERSON> (right fielder)\"><PERSON></a>, American baseball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right_fielder)\" class=\"mw-redirect\" title=\"<PERSON> (right fielder)\"><PERSON></a>, American baseball player and coach (d. 2013)", "links": [{"title": "<PERSON> (right fielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(right_fielder)"}]}, {"year": "1933", "text": "<PERSON>, Baron King of Bridgwater, English soldier and politician, Secretary of State for Defence", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_King_of_Bridgwater\" title=\"<PERSON>, Baron King of Bridgwater\"><PERSON>, Baron King of Bridgwater</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_King_of_Bridgwater\" title=\"<PERSON>, Baron King of Bridgwater\"><PERSON>, Baron King of Bridgwater</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "links": [{"title": "<PERSON>, Baron King of Bridgwater", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_King_of_Bridgwater"}, {"title": "Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Defence"}]}, {"year": "1933", "text": "<PERSON>, Welsh lawyer and politician, Lord Lieutenant of South Glamorgan", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_South_Glamorgan\" title=\"Lord Lieutenant of South Glamorgan\">Lord Lieutenant of South Glamorgan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_South_Glamorgan\" title=\"Lord Lieutenant of South Glamorgan\">Lord Lieutenant of South Glamorgan</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of South Glamorgan", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_South_Glamorgan"}]}, {"year": "1934", "text": "<PERSON>, American basketball player and coach (d. 2010)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Polish footballer and coach (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer and coach (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Mexican businessman and politician (d. 1989)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American bishop", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American drummer (d. 2009)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American computer scientist and engineer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Bulgarian-French sculptor and painter (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian-French sculptor and painter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian-French sculptor and painter (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Christo"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Moroccan sculptor and painter (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan sculptor and painter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan sculptor and painter (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Thai politician, 25th Prime Minister of Thailand (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>j\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai politician, 25th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ak_<PERSON>avej"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, German footballer and manager", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English journalist and publisher, co-founded The Independent", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Independent\" title=\"The Independent\">The Independent</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Independent\" title=\"The Independent\">The Independent</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Independent", "link": "https://wikipedia.org/wiki/The_Independent"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter, pianist, and producer (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American shot putter and physician (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Dallas_Long\" title=\"Dallas Long\"><PERSON></a>, American shot putter and physician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dallas_Long\" title=\"Dallas Long\"><PERSON></a>, American shot putter and physician (d. 2024)", "links": [{"title": "Dallas Long", "link": "https://wikipedia.org/wiki/Dallas_Long"}]}, {"year": "1941", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian painter (d. 1998)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American guitarist and songwriter (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Marv_<PERSON><PERSON>\" title=\"<PERSON>v Tarplin\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marv_<PERSON><PERSON>\" title=\"<PERSON>v Tarplin\"><PERSON><PERSON></a>, American guitarist and songwriter (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON><PERSON>lin"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Greek businessman and politician, Mayor of Thessaloniki", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Thessaloniki\" title=\"List of mayors of Thessaloniki\">Mayor of Thessaloniki</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek businessman and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Thessaloniki\" title=\"List of mayors of Thessaloniki\">Mayor of Thessaloniki</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of mayors of Thessaloniki", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Thessaloniki"}]}, {"year": "1943", "text": "<PERSON>, English sociologist, author, and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sociologist, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>,  American lawyer and politician, 43rd Governor of Arkansas (d. 2025)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1944", "text": "<PERSON>, English nursing administrator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nursing administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nursing administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist and politician, Secretary of State for Communities and Local Government", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government\" class=\"mw-redirect\" title=\"Secretary of State for Communities and Local Government\">Secretary of State for Communities and Local Government</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government\" class=\"mw-redirect\" title=\"Secretary of State for Communities and Local Government\">Secretary of State for Communities and Local Government</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Communities and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government"}]}, {"year": "1944", "text": "<PERSON>, South Korean politician and diplomat, 8th Secretary-General of the United Nations", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean politician and diplomat, 8th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean politician and diplomat, 8th <a href=\"https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations\" title=\"Secretary-General of the United Nations\">Secretary-General of the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Secretary-General_of_the_United_Nations"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, American author", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Whitley_Strieber\" title=\"Whit<PERSON> Strieber\"><PERSON><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whitley_Strieber\" title=\"Whit<PERSON> Strieber\"><PERSON><PERSON><PERSON></a>, American author", "links": [{"title": "W<PERSON>ley Strieber", "link": "https://wikipedia.org/wiki/Whitley_Strieber"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Nepalese politician, 32nd Prime Minister of Nepal", "html": "1946 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Nepalese politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Nepalese politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Nepal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Nepal"}]}, {"year": "1946", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1946", "text": "<PERSON> Komana, Belgian-Dutch archbishop (d. 2013)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Komana\" title=\"<PERSON> of Komana\"><PERSON> of Komana</a>, Belgian-Dutch archbishop (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ko<PERSON>\" title=\"<PERSON> of Komana\"><PERSON> of Komana</a>, Belgian-Dutch archbishop (d. 2013)", "links": [{"title": "<PERSON> of Komana", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Komana"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Canadian-American ice hockey player and scout (d. 2001)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American ice hockey player and scout (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian-American ice hockey player and scout (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gar<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American director and producer, co-founded Morgan Creek Productions", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/Morgan_Creek_Productions\" class=\"mw-redirect\" title=\"Morgan Creek Productions\">Morgan Creek Productions</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, co-founded <a href=\"https://wikipedia.org/wiki/Morgan_Creek_Productions\" class=\"mw-redirect\" title=\"Morgan Creek Productions\">Morgan Creek Productions</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Morgan Creek Productions", "link": "https://wikipedia.org/wiki/Morgan_Creek_Productions"}]}, {"year": "1949", "text": "<PERSON>, American popular science writer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American popular science writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American popular science writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer and musician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German educator and politician, German Federal Minister of Health", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)\" title=\"Federal Ministry of Health (Germany)\">German Federal Minister of Health</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)\" title=\"Federal Ministry of Health (Germany)\">German Federal Minister of Health</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Federal Ministry of Health (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Health_(Germany)"}]}, {"year": "1949", "text": "<PERSON>, English-Australian musician, television, and radio personality", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Red_Symons\" title=\"Red Symons\"><PERSON></a>, English-Australian musician, television, and radio personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Symons\" title=\"Red Symons\"><PERSON></a>, English-Australian musician, television, and radio personality", "links": [{"title": "<PERSON> S<PERSON>", "link": "https://wikipedia.org/wiki/Red_Symons"}]}, {"year": "1950", "text": "<PERSON>, English politician, Minister of Agriculture, Fisheries and Food", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food\" title=\"Minister of Agriculture, Fisheries and Food\">Minister of Agriculture, Fisheries and Food</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food\" title=\"Minister of Agriculture, Fisheries and Food\">Minister of Agriculture, Fisheries and Food</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Agriculture, Fisheries and Food", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture,_Fisheries_and_Food"}]}, {"year": "1950", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American guitarist and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor, director, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Swedish actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Stellan_Skarsg%C3%A5rd\" title=\"<PERSON><PERSON>gård\"><PERSON><PERSON></a>, Swedish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stellan_Skarsg%C3%A5rd\" title=\"<PERSON><PERSON>gård\"><PERSON><PERSON></a>, Swedish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stellan_Skarsg%C3%A5rd"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Belgian martial artist and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian martial artist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian martial artist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor, comedian, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Polish politician, Deputy Prime Minister of the Republic of Poland (d. 2011)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"Deputy Prime Minister of the Republic of Poland\">Deputy Prime Minister of the Republic of Poland</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Republic_of_Poland\" class=\"mw-redirect\" title=\"Deputy Prime Minister of the Republic of Poland\">Deputy Prime Minister of the Republic of Poland</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the Republic of Poland", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Republic_of_Poland"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Nigerian economist and politician, Minister of Foreign Affairs for Nigeria", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Minister of Foreign Affairs for Nigeria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian economist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)\" class=\"mw-redirect\" title=\"Minister of Foreign Affairs (Nigeria)\">Minister of Foreign Affairs for Nigeria</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Okonjo-<PERSON>"}, {"title": "Minister of Foreign Affairs (Nigeria)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Nigeria)"}]}, {"year": "1955", "text": "<PERSON>, Scottish footballer and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German-American lawyer and jurist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American lawyer and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American lawyer and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian ice hockey player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lieutenant and journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_Paolantonio"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player (d. 2019)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and politician, 75th Governor of North Carolina", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "1957", "text": "<PERSON>,  American basketball player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Polish journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Bulgarian footballer and politician, 50th Prime Minister of Bulgaria", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria\" title=\"Prime Minister of Bulgaria\">Prime Minister of Bulgaria</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Bulgaria", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bulgaria"}]}, {"year": "1959", "text": "<PERSON>, French-born Canadian science fiction writer (d. 2016)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born Canadian science fiction writer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-born Canadian science fiction writer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Romanian educator and politician, 5th President of Romania", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian educator and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian educator and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}]}, {"year": "1960", "text": "<PERSON>, Canadian wrestler", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian-American tennis player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Storm\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Swiss-German tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bunge\" title=\"<PERSON><PERSON> Bunge\"><PERSON><PERSON></a>, Swiss-German tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bunge\" title=\"<PERSON><PERSON> Bunge\"><PERSON><PERSON></a>, Swiss-German tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>unge"}]}, {"year": "1963", "text": "<PERSON>, English soprano and actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American author and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Romanian organist, composer, and educator", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian organist, composer, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian organist, composer, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actress, director, and playwright", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, director, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Indian politician, Minister of Railways", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, Minister of Railways", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, Minister of Railways", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>yal"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Mar%C4%8Diulionis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Mar%C4%8Diulionis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "Šar<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%A0ar%C5%ABnas_Mar%C4%8Diulionis"}]}, {"year": "1965", "text": "<PERSON>, Pakistani-American businessman and criminal", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-American businessman and criminal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-American businessman and criminal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sunny_Balwani"}]}, {"year": "1965", "text": "Infanta Cristina Federica of Spain", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Infanta_Cristina_Federica_of_Spain\" class=\"mw-redirect\" title=\"Infanta Cristina Federica of Spain\">Infanta Cristina Federica of Spain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Infanta_Cristina_Federica_of_Spain\" class=\"mw-redirect\" title=\"Infanta Cristina Federica of Spain\">Infanta Cristina Federica of Spain</a>", "links": [{"title": "Infanta Cristina Federica of Spain", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_Cristina_Federica_of_Spain"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Austrian-American drummer and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ligeti\" title=\"Lukas Ligeti\"><PERSON><PERSON></a>, Austrian-American drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ligeti\" title=\"Lukas Ligeti\"><PERSON><PERSON></a>, Austrian-American drummer and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lukas_Ligeti"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cricketer)\" title=\"<PERSON><PERSON> (cricketer)\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cricketer)\" title=\"<PERSON><PERSON> (cricketer)\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1966", "text": "<PERSON>, English photographer and curator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Russian mathematician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German-Turkish footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ta%C5%9Fk%C4%B1n_Aksoy\" title=\"Taşkın Aksoy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ta%C5%9Fk%C4%B1n_Aks<PERSON>\" title=\"Taşkın Aksoy\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German-Turkish footballer and manager", "links": [{"title": "Taşkın Aksoy", "link": "https://wikipedia.org/wiki/Ta%C5%9Fk%C4%B1n_Aksoy"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Italian cyclist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English-Welsh singer-songwriter, guitarist, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English-Welsh singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, English-Welsh singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_musician)"}]}, {"year": "1968", "text": "<PERSON>, English singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Ugandan-English journalist and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish actress, director, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Cayetana_Guill%C3%A9n_Cuervo\" title=\"Cayetana <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cayetana_Guill%C3%A9n_Cuervo\" title=\"Cayetana Guillén Cuervo\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish actress, director, and screenwriter", "links": [{"title": "Cayetana <PERSON>", "link": "https://wikipedia.org/wiki/Cayetana_Guill%C3%A9n_Cuervo"}]}, {"year": "1969", "text": "<PERSON><PERSON>, French author, screenwriter, and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author, screenwriter, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author, screenwriter, and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress, comedian, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Russian shot putter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian shot putter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Danish singer-songwriter, guitarist, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8ren_Rasted"}]}, {"year": "1970", "text": "<PERSON>, American rock musician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Rivers_Cuomo\" title=\"Rivers Cuomo\"><PERSON></a>, American rock musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rivers_Cuomo\" title=\"Rivers Cuomo\"><PERSON></a>, American rock musician", "links": [{"title": "Rivers Cuomo", "link": "https://wikipedia.org/wiki/Rivers_Cuomo"}]}, {"year": "1970", "text": "<PERSON>, New Zealand cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/N%C3%B3ra_K%C3%B6ves\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%B3ra_K%C3%B6ves\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%B3ra_K%C3%B6ves"}]}, {"year": "1972", "text": "<PERSON>, Canadian fiddler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian fiddler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian fiddler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Polish philosopher, historian, genealogist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher, historian, genealogist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish philosopher, historian, genealogist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American football player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1973", "text": "<PERSON>, American race car driver and television host", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>t\" title=\"<PERSON> Foust\"><PERSON></a>, American race car driver and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Foust\"><PERSON></a>, American race car driver and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oust"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Swedish singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian cricketer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Finnish singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lai<PERSON>\"><PERSON></a>, Finnish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ville_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian-American ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ri_Bure"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American stunt performer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American stunt performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, American stunt performer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American screenwriter and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" class=\"mw-redirect\" title=\"<PERSON> (writer)\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1975", "text": "<PERSON>, American model, actress, and author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model, actress, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, English footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, French pole vaulter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French pole vaulter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Earthwind_Moreland\" title=\"Earthwind Moreland\">Earth<PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Earthwind_Moreland\" title=\"Earthwind Moreland\">Earth<PERSON> Moreland</a>, American football player", "links": [{"title": "Earthwind Moreland", "link": "https://wikipedia.org/wiki/Earthwind_Moreland"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Finnish politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Rii<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rii<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ii<PERSON>_<PERSON>ra"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Hindu guru", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Vishwananda\" title=\"Vishwananda\"><PERSON><PERSON><PERSON><PERSON></a>, Hindu guru", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vishwananda\" title=\"Vishwana<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hindu guru", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vishwananda"}]}, {"year": "1979", "text": "<PERSON>, Australian actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)\" title=\"<PERSON> (Australian actress)\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)\" title=\"<PERSON> (Australian actress)\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON> (Australian actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actress)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Norwegian volleyball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%A5kedal\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_H%C3%A5kedal\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nila_H%C3%A5kedal"}]}, {"year": "1979", "text": "<PERSON>, American long jumper", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Florent_Mal<PERSON>\" title=\"Florent Mal<PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_<PERSON>\" title=\"Florent Mal<PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_<PERSON><PERSON>a"}]}, {"year": "1980", "text": "<PERSON>, Paraguayan footballer (d. 2012)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Mendieta\"><PERSON></a>, Paraguayan footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Diego Mendieta\"><PERSON></a>, Paraguayan footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Diego_Mendieta"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moon\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Moon\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Spanish basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German racing driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1981", "text": "<PERSON>, founder and executive director of the National History Bee and the National History Bowl", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>!_contestant)\" title=\"<PERSON> (<PERSON><PERSON><PERSON><PERSON>! contestant)\"><PERSON></a>, founder and executive director of the <a href=\"https://wikipedia.org/wiki/National_History_Bee\" class=\"mw-redirect\" title=\"National History Bee\">National History Bee</a> and the <a href=\"https://wikipedia.org/wiki/National_History_Bowl\" class=\"mw-redirect\" title=\"National History Bowl\">National History Bowl</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>!_contestant)\" title=\"<PERSON> (<PERSON><PERSON><PERSON><PERSON>! contestant)\"><PERSON></a>, founder and executive director of the <a href=\"https://wikipedia.org/wiki/National_History_Bee\" class=\"mw-redirect\" title=\"National History Bee\">National History Bee</a> and the <a href=\"https://wikipedia.org/wiki/National_History_Bowl\" class=\"mw-redirect\" title=\"National History Bowl\">National History Bowl</a>", "links": [{"title": "<PERSON> (Jeopardy! contestant)", "link": "https://wikipedia.org/wiki/<PERSON>_(<PERSON><PERSON><PERSON><PERSON>!_contestant)"}, {"title": "National History Bee", "link": "https://wikipedia.org/wiki/National_History_Bee"}, {"title": "National History Bowl", "link": "https://wikipedia.org/wiki/National_History_Bowl"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Czech ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Radim_Vrbata\" title=\"Radi<PERSON> Vrbata\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Radi<PERSON>_Vrbata\" title=\"Radi<PERSON> Vrbata\"><PERSON><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radim_<PERSON>rbata"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish politician", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)\" title=\"<PERSON> (cornerback)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (cornerback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cornerback)"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Welsh rugby union player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, Welsh rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, Welsh rugby union player", "links": [{"title": "<PERSON> (rugby player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Mexican-Uruguayan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-Uruguayan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, German runner", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Antje_M%C3%B<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antje_M%C3%B<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antje_M%C3%B6<PERSON><PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Portuguese racing driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_Albuquerque"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dominican baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>rop"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress and comedian", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Keisuke Honda\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Keis<PERSON> Honda\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball catcher", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball catcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball catcher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American child actress, fashion designer, and businesswoman", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child actress, fashion designer, and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American child actress, fashion designer, and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "Mary<PERSON><PERSON>, American child actress, fashion designer, and businesswoman", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American child actress, fashion designer, and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American child actress, fashion designer, and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1986", "text": "DJ <PERSON>, French DJ and record producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/DJ_<PERSON>\" title=\"DJ <PERSON>\">DJ <PERSON></a>, French DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_<PERSON>\" title=\"DJ <PERSON>\">DJ <PERSON></a>, French DJ and record producer", "links": [{"title": "DJ <PERSON>", "link": "https://wikipedia.org/wiki/DJ_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Greek computer scientist and author", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Verou\" title=\"<PERSON> Verou\"><PERSON></a>, Greek computer scientist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Verou\" title=\"<PERSON> Verou\"><PERSON></a>, Greek computer scientist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Verou"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Swedish singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/M%C3%A5ns_Zelmerl%C3%B6w\" title=\"<PERSON><PERSON><PERSON>löw\"><PERSON><PERSON><PERSON></a>, Swedish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A5ns_Zelmerl%C3%B6w\" title=\"<PERSON><PERSON><PERSON>löw\"><PERSON><PERSON><PERSON></a>, Swedish singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A5ns_Zelmerl%C3%B6w"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Marko_Grgi%C4%87_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marko_Grgi%C4%87_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Marko_Grgi%C4%87_(footballer)"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Finnish cross-country skier", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Kerttu_Niska<PERSON>\" title=\"Kerttu Ni<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish cross-country skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kerttu_Ni<PERSON>\" title=\"Kerttu Niska<PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish cross-country skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kert<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, British actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ben <PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ben Barba\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ben_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English racing driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/James_Calado"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey defenseman", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey defenseman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey defenseman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Greek footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English motocross racer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Motocross\" title=\"Motocross\">motocross</a> racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Motocross", "link": "https://wikipedia.org/wiki/Motocross"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>side\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>side"}]}, {"year": "1989", "text": "<PERSON>, Canadian wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1990", "text": "<PERSON>, Swiss tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>-<PERSON>, English actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American jumper", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Fijian rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Semi_Radradra\" title=\"Semi Radradra\"><PERSON></a>, Fijian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Semi_Radradra\" title=\"Semi Radradra\"><PERSON></a>, Fijian rugby league player", "links": [{"title": "<PERSON> Ra<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>dra"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Italian ski jumper (d. 2011)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian ski jumper (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian ski jumper (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Kazakhstani figure skater (d. 2018)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Denis Ten\"><PERSON></a>, Kazakhstani figure skater (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denis_Ten\" title=\"Denis Ten\"><PERSON></a>, Kazakhstani figure skater (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Indian archer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian archer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1994 —<PERSON><PERSON><PERSON>, Japanese actor[49]", "text": null, "html": "1994 —<PERSON><PERSON><PERSON>, Japanese actor[49] - <a href=\"https://wikipedia.org/wiki/1994\" title=\"1994\">1994</a> —<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1994\" title=\"1994\">1994</a> —<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "1994", "link": "https://wikipedia.org/wiki/1994"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1995", "text": "<PERSON>, New Zealand tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Colombian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B3s\" title=\"<PERSON>\"><PERSON></a>, Colombian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B3s\" title=\"<PERSON>\"><PERSON></a>, Colombian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_Ucr%C3%B3s"}]}, {"year": "2000", "text": "<PERSON>, Canadian swimmer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Canadian ice hockey player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean singer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Han-bin\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> Han-<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}], "Deaths": [{"year": "220", "text": "<PERSON><PERSON><PERSON>, Chinese general", "html": "220 - <a href=\"https://wikipedia.org/wiki/Xiahou_Dun\" title=\"Xiahou Dun\"><PERSON><PERSON><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Xiahou_Dun\" title=\"Xiahou Dun\"><PERSON><PERSON><PERSON></a>, Chinese general", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "976", "text": "<PERSON><PERSON>, Samanid emir", "html": "976 - <a href=\"https://wikipedia.org/wiki/Mansur_I\" title=\"Mansur I\"><PERSON><PERSON> <PERSON></a>, <PERSON><PERSON><PERSON> emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mansur_I\" title=\"Mansur I\"><PERSON><PERSON> <PERSON></a>, <PERSON><PERSON><PERSON> emir", "links": [{"title": "<PERSON>ur I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "995", "text": "<PERSON><PERSON>, Japanese nobleman (b. 961)", "html": "995 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Michikane\" title=\"Fujiwara no Michikane\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Michikane\" title=\"Fujiwara no Michikane\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 961)", "links": [{"title": "<PERSON><PERSON> no Michikane", "link": "https://wikipedia.org/wiki/Fujiwara_no_Michikane"}]}, {"year": "1036", "text": "<PERSON>, <PERSON><PERSON><PERSON> caliph (b. 1005)", "html": "1036 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> caliph (b. 1005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON> calip<PERSON> (b. 1005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1231", "text": "<PERSON> of Padua, Portuguese priest and saint (b. 1195)", "html": "1231 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua\" title=\"<PERSON> of Padua\"><PERSON> of Padua</a>, Portuguese priest and saint (b. 1195)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua\" title=\"<PERSON> of Padua\"><PERSON> of Padua</a>, Portuguese priest and saint (b. 1195)", "links": [{"title": "<PERSON> of Padua", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua"}]}, {"year": "1256", "text": "<PERSON><PERSON>, Japanese sculptor (b. 1173)", "html": "1256 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sculptor (b. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese sculptor (b. 1173)", "links": [{"title": "Tankei", "link": "https://wikipedia.org/wiki/Tankei"}]}, {"year": "1348", "text": "<PERSON>, Spanish prince (b. 1282)", "html": "1348 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Villena\"><PERSON></a>, Spanish prince (b. 1282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena\" class=\"mw-redirect\" title=\"<PERSON>, Prince of Villena\"><PERSON></a>, Spanish prince (b. 1282)", "links": [{"title": "<PERSON>, Prince of Villena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Villena"}]}, {"year": "1432", "text": "<PERSON><PERSON>, Frisian chieftain (b. c. 1408)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fockena\"><PERSON><PERSON></a>, Frisian chieftain (b. c. 1408)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fock<PERSON>\"><PERSON><PERSON></a>, Frisian chieftain (b. c. 1408)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ena"}]}, {"year": "1550", "text": "<PERSON>, Italian poet (b. 1485)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/Veronica_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (b. 1485)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Veronica_<PERSON>"}]}, {"year": "1636", "text": "<PERSON>, 1st Marquess of Huntly, Scottish politician (b. 1562)", "html": "1636 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Huntly\" title=\"<PERSON>, 1st Marquess of Huntly\"><PERSON>, 1st Marquess of Huntly</a>, Scottish politician (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Huntly\" title=\"<PERSON>, 1st Marquess of Huntly\"><PERSON>, 1st Marquess of Huntly</a>, Scottish politician (b. 1562)", "links": [{"title": "<PERSON>, 1st Marquess of Huntly", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_<PERSON>ly"}]}, {"year": "1645", "text": "<PERSON><PERSON><PERSON>, Japanese samurai (b. 1584)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai (b. 1584)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1661", "text": "<PERSON>, 2nd Earl of Monmouth, English politician (b. 1595)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Monmouth\" title=\"<PERSON>, 2nd Earl of Monmouth\"><PERSON>, 2nd Earl of Monmouth</a>, English politician (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Monmouth\" title=\"<PERSON>, 2nd Earl of Monmouth\"><PERSON>, 2nd Earl of Monmouth</a>, English politician (b. 1595)", "links": [{"title": "<PERSON>, 2nd Earl of Monmouth", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Monmouth"}]}, {"year": "1665", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (b. 1604)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1604)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, first German female doctor (b. 1715)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first German female doctor (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, first German female doctor (b. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1784", "text": "<PERSON>, American farmer and politician, 2nd President of the Continental Congress (b. 1717)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1846", "text": "<PERSON><PERSON><PERSON>, French geographer and author (b. 1767)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and author (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geographer and author (b. 1767)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%AEt_Eyri%C3%A8s"}]}, {"year": "1861", "text": "<PERSON>, English anatomist and surgeon (b. 1827)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anatomist and surgeon (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anatomist and surgeon (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Czech physician and dermatologist (b. 1805)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0koda\" title=\"<PERSON>\"><PERSON></a>, Czech physician and dermatologist (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0koda\" title=\"<PERSON>\"><PERSON></a>, Czech physician and dermatologist (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0koda"}]}, {"year": "1886", "text": "<PERSON>, king of Bavaria (b. 1845)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Bavaria\" title=\"<PERSON> II of Bavaria\"><PERSON> II</a>, king of Bavaria (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Bavaria\" title=\"<PERSON> II of Bavaria\"><PERSON> II</a>, king of Bavaria (b. 1845)", "links": [{"title": "<PERSON> II of Bavaria", "link": "https://wikipedia.org/wiki/Ludwig_II_of_Bavaria"}]}, {"year": "1894", "text": "<PERSON>, Australian politician, 15th Premier of South Australia (b. 1842)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 15th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 5th Premier of Quebec (b. 1840)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek painter and educator (b. 1832)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Nikif<PERSON><PERSON>_Lytras\" title=\"Nik<PERSON><PERSON>s Lytras\"><PERSON><PERSON><PERSON><PERSON></a>, Greek painter and educator (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nik<PERSON><PERSON><PERSON>_Lyt<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Lyt<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek painter and educator (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikiforos_Lytras"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Canadian sculptor (b. 1850)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sculptor (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian sculptor (b. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9bert"}]}, {"year": "1918", "text": "<PERSON>, Russian Grand Duke (b. 1878)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\"><PERSON></a>, Russian Grand Duke (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duke <PERSON> of Russia\"><PERSON></a>, Russian Grand Duke (b. 1878)", "links": [{"title": "Grand Duke <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duke_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1930", "text": "<PERSON>, American-English racing driver (b. 1896)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English racing driver (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English racing driver (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Japanese physician and bacteriologist (b. 1851)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Kitasato_Shi<PERSON>abur%C5%8D\" title=\"Kitasa<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physician and bacteriologist (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kitasato_Shi<PERSON>abur%C5%8D\" title=\"Kitasato <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physician and bacteriologist (b. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kitasato_Shibasabur%C5%8D"}]}, {"year": "1939", "text": "<PERSON>, Australian cricketer (b. 1863)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (b. 1863)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Macedonian author and activist (b. 1908)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Ko%C4%8Do_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian author and activist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ko%C4%8Do_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Macedonian author and activist (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko%C4%8Do_<PERSON>cin"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Yugoslav Partisan divisional commander and People's Hero of Yugoslavia (b. 1905)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav Partisan divisional commander and <a href=\"https://wikipedia.org/wiki/People%27s_Hero_of_Yugoslavia\" class=\"mw-redirect\" title=\"People's Hero of Yugoslavia\">People's Hero of Yugoslavia</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Devi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav Partisan divisional commander and <a href=\"https://wikipedia.org/wiki/People%27s_Hero_of_Yugoslavia\" class=\"mw-redirect\" title=\"People's Hero of Yugoslavia\">People's Hero of Yugoslavia</a> (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>va_Kova%C4%8Devi%C4%87"}, {"title": "People's Hero of Yugoslavia", "link": "https://wikipedia.org/wiki/People%27s_Hero_of_Yugoslavia"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese author (b. 1909)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian engineer and politician, 16th Prime Minister of Australia (b. 1885)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian engineer and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian engineer and politician, 16th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1957", "text": "<PERSON>, American high jumper and pole vaulter (b. 1876)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high jumper and pole vaulter (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high jumper and pole vaulter (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English poet and academic (b. 1887)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Austrian-Israeli philosopher and theologian (b. 1878)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli philosopher and theologian (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Israeli philosopher and theologian (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian farmer and politician (b. 1890)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian farmer and politician (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian farmer and politician (b. 1890)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Indian journalist, director, and producer (b. 1898)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Keshav_Atre\" title=\"<PERSON><PERSON>had Keshav Atre\"><PERSON><PERSON><PERSON></a>, Indian journalist, director, and producer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Keshav_Atre\" title=\"Pralhad Keshav Atre\"><PERSON><PERSON><PERSON>re</a>, Indian journalist, director, and producer (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Keshav_Atre"}]}, {"year": "1972", "text": "<PERSON>, Hungarian biophysicist and academic, Nobel Prize laureate (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9k%C3%A9sy\" title=\"<PERSON>\"><PERSON></a>, Hungarian biophysicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9k%C3%A9sy\" title=\"<PERSON>\"><PERSON></a>, Hungarian biophysicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9k%C3%A9sy"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1972", "text": "<PERSON>, Austrian-German spy (b. 1891)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German spy (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German spy (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Egyptian-Italian singer-songwriter and pianist (b. 1945)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Demetrio_Stratos\" title=\"Demetrio Stratos\"><PERSON><PERSON><PERSON></a>, Egyptian-Italian singer-songwriter and pianist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Demetrio_Stratos\" title=\"Demetrio Stratos\"><PERSON><PERSON><PERSON></a>, Egyptian-Italian singer-songwriter and pianist (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetrio_Stratos"}]}, {"year": "1980", "text": "<PERSON>, Guyanese historian and activist (b. 1942)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese historian and activist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese historian and activist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Brazilian zoologist and physician (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Oliv%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist and physician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliv%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian zoologist and physician (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oliv%C3%A9<PERSON>_<PERSON>nto"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese singer-songwriter (b. 1944)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es\" title=\"António Variações\"><PERSON><PERSON><PERSON><PERSON> Variaçõ<PERSON></a>, Portuguese singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es\" title=\"António Variações\"><PERSON><PERSON><PERSON><PERSON> Variações</a>, Portuguese singer-songwriter (b. 1944)", "links": [{"title": "António Variações", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_Varia%C3%A7%C3%B5es"}]}, {"year": "1986", "text": "<PERSON>, American clarinet player, songwriter, and bandleader (b. 1909)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Goodman\" title=\"Benny Goodman\"><PERSON></a>, American clarinet player, songwriter, and bandleader (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Goodman\" title=\"Benny Goodman\"><PERSON></a>, American clarinet player, songwriter, and bandleader (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American actress (b. 1924)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American television personality and puppeteer (b. 1907)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality and puppeteer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American television personality and puppeteer (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Canadian runner (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian runner (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian runner (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_C%C3%B4t%C3%A9"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American soldier, pilot, and astronaut (b. 1924)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, pilot, and astronaut (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soldier, pilot, and astronaut (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Romanian-French actress (b. 1923)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Vietnamese lawyer and academic (b. 1909)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Nguy<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lawyer and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uy<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese lawyer and academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English sculptor and academic (b. 1899)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English sculptor and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Norwegian ski jumper (b. 1911)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Birger_Ruud\" title=\"Bir<PERSON> Ruud\"><PERSON><PERSON><PERSON></a>, Norwegian ski jumper (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birger_Ruud\" title=\"Birger Ruud\"><PERSON><PERSON><PERSON></a>, Norwegian ski jumper (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birger_<PERSON>uud"}]}, {"year": "1998", "text": "<PERSON>, English cartoonist  (b. 1917)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American navigator and meteorologist (b. 1919)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American navigator and meteorologist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American navigator and meteorologist (b. 1919)", "links": [{"title": "<PERSON> (meteorologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(meteorologist)"}]}, {"year": "2002", "text": "<PERSON>, Polish-American author (b. 1927)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_W<PERSON>j<PERSON><PERSON>ws<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Pakistani lawyer and politician, Prime Minister of Pakistan (b. 1916)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "2004", "text": "<PERSON>, American journalist and author (b. 1952)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Portuguese academic and politician (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese academic and politician (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese academic and politician (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American pianist and composer (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist and composer (b. 1915)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(composer)"}]}, {"year": "2006", "text": "<PERSON>, Irish lawyer and politician, 7th Taoiseach of Ireland (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoise<PERSON> of Ireland</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Lebanese judge and politician (b. 1942)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Walid_E<PERSON>\" title=\"Walid E<PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese judge and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Walid_E<PERSON>\" title=\"Walid Eido\"><PERSON><PERSON><PERSON></a>, Lebanese judge and politician (b. 1942)", "links": [{"title": "Walid Eido", "link": "https://wikipedia.org/wiki/Walid_Eido"}]}, {"year": "2008", "text": "<PERSON>, American journalist and lawyer (b. 1950)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and lawyer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and lawyer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Lebanese scholar and politician (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese scholar and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lebanese scholar and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Yakan"}]}, {"year": "2010", "text": "<PERSON>, American singer and businessman, founded <PERSON> (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" title=\"<PERSON> (brand)\"><PERSON></a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" title=\"<PERSON> (brand)\"><PERSON></a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (brand)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)"}]}, {"year": "2012", "text": "<PERSON>, American pilot and engineer (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sam <PERSON>field\"><PERSON></a>, American pilot and engineer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sam <PERSON>field\"><PERSON></a>, American pilot and engineer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sam_Beddingfield"}]}, {"year": "2012", "text": "<PERSON>, Australian pianist, composer, and bandleader (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and bandleader (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian pianist, composer, and bandleader (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French philosopher and author (b. 1913)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Slovenian composer and translator (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEe_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian composer and translator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian composer and translator (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEe_Humer"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Pakistani ghazal singer and playback singer for Lollywood (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani ghazal singer and playback singer for Lollywood (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani ghazal singer and playback singer for Lollywood (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman, founded Deutsch Inc. (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ad_executive)\" title=\"<PERSON> (ad executive)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Deutsch_Inc.\" class=\"mw-redirect\" title=\"Deutsch Inc.\">Deutsch Inc.</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ad_executive)\" title=\"<PERSON> (ad executive)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Deutsch_Inc.\" class=\"mw-redirect\" title=\"Deutsch Inc.\">Deutsch Inc.</a> (b. 1929)", "links": [{"title": "<PERSON> (ad executive)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ad_executive)"}, {"title": "Deutsch Inc.", "link": "https://wikipedia.org/wiki/Deutsch_Inc."}]}, {"year": "2013", "text": "<PERSON>, American flute player and saxophonist (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Sam_Most\" title=\"Sam Most\"><PERSON></a>, American flute player and saxophonist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sam_Most\" title=\"Sam Most\"><PERSON></a>, American flute player and saxophonist (b. 1930)", "links": [{"title": "Sam Most", "link": "https://wikipedia.org/wiki/Sam_Most"}]}, {"year": "2013", "text": "<PERSON>, American educator and activist (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Albert White Hat\"><PERSON></a>, American educator and activist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Albert White Hat\"><PERSON></a>, American educator and activist (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Moroccan economist and sociologist (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan economist and sociologist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan economist and sociologist (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_G<PERSON>\" title=\"G<PERSON>la Grosics\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>la Grosics\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>la_G<PERSON>ics"}]}, {"year": "2014", "text": "<PERSON>, Scottish-Australian singer-songwriter and guitarist (b. 1946)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian singer-songwriter and guitarist (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player and coach (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American poet, playwright, and critic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" class=\"mw-redirect\" title=\"<PERSON> (playwright)\"><PERSON></a>, American poet, playwright, and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" class=\"mw-redirect\" title=\"<PERSON> (playwright)\"><PERSON></a>, American poet, playwright, and critic (b. 1924)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_(playwright)"}]}, {"year": "2015", "text": "<PERSON>, American saxophonist and clarinet player (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Argentinian actor, director, and screenwriter (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor, director, and screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ren%C3%A1n"}]}, {"year": "2015", "text": "<PERSON>, New Zealand cricketer and coach (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1937)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, American author (b. 1933) ", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1933) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (b. 1933) ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American R&B singer (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American child actor (b. 1978)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American child actor (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American child actor (b. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}